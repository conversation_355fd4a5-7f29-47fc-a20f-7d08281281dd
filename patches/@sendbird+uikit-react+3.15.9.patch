diff --git a/node_modules/@sendbird/uikit-react/chunks/bundle-Dsmtu-nW.js b/node_modules/@sendbird/uikit-react/chunks/bundle-Dsmtu-nW.js
index 1d3a249..18747f9 100644
--- a/node_modules/@sendbird/uikit-react/chunks/bundle-Dsmtu-nW.js
+++ b/node_modules/@sendbird/uikit-react/chunks/bundle-Dsmtu-nW.js
@@ -93,7 +93,8 @@ var SUPPORTED_MIMES = {
         'video/ogg',
         'video/webm',
         'video/mp4',
-        // 'video/quicktime', // NOTE: Do not support ThumbnailMessage for the .mov video
+        'video/quicktime', // NOTE: Do not support ThumbnailMessage for the .mov video
+        'video/3gpp', // .3gp
     ],
     AUDIO: [
         'audio/aac',
@@ -117,7 +118,6 @@ var SUPPORTED_MIMES = {
         'text/javascript',
         'text/xml',
         'text/x-log',
-        'video/quicktime', // NOTE: Assume this video is a normal file, not video
     ],
     APPLICATION: [
         'application/x-abiword',
diff --git a/node_modules/@sendbird/uikit-react/cjs/chunks/bundle-adc_Gum7.js b/node_modules/@sendbird/uikit-react/cjs/chunks/bundle-adc_Gum7.js
index da4ddc8..ab9a04f 100644
--- a/node_modules/@sendbird/uikit-react/cjs/chunks/bundle-adc_Gum7.js
+++ b/node_modules/@sendbird/uikit-react/cjs/chunks/bundle-adc_Gum7.js
@@ -95,7 +95,8 @@ var SUPPORTED_MIMES = {
         'video/ogg',
         'video/webm',
         'video/mp4',
-        // 'video/quicktime', // NOTE: Do not support ThumbnailMessage for the .mov video
+        'video/quicktime', // NOTE: Do not support ThumbnailMessage for the .mov video
+        'video/3gpp', // .3gp
     ],
     AUDIO: [
         'audio/aac',
@@ -119,7 +120,6 @@ var SUPPORTED_MIMES = {
         'text/javascript',
         'text/xml',
         'text/x-log',
-        'video/quicktime', // NOTE: Assume this video is a normal file, not video
     ],
     APPLICATION: [
         'application/x-abiword',
diff --git a/node_modules/@sendbird/uikit-react/ui/MessageInput.js b/node_modules/@sendbird/uikit-react/ui/MessageInput.js
index d143762..0a16c32 100644
--- a/node_modules/@sendbird/uikit-react/ui/MessageInput.js
+++ b/node_modules/@sendbird/uikit-react/ui/MessageInput.js
@@ -455,7 +455,7 @@ var MessageInput = React__default.forwardRef(function (props, externalRef) {
                         React__default.createElement(Icon, { type: IconTypes.ATTACH, fillColor: disabled ? IconColors.ON_BACKGROUND_4 : IconColors.CONTENT_INVERSE, width: "20px", height: "20px" }),
                         React__default.createElement("input", { className: "sendbird-message-input--attach-input", type: "file", ref: fileInputRef, 
                             // It will affect to <Channel /> and <Thread />
-                            onChange: function (event) { return uploadFile(event); }, accept: getMimeTypesUIKitAccepts(acceptableMimeTypes), multiple: isSelectingMultipleFilesEnabled && isChannelTypeSupportsMultipleFilesMessage(channel) }))))),
+                            onChange: function (event) { return uploadFile(event); }, multiple: isSelectingMultipleFilesEnabled && isChannelTypeSupportsMultipleFilesMessage(channel) }))))),
             isVoiceMessageEnabled && !isEdit && !isInput && (React__default.createElement(IconButton, { className: "sendbird-message-input--voice-message", width: "32px", height: "32px", onClick: onVoiceMessageIconClick }, (renderVoiceMessageIcon === null || renderVoiceMessageIcon === void 0 ? void 0 : renderVoiceMessageIcon()) || (React__default.createElement(Icon, { type: IconTypes.AUDIO_ON_LINED, fillColor: disabled ? IconColors.ON_BACKGROUND_4 : IconColors.CONTENT_INVERSE, width: "20px", height: "20px" }))))),
         isEdit && (React__default.createElement("div", { className: "sendbird-message-input--edit-action", "data-testid": "sendbird-message-input--edit-action" },
             React__default.createElement(Button, { className: "sendbird-message-input--edit-action__cancel", type: ButtonTypes.SECONDARY, size: ButtonSizes.SMALL, onClick: onCancelEdit }, stringSet.BUTTON__CANCEL),
