# Hammr Web

## Prerequisites

Copy `environment variables` locally:

```bash
# Google Maps API key
$ cp .env.example .env

$ cp .env.local.example .env.local
```

## Scripts

### ag-grid-update.sh

Installs all the dependencies from `ag-grid` in order to save time and ensure consistency across all the dependencies.

Run `yarn install-ag-grid` every time you want to update the version of the package.

All new `ag-grid` packages must also be added to the script.

## Support

Reach out to <PERSON> at <EMAIL> for questions.
