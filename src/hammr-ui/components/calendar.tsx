import { Date<PERSON><PERSON><PERSON>, DayPicker, <PERSON><PERSON><PERSON><PERSON><PERSON>, Matcher } from 'react-day-picker';
import { cn } from '@/utils/cn';
import Button from '../components/button';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { FormMessage } from './form';
import dayjs from 'dayjs';
import { RiArrowLeftSLine, RiArrowRightSLine, RiErrorWarningFill } from '@remixicon/react';

type CalendarSingleProps = {
  mode: 'single';
  onApply?: (date: Date | null) => void;
  value: Date | null;
};

type CalendarRangeProps = {
  mode: 'range';
  onApply?: (range: [Date, Date] | null) => void;
  value: [Date, Date] | null;
};

export type Props = {
  onCancel?: () => void;
  dayPickerProps?: DayPickerProps;
  autoApply?: boolean;
  isLoading?: boolean;
  error?: ReactNode | undefined;
} & (CalendarSingleProps | CalendarRangeProps);

function isDateRange(value: Matcher | Matcher[]): value is DateRange {
  return typeof value !== 'string' && 'from' in (value as DateRange) && 'to' in (value as DateRange);
}

export function Calendar(props: Props) {
  // Implement keying to ensure the component state resets properly when the value changes. This is better than using useEffect.
  return <CalendarInner {...props} key={props.value?.toString()} />;
}

function CalendarInner({ error, mode, onApply, onCancel, value, dayPickerProps, autoApply, isLoading }: Props) {
  const [internalSelected, setInternalSelected] = useState<any>(
    value ? (mode === 'single' ? value : { from: value[0], to: value[1] }) : null
  );
  const [month, setMonth] = useState<Date>(value ? (mode === 'single' ? value : value[1]) : new Date());

  const canAutoApply = useMemo(() => {
    return autoApply && mode === 'single';
  }, [autoApply, mode]);

  const handleApply = () => {
    if (!internalSelected) {
      onApply?.(null);
      return;
    }

    if (mode === 'single') {
      onApply?.(internalSelected as Date);
    } else {
      if (dayjs(internalSelected.from).isBefore(internalSelected.to)) {
        onApply?.([internalSelected.from as Date, internalSelected.to as Date]);
      } else {
        onApply?.([internalSelected.to as Date, internalSelected.from as Date]);
      }
    }
  };

  const handleSelectRange = (value: [Date, Date] | undefined) => {
    if (!value) {
      setInternalSelected(undefined);
    } else {
      setInternalSelected({
        from: value[0],
        to: value[1],
      });
    }
  };

  const handleSelect = (value: any) => {
    setInternalSelected(value);

    if (canAutoApply && value) {
      onApply?.(value);
    }
  };

  const [isMouseDown, setIsMouseDown] = useState(false);
  const [mouseDownOn, setMouseDownOn] = useState<'start' | 'end' | null>(null);

  useEffect(() => {
    function handleMouseUp() {
      setIsMouseDown(false);
      setMouseDownOn(null);
    }
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  return (
    <div className={cn('flex rounded-2xl border border-soft-200 bg-background')}>
      <div
        className={cn('flex flex-col')}
        onMouseDown={(e) => {
          if (!(mode === 'range')) return;

          if (e.button === 0) {
            setIsMouseDown(true);
          }

          if (e.target instanceof HTMLButtonElement) {
            if (e.target.parentElement.classList.contains('rdp-range_start')) {
              setMouseDownOn('start');
            }
            if (e.target.parentElement.classList.contains('rdp-range_end')) {
              setMouseDownOn('end');
            }
          }
        }}
        onMouseUp={() => {
          setIsMouseDown(false);
          setMouseDownOn(null);
        }}
      >
        <DayPicker
          showOutsideDays
          components={{
            Chevron: ({ orientation }) => {
              switch (orientation) {
                case 'left':
                  return <RiArrowLeftSLine className="pointer-events-none" />;
                case 'right':
                  return <RiArrowRightSLine className="pointer-events-none" />;
                default:
                  return <></>;
              }
            },
          }}
          required
          mode={mode}
          selected={internalSelected}
          month={month}
          defaultMonth={month}
          onMonthChange={(month) => setMonth(month)}
          onDayMouseEnter={(day) => {
            if (isMouseDown && mode === 'range' && internalSelected && isDateRange(internalSelected)) {
              const selectedRange = internalSelected as DateRange;
              if (selectedRange.from && !selectedRange.to) {
                handleSelectRange([selectedRange.from, day]);
              } else {
                if (mouseDownOn === 'start') {
                  handleSelectRange([day, selectedRange.to]);
                }
                if (mouseDownOn === 'end') {
                  handleSelectRange([selectedRange.from, day]);
                }
              }
            }
          }}
          onDayClick={(day, modifiers) => {
            if (modifiers.disabled) {
              return;
            }

            if (mode === 'range') {
              const selectedRange = internalSelected as DateRange | undefined;
              if (selectedRange && selectedRange.from && !selectedRange.to) {
                handleSelectRange([selectedRange.from, day]);
              } else {
                handleSelectRange([day, undefined]);
              }
            }
          }}
          onSelect={(value: any) => handleSelect(value)}
          className="size-small"
          {...dayPickerProps}
        />

        {error && (
          <div className="max-w-[280px] px-3 pb-3">
            <div className="flex rounded-lg bg-error-lighter p-2">
              <div>
                <RiErrorWarningFill color="#FB3748" />
              </div>
              <FormMessage className="flex items-center justify-center text-wrap px-3 text-strong-950">
                {error}
              </FormMessage>
            </div>
          </div>
        )}

        {!canAutoApply && (
          <div className="flex items-center gap-4 border-t border-soft-200 p-4 text-sm">
            <Button variant="outline" color="neutral" fullWidth size="small" onClick={onCancel}>
              Cancel
            </Button>
            <Button fullWidth size="small" onClick={handleApply} disabled={!!error} loading={isLoading}>
              Apply
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
