import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { cn } from '../lib/utils';
import s from './time-picker.module.css'; // Using the same styles
import { Popover, PopoverContent, PopoverTrigger } from './popover';

export interface DurationPickerProps {
  disabled?: boolean;
  value?: number | null;
  onChange?: (value: number | null) => void;
  placeholder?: string;
  fullWidth?: boolean;
  name?: string;
  defaultValue?: number;
  minHour?: number;
  maxHour?: number;
}

const CELL_SIZE = 40;
const CELL_GAP = 4;
const ROWS = 5;

function DurationPicker({
  value,
  onChange,
  disabled,
  fullWidth,
  placeholder,
  defaultValue,
  minHour = 1,
  maxHour = 167,
}: DurationPickerProps) {
  const [open, setOpen] = useState(false);
  const controlled = value !== undefined;
  const [internalValue, setInternalValue] = useState<number | undefined>(defaultValue);

  const finalValue = controlled ? value : internalValue;

  const selectedHours = useMemo(() => Math.floor((finalValue ?? 0) / 60), [finalValue]);
  const selectedMinutes = useMemo(() => (finalValue ?? 0) % 60, [finalValue]);

  const displayValue = useMemo(() => {
    if (finalValue === undefined || finalValue === null) {
      return placeholder ?? 'Duration';
    }

    const hoursText = selectedHours > 0 ? `${selectedHours}h` : '';
    const minutesText = selectedMinutes > 0 ? `${selectedMinutes}m` : '';
    return `${hoursText} ${minutesText}`.trim() || '0m';
  }, [finalValue, placeholder, selectedHours, selectedMinutes]);

  return (
    <Popover open={open} onOpenChange={(open) => setOpen(open)}>
      <PopoverTrigger asChild>
        <button
          type="button"
          className={cn(
            'flex h-10 w-full items-center gap-2 overflow-hidden rounded-10 border border-soft-200 bg-background shadow-xs',
            'ring-2 ring-transparent ring-offset-background',
            'group',
            'data-[state="open"]:border-strong-950 data-[state="open"]:ring-strong-400/25 data-[state="open"]:ring-offset-2',
            disabled && 'pointer-events-none border-transparent bg-weak-50 !text-disabled-300',
            'aria-[invalid=true]:border-error-base',
            fullWidth && 'w-full',
            'bg-background text-sm text-sub-600',
            'px-2'
          )}
        >
          <span className={cn(!finalValue ? 'text-soft-400' : 'text-foreground')}>{displayValue}</span>
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="mt-1 w-auto overflow-hidden !rounded-20 border border-soft-200 p-0 !shadow-md"
        align="start"
      >
        <DurationSelection
          value={finalValue ?? 0}
          onChange={(value) => {
            if (controlled) {
              onChange?.(value);
            } else {
              setInternalValue(value);
            }
          }}
          minHour={minHour}
          maxHour={maxHour}
        />
      </PopoverContent>
    </Popover>
  );
}

DurationPicker.displayName = 'DurationPicker';

export { DurationPicker };

interface DurationSelectionProps {
  value: number;
  onChange: (value: number) => void;
  minHour: number;
  maxHour: number;
}

const DurationSelection: FC<DurationSelectionProps> = ({ value, onChange, minHour, maxHour }) => {
  const hoursList = useMemo(() => {
    return Array.from({ length: maxHour - minHour + 1 }, (_, i) => (i + minHour).toString().padStart(2, '0'));
  }, [minHour, maxHour]);

  const minutesList = useMemo(() => {
    return Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  }, []);

  const selectedHours = Math.floor(value / 60);
  const selectedMinutes = value % 60;

  return (
    <div className="bg-background">
      <div
        className="relative flex flex-row"
        style={{
          width: CELL_SIZE * 2 + CELL_GAP + 24,
          padding: 12,
          gap: CELL_GAP,
        }}
      >
        <ItemSelectionList
          items={hoursList}
          value={selectedHours.toString().padStart(2, '0')}
          onChange={(value) => {
            const newHours = parseInt(value, 10);
            const totalMinutes = newHours * 60 + selectedMinutes;
            onChange(totalMinutes);
          }}
        />
        <ItemSelectionList
          items={minutesList}
          value={selectedMinutes.toString().padStart(2, '0')}
          onChange={(value) => {
            const newMinutes = parseInt(value, 10);
            const totalMinutes = selectedHours * 60 + newMinutes;
            onChange(totalMinutes);
          }}
        />
        {/* Overlay labels */}
        <div
          style={{
            height: CELL_SIZE,
            zIndex: 1,
            top: CELL_SIZE * 2 + CELL_GAP * 2 + 12,
            left: 12,
            right: 12,
          }}
          className="absolute flex items-center justify-center rounded-8 border border-soft-200 bg-weak-50 text-foreground shadow"
        >
          <span style={{ marginTop: -2 }}>:</span>
        </div>
      </div>
    </div>
  );
};

interface ItemSelectionListProps {
  items: string[];
  value: string;
  onChange?: (value: string) => void;
}

const ItemSelectionList: FC<ItemSelectionListProps> = (props) => {
  const items = useMemo(() => {
    const topEmptyRows = 2;
    const bottomEmptyRows = 2;
    return [...Array(topEmptyRows).fill(null), ...props.items, ...Array(bottomEmptyRows).fill(null)];
  }, [props.items]);

  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      const _value = props.value || props.items[0];
      const index = items.indexOf(_value);
      if (index !== -1) {
        ref.current.scrollTo({
          top: (index - 2) * (CELL_SIZE + CELL_GAP),
          behavior: 'instant',
        });
      }
    }
  }, [props.value, props.items, items]);

  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const valueRef = useRef(props.value);
  const onChangeRef = useRef(props.onChange);

  valueRef.current = props.value;
  onChangeRef.current = props.onChange;

  const handleScroll = useCallback(
    (target: HTMLDivElement) => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        const scrollY = target.scrollTop;
        const itemIndex = Math.round(scrollY / (CELL_SIZE + CELL_GAP));

        const _value = items[itemIndex + 2];

        if (_value && valueRef.current !== _value) {
          onChangeRef.current?.(_value);
        }

        clearTimeout(scrollTimeoutRef.current!);
      }, 300);
    },
    [items]
  );

  return (
    <div
      ref={ref}
      className={cn('z-10 flex flex-col', s.noscrollbar)}
      style={{
        gap: `${CELL_GAP}px`,
        height: ROWS * CELL_SIZE + (ROWS - 1) * CELL_GAP,
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollSnapType: 'y mandatory',
      }}
      onClick={(event) => {
        event.stopPropagation();
      }}
      onScroll={(event) => {
        event.stopPropagation();
        handleScroll(event.currentTarget);
      }}
      onWheel={(event) => {
        event.stopPropagation();
      }}
    >
      {items.map((item, index) => (
        <div
          key={index}
          className={cn(
            'flex shrink-0 cursor-pointer items-center justify-center text-soft-400',
            props.value === item && 'text-foreground'
          )}
          style={{
            width: CELL_SIZE,
            height: CELL_SIZE,
            scrollSnapAlign: 'start',
          }}
          onClick={() => {
            if (item !== null) {
              ref.current?.scrollTo({
                top: (index - 2) * (CELL_SIZE + CELL_GAP),
                behavior: 'smooth',
              });
            }
          }}
        >
          {item}
        </div>
      ))}
    </div>
  );
};
