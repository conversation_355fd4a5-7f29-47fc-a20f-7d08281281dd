import { cn } from '@/utils/cn';
import { DayPickerProps } from 'react-day-picker';
import { useRef, useState } from 'react';
import { RiCalendarLine } from '@remixicon/react';
import dayjs from 'dayjs';
import { Popover, PopoverContent } from './popover';
import { Calendar } from './calendar';
import { PopoverAnchor } from '@radix-ui/react-popover';
import { TimePicker } from './time-input';

interface Props {
  value: Date | null;
  onChange: (value: Date | null) => void;
  className?: string;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  dayPickerProps?: DayPickerProps;
  disabled?: boolean;
  'aria-invalid'?: boolean;
}

export function DateTimeInput({
  value,
  onChange,
  className,
  inputProps,
  dayPickerProps,
  disabled,
  'aria-invalid': ariaInvalid,
}: Props) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isTimePickerOpen, setIsTimePickerOpen] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="relative">
      <div
        aria-invalid={!!ariaInvalid}
        className={cn(
          'flex cursor-pointer gap-2 rounded-10 border border-soft-200 px-3 py-[9px] text-soft-400',
          'hover:bg-weak-50 hover:text-sub-600',
          'ring-strong-400/25 ring-offset-2 ring-offset-white-0 focus-within:border-strong-950 focus-within:text-sub-600 focus-within:ring-2',
          value && 'text-strong-950 hover:text-strong-950',
          'disabled-within:cursor-not-allowed disabled-within:border-transparent disabled-within:bg-weak-50 disabled-within:text-disabled-300',
          'aria-[invalid=true]:border-error-base aria-[invalid=true]:ring-error-light',
          className
        )}
        onClick={(event) => {
          // first focus on the input element when the div is clicked on
          inputRef.current?.focus();

          // then open the calendar or time picker based on mouse position
          const rect = event.currentTarget.getBoundingClientRect();
          const mousePositionFromLeft = event.clientX - rect.left;

          const DEFAULT_DATE_INPUT_WIDTH = 130; // this is less accurate but good enough guess
          const dateInput = document.querySelector('.date-text-for-size');
          const dateInputWidth = dateInput?.getBoundingClientRect().width;

          const calendarTriggerRegionWidth = 12 + 20 + 8 + (dateInputWidth ?? DEFAULT_DATE_INPUT_WIDTH) + 5; // 12 = padding, 20 = icon width, 8 = gap

          if (mousePositionFromLeft < calendarTriggerRegionWidth) {
            setIsCalendarOpen(true);
          } else {
            setIsTimePickerOpen(true);
          }
        }}
      >
        <RiCalendarLine className={cn('size-5 shrink-0', value && 'text-sub-600', disabled && 'text-disabled-300')} />
        <input
          ref={inputRef}
          disabled={disabled}
          {...inputProps}
          type="datetime-local"
          className={cn(
            'h-5 w-full cursor-[inherit] bg-inherit text-sm uppercase focus:text-strong-950 focus:outline-none',
            '[&::-webkit-calendar-picker-indicator]:hidden',
            inputProps?.className
          )}
          value={value ? dayjs(value).format('YYYY-MM-DDTHH:mm') : ''}
          onChange={(e) => {
            const _value = e.target.value;

            if (_value) {
              const date = new Date(_value); // this intermediate step because dayjs interprets year of the format '0020' as 1920
              onChange(dayjs(date).toDate());
            } else {
              onChange(null);
            }
          }}
          max="9999-12-31T23:59"
          onKeyDown={(event) => {
            if (event.code === 'Space') {
              event.preventDefault();
              event.stopPropagation();
            }
            if (event.key === 'Enter') {
              event.preventDefault();
              setIsCalendarOpen(false);
              setIsTimePickerOpen(false);
            }
          }}
        />
      </div>

      {/* the below date text is only for calculating the width of the element */}
      <div className="date-text-for-size pointer-events-none absolute top-0 flex gap-px bg-inherit text-sm tracking-wider opacity-0">
        {(value ? dayjs(value).format('MM/DD/YYYY') : 'MM/DD/YYYY').split(/(\/)/).map((string, i) => (
          <span key={i}>{string}</span>
        ))}
      </div>

      {/* It's important that we keep the popover code outside of the div that focuses the input when clicking on it. */}
      {/* Otherwise clicking on the popover will trigger a click event on the div causing the input to focus and state change. */}
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverAnchor className="pointer-events-none absolute inset-y-0 left-0 w-px"></PopoverAnchor>
        <PopoverContent className="my-1 w-auto" align="start" onOpenAutoFocus={(e) => e.preventDefault()}>
          <Calendar
            mode="single"
            value={value}
            autoApply
            onApply={(date) => {
              // keep the time part of the date
              date?.setHours(value?.getHours() || 0);
              date?.setMinutes(value?.getMinutes() || 0);

              onChange(date);
              setIsCalendarOpen(false);
            }}
            dayPickerProps={dayPickerProps}
          />
        </PopoverContent>
      </Popover>

      <Popover open={isTimePickerOpen} onOpenChange={setIsTimePickerOpen}>
        <PopoverAnchor className="pointer-events-none absolute inset-y-0 left-[130px] w-px"></PopoverAnchor>
        <PopoverContent className="my-1 w-auto" align="start" onOpenAutoFocus={(e) => e.preventDefault()}>
          <TimePicker value={value} onChange={onChange} />
        </PopoverContent>
      </Popover>
    </div>
  );
}
