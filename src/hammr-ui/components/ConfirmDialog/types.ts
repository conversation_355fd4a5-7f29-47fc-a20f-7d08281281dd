import { ComponentProps, ReactNode } from 'react';
import Button from '../button';

export interface ConfirmResult {
  confirmed: boolean;
}

export interface BasicDialogProps {
  id: string;
  isOpen: boolean;
}

export interface ConfirmDialogOptions {
  title?: string;
  subtitle: ReactNode;
  icon?: ReactNode;
  confirmButton?: ComponentProps<typeof Button>;
  confirmButtonText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}
