import { ConfirmDialogItemState, useConfirmDialogStore } from './store';
import { v4 as uuid } from 'uuid';
import { ConfirmDialogOptions, ConfirmResult } from './types';

function closeConfirmDialog(id: string) {
  useConfirmDialogStore.setState((state) => ({
    items: state.items.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          isOpen: false,
        };
      }

      return item;
    }),
  }));

  // Simulate a delay to show the dialog closing animation
  setTimeout(() => {
    useConfirmDialogStore.setState((state) => ({
      items: state.items.filter((item) => item.id !== id),
    }));
  }, 1000);
}

export async function openConfirmDialog(options: Omit<ConfirmDialogOptions, 'onCancel' | 'onConfirm'>) {
  return new Promise<ConfirmResult>((resolve) => {
    const id = uuid();
    useConfirmDialogStore.setState((state) => ({
      items: [
        ...state.items,
        {
          id,
          isOpen: true,
          ...options,
          onCancel: () => {
            closeConfirmDialog(id);
            resolve({
              confirmed: false,
            });
          },
          onConfirm: () => {
            closeConfirmDialog(id);
            resolve({
              confirmed: true,
            });
          },
        } as ConfirmDialogItemState,
      ],
    }));
  });
}
