import { cn } from '../lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { ComponentPropsWithoutRef, ElementRef, forwardRef } from 'react';
import { DropdownItem } from './Dropdown';

export const Menu = Popover;
export const MenuTrigger = PopoverTrigger;
export const MenuItem = DropdownItem;

export const MenuContent = forwardRef<
  ElementRef<typeof PopoverContent>,
  ComponentPropsWithoutRef<typeof PopoverContent>
>(({ className, ...props }, ref) => {
  return (
    <PopoverContent
      ref={ref}
      className={cn('mt-2 overflow-auto border border-soft-200 bg-background p-2 shadow-md', className)}
      {...props}
    />
  );
});

MenuContent.displayName = 'MenuContent';
