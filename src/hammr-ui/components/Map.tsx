import {
  CircleF,
  GoogleMap,
  Libraries,
  MarkerClustererF,
  MarkerF,
  OverlayView,
  OverlayViewF,
  PolylineF,
  useLoadScript,
} from '@react-google-maps/api';
import { cn } from '@/utils/cn';
import React, { useEffect, useRef, useState } from 'react';
import CompactButton from './CompactButton';
import { RiArrowDownSFill, RiCloseLine } from '@remixicon/react';
import LoadingIndicator from './LoadingIndicator';
import { defaultMarkerData } from '@/components/projects/constants';
import { LocationEvent } from '@/interfaces/userlocation';

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

const TRANSPARENT_PIXEL = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

const mapOptions: google.maps.MapOptions = {
  zoomControl: true,
  scaleControl: false,
  streetViewControl: false,
  fullscreenControl: false,
  zoomControlOptions: {
    position: 3, // TOP RIGHT,
  },
};

export interface Location {
  lng: number;
  lat: number;
  markerInfo?: { title: string; description: React.ReactNode };
  /** The radius in meters on the Earth's surface. */
  radius?: number;
  locationEvent?: LocationEvent;
}

export interface Path {
  points: google.maps.LatLngLiteral[];
}

interface Props {
  locations: Location[];
  paths?: Path[];
  className?: string;
  onClick?: (event: google.maps.MapMouseEvent) => void;
  zoom?: number;
  panToLocation?: { lat: number; lng: number; locationEvent?: LocationEvent };
  disableClustering?: boolean;
}

const libraries: Libraries = ['places'];

function MapComponent({ locations, paths, className, onClick, zoom, panToLocation, disableClustering = false }: Props) {
  const mapRef = useRef<google.maps.Map | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number | undefined>(undefined);

  useEffect(() => {
    const map = mapRef.current;
    if (!map) return;

    // if it's only one location just pan to it
    if (locations.length === 1) {
      map.panTo({ ...locations[0] });
      if (zoom) map.setZoom(14);
      return;
    }

    renderLocations(map, locations, zoom);
  }, [locations, zoom]);

  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  useEffect(() => {
    if (selectedLocation && mapRef.current && infoWindowRef.current) {
      const position = { lat: selectedLocation.lat, lng: selectedLocation.lng };
      panToInfoWindow(mapRef.current, infoWindowRef.current, position);
    }
  }, [selectedLocation]);
  useEffect(() => {
    if (mapRef.current && panToLocation) {
      // first pan to the position
      const pinBounds = new google.maps.LatLngBounds();
      pinBounds.extend(panToLocation);

      const mapDiv = mapRef.current.getDiv();
      mapRef.current.panToBounds(pinBounds, {
        top: mapDiv.offsetHeight / 2 + 75,
        bottom: mapDiv.offsetHeight / 2,
        left: mapDiv.offsetWidth / 2,
        right: mapDiv.offsetWidth / 2,
      });

      // next if the position is part of locations array (prop) and any markerInfo exist then trigger it
      const location = locations.find(
        (location) =>
          location.lat === panToLocation.lat &&
          location.lng === panToLocation.lng &&
          (panToLocation.locationEvent ? location.locationEvent === panToLocation.locationEvent : true)
      );
      if (location?.markerInfo) {
        setSelectedLocation(location);
      }
    }
  }, [panToLocation, locations]);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY!,
    libraries,
  });

  const infoWindowRef = useRef<HTMLDivElement>(null);
  const [clusteredMarkers, setClusteredMarkers] = useState<Set<string>>(new Set());

  // Clear clustered markers when zoom is above clustering threshold
  useEffect(() => {
    if (currentZoom !== undefined && currentZoom > 15) {
      setClusteredMarkers(new Set());
    }
  }, [currentZoom]);

  if (!isLoaded) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  return (
    <GoogleMap
      mapContainerClassName={cn(
        'size-full [&_img[alt="Google"]]:hidden [&_.gm-bundled-control>.gmnoprint]:scale-[0.7] [&_.gm-bundled-control>.gmnoprint]:origin-top-right h-80',
        className
      )}
      options={mapOptions}
      onLoad={(map) => {
        mapRef.current = map;
        setCurrentZoom(map.getZoom());
        renderLocations(map, locations, zoom);
      }}
      onZoomChanged={() => {
        if (mapRef.current) {
          setCurrentZoom(mapRef.current.getZoom());
        }
      }}
      onClick={onClick}
    >
      {disableClustering ? (
        <LocationMarkers locations={locations} setSelectedLocation={setSelectedLocation} />
      ) : (
        <MarkerClustererF
          averageCenter
          gridSize={50}
          minimumClusterSize={2}
          maxZoom={15}
          onClusteringBegin={() => setClusteredMarkers(new Set())}
          onClusteringEnd={(clusterer) => {
            const newClusteredMarkers = new Set<string>();
            const clusters = clusterer.getClusters();
            clusters.forEach((cluster) => {
              if (cluster.getSize() > 1) {
                cluster.getMarkers()?.forEach((marker: google.maps.Marker) => {
                  const title = marker.getTitle();
                  if (!title) return;
                  newClusteredMarkers.add(title);
                });
              }
            });
            setClusteredMarkers(newClusteredMarkers);
          }}
          options={{
            styles: [
              {
                className: 'bg-primary-base rounded-full flex items-center justify-center text-white font-medium',
                height: 40,
                width: 40,
                textColor: '#ffffff',
                textSize: 14,
                url: TRANSPARENT_PIXEL, // this is required by google maps
              },
            ],
          }}
        >
          {(clusterer) => (
            <LocationMarkers
              locations={locations}
              setSelectedLocation={setSelectedLocation}
              clusterer={clusterer}
              clusteredMarkers={clusteredMarkers}
              currentZoom={currentZoom}
            />
          )}
        </MarkerClustererF>
      )}

      {paths?.map((path, index) => (
        <PolylineF
          key={index}
          path={path.points}
          options={{
            strokeColor: '#0066ff',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            geodesic: false,
          }}
        />
      ))}

      {selectedLocation?.markerInfo && (
        <OverlayViewF
          position={{ lat: selectedLocation.lat, lng: selectedLocation.lng }}
          mapPaneName={OverlayView.FLOAT_PANE}
          getPixelPositionOffset={(width, height) => ({
            x: -(width / 2),
            y: -(height + 30),
          })}
        >
          <div ref={infoWindowRef} onClick={(e) => e.stopPropagation()}>
            <MarkerInfo
              title={selectedLocation.markerInfo?.title}
              description={selectedLocation.markerInfo?.description}
              onClose={() => setSelectedLocation(null)}
              locationEvent={selectedLocation.locationEvent}
            />
          </div>
        </OverlayViewF>
      )}
    </GoogleMap>
  );
}

export default React.memo(MapComponent);

function renderLocations(map: google.maps.Map, locations: Location[], zoom?: number) {
  const bounds = new google.maps.LatLngBounds();

  if (locations.length === 0) {
    bounds.extend(defaultMarkerData);
    map.fitBounds(bounds);
  } else {
    locations.forEach((location) => {
      bounds.extend(location);
    });
    map.fitBounds(bounds);
  }

  map.panToBounds(bounds, {
    top: 100,
    right: 100,
    bottom: 100,
    left: 100,
  });

  google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
    const currentZoom = map.getZoom();
    if (currentZoom !== undefined) {
      if (locations.length <= 1 || currentZoom > 14) {
        map.setZoom(Math.min(currentZoom, 14));
      }
      if (currentZoom > 16) {
        map.setZoom(zoom ?? 16);
      }
    }
  });
}

function panToInfoWindow(map: google.maps.Map, infoWindow: HTMLDivElement, position: google.maps.LatLngLiteral) {
  // wait for the map to render the pins first
  requestAnimationFrame(() => {
    const infoWindowBounds = new google.maps.LatLngBounds();
    infoWindowBounds.extend(position);

    // add some offset (100px) so we have some space between the edges of the map and the tooltip
    const padding = {
      top: infoWindow.offsetHeight + 100,
      right: infoWindow.offsetWidth / 2 + 50,
      bottom: 50,
      left: infoWindow.offsetWidth / 2 + 50,
    };

    map.panToBounds(infoWindowBounds, padding);
  });
}

interface MarkerInfoProps {
  title: string;
  description: string | React.ReactNode;
  onClose: () => void;
  locationEvent?: LocationEvent;
}

function MarkerInfo({ title, description, onClose, locationEvent }: MarkerInfoProps) {
  return (
    <div className="relative flex max-w-sm flex-row gap-2 rounded-xl bg-white-0 p-3 shadow-lg">
      <CircularPin pinType={locationEvent} />
      <div className="flex min-w-32 flex-col gap-1">
        <div className="text-sm font-medium text-strong-950">{title}</div>
        <div className="text-xs text-sub-600">{description}</div>
      </div>
      <CompactButton
        size="large"
        onClick={(e) => {
          e.stopPropagation();
          onClose();
        }}
      >
        <RiCloseLine />
      </CompactButton>
      <RiArrowDownSFill className="absolute -bottom-6 left-1/2 size-11 -translate-x-1/2  transform bg-transparent text-white-0" />
    </div>
  );
}

interface LocationMarkersProps {
  locations: Location[];
  setSelectedLocation: (location: Location | null) => void;
  clusterer?: any;
  clusteredMarkers?: Set<string>;
  currentZoom?: number;
}

function LocationMarkers({
  locations,
  setSelectedLocation,
  clusterer,
  clusteredMarkers,
  currentZoom,
}: LocationMarkersProps) {
  return (
    <>
      {locations.map((location, i) => {
        const { lng, lat } = location;
        const position = { lng, lat };
        const markerId = `${lng}-${lat}-${i}`;

        // Show individual markers when zoom is above clustering threshold OR when not clustered OR when clustering is disabled
        const shouldShowOverlay =
          !clusterer ||
          !clusteredMarkers ||
          (currentZoom !== undefined && currentZoom > 15) ||
          !clusteredMarkers.has(markerId);

        return (
          <React.Fragment key={markerId}>
            <MarkerF
              position={position}
              clusterer={clusterer}
              title={markerId}
              icon={{
                url: TRANSPARENT_PIXEL, // this is required by google maps
                scaledSize: new google.maps.Size(1, 1),
              }}
            />
            <CircleF
              options={{
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.35,
                radius: location.radius,
              }}
              center={position}
            />
            {shouldShowOverlay && (
              <OverlayViewF
                position={position}
                mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
                getPixelPositionOffset={(width, height) => ({
                  x: -(width / 2),
                  y: -(height / 2),
                })}
              >
                <div
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (location.markerInfo) {
                      setSelectedLocation({
                        lat,
                        lng,
                        markerInfo: {
                          title: location.markerInfo.title,
                          description: location.markerInfo.description,
                        },
                        locationEvent: location.locationEvent,
                      });
                    }
                  }}
                >
                  <CircularPin pinType={location.locationEvent} />
                </div>
              </OverlayViewF>
            )}
          </React.Fragment>
        );
      })}
    </>
  );
}

export const pinColors = {
  CLOCK_IN: 'rgb(var(--raw-success-base))',
  CLOCK_OUT: 'rgb(var(--raw-error-base))',
  BREAK_START: 'rgb(var(--raw-information-base))',
  BREAK_END: 'rgb(var(--raw-away-base))',
  GEOFENCE_EXIT: 'rgb(var(--raw-highlighted-base))',
  GEOFENCE_ENTER: 'rgb(var(--raw-stable-base))',
};

function CircularPin({ pinType }: { pinType?: LocationEvent }) {
  if (pinType === 'LOCATION_CHANGE') {
    return (
      <div className="flex h-5 shrink-0 items-center justify-center">
        <div className="size-2.5 rounded-full border-2 border-blue-500 bg-white" />
      </div>
    );
  }

  const baseColor = pinType ? pinColors[pinType] : '#4BAB5A'; // Default to CLOCK_IN color
  const lightColor = `${baseColor}20`; // Add 20 for ~12.5% opacity

  return (
    <div
      className="flex size-7 shrink-0 items-center justify-center rounded-full border"
      style={{
        borderColor: baseColor,
        backgroundColor: lightColor,
      }}
    >
      <div className="size-2 rounded-full" style={{ backgroundColor: baseColor }}></div>
    </div>
  );
}
