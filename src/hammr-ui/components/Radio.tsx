'use client';

import * as React from 'react';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';

import { cn } from '../lib/utils';

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return <RadioGroupPrimitive.Root className={cn('grid gap-3', className)} {...props} ref={ref} />;
});
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName;

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
  return (
    <RadioGroupPrimitive.Item
      ref={ref}
      className={cn(
        'group flex aspect-square size-4 items-center justify-center rounded-full',
        'data-[state=unchecked]:bg-soft-200 data-[state=unchecked]:hover:bg-sub-300',
        'data-[state=checked]:bg-primary-base data-[state=checked]:hover:bg-primary-darker data-[state=checked]:focus-visible:bg-primary-darker data-[state=checked]:disabled:bg-soft-200',
        'focus:outline-none disabled:cursor-not-allowed',
        className
      )}
      {...props}
    >
      <span className="hidden size-3 rounded-full bg-white-0 shadow group-data-[state=unchecked]:block group-data-[state=unchecked]:group-disabled:hidden"></span>
      <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
        <span className="size-2 rounded-full bg-white-0 shadow"></span>
      </RadioGroupPrimitive.Indicator>
    </RadioGroupPrimitive.Item>
  );
});
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

export { RadioGroup, RadioGroupItem };
