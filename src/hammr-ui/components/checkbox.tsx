'use client';

import * as React from 'react';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';

import { cn } from '../lib/utils';

export const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>
>(({ className, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      'group peer flex size-4 shrink-0 items-center justify-center rounded focus-visible:outline-none disabled:cursor-not-allowed',
      'bg-soft-200 hover:bg-sub-300 focus-visible:bg-primary-base disabled:bg-soft-200',
      'data-[state=checked]:bg-primary-base data-[state=checked]:hover:bg-primary-darker data-[state=checked]:focus-visible:bg-primary-dark data-[state=checked]:disabled:bg-soft-200',
      'data-[state=indeterminate]:bg-primary-base data-[state=indeterminate]:hover:bg-primary-darker data-[state=indeterminate]:focus-visible:bg-primary-dark data-[state=indeterminate]:disabled:bg-soft-200',
      className
    )}
    {...props}
  >
    <span className="hidden size-3 rounded-[2.6px] bg-white-0 shadow group-data-[state=unchecked]:block group-data-[state=unchecked]:group-disabled:hidden"></span>
    <SubtractIcon className="hidden group-data-[state=indeterminate]:block" />
    <CheckboxPrimitive.Indicator asChild>
      <CheckIcon className="pointer-events-none hidden group-data-[state=checked]:block" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

interface IconProps {
  className: string;
}

const CheckIcon = React.forwardRef(function CheckIcon(
  { className }: IconProps,
  ref
) {
  return (
    <svg
      ref={ref as React.Ref<SVGSVGElement>}
      width="10"
      height="8"
      viewBox="0 0 10 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ filter: 'drop-shadow(0 1px 1.5px rgba(10, 13, 20, 0.4))' }}
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.53039 2.03039L4.00006 7.56072L0.469727 4.03039L1.53039 2.96973L4.00006 5.4394L8.46973 0.969727L9.53039 2.03039Z"
        fill="white"
      />
    </svg>
  );
});

function SubtractIcon({ className }: IconProps) {
  return (
    <svg
      width="8"
      height="2"
      viewBox="0 0 8 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ filter: 'drop-shadow(0 1px 1.5px rgba(10, 13, 20, 0.4))' }}
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 1.75V0.25H8V1.75H0Z"
        fill="white"
      />
    </svg>
  );
}
