import React from 'react';
import CheckFill from '@/hammr-icons/CheckFill';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { cn } from '@/utils/cn';

interface StepIndicatorProps {
  currentStep: number;
  steps: string[];
  className?: string;
}

export function StepIndicator({ currentStep, steps, className }: StepIndicatorProps) {
  return (
    <div className={cn('flex items-center gap-4', className)}>
      {steps.map((step, index) => (
        <div key={step} className="flex items-center">
          <div className="flex items-center">
            <div
              className={`flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium ${
                index + 1 < currentStep
                  ? 'border-success-base bg-success-base'
                  : index + 1 === currentStep
                    ? 'border-primary-base bg-primary-base'
                    : 'border-soft-200 bg-white-0 text-sub-600'
              }`}
            >
              {index + 1 < currentStep ? (
                <CheckFill className="h-4 w-4 text-static-white" />
              ) : index + 1 === currentStep ? (
                <span className="text-static-white">{index + 1}</span>
              ) : (
                index + 1
              )}
            </div>
            <span
              className={`ml-2 text-sm font-medium ${index + 1 <= currentStep ? 'text-strong-950' : 'text-sub-600'}`}
            >
              {step}
            </span>
          </div>
          {index < steps.length - 1 && (
            <ArrowRightSLine
              className={`ml-4 h-5 w-5 ${currentStep > index + 1 ? 'text-success-base' : 'text-soft-200'}`}
            />
          )}
        </div>
      ))}
    </div>
  );
}

export default StepIndicator;
