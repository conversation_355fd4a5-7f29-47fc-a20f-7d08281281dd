import React, { Children, FC, Fragment, PropsWithChildren } from 'react';
import { cn } from '../lib/utils';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';

interface BreadcrumbsProps {
  seperator?: 'arrow' | 'slash' | 'dot' | React.ReactNode;
}

export const Breadcrumbs: FC<PropsWithChildren<BreadcrumbsProps>> = ({ children, seperator = 'arrow' }) => {
  const items = Children.toArray(children).filter((child: any) => child.type === BreadcrumbItem);

  function renderSeperator(seperator: BreadcrumbsProps['seperator']) {
    if (!seperator) return null;
    switch (seperator) {
      case 'arrow':
        return <ArrowRightSLine className="size-5 text-disabled-300" />;
      case 'slash':
        return '/';
      case 'dot':
        return '•';
      default:
        return seperator;
    }
  }

  return (
    <div className="flex items-center gap-1.5 text-sm">
      {items.map((item, index) => {
        return (
          <Fragment key={index}>
            {index > 0 &&
              (typeof renderSeperator(seperator) === 'function' ? (
                renderSeperator(seperator)
              ) : (
                <span className="inline-flex w-5 items-center justify-center text-disabled-300">
                  {renderSeperator(seperator)}
                </span>
              ))}
            {item}
          </Fragment>
        );
      })}
    </div>
  );
};

interface BreadcrumbItemProps {
  active?: boolean;
  text: React.ReactNode;
  icon?: React.ReactNode;
  onClick?: () => void;
}

export const BreadcrumbItem: FC<BreadcrumbItemProps> = ({ icon, text, active, onClick }) => {
  return (
    <div
      className={cn(
        'flex gap-1.5 text-sub-600',
        active && 'font-medium text-strong-950',
        onClick && 'cursor-pointer hover:underline'
      )}
      onClick={onClick}
    >
      {icon}
      <span>{text}</span>
    </div>
  );
};
