import { useEffect, useRef, useState } from 'react';
import { cn } from '@/utils/cn';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { RiTimeLine } from '@remixicon/react';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { ScrollArea } from './scroll-area';

dayjs.extend(customParseFormat); // required for parsing time (e.g. HH:mm)

interface Props {
  value: Date | null;
  onChange: (value: Date | null) => void;
  className?: string;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  disabled?: boolean;
  'aria-invalid'?: boolean;
}

export function TimeInput({ value, onChange, className, inputProps, disabled, 'aria-invalid': ariaInvalid }: Props) {
  const [open, setOpen] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild onClick={(e) => disabled && e.preventDefault()}>
        <div
          aria-invalid={!!ariaInvalid}
          className={cn(
            'flex cursor-pointer gap-2 rounded-10 border border-soft-200 px-3 py-[9px] text-soft-400',
            'hover:bg-weak-50 hover:text-sub-600',
            'ring-strong-400/25 ring-offset-2 ring-offset-white-0 focus-within:border-strong-950 focus-within:text-sub-600 focus-within:ring-2',
            value && 'text-strong-950 hover:text-strong-950',
            'disabled-within:cursor-not-allowed disabled-within:border-transparent disabled-within:bg-weak-50 disabled-within:text-disabled-300',
            'aria-[invalid=true]:border-error-base aria-[invalid=true]:ring-error-light',
            className
          )}
          onClick={() => inputRef.current?.focus()}
        >
          <RiTimeLine className={cn('size-5 shrink-0', value && 'text-sub-600', disabled && 'text-disabled-300')} />
          <input
            ref={inputRef}
            disabled={disabled}
            {...inputProps}
            type="time"
            className={cn(
              'h-5 w-full cursor-[inherit] bg-inherit text-sm uppercase focus:text-strong-950 focus:outline-none',
              '[&::-webkit-calendar-picker-indicator]:hidden',
              inputProps?.className
            )}
            value={value ? dayjs(value).format('HH:mm') : ''}
            onChange={(e) => {
              const _value = e.target.value;

              if (_value) {
                onChange(dayjs(_value, 'HH:mm').toDate());
              } else {
                onChange(null);
              }
            }}
            onKeyDown={(event) => {
              if (event.code === 'Space') {
                event.preventDefault();
                event.stopPropagation();
              }
              if (event.key === 'Enter') {
                event.preventDefault();
                setOpen(false);
              }
            }}
          />
        </div>
      </PopoverTrigger>
      <PopoverContent className="mt-1 w-auto" align="start" onOpenAutoFocus={(e) => e.preventDefault()}>
        <TimePicker value={value} onChange={onChange} />
      </PopoverContent>
    </Popover>
  );
}

interface TimePickerProps {
  value: Date | null;
  onChange: (value: Date) => void;
}

export function TimePicker({ value, onChange }: TimePickerProps) {
  const [oldValue] = useState(value);
  value = value || oldValue;

  const [firstTimeRendering, setFirstTimeRendering] = useState(true);
  useEffect(() => {
    setFirstTimeRendering(false);

    return () => {
      setFirstTimeRendering(true);
    };
  }, []);

  return (
    <div className="grid h-56 w-[174px] grid-cols-3 rounded-2xl border border-soft-200 p-3">
      <ScrollArea className="h-[200px] pr-2">
        {Array.from({ length: 12 }).map((_, i) => {
          const hour = i === 0 ? 12 : i;
          return (
            <button
              ref={(element) => {
                const previouslySelectedHour = value && Number(dayjs(value).format('h'));
                if (previouslySelectedHour === hour) {
                  element?.scrollIntoView({ block: 'start', behavior: !firstTimeRendering ? 'smooth' : undefined });
                }
              }}
              key={i}
              className={cn(
                'size-10 text-sm text-soft-400',
                value &&
                  Number(dayjs(value).format('h')) === hour &&
                  'rounded-8 border border-soft-200 bg-weak-50 text-strong-950'
              )}
              onClick={() => {
                if (value !== null) {
                  const isAM = dayjs(value).hour() < 12;
                  const hoursToSet = i + (isAM ? 0 : 12);
                  onChange(dayjs(value).hour(hoursToSet).toDate());
                } else {
                  onChange(dayjs().startOf('day').hour(i).toDate());
                }
              }}
            >
              {hour.toString().padStart(2, '0')}
            </button>
          );
        })}
      </ScrollArea>
      <ScrollArea className="h-[200px]">
        {Array.from({ length: 60 }).map((_, i) => (
          <button
            ref={(element) => {
              const previouslySelectedMinute = value && Number(dayjs(value).format('mm'));
              if (previouslySelectedMinute === i) {
                element?.scrollIntoView({ block: 'start', behavior: !firstTimeRendering ? 'smooth' : undefined });
              }
            }}
            key={i}
            className={cn(
              'size-10 text-sm text-soft-400',
              value && dayjs(value).minute() === i && 'rounded-8 border border-soft-200 bg-weak-50 text-strong-950'
            )}
            onClick={() => {
              if (value !== null) {
                onChange(dayjs(value).minute(i).toDate());
              } else {
                onChange(dayjs().startOf('day').minute(i).toDate());
              }
            }}
          >
            {i.toString().padStart(2, '0')}
          </button>
        ))}
      </ScrollArea>
      <div className="text-sm text-soft-400">
        <button
          className={cn(
            'size-10',
            value && dayjs(value).hour() < 12 && 'rounded-8 border border-soft-200 bg-weak-50 text-strong-950'
          )}
          onClick={() => {
            const isAM = dayjs(value).hour() < 12;
            if (isAM) return;
            onChange(dayjs(value).subtract(12, 'hour').toDate());
          }}
        >
          AM
        </button>
        <button
          className={cn(
            'size-10',
            value && dayjs(value).hour() >= 12 && 'rounded-8 border border-soft-200 bg-weak-50 text-strong-950'
          )}
          onClick={() => {
            const isPM = dayjs(value).hour() >= 12;
            if (isPM) return;
            onChange(dayjs(value).add(12, 'hour').toDate());
          }}
        >
          PM
        </button>
      </div>
    </div>
  );
}
