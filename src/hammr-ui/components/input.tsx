import * as React from 'react';

import { cn } from '../lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  beforeContent?: React.ReactNode;
  afterContent?: React.ReactNode;
  boxSize?: 'medium' | 'small' | 'x-small';
  noBorder?: boolean;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      beforeContent,
      afterContent,
      value,
      disabled,
      onChange,
      'aria-invalid': areaInvalid,
      boxSize = 'medium',
      noBorder,
      ...props
    },
    ref
  ) => {
    return (
      <label
        aria-invalid={!!areaInvalid}
        className={cn(
          'group flex cursor-text items-center border border-soft-200 bg-white-0 shadow-xs',
          'hover:bg-weak-50',
          'ring-strong-400/25 ring-offset-2 ring-offset-white-0',
          'focus-within:border-strong-950 focus-within:ring-2 focus-within:hover:bg-white-0',
          'disabled-within:cursor-not-allowed disabled-within:border-transparent disabled-within:bg-weak-50',
          'aria-[invalid=true]:border-error-base aria-[invalid=true]:ring-error-light',
          {
            'h-10 gap-2 rounded-10 py-2.5 pl-3 pr-2.5': boxSize === 'medium',
            'h-9 gap-2 rounded-lg py-2 pl-2.5 pr-2': boxSize === 'small',
            'h-8 gap-1.5 rounded-lg py-1.5 pl-2 pr-1.5': boxSize === 'x-small',
          },
          noBorder && '!border-transparent !ring-0',
          className
        )}
      >
        <input
          type={type}
          className={cn(
            'peer order-2 block size-full bg-inherit text-sm text-strong-950',
            'file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground',
            'placeholder:text-soft-400 focus:placeholder:text-sub-600 disabled:placeholder:text-disabled-300 group-hover:placeholder:text-sub-600',
            'focus-visible:outline-none',
            'disabled:text-disabled-300 group-hover:disabled:placeholder:text-disabled-300'
          )}
          ref={ref}
          value={value}
          onChange={(e) => onChange?.(e)}
          disabled={disabled}
          aria-invalid={!!areaInvalid}
          {...props}
        />
        {!!beforeContent && (
          <span
            className={cn(
              'order-1 text-sm text-sub-600 peer-placeholder-shown:text-soft-400',
              'group-hover:peer-placeholder-shown:text-sub-600',
              'peer-focus:text-sub-600',
              'peer-disabled:text-disabled-300 group-hover:peer-disabled:text-disabled-300'
            )}
          >
            {beforeContent}
          </span>
        )}
        {!!afterContent && (
          <span
            className={cn(
              'order-3 text-sm text-sub-600 peer-placeholder-shown:text-soft-400',
              'group-hover:peer-placeholder-shown:text-sub-600',
              'peer-focus:text-sub-600',
              'peer-disabled:text-disabled-300 group-hover:peer-disabled:text-disabled-300'
            )}
          >
            {afterContent}
          </span>
        )}
      </label>
    );
  }
);
Input.displayName = 'Input';
