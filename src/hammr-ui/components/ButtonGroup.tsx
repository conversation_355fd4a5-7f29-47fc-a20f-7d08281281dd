import { Slot } from '@radix-ui/react-slot';
import { forwardRef, PropsWithChildren } from 'react';
import { cn } from '../lib/utils';

interface ButtonGroupProps {
  className?: string;
}

export const ButtonGroup: React.FC<PropsWithChildren<ButtonGroupProps>> = ({ children, className }) => {
  return (
    <div
      className={cn(
        'flex overflow-hidden rounded-8 border border-soft-200 bg-background shadow-xs',
        'text-sm text-sub-600',
        '[&>:not(:first-child)]:border-l [&>:not(:first-child)]:border-soft-200',
        className
      )}
    >
      {children}
    </div>
  );
};

interface ButtonGroupItemProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'> {
  asChild?: boolean;
  beforeContent?: React.ReactNode;
  afterContent?: React.ReactNode;
  active?: boolean;
}

export const ButtonGroupItem = forwardRef<HTMLButtonElement, ButtonGroupItemProps>(
  ({ afterContent, asChild, beforeContent, children, active, className, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';

    return (
      <Comp
        className={cn(
          'flex h-9 max-h-[36px] items-center justify-center px-2',
          'hover:bg-weak-50',
          'disabled:text-disabled-300',
          active && '!bg-weak-50 text-strong-950',
          'flex-1',
          className
        )}
        type="button"
        ref={ref}
        {...props}
      >
        {!!beforeContent && (
          <span className="mr-1 flex h-5 w-5 items-center justify-center [&>svg]:h-5 [&>svg]:w-5">{beforeContent}</span>
        )}
        {!!children && <span>{children}</span>}
        {!!afterContent && (
          <span className="ml-1 flex h-5 w-5 items-center justify-center [&>svg]:h-5 [&>svg]:w-5">{afterContent}</span>
        )}
      </Comp>
    );
  }
);

ButtonGroupItem.displayName = 'ButtonGroupItem';
