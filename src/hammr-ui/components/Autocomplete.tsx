import { cn } from '../lib/utils';
import s from './Dropdown.module.css';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import ArrowUpSLine from '@/hammr-icons/ArrowUpSLine';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { DropdownItem } from './Dropdown';
import Search2Line from '@/hammr-icons/Search2Line';
import Spinner from './spinner';

interface Item<T extends string | number> {
  value: T;
  label: string;
  subTitle?: string;
  avatar?: {
    content: React.ReactNode;
    type: 'icon' | 'image' | 'avatar';
  };
}

interface DropdownPickerProps<T extends string | number = string>
  extends Omit<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    'onChange' | 'type' | 'value'
  > {
  items: Item<T>[];
  withSubTitle?: boolean;
  selectedItem?: Item<T> | null;
  onSelectItem?: (item: Item<T>) => void;
  placeholder?: string;
  beforeContent?: React.ReactNode;
  disabled?: boolean;
  fullWidth?: boolean;
  loading?: boolean;
  noResultText?: string;
  searchText?: string;
  onSearchTextChange?: (searchText: string) => void;
}

export const Autocomplete = forwardRef(function DropdownPicker<
  T extends string | number,
>(
  {
    items,
    beforeContent,
    disabled,
    fullWidth,
    selectedItem,
    onSelectItem,
    placeholder,
    withSubTitle,
    className,
    searchText,
    onSearchTextChange,
    ...props
  }: DropdownPickerProps<T>,
  ref: React.Ref<HTMLButtonElement>
) {
  const [open, setOpen] = useState(false);

  const popoverContentRef = useRef<HTMLDivElement>();

  useEffect(() => {
    setTimeout(() => {
      popoverContentRef.current
        ?.querySelector('button[aria-selected="true"]')
        ?.scrollIntoView({ block: 'center' });
    });
  }, [selectedItem, open]);

  return (
    <Popover
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) {
          onSearchTextChange?.('');
        }
      }}
    >
      <PopoverTrigger asChild>
        <button
          ref={ref}
          type="button"
          className={cn(
            'flex h-10 w-full items-center gap-2 overflow-hidden rounded-10 border border-soft-200 bg-background shadow-xs',
            'ring-2 ring-transparent ring-offset-background',
            'group',
            'data-[state="open"]:border-strong-950 data-[state="open"]:ring-strong-400/25 data-[state="open"]:ring-offset-2',
            '!disabled:text-disabled-300 disabled:pointer-events-none disabled:border-transparent disabled:bg-weak-50',
            'aria-[invalid=true]:border-error-base',
            fullWidth && 'w-full',
            'bg-background text-sm text-sub-600',
            'px-2',
            'aria-[invalid=true]:border-error-base'
          )}
          {...props}
        >
          {!!beforeContent && (
            <span
              className={cn(
                'h-5 w-5 [&>svg]:h-5 [&>svg]:w-5',
                'pr-2',
                !selectedItem ? 'text-soft-400' : 'text-sub-600',
                'group-hover:text-sub-600',
                'group-focus:text-sub-600',
                disabled && '!text-disabled-300'
              )}
            >
              {beforeContent}
            </span>
          )}
          <span
            className={cn(
              'flex-1 text-left',
              !selectedItem ? 'text-soft-400' : 'text-foreground'
            )}
          >
            {selectedItem?.label ?? placeholder ?? 'Select'}
          </span>
          {open ? (
            <ArrowUpSLine className="h-5 w-5 text-sub-600" />
          ) : (
            <ArrowDownSLine className="h-5 w-5 text-sub-600" />
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          'mt-2 overflow-auto border border-soft-200 bg-background shadow-md',
          s.PopoverContent
        )}
        onWheel={(e) => {
          e.stopPropagation();
        }}
        ref={popoverContentRef}
      >
        <SearchInput
          value={searchText ?? ''}
          onChange={(event) => onSearchTextChange?.(event.target.value)}
        />
        {(items.length > 0 || (searchText && !props.loading)) && (
          <>
            <div className="border-b border-soft-200" />
            <div className="p-2">
              {!props.loading && !!searchText && items.length === 0 && (
                <div className="flex h-20 flex-col items-center justify-center">
                  <div className="text-sub-600">
                    {props.noResultText ?? 'No result found'}
                  </div>
                </div>
              )}
              {items.map((item, index) => (
                <DropdownItem
                  key={index}
                  title={item.label}
                  selected={item.value === selectedItem?.value}
                  subTitle={withSubTitle ? item.subTitle || ' ' : undefined}
                  onClick={() => {
                    onSelectItem?.(item);
                    setOpen(false);
                  }}
                  beforeContent={
                    item.avatar ? (
                      <div
                        className={cn(
                          'flex items-center justify-center rounded-full text-sub-600',
                          withSubTitle ? 'h-10 w-10' : 'h-5 w-5',
                          '[&>svg]:h-5 [&>svg]:w-5',
                          '[&>img]:rounded-full',
                          item.avatar?.type === 'avatar' && withSubTitle
                            ? '[&>img]:h-10 [&>img]:w-10'
                            : '[&>img]:h-5 [&>img]:w-5',
                          item.avatar?.type !== 'avatar' &&
                            withSubTitle &&
                            'border border-soft-200'
                        )}
                      >
                        {item.avatar?.content}
                      </div>
                    ) : null
                  }
                />
              ))}
            </div>
          </>
        )}
      </PopoverContent>
    </Popover>
  );
});

Autocomplete.displayName = 'DropdownPicker';

export interface SearchInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  loading?: boolean;
}

export const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
  ({ className, loading, ...props }, ref) => {
    return (
      <div className={'relative flex items-center text-sub-600'}>
        <Search2Line className="absolute left-2 h-4 w-4" />
        {loading && <Spinner className="h4 absolute right-3 w-4" />}
        <input
          ref={ref}
          className="flex h-10 w-full bg-transparent pl-8 pr-9 text-sm outline-none placeholder:text-sub-600"
          placeholder="Search"
          {...props}
        />
      </div>
    );
  }
);

export default Autocomplete;
