'use client';

import * as React from 'react';
import { ComponentPropsWithoutRef } from 'react';

import { Button } from '@hammr-ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@hammr-ui/components/command';
import { Popover, PopoverContent, PopoverTrigger } from '@hammr-ui/components/popover';
import ArrowDownSLine from '@hammr-icons/ArrowDownSLine';
import CheckLine from '@hammr-icons/CheckLine';
import { cn } from '../lib/cn';
import * as PopoverPrimitive from '@radix-ui/react-popover';
import ArrowUpSLine from '@hammr-icons/ArrowUpSLine';

export interface ComboboxItem {
  value: string;
  label: React.ReactNode;
  selectedLabel?: React.ReactNode;
  disabled?: boolean;
}

export interface ComboboxProps {
  items: ComboboxItem[] | Record<string, ComboboxItem[]>; // either pass an array or an object of arrays where key is the CommandGroup header
  value: string | null;
  onChange?: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  error?: string | any;
  disabled?: boolean;
  className?: string;
  searchable?: boolean;
  itemClassName?: string;
  popoverContentProps?: ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>;
  enableTextAsFirstOption?: boolean;
  'aria-invalid'?: boolean;
}

export function Combobox({
  items,
  value,
  onChange,
  placeholder = 'Select an option...',
  searchPlaceholder = 'Search...',
  emptyMessage = 'No options available.',
  searchable = true,
  // TODO: remove `error` prop, use `aria-invalid` instead
  error,
  disabled,
  className,
  itemClassName,
  popoverContentProps,
  'aria-invalid': ariaInvalid,
  enableTextAsFirstOption = false,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [customSelectedValue, setCustomSelectedValue] = React.useState<string | null>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);

  const flattenedItems = React.useMemo(() => {
    if (Array.isArray(items)) {
      return items;
    }
    return Object.values(items).flat();
  }, [items]);

  // Check if current value is from a custom search option
  const isCustomValue = React.useMemo(() => {
    if (!value) return false;
    return !flattenedItems.some((item) => item.value === value) && customSelectedValue === value;
  }, [value, flattenedItems, customSelectedValue]);

  const shouldShowInputAsOption = React.useMemo(() => {
    if (!enableTextAsFirstOption || !inputValue) return false;

    // Check if the input value already exists in the items list (case insensitive)
    return !flattenedItems.some(
      (item) =>
        item.value.toLowerCase() === inputValue.toLowerCase() ||
        (typeof item.label === 'string' && item.label.toLowerCase() === inputValue.toLowerCase())
    );
  }, [enableTextAsFirstOption, inputValue, flattenedItems]);

  // Reset input value when popover closes
  React.useEffect(() => {
    if (!open) {
      setInputValue('');
    }
  }, [open]);

  // Handle manual input tracking
  const handleInputChange = React.useCallback((value: string) => {
    setInputValue(value);
  }, []);

  // Handle selection of custom option
  const handleSelectCustomOption = React.useCallback(
    (text: string) => {
      setCustomSelectedValue(text);
      onChange?.(text);
      setOpen(false);
    },
    [onChange]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          color="neutral"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          afterContent={
            open ? (
              <ArrowUpSLine className="h-5 w-5 text-sub-600" />
            ) : (
              <ArrowDownSLine className="h-5 w-5 text-sub-600" />
            )
          }
          className={cn(
            'justify-between px-2 !text-sub-600 hover:border-soft-200 hover:bg-background [&>span]:p-0',
            (error || ariaInvalid) && 'border-error-base', // TODO: remove the error prop, only use ariaInvalid to be consistent with other components
            disabled && 'opacity-50',
            className
          )}
        >
          <div className="flex flex-row items-center gap-1">
            {value ? (
              <span className="truncate font-normal text-foreground">
                {isCustomValue
                  ? customSelectedValue
                  : flattenedItems.find((item) => item.value === value)?.selectedLabel ??
                    flattenedItems.find((item) => item.value === value)?.label}
              </span>
            ) : (
              <span className="capitalize-first font-normal text-soft-400">{placeholder}</span>
            )}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent {...popoverContentProps} className={cn('mt-2 p-0', popoverContentProps?.className)} align="start">
        <Command
          className="max-h-[300px] !rounded-10"
          filter={(value, search) => {
            if (value.toLowerCase().includes(search.toLowerCase())) return 1;
            return 0;
          }}
        >
          {searchable ? (
            <CommandInput
              ref={inputRef}
              placeholder={searchPlaceholder}
              className="h-9"
              value={inputValue}
              onValueChange={handleInputChange}
            />
          ) : undefined}
          <CommandList>
            <CommandEmpty className="p-2 text-center text-sm font-normal text-soft-400">{emptyMessage}</CommandEmpty>
            {Array.isArray(items) ? (
              <CommandGroup>
                {/* Show current custom value if it's selected */}
                {isCustomValue && (
                  <CommandItem
                    key={`selected-custom-${value}`}
                    value={value}
                    onSelect={() => {
                      onChange?.('');
                      setCustomSelectedValue(null);
                      setOpen(false);
                    }}
                    className={cn(
                      'combobox-text-option flex items-center justify-between gap-3 text-nowrap !bg-transparent hover:!bg-weak-50',
                      itemClassName
                    )}
                  >
                    <span className="w-full truncate">{value}</span>
                    <CheckLine className="h-5 w-5 text-sub-600" />
                  </CommandItem>
                )}
                {/* Show new search option if applicable */}
                {shouldShowInputAsOption && inputValue && value !== inputValue && (
                  <CommandItem
                    key={`text-option-${inputValue}`}
                    value={inputValue}
                    onSelect={() => handleSelectCustomOption(inputValue)}
                    className={cn(
                      'combobox-text-option flex items-center justify-between gap-3 text-nowrap !bg-transparent hover:!bg-weak-50',
                      itemClassName
                    )}
                  >
                    <span className="w-full truncate">{inputValue}</span>
                  </CommandItem>
                )}
                {/* Show regular items */}
                {items.map((item) => (
                  <CommandItem
                    key={item.value}
                    onSelect={() => {
                      onChange?.(item.value === value ? '' : item.value);
                      if (item.value === value) {
                        setCustomSelectedValue(null);
                      }
                      setOpen(false);
                    }}
                    disabled={item.disabled}
                    className={cn(
                      'flex items-center justify-between gap-3 text-nowrap !bg-transparent hover:!bg-weak-50',
                      itemClassName
                    )}
                  >
                    <span className="w-full truncate">{item.label}</span>
                    {value === item.value && <CheckLine className="h-5 w-5 text-sub-600" />}
                  </CommandItem>
                ))}
              </CommandGroup>
            ) : (
              Object.keys(items).map((key) => {
                const innerItems = items[key];
                return (
                  <CommandGroup key={key} heading={key}>
                    {/* Show current custom value in first group if it's selected */}
                    {key === Object.keys(items)[0] && isCustomValue && (
                      <CommandItem
                        key={`selected-custom-${value}`}
                        value={value}
                        onSelect={() => {
                          onChange?.('');
                          setCustomSelectedValue(null);
                          setOpen(false);
                        }}
                        className={cn(
                          'combobox-text-option flex items-center justify-between gap-3 !bg-transparent hover:!bg-weak-50',
                          itemClassName
                        )}
                      >
                        <span className="w-full truncate">{value}</span>
                        <CheckLine className="h-5 w-5 text-sub-600" />
                      </CommandItem>
                    )}
                    {/* Show new search option in first group if applicable */}
                    {key === Object.keys(items)[0] && shouldShowInputAsOption && inputValue && value !== inputValue && (
                      <CommandItem
                        key={`text-option-${inputValue}`}
                        value={inputValue}
                        onSelect={() => handleSelectCustomOption(inputValue)}
                        className={cn(
                          'combobox-text-option flex items-center justify-between gap-3 !bg-transparent hover:!bg-weak-50',
                          itemClassName
                        )}
                      >
                        <span className="w-full truncate">{inputValue}</span>
                      </CommandItem>
                    )}
                    {/* Show regular items */}
                    {innerItems.map((item) => (
                      <CommandItem
                        key={item.value}
                        onSelect={() => {
                          onChange?.(item.value === value ? '' : item.value);
                          if (item.value === value) {
                            setCustomSelectedValue(null);
                          }
                          setOpen(false);
                        }}
                        disabled={item.disabled}
                        className={cn(
                          'flex items-center justify-between gap-3 !bg-transparent hover:!bg-weak-50',
                          itemClassName
                        )}
                      >
                        <span>{item.label}</span>
                        {value === item.value && <CheckLine className="h-5 w-5 text-sub-600" />}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                );
              })
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
