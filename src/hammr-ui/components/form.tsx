import * as React from 'react';
import { ReactNode } from 'react';
import * as LabelPrimitive from '@radix-ui/react-label';
import { Slot } from '@radix-ui/react-slot';

import { cn } from '../lib/cn';
import { Label } from './label';
import { Tooltip } from './tooltip';
import InfoCustomFill from '@hammr-icons/InfoCustomFill';

type FormItemContextValue = {
  id: string;
  error?: boolean;
  required?: boolean;
  disabled?: boolean;
};

const FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);

function useFormItem() {
  const context = React.useContext(FormItemContext);

  return {
    id: context.id,
    error: context.error,
    required: context.required,
    disabled: context.disabled,
    formItemId: `${context.id}-form-item`,
    formDescriptionId: `${context.id}-form-item-description`,
    formMessageId: `${context.id}-form-item-message`,
  };
}

const FormItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    error?: boolean;
    required?: boolean;
    disabled?: boolean;
  }
>(({ className, error, required, disabled, children, ...props }, ref) => {
  const id = React.useId();

  return (
    <FormItemContext.Provider value={{ id, error, required, disabled }}>
      <div ref={ref} className={cn('flex flex-col gap-1', className)} {...props}>
        {children}
      </div>
    </FormItemContext.Provider>
  );
});
FormItem.displayName = 'FormItem';

const FormLabel = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & {
    tooltip?: ReactNode;
    capitalize?: boolean;
    labelHelper?: ReactNode;
  }
>(({ className, children, tooltip, labelHelper, capitalize, ...props }, ref) => {
  const { formItemId, required } = useFormItem();

  return (
    <Label
      ref={ref}
      className={cn('relative  aria-[disabled=true]:text-disabled-300', className, { capitalize })}
      htmlFor={formItemId}
      {...props}
    >
      {children}
      {required && <span className="text-error-base"> *</span>}
      {labelHelper ? <span className="ml-1 text-sub-600">{labelHelper}</span> : undefined}
      {!!tooltip && (
        <div className="absolute top-1/2 inline-flex -translate-y-1/2">
          <Tooltip content={tooltip}>
            <span>
              <InfoCustomFill className="ml-1 size-5 text-disabled-300" />
            </span>
          </Tooltip>
        </div>
      )}
    </Label>
  );
});
FormLabel.displayName = 'FormLabel';

const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot> & {
    disabled?: boolean;
  },
  React.ComponentPropsWithoutRef<typeof Slot> & {
    disabled?: boolean;
  }
>(({ ...props }, ref) => {
  const { error, formItemId, formDescriptionId, formMessageId, disabled } = useFormItem();

  return (
    <Slot
      ref={ref}
      id={formItemId}
      aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`}
      aria-invalid={!!error}
      disabled={disabled}
      aria-disabled={disabled}
      {...props}
    />
  );
});
FormControl.displayName = 'FormControl';

const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement> & {
    messageType?: 'error' | 'success' | 'warning' | 'info' | 'neutral';
  }
>(({ className, messageType, children, ...props }, ref) => {
  const { formMessageId, error, disabled } = useFormItem();

  if (!children) {
    return null;
  }

  return (
    <div
      ref={ref}
      id={formMessageId}
      className={cn(
        'text-xs font-normal text-sub-600',
        (error || messageType === 'error') && 'text-error-base',
        disabled && 'text-disabled-300',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
});
FormMessage.displayName = 'FormMessage';

export { FormItem, FormLabel, FormControl, FormMessage };
