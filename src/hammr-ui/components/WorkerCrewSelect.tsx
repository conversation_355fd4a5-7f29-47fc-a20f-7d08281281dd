import { cn } from '../lib/utils';
import { <PERSON>b<PERSON><PERSON><PERSON>, TabItem, Tab<PERSON>ist, Tabs } from '@/hammr-ui/components/tabs';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import { Checkbox } from '@/hammr-ui/components/checkbox';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { HammrUser } from '@/interfaces/user';
import { Crew } from '@/interfaces/crew';
import LoadingIndicator from './LoadingIndicator';
import { useEffect, useRef, useState } from 'react';
import { Input } from './input';
import Search2Line from '@/hammr-icons/Search2Line';
import { LinkButton } from './LinkButton';

interface Props {
  selectedWorkerIds: number[];
  onChange: (selectedWorkerIds: number[]) => void;
  className?: string;
  showCrews?: boolean;
  error?: boolean;
  showSelectAll?: boolean;
  disabledWorkers?: { workerId: number; selected: boolean; reason: string }[];
  queryFilters?: Record<string, any>;
}

export default function WorkerCrewSelect({
  className,
  selectedWorkerIds,
  onChange,
  showCrews,
  error,
  showSelectAll,
  disabledWorkers,
  queryFilters,
}: Props) {
  const [searchText, setSearchText] = useState('');

  const workersQuery = useQuery({
    queryKey: ['workers'],
    queryFn: () => apiRequest<{ users: HammrUser[] }>('users', { urlParams: queryFilters }),
  });

  const crewsQuery = useQuery({
    queryKey: ['crews'],
    queryFn: () => apiRequest<{ crews: Crew[] }>('crews'),
    enabled: !!showCrews,
  });

  // focus the input on first render
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    inputRef.current?.focus();
  }, [inputRef]);

  return (
    <Tabs
      defaultValue="workers"
      className={cn('rounded-16 border border-soft-200 p-[7px]', className, { 'border-error-base': error })}
    >
      {showCrews && (
        <TabList className="mb-1 w-full">
          <TabItem className="grow" value="workers">
            Employees
          </TabItem>
          <TabItem className="grow" value="crews">
            Crews
          </TabItem>
        </TabList>
      )}

      {/* set tabIndex of TabContent to -1 so that it doesn't steal the focus from search input below */}
      <TabContent value="workers" tabIndex={-1}>
        {workersQuery.isPending ? (
          <div className="flex h-72 items-center justify-center">
            <LoadingIndicator text="Loading Workers..." />
          </div>
        ) : workersQuery.isError ? (
          <p className="text-sm text-error-base">An error occured when fetching workers.</p>
        ) : (
          <>
            <Input
              ref={inputRef}
              className="border-none pl-2"
              beforeContent={<Search2Line className="size-5 text-soft-400" />}
              placeholder="Search..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              boxSize="small"
              noBorder
            />
            {filteredWorkers(workersQuery.data.users, searchText).length === 0 ? (
              <div className="mt-1 flex h-[276px] items-center justify-center text-sm">No results found.</div>
            ) : (
              <ScrollArea className="-mr-1.5 mt-1 h-[276px]">
                <div className="flex flex-col gap-1">
                  {filteredWorkers(workersQuery.data.users, searchText).map((employee) => {
                    const worker = disabledWorkers?.find((worker) => worker.workerId === employee.id);

                    return (
                      <label
                        key={employee.id}
                        className={`grid select-none grid-cols-[20px_auto] items-center gap-2 p-2 text-sm ${worker ? 'text-disabled-300' : 'text-strong-950'}`}
                      >
                        <Checkbox
                          className="justify-self-center"
                          checked={worker?.selected || selectedWorkerIds.includes(employee.id)}
                          onCheckedChange={(checked) => {
                            if (checked === true) {
                              onChange([...new Set([...selectedWorkerIds, employee.id])]);
                            } else {
                              onChange(selectedWorkerIds.filter((workerId) => workerId !== employee.id));
                            }
                          }}
                          disabled={!!worker}
                        />
                        {employee.firstName} {employee.lastName} {worker?.reason}
                      </label>
                    );
                  })}
                </div>
              </ScrollArea>
            )}
            {showSelectAll && (
              <>
                <hr className="mt-1 border-soft-200" />
                <div className="-mb-1 mt-1 p-2">
                  {selectedWorkerIds.length === workersQuery.data.users.length ? (
                    <LinkButton style="primary" size="medium" onClick={() => onChange([])}>
                      Deselect All
                    </LinkButton>
                  ) : (
                    <LinkButton
                      style="primary"
                      size="medium"
                      onClick={() => onChange(workersQuery.data.users.map((user) => user.id))}
                    >
                      Select All
                    </LinkButton>
                  )}
                </div>
              </>
            )}
          </>
        )}
      </TabContent>
      <TabContent value="crews">
        {crewsQuery.isPending ? (
          <div className="flex h-72 items-center justify-center">
            <LoadingIndicator text="Loading Crews..." />
          </div>
        ) : crewsQuery.isError ? (
          <p className="text-sm text-error-base">An error occured when fetching workers.</p>
        ) : (
          <>
            <Input
              className="border-none pl-2"
              beforeContent={<Search2Line className="size-5 text-soft-400" />}
              placeholder="Search..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              boxSize="small"
              noBorder
            />
            {filteredCrews(crewsQuery.data.crews, searchText).length === 0 ? (
              <div className="mt-1 flex h-[276px] items-center justify-center text-sm">No results found.</div>
            ) : (
              <ScrollArea className="-mr-1.5 mt-1 h-[276px]">
                <div className="flex flex-col gap-1">
                  {filteredCrews(crewsQuery.data.crews, searchText).map((crew) => {
                    return (
                      <CrewAccordion
                        key={crew.id}
                        crew={crew}
                        selectedWorkerIds={selectedWorkerIds}
                        onChange={onChange}
                        disabledWorkers={disabledWorkers}
                      />
                    );
                  })}
                </div>
              </ScrollArea>
            )}
          </>
        )}
      </TabContent>
    </Tabs>
  );
}

interface CrewAccordionProps {
  crew: Crew;
  selectedWorkerIds: number[];
  onChange: (selectedWorkerIds: number[]) => void;
  disabledWorkers?: { workerId: number; selected: boolean; reason: string }[];
}

function CrewAccordion({ crew, selectedWorkerIds, onChange, disabledWorkers }: CrewAccordionProps) {
  const crewMemberIds = crew.crewMembers.map((crewMember) => crewMember.crewMemberUser.id);
  const [open, setOpen] = useState(false);

  const crewMemberIdsWhoAreNotDisabled = crewMemberIds.filter(
    (id) => !disabledWorkers?.find((worker) => worker.workerId === id)
  );

  return (
    <div>
      <span className="flex w-full items-center gap-2 p-2">
        <ArrowDownSLine
          className={`size-5 cursor-pointer text-sub-600 ${open ? 'rotate-180' : ''}`}
          onClick={() => setOpen((open) => !open)}
        />
        <label
          key={`crew-${crew.id}`}
          className="grid grow select-none grid-cols-[20px_auto] items-center gap-2 text-sm text-strong-950"
        >
          <Checkbox
            checked={
              crewMemberIdsWhoAreNotDisabled.every((id) => selectedWorkerIds.includes(id))
                ? true
                : crewMemberIds.some((id) => selectedWorkerIds.includes(id))
                  ? 'indeterminate'
                  : false
            }
            onCheckedChange={(checked) => {
              if (checked === true) {
                onChange([...new Set([...selectedWorkerIds, ...crewMemberIdsWhoAreNotDisabled])]);
              } else {
                onChange(selectedWorkerIds.filter((workerId) => !crewMemberIds.includes(workerId)));
              }
            }}
            className="justify-self-center"
          />
          {crew.name}
        </label>
      </span>
      {open && (
        <ul className="mt-1 space-y-1">
          {crew.crewMembers.map((member) => {
            const worker = disabledWorkers?.find((worker) => worker.workerId === member.crewMemberUser.id);

            return (
              <label
                key={`${member.crewMemberUser.id}-${member.crewMemberUser.firstName}`}
                className={`grid select-none grid-cols-[20px_auto] items-center gap-2 py-2 pl-16 pr-2 text-sm ${worker ? 'text-disabled-300' : 'text-strong-950'}`}
              >
                <Checkbox
                  checked={worker?.selected || selectedWorkerIds.includes(member.crewMemberUser.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      onChange([...new Set([...selectedWorkerIds, member.crewMemberUser.id])]);
                    } else {
                      onChange(selectedWorkerIds.filter((workerId) => workerId !== member.crewMemberUser.id));
                    }
                  }}
                  className="justify-self-center"
                  disabled={!!worker}
                />
                {`${member.crewMemberUser.firstName} ${member.crewMemberUser.lastName}${
                  member.crewMemberUser.id === crew.crewLead ? ' (Crew Lead)' : ''
                }`}{' '}
                {worker?.reason}
              </label>
            );
          })}
        </ul>
      )}
    </div>
  );
}

function filteredWorkers(workers: HammrUser[], searchText: string) {
  if (!searchText) {
    return workers;
  }

  return workers.filter((worker) => {
    const fullName = worker?.fullName || `${worker.firstName} ${worker.lastName}`;
    return fullName.toLowerCase().includes(searchText.toLowerCase());
  });
}

function filteredCrews(crews: Crew[], searchText: string) {
  if (!searchText) {
    return crews;
  }

  return crews.filter((crew) => {
    return crew.name.toLowerCase().includes(searchText.toLowerCase());
  });
}
