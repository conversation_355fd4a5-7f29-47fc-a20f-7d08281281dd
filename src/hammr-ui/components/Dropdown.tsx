import { cn } from '../lib/utils';
import s from './Dropdown.module.css';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { Slot } from '@radix-ui/react-slot';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import ArrowUpSLine from '@/hammr-icons/ArrowUpSLine';
import CheckLine from '@/hammr-icons/CheckLine';
import * as React from 'react';
import { forwardRef, useEffect, useMemo, useRef, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

interface Item<T extends string | number> {
  value: T;
  label: string;
  subTitle?: string;
  avatar?: {
    content: React.ReactNode;
    type: 'icon' | 'image' | 'avatar';
  };
}

const dropdownVariants = cva(
  'flex items-center gap-2 overflow-hidden rounded-10 border border-soft-200 bg-background shadow-xs',
  {
    variants: {
      size: {
        small: 'h-9',
        medium: 'h-10',
      },
    },
    defaultVariants: {
      size: 'medium',
    },
  }
);

interface DropdownPickerProps<T extends string | number = string>
  extends Omit<React.InputHTMLAttributes<HTMLButtonElement>, 'onChange' | 'type' | 'size'>,
    VariantProps<typeof dropdownVariants> {
  items: Item<T>[];
  withSubTitle?: boolean;
  value?: string | number | null;
  onChange?: (value: T | null) => void;
  onSelectItem?: (item: Item<T> | null) => void;
  placeholder?: string;
  popoverContentClassName?: string;
  beforeContent?: React.ReactNode;
  emptyMessage?: string;
  disabled?: boolean;
  fullWidth?: boolean;
  allowClear?: boolean;
}

export const DropdownPicker = forwardRef(function DropdownPicker<T extends string | number>(
  {
    items,
    beforeContent,
    disabled,
    fullWidth,
    onChange,
    onSelectItem,
    placeholder,
    value,
    withSubTitle,
    className,
    allowClear,
    popoverContentClassName,
    emptyMessage = 'No options available.',
    size,
    ...props
  }: DropdownPickerProps<T>,
  ref: React.Ref<HTMLButtonElement>
) {
  const [open, setOpen] = useState(false);

  const selectedItem = useMemo(() => items.find((item) => item.value === value), [items, value]);

  const popoverContentRef = useRef<HTMLDivElement>();

  useEffect(() => {
    setTimeout(() => {
      popoverContentRef.current?.querySelector('button[aria-selected="true"]')?.scrollIntoView({ block: 'center' });
    });
  }, [value, open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <button
          ref={ref}
          type="button"
          className={cn(
            dropdownVariants({ size }),
            'ring-2 ring-transparent ring-offset-background',
            'group',
            'data-[state="open"]:border-strong-950 data-[state="open"]:ring-strong-400/25 data-[state="open"]:ring-offset-2',
            '!disabled:text-disabled-300 disabled:pointer-events-none disabled:border-transparent disabled:bg-weak-50',
            'aria-[invalid=true]:border-error-base',
            fullWidth && 'w-full',
            'bg-background text-sm text-sub-600',
            'px-2',
            'aria-[invalid=true]:border-error-base',
            className
          )}
          disabled={disabled}
          {...props}
        >
          {!!beforeContent && (
            <span
              className={cn(
                'h-5 w-5 [&>svg]:h-5 [&>svg]:w-5',
                'pr-2',
                !selectedItem ? 'text-soft-400' : 'text-sub-600',
                'group-hover:text-sub-600',
                'group-focus:text-sub-600',
                disabled && '!text-disabled-300'
              )}
            >
              {beforeContent}
            </span>
          )}
          <span
            className={cn(
              'flex-1 text-left',
              selectedItem && !disabled ? 'text-foreground' : disabled ? 'text-disabled-300' : 'text-soft-400'
            )}
          >
            {selectedItem?.label ?? placeholder ?? 'Select'}
          </span>
          {open ? (
            <ArrowUpSLine className="h-5 w-5 text-sub-600" />
          ) : (
            <ArrowDownSLine className="h-5 w-5 text-sub-600" />
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          'mt-2 overflow-auto border border-soft-200 bg-background p-2 shadow-md',
          popoverContentClassName,
          s.PopoverContent
        )}
        onWheel={(e) => {
          e.stopPropagation();
        }}
        ref={popoverContentRef}
      >
        {items.length ? (
          items.map((item, index) => (
            <DropdownItem
              key={index}
              title={item.label}
              selected={item.value === value}
              subTitle={withSubTitle ? item.subTitle || ' ' : undefined}
              onClick={() => {
                if (allowClear && selectedItem === item) {
                  onSelectItem?.(null);
                  onChange?.(null);
                } else {
                  onSelectItem?.(item);
                  onChange?.(item.value);
                }
                setOpen(false);
              }}
              beforeContent={
                item.avatar ? (
                  <div
                    className={cn(
                      'flex items-center justify-center rounded-full text-sub-600',
                      withSubTitle ? 'h-10 w-10' : 'h-5 w-5',
                      '[&>svg]:h-5 [&>svg]:w-5',
                      '[&>img]:rounded-full',
                      item.avatar?.type === 'avatar' && withSubTitle
                        ? '[&>img]:h-10 [&>img]:w-10'
                        : '[&>img]:h-5 [&>img]:w-5',
                      item.avatar?.type !== 'avatar' && withSubTitle && 'border border-soft-200'
                    )}
                  >
                    {item.avatar?.content}
                  </div>
                ) : null
              }
            />
          ))
        ) : (
          <div className="p-2 text-sm text-muted-foreground">{emptyMessage}</div>
        )}
      </PopoverContent>
    </Popover>
  );
});

DropdownPicker.displayName = 'DropdownPicker';

export interface DropdownItemProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'children'> {
  asChild?: boolean;
  beforeContent?: React.ReactNode;
  afterContent?: React.ReactNode;
  selected?: boolean;
  title: string;
  subTitle?: string;
  onClick?: () => void;
}

export const DropdownItem = forwardRef<HTMLButtonElement, DropdownItemProps>(
  ({ className, asChild = false, beforeContent, title, subTitle, selected, afterContent, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';

    return (
      <Comp
        className={'flex w-full items-center gap-3 rounded-8 p-2 hover:bg-weak-50'}
        aria-selected={selected}
        ref={ref}
        {...props}
      >
        {!!beforeContent && <span>{beforeContent}</span>}
        <div className="flex flex-1 flex-col gap-1 text-left">
          <div className={cn('text-sm font-medium text-strong-950', !subTitle && 'font-normal')}>{title}</div>
          {!!subTitle && <div className="text-xs font-normal text-sub-600">{subTitle}</div>}
        </div>
        {selected && <CheckLine className="h-5 w-5 text-sub-600" />}
        {!!afterContent && <span>{afterContent}</span>}
      </Comp>
    );
  }
);

DropdownItem.displayName = 'DropdownItem';
