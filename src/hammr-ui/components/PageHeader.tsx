import { FC, ReactNode } from 'react';
import { cn } from '@/utils/cn';

interface PageHeaderProps {
  icon?: React.ReactNode;
  avatarSrc?: string;
  title: ReactNode;
  subtitle?: ReactNode;
  headerRight?: React.ReactNode;
  breadcrumb?: React.ReactNode;
  className?: string;
  noPadding?: boolean;
  badge?: React.ReactNode;
}

export const PageHeader: FC<PageHeaderProps> = ({
  title,
  avatarSrc,
  headerRight,
  icon,
  subtitle,
  breadcrumb,
  noPadding,
  badge,
}) => {
  function renderLeft() {
    if (avatarSrc) {
      return (
        <img src={avatarSrc} alt={typeof title === 'string' ? title : 'avatar'} className="h-12 w-12 rounded-full" />
      );
    }

    if (icon) {
      return (
        <div className="flex h-12 w-12 items-center justify-center rounded-full border border-soft-200 text-sub-600 [&>svg]:h-6 [&>svg]:w-6">
          {icon}
        </div>
      );
    }

    return null;
  }

  return (
    <div className={cn('flex flex-col gap-4', !noPadding && 'px-8 py-5')}>
      {!!breadcrumb && <div>{breadcrumb}</div>}
      <div className="flex items-center gap-3.5">
        {renderLeft()}
        <div className="flex flex-1 flex-col">
          <div className="flex items-center gap-2">
            <div className="text-2xl font-medium text-strong-950">{title}</div>
            {!!badge && <div>{badge}</div>}
          </div>
          {subtitle && <div className="mt-1 flex items-center gap-2 text-xs text-sub-600">{subtitle}</div>}
        </div>
        {!!headerRight && <div>{headerRight}</div>}
      </div>
      {/* Badge is now displayed beside the title, no need for separate row */}
    </div>
  );
};
