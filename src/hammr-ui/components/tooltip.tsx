'use client';

import * as React from 'react';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { cn } from '@hammr-ui/lib/utils';

const TooltipProvider = TooltipPrimitive.Provider;

const TooltipRoot = TooltipPrimitive.Root;

const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipPortal = TooltipPrimitive.Portal;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      'animate-in fade-in-0 zoom-in-95',
      'data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95',
      'data-[side=bottom]:slide-in-from-top-2',
      'data-[side=left]:slide-in-from-right-2',
      'data-[side=right]:slide-in-from-left-2',
      'data-[side=top]:slide-in-from-bottom-2',
      'z-50 max-w-[360px] overflow-hidden',
      'rounded-md',
      'bg-white-0 py-3',
      'text-sm text-strong-950',
      'whitespace-break-spaces',
      'shadow-md',
      className
    )}
    {...props}
  />
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

const TooltipArrow = TooltipPrimitive.Arrow;

export { TooltipRoot, TooltipTrigger, TooltipContent, TooltipProvider };

export function Tooltip({
  content,
  description,
  icon,
  children,
  contentClassName,
}: {
  content: React.ReactNode;
  description?: React.ReactNode;
  icon?: React.ReactNode;
  children: React.ReactNode;
  contentClassName?: string;
}) {
  return (
    <TooltipProvider>
      <TooltipRoot delayDuration={0}>
        <TooltipTrigger asChild className="cursor-pointer transition-all ease-in-out hover:opacity-60">
          {children}
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent className={cn('bg-strong-950 p-3 text-white-0', contentClassName)}>
            <div className="flex flex-row gap-3">
              {icon ? <div className="text-soft-400">{icon}</div> : undefined}
              <div className="flex flex-col gap-2">
                <div className="text-sm">{content}</div>
                {description ? <div className="text-xs text-soft-400">{description}</div> : undefined}
              </div>
            </div>
            <TooltipArrow className="-mt-[1px] h-1.5 w-3 fill-strong-950" />
          </TooltipContent>
        </TooltipPortal>
      </TooltipRoot>
    </TooltipProvider>
  );
}
