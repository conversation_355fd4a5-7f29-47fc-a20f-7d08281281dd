import AlertFill from '@/hammr-icons/AlertFill';
import CheckboxCircleFill from '@/hammr-icons/CheckboxCircleFill';
import ErrorWarningFill from '@/hammr-icons/ErrorWarningFill';
import ForbidFill from '@/hammr-icons/ForbidFill';
import React from 'react';
import { cn } from '../lib/cn';

interface Props {
  status?: 'completed' | 'pending' | 'failed' | 'disabled';
  children?: React.ReactNode;
  className?: string;
}

type RefType = React.ForwardedRef<HTMLSpanElement>;

function StatusBadge({ status = 'completed', children, className, ...props }: Props, ref: RefType) {
  return (
    <span
      className={cn(
        'flex h-6 items-center gap-1 rounded-6 border border-soft-200 bg-white-0 p-1 pr-2 font-sans text-xs font-medium text-sub-600',
        className
      )}
      ref={ref}
      {...props}
    >
      {status === 'completed' && <CheckboxCircleFill className="size-4 text-success-base" />}
      {status === 'pending' && <AlertFill className="size-4 text-warning-base" />}
      {status === 'failed' && <ErrorWarningFill className="size-4 text-error-base" />}
      {status === 'disabled' && <ForbidFill className="size-4 text-faded-base" />}
      {children}
    </span>
  );
}

export default React.forwardRef(StatusBadge);
