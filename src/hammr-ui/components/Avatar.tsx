import { FC } from 'react';
import avatarPlaceholder from '@/assets/imgs/avatar.svg';
import { cva, VariantProps } from 'class-variance-authority';
import { cn } from '../lib/utils';

export const avatarVariants = cva(cn('flex justify-center items-center rounded-full uppercase font-medium'), {
  variants: {
    color: {
      neutral: 'text-strong-950 bg-soft-200',
    },
  },
  defaultVariants: {
    color: 'neutral',
  },
});

interface AvatarProps extends VariantProps<typeof avatarVariants> {
  imageUrl?: string;
  initials?: string;
  name?: string;
  size?: number;
}

export const Avatar: FC<AvatarProps> = ({ imageUrl, initials, name, size = 32, color }) => {
  const initialsText = initials || getInitials(name || '');

  const background = imageUrl ? imageUrl : initialsText ? null : avatarPlaceholder.src;

  return (
    <div
      style={{
        width: size,
        height: size,
        backgroundImage: background ? `url(${background})` : undefined,
        backgroundSize: 'cover',
        fontSize: `${size / 2.5}px`,
      }}
      className={avatarVariants({ color })}
    >
      {initialsText ? <span className="">{initialsText}</span> : null}
    </div>
  );
};

export function getInitials(name: string) {
  if (!name) return null;

  return name
    .split(' ')
    .map((word) => word[0])
    .join('');
}
