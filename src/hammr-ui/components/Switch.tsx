'use client';

import * as React from 'react';
import * as SwitchPrimitives from '@radix-ui/react-switch';

import { cn } from '../lib/utils';

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      'group h-4 w-7 shrink-0 cursor-pointer rounded-full border border-transparent transition-colors',
      'focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-strong-400/25 focus-visible:ring-offset-2 focus-visible:ring-offset-white-0',
      'disabled:cursor-not-allowed disabled:border-soft-200',
      'data-[state=checked]:bg-primary-base data-[state=unchecked]:bg-soft-200',
      'data-[state=checked]:hover:bg-primary-darker data-[state=unchecked]:hover:bg-sub-300',
      'data-[state=checked]:disabled:bg-white-0 data-[state=unchecked]:disabled:bg-white-0',
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={
        'pointer-events-none flex size-3 items-center justify-center transition-transform ' +
        'data-[state=checked]:translate-x-[13px] data-[state=unchecked]:translate-x-px'
      }
    >
      <span
        className={
          'box-content block size-1 rounded-full border-4 border-static-white shadow transition-all group-active:border-[3px] ' +
          'group-data-[state=checked]:bg-primary-base group-data-[state=unchecked]:bg-soft-200 ' +
          'group-data-[state=checked]:group-hover:bg-primary-darker group-data-[state=unchecked]:group-hover:bg-sub-300 ' +
          'group-disabled:border-soft-200 group-disabled:shadow-none ' +
          'group-disabled:group-data-[state=checked]:bg-soft-200 group-disabled:group-data-[state=unchecked]:bg-soft-200'
        }
      ></span>
    </SwitchPrimitives.Thumb>
  </SwitchPrimitives.Root>
));
Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch };
