import { MouseE<PERSON>Handler, ReactElement, ReactNode } from 'react';
import { cn } from '@hammr-ui/lib/utils';
import { Badge } from '@hammr-ui/components/badge';

export interface ContentCardProps {
  label: ReactNode;
  subLabel?: ReactNode;
  description?: ReactNode;
  icon?: ReactNode;
  afterContent?: ReactNode;
  className?: string;
  badge?: ReactElement<typeof Badge>;
  onClick?: MouseEventHandler<HTMLDivElement>;
}

export default function ContentCard({
  label,
  subLabel,
  description,
  icon,
  afterContent,
  badge,
  className,
  onClick,
}: ContentCardProps) {
  return (
    <div className={cn('flex flex-row items-center gap-3.5 p-2', className)} onClick={onClick}>
      {icon}
      <div className="flex flex-1 flex-col justify-center gap-1">
        <div className="flex flex-row items-center gap-1">
          <span className="text-sm font-medium">{label}</span>{' '}
          {subLabel && <span className="text-xs text-sub-600">({subLabel})</span>} {badge}
        </div>
        {description && <div className="text-xs text-sub-600">{description}</div>}
      </div>
      {afterContent}
    </div>
  );
}
