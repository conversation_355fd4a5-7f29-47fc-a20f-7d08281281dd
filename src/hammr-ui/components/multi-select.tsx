import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { Input } from './input';
import { Checkbox } from './checkbox';
import { LinkButton } from './LinkButton';
import { ReactNode, useMemo, useState } from 'react';
import Search2Line from '@/hammr-icons/Search2Line';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { cn } from '../lib/utils';
import { ScrollArea } from './scroll-area';

export interface Item {
  label: string;
  labelWhenSelected?: ReactNode;
  value: string | number | boolean;
  isSelected?: boolean;
}

interface Props {
  label: string;
  items: Item[];
  searchable?: boolean;
  onChange: (items: Item[]) => void;
  buttonProps?: React.ComponentProps<'button'>;
  popoverProps?: React.ComponentProps<typeof PopoverContent>;
}

export function MultiSelect({
  label = 'Label',
  items = [],
  onChange,
  searchable = true,
  buttonProps,
  popoverProps,
}: Props) {
  const [searchText, setSearchText] = useState('');

  const filteredItems = items.filter((item) => item.label.toLowerCase().includes(searchText.toLowerCase()));

  const _label = useMemo(() => {
    const selectedItems = items.filter((item) => item.isSelected);
    if (selectedItems.length === 0) {
      return label;
    }
    return selectedItems.slice(0, 10).map(
      (item, index) =>
        item.labelWhenSelected ??
        (item.label
          ? item.label + (index + 1 !== selectedItems.length ? ', ' : '') // we can't use .join() because we can also have JSX here
          : '(Blanks)')
    );
  }, [items, label]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          {...buttonProps}
          className={cn(
            'group flex items-center justify-between gap-2 rounded-lg border border-soft-200 bg-white-0 py-2 pl-2.5 pr-2 text-sm text-strong-950 ring-neutral-alpha-24 ring-offset-2 hover:bg-weak-50 focus-visible:border-strong-950 focus-visible:outline-none focus-visible:ring',
            buttonProps?.className
          )}
        >
          <span className="flex gap-2 overflow-hidden text-ellipsis whitespace-nowrap">{_label}</span>
          <ArrowDownSLine className="size-5 shrink-0 text-sub-600 group-data-[state=open]:rotate-180" />
        </button>
      </PopoverTrigger>
      <PopoverContent {...popoverProps} className={cn('flex flex-col gap-1 px-2 pt-2', popoverProps?.className)}>
        {searchable && items.length ? (
          <Input
            boxSize="x-small"
            beforeContent={<Search2Line className="size-5" />}
            placeholder="Search..."
            value={searchText}
            className="[&>input]:min-w-[100px]"
            onChange={(e) => setSearchText(e.target.value)}
          />
        ) : undefined}
        {filteredItems.length !== 0 && (
          <ScrollArea className="h-[195px]">
            <div className="flex flex-col gap-1">
              {filteredItems.map((item) => (
                <label
                  className="flex select-none items-center gap-2 rounded-lg p-2 text-sm text-strong-950 hover:bg-weak-50"
                  key={String(item.value)}
                >
                  <Checkbox
                    id={item.value.toString()}
                    className="m-0.5"
                    checked={item.isSelected}
                    onCheckedChange={(checked) => {
                      const newItems = items.map((i) =>
                        i.value === item.value ? { ...i, isSelected: checked as boolean } : i
                      );
                      onChange?.(newItems);
                    }}
                  />
                  <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                    {item.label ? item.label : '(Blanks)'}
                  </span>
                </label>
              ))}
            </div>
          </ScrollArea>
        )}
        {filteredItems.length === 0 && (
          <div className="flex items-center justify-center py-4 text-sm text-sub-600">No result for this search</div>
        )}
        <hr className="border-soft-200" />
        <div className="flex items-center justify-between px-2 py-3">
          <LinkButton
            onClick={() => {
              onChange(items.map((i) => ({ ...i, isSelected: false })));
            }}
          >
            Clear
          </LinkButton>
          <LinkButton
            style="primary"
            onClick={() => {
              onChange(items.map((i) => ({ ...i, isSelected: true })));
            }}
          >
            Select All
          </LinkButton>
        </div>
      </PopoverContent>
    </Popover>
  );
}
