import { equipmentService } from '@/services/equipment';
import { useQuery } from '@tanstack/react-query';
import { cn } from '@/utils/cn';
import LoadingIndicator from './LoadingIndicator';
import { Input } from './input';
import { RiSearch2Line } from '@remixicon/react';
import { Equipment } from '@/interfaces/equipment';
import { ScrollArea } from './scroll-area';
import { Checkbox } from './checkbox';
import { useState } from 'react';

interface Props {
  selectedEquipmentIds: number[];
  onChange: (selectedEquipmentIds: number[]) => void;
  className?: string;
}

export default function EquipentSelect({ selectedEquipmentIds, onChange, className }: Props) {
  const [searchText, setSearchText] = useState('');

  const equipmentQuery = useQuery({
    queryKey: ['equipment'],
    queryFn() {
      return equipmentService.getAll({ isArchived: false });
    },
  });

  return (
    <div className={cn('rounded-16 border border-soft-200 p-[7px]', className)}>
      {equipmentQuery.isPending ? (
        <div className="flex h-72 items-center justify-center">
          <LoadingIndicator text="Fetching equipment..." />
        </div>
      ) : equipmentQuery.isError ? (
        <p className="text-sm text-error-base">An error occured when fetching equipment.</p>
      ) : (
        <>
          <Input
            className="border-none pl-2"
            beforeContent={<RiSearch2Line className="size-5 text-soft-400" />}
            placeholder="Search..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            boxSize="small"
            noBorder
          />
          {filteredEquipment(equipmentQuery.data, searchText).length === 0 ? (
            <div className="mt-1 flex h-[276px] items-center justify-center text-sm">No results found.</div>
          ) : (
            <ScrollArea className="-mr-1.5 mt-1 h-40">
              <div className="flex flex-col gap-1">
                {filteredEquipment(equipmentQuery.data, searchText).map((singleEquipment) => {
                  return (
                    <label
                      key={singleEquipment.id}
                      className="grid select-none grid-cols-[20px_auto] items-center gap-2 p-2 text-sm text-strong-950"
                    >
                      <Checkbox
                        className="justify-self-center"
                        checked={selectedEquipmentIds.includes(singleEquipment.id)}
                        onCheckedChange={(checked) => {
                          if (checked === true) {
                            onChange([...new Set([...selectedEquipmentIds, singleEquipment.id])]);
                          } else {
                            onChange(selectedEquipmentIds.filter((equipmentId) => equipmentId !== singleEquipment.id));
                          }
                        }}
                      />
                      {singleEquipment.name}
                    </label>
                  );
                })}
              </div>
            </ScrollArea>
          )}
        </>
      )}
    </div>
  );
}

function filteredEquipment(equipment: Equipment[], searchText: string) {
  if (!searchText) {
    return equipment;
  }

  return equipment.filter((singleEquipment) => {
    return singleEquipment.name.toLowerCase().includes(searchText.toLowerCase());
  });
}
