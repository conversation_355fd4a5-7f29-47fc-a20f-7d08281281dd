import * as React from 'react';

import { cn } from '../lib/utils';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  hideCounter?: boolean;
  autoSize?: boolean;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, 'aria-invalid': ariaInvalid, onChange, maxLength = 200, hideCounter, autoSize, ...props }, ref) => {
    const [text, setText] = React.useState('');
    const inputRef = React.useRef<HTMLTextAreaElement | null>(null);

    React.useEffect(() => {
      if (autoSize && inputRef.current) {
        inputRef.current.style.height = 'auto';
        inputRef.current.style.height = `${inputRef.current.scrollHeight}px`;
      }
    }, [props.value, autoSize]);

    return (
      <div
        className={cn(
          'group relative flex',
          'overflow-hidden rounded-xl border border-soft-200',
          'ring-strong-400/25 ring-offset-2 ring-offset-white-0',
          'focus-within:border-strong-950 focus-within:ring-2',
          'aria-[invalid=true]:border-error-base'
        )}
        aria-invalid={!!ariaInvalid}
      >
        <textarea
          className={cn(
            'min-h-[132px] w-full px-3 py-2.5',
            'text-sm text-strong-950',
            'placeholder:text-soft-400',
            'bg-white-0',
            'group-hover:bg-weak-50 group-hover:placeholder:text-sub-600',
            'focus:outline-none focus:placeholder:text-sub-600 focus:group-hover:bg-white-0',
            'disable:cursor-not-allowed disabled:border-transparent disabled:bg-weak-50 disabled:text-disabled-300 disabled:placeholder:text-disabled-300 disabled:group-hover:placeholder:text-disabled-300',
            className
          )}
          ref={(el) => {
            inputRef.current = el;
            if (typeof ref === 'function') {
              ref(el);
            } else if (ref) {
              ref.current = el;
            }
          }}
          {...props}
          onChange={(e) => {
            setText(e.target.value);
            onChange?.(e);
          }}
        />
        {!hideCounter && (
          <span
            className={`pointer-events-none absolute bottom-2.5 right-2.5 flex items-center gap-1.5 text-[11px] leading-3 ${text.length > maxLength ? 'text-error-base' : 'text-soft-400'}`}
          >
            {text.length}/{maxLength}
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path d="M9.11111 2L2 9.11111M10 6.44444L6.44444 10" stroke="currentColor" />
            </svg>
          </span>
        )}
      </div>
    );
  }
);
Textarea.displayName = 'Textarea';

export { Textarea };
