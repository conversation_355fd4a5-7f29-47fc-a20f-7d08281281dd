import { cva, VariantProps } from 'class-variance-authority';
import { cn } from '@hammr-ui/lib/utils';
import * as React from 'react';

const keyIconVariants = cva(cn('flex h-6 w-6 items-center shrink-0 justify-center [&>svg]:h-5 [&>svg]:w-5'), {
  variants: {
    color: {
      grey: 'text-sub-600',
      blue: 'text-information-base',
      orange: 'text-warning-base',
      red: 'text-error-base',
      green: 'text-success-base',
      purple: 'text-feature-base',
      yellow: 'text-away-base',
    },
    variant: {
      stroke: 'border border-soft-200',
      lighter: '',
    },
    shape: {
      square: 'rounded-10',
      circle: 'rounded-full',
    },
    size: {
      small: 'h-8 w-8',
      medium: 'h-10 w-10',
      large: 'h-12 w-12',
      xLarge: 'h-14 w-14',
      '2XLarger': 'h-16 w-16',
    },
  },
  defaultVariants: {
    color: 'grey',
    shape: 'circle',
    size: 'medium',
    variant: 'stroke',
  },
  compoundVariants: [
    {
      variant: 'lighter',
      color: 'grey',
      className: 'bg-faded-lighter',
    },
    {
      variant: 'lighter',
      color: 'red',
      className: 'bg-error-lighter',
    },
    {
      variant: 'lighter',
      color: 'blue',
      className: 'bg-information-lighter',
    },
    {
      variant: 'lighter',
      color: 'orange',
      className: 'bg-warning-lighter',
    },
    {
      variant: 'lighter',
      color: 'green',
      className: 'bg-success-lighter',
    },
    {
      variant: 'lighter',
      color: 'purple',
      className: 'bg-feature-lighter',
    },
  ],
});

export function KeyIcon({
  icon,
  color,
  size,
  variant,
}: {
  icon: React.ReactNode;
  color?: VariantProps<typeof keyIconVariants>['color'];
  variant?: VariantProps<typeof keyIconVariants>['variant'];
  size?: VariantProps<typeof keyIconVariants>['size'];
}) {
  return (
    <div
      className={cn(
        keyIconVariants({
          variant,
          size,
          color,
        })
      )}
    >
      {icon}
    </div>
  );
}
