import * as React from 'react';
import { CSSProperties, useEffect, useRef } from 'react';
import * as RPNInput from 'react-phone-number-input';
import flags from 'react-phone-number-input/flags';

import { Input, InputProps } from '@hammr-ui/components/input';
import { Combobox, type ComboboxItem } from '@hammr-ui/components/combobox';
import { cn } from '@hammr-ui/lib/utils';

type PhoneInputProps = Omit<React.ComponentProps<'input'>, 'onChange' | 'value' | 'ref'> &
  Omit<RPNInput.Props<typeof RPNInput.default>, 'onChange'> & {
    onChange?: (value: RPNInput.Value) => void;
    error?: string;
  };

const PhoneNumberInput: React.ForwardRefExoticComponent<PhoneInputProps> = React.forwardRef<
  React.ElementRef<typeof RPNInput.default>,
  PhoneInputProps
>(({ className, onChange, error, ...props }, ref) => {
  return (
    <RPNInput.default
      ref={ref}
      className={cn('flex', className)}
      flagComponent={FlagComponent}
      countrySelectComponent={CountrySelect}
      countrySelectProps={{ error }}
      inputComponent={InputComponent}
      numberInputProps={{
        placeholder: '(*************',
        className: cn({ 'border-error-base focus-within:border-error-base': error }),
      }}
      smartCaret={false}
      onChange={(value) => onChange?.(value || ('' as RPNInput.Value))}
      {...props}
    />
  );
});
PhoneNumberInput.displayName = 'PhoneInput';

const InputComponent = React.forwardRef<HTMLInputElement, InputProps>(({ className, ...props }, ref) => (
  <Input className={cn('rounded-e-lg rounded-s-none', className)} {...props} ref={ref} />
));
InputComponent.displayName = 'InputComponent';

type CountryEntry = { label: string; value: RPNInput.Country | undefined };

type CountrySelectProps = {
  disabled?: boolean;
  value: RPNInput.Country;
  options: CountryEntry[];
  onChange: (country: RPNInput.Country) => void;
  showCountryName: boolean;
  error?: string;
};

const CountrySelect = ({
  disabled,
  value: selectedCountry = 'US',
  options: countryList,
  error,
  onChange,
}: CountrySelectProps) => {
  const comboboxItems: ComboboxItem[] = countryList
    .filter((country): country is { label: string; value: RPNInput.Country } => !!country.value)
    .map((country) => ({
      value: country.value,
      description: country.label,
      selectedLabel: (
        <div className="flex items-center gap-2">
          <FlagComponent country={country.value} countryName={country.label} />
          <span className="text-muted-foreground">{`+${RPNInput.getCountryCallingCode(country.value)}`}</span>
        </div>
      ),
      label: (
        <div className="flex items-center gap-2">
          <FlagComponent country={country.value} countryName={country.label} />
          <span>{country.label}</span>
          <span className="text-muted-foreground">{`+${RPNInput.getCountryCallingCode(country.value)}`}</span>
        </div>
      ),
    }));

  return (
    <Combobox
      items={comboboxItems}
      value={selectedCountry}
      onChange={onChange}
      disabled={disabled}
      error={error}
      placeholder="Select country"
      searchPlaceholder="Search country..."
      className="rounded-e-none rounded-s-lg border-r-0"
      popoverContentProps={{
        style: {
          width: 406, //  we need to find a better dynamic way
        } as CSSProperties,
      }}
    />
  );
};

const FlagComponent = ({ country, countryName }: RPNInput.FlagProps) => {
  const Flag = flags[country];

  return (
    <span className="flex h-4 w-6 overflow-hidden rounded-sm bg-foreground/20">
      {Flag && <Flag title={countryName} />}
    </span>
  );
};

export { PhoneNumberInput };
