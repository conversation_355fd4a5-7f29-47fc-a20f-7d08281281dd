import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '../lib/utils';

export const buttonVariants = cva(
  cn(
    'inline-flex items-center justify-center whitespace-nowrap rounded-10 transition-all gap-1', // layout
    'text-sm font-medium text-sub-600 hover:text-strong-950 focus:text-background', // typography
    'border border-transparent', // border
    'outline-none focus:outline-none focus:ring-offset-2', // focus
    'hover:bg-weak-50 focus:bg-foreground', // background
    'disabled:pointer-events-none disabled:bg-transparent disabled:text-disabled-300 disabled:border-transparent' // disabled
  ),
  {
    variants: {
      variant: {
        outline: 'border-soft-200 hover:border-transparent',
        ghost: 'hover:bg-weak-50',
      },
      shape: {
        round: 'rounded-full',
        square: 'rounded-6',
      },
      size: {
        large: 'w-6 h-6 [&>svg]:h-5 [&>svg]:w-5',
        medium: 'w-5 h-5 [&>svg]:h-4 [&>svg]:w-4',
      },
    },
    defaultVariants: {
      variant: 'ghost',
      size: 'medium',
      shape: 'square',
    },
  }
);

export interface CompactButtonProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

export const CompactButton = React.forwardRef<
  HTMLButtonElement,
  CompactButtonProps
>(
  (
    { className, variant, size, shape, asChild = false, children, ...props },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, shape, className }))}
        ref={ref}
        {...props}
      >
        {children}
      </Comp>
    );
  }
);

CompactButton.displayName = 'CompactButton';

export default CompactButton;
