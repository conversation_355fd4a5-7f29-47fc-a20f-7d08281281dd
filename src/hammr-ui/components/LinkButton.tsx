import { cn } from '../lib/utils';

interface Props {
  style?: 'gray' | 'primary' | 'error';
  size?: 'small' | 'medium';
  className?: string;
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  disabled?: boolean;
}

export function LinkButton({ style = 'gray', size = 'small', className, children, onClick, disabled }: Props) {
  return (
    <button
      type="button"
      className={cn(
        'font-medium hover:underline disabled:cursor-not-allowed disabled:text-disabled-300 disabled:hover:no-underline',
        {
          'text-sub-600': style === 'gray',
          'text-primary-base': style === 'primary',
          'text-error-base': style === 'error',
          'text-xs': size === 'small',
          'text-sm': size === 'medium',
        },
        className
      )}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
}
