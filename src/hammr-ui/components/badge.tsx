import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '../lib/utils';

function createCompoundVariants({
  color,
  bgBase,
  bgLight,
  bgLighter,
  textDark,
  textBase,
  borderBase,
}: {
  color: 'green' | 'red' | 'orange' | 'blue' | 'purple' | 'yellow' | 'gray' | 'sky';
  bgBase: string;
  textBase: string;
  bgLight: string;
  bgLighter: string;
  textDark: string;
  borderBase: string;
}): Array<{
  color: 'green' | 'red' | 'orange' | 'blue' | 'purple' | 'yellow' | 'gray' | 'sky';
  variant: 'default' | 'light' | 'lighter' | 'outline';
  className: string;
}> {
  return [
    {
      variant: 'default',
      color,
      className: cn('text-static-white', bgBase),
    },
    {
      variant: 'light',
      color,
      className: cn(textDark, bgLight),
    },
    {
      variant: 'lighter',
      color,
      className: cn(textBase, bgLighter),
    },
    {
      variant: 'outline',
      color,
      className: cn('border', textBase, borderBase),
    },
  ];
}

const badgeVariants = cva(cn('inline-flex items-center gap-1 border px-2', 'font-medium'), {
  variants: {
    variant: {
      default: 'border-transparent',
      light: 'border-transparent',
      lighter: 'border-transparent',
      outline: '',
    },
    shape: {
      round: 'rounded-full',
      square: 'rounded-6',
    },
    color: {
      gray: '',
      blue: '',
      orange: '',
      red: '',
      green: '',
      yellow: '',
      purple: '',
      sky: '',
      pink: '',
      teal: '',
    },
    size: {
      small: 'h-4 text-[11px]',
      medium: 'h-5 text-xs',
      large: 'h-6 text-xs',
    },
    disabled: {
      true: '',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
  compoundVariants: [
    ...createCompoundVariants({
      color: 'green',
      bgBase: 'bg-success-base',
      bgLight: 'bg-success-light',
      bgLighter: 'bg-success-lighter',
      textDark: 'text-success-dark',
      textBase: 'text-success-base',
      borderBase: 'border-success-base',
    }),
    ...createCompoundVariants({
      color: 'red',
      bgBase: 'bg-error-base',
      bgLight: 'bg-error-light',
      bgLighter: 'bg-error-lighter',
      textDark: 'text-error-dark',
      textBase: 'text-error-base',
      borderBase: 'border-error-base',
    }),
    ...createCompoundVariants({
      color: 'orange',
      bgBase: 'bg-warning-base',
      bgLight: 'bg-warning-light',
      bgLighter: 'bg-warning-lighter',
      textDark: 'text-warning-dark',
      textBase: 'text-warning-base',
      borderBase: 'border-warning-base',
    }),
    ...createCompoundVariants({
      color: 'blue',
      bgBase: 'bg-information-base',
      bgLight: 'bg-information-light',
      bgLighter: 'bg-information-lighter',
      textDark: 'text-information-dark',
      textBase: 'text-information-base',
      borderBase: 'border-information-base',
    }),
    ...createCompoundVariants({
      color: 'purple',
      bgBase: 'bg-feature-base',
      bgLight: 'bg-feature-light',
      bgLighter: 'bg-feature-lighter',
      textDark: 'text-feature-dark',
      textBase: 'text-feature-base',
      borderBase: 'border-feature-base',
    }),
    ...createCompoundVariants({
      color: 'yellow',
      bgBase: 'bg-away-base',
      bgLight: 'bg-away-light',
      bgLighter: 'bg-away-lighter',
      textDark: 'text-away-dark',
      textBase: 'text-away-base',
      borderBase: 'border-away-base',
    }),
    ...createCompoundVariants({
      color: 'gray',
      bgBase: 'bg-faded-base',
      bgLight: 'bg-faded-light',
      bgLighter: 'bg-faded-lighter',
      textDark: 'text-faded-dark',
      textBase: 'text-faded-base',
      borderBase: 'border-faded-base',
    }),
    ...createCompoundVariants({
      color: 'sky',
      bgBase: 'bg-verified-base',
      bgLight: 'bg-verified-light',
      bgLighter: 'bg-verified-lighter',
      textDark: 'text-verified-dark',
      textBase: 'text-verified-base',
      borderBase: 'border-verified-base',
    }),
  ],
});

const beforeAfterContentVariants = cva('', {
  variants: {
    size: {
      small: 'h-2.5 w-2.5 [&>svg]:h-2.5 [&>svg]:w-2.5',
      medium: 'h-3 w-3 [&>svg]:h-3 [&>svg]:w-3',
      large: 'h-4 w-4 [&>svg]:h-4 [&>svg]:w-4',
    },
  },
});

export interface BadgeProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'color'>,
    VariantProps<typeof badgeVariants> {
  beforeContent?: React.ReactNode;
  afterContent?: React.ReactNode;
}

const Badge = React.forwardRef(function Badge(
  {
    className,
    variant = 'default',
    color,
    shape = 'round',
    size = 'medium',
    disabled,
    children,
    beforeContent,
    afterContent,
    ...props
  }: BadgeProps,
  ref: React.Ref<HTMLSpanElement>
) {
  return (
    <span className={cn(badgeVariants({ variant, color, size, shape, disabled }), className)} {...props} ref={ref}>
      {!!beforeContent && <span className={cn('-ml-0.5', beforeAfterContentVariants({ size }))}>{beforeContent}</span>}
      <span>{children}</span>
      {!!afterContent && <span className={cn('-mr-0.5', beforeAfterContentVariants({ size }))}>{afterContent}</span>}
    </span>
  );
});

export { Badge, badgeVariants };
