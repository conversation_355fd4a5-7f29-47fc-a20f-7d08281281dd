import { FC } from 'react';

export const EmptytStateChat: FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg width="148" height="148" viewBox="0 0 148 148" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="148" height="148" rx="74" className="fill-weak-100" />
      <path
        d="M98.7759 71.8377C101.046 71.8377 102.887 73.6783 102.887 75.9488V107.072C102.887 109.342 101.046 111.183 98.7759 111.183H94.2481C93.4912 111.183 92.8777 111.797 92.8777 112.553V119.748C92.8777 120.969 91.4016 121.581 90.5383 120.717L81.4053 111.584C81.1483 111.327 80.7997 111.183 80.4363 111.183H49.1035C46.833 111.183 44.9924 109.342 44.9924 107.072V75.9488C44.9924 73.6783 46.833 71.8377 49.1035 71.8377H98.7759Z"
        className="fill-sub-300 stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M122.59 31.3174C124.861 31.3174 126.701 33.158 126.701 35.4285V72.4173C126.701 74.6878 124.861 76.5284 122.59 76.5284H116.57C115.813 76.5284 115.199 77.1419 115.199 77.8988V86.8631C115.199 88.084 113.723 88.6954 112.86 87.8321L101.958 76.9298C101.701 76.6728 101.352 76.5284 100.989 76.5284H64.2867C62.0162 76.5284 60.1756 74.6878 60.1756 72.4173V35.4285C60.1756 33.158 62.0162 31.3174 64.2867 31.3174H122.59Z"
        className="fill-soft-200 stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M99.9401 40.9683C102.197 40.9701 104.362 41.8805 105.959 43.4998C107.56 45.1223 108.459 47.3239 108.459 49.6198C108.459 53.8961 106.386 57.6748 102.288 61.473C99.713 63.7356 96.9658 65.7877 94.0719 67.6111L93.4462 68.005L92.8164 67.618C89.8929 65.8233 87.1355 63.7634 84.5545 61.442C80.6604 57.7466 78.4177 53.7638 78.4177 49.6171C78.4256 45.6379 81.1057 42.1762 84.914 41.227C88.0948 40.4342 91.3784 41.5644 93.4364 44.0107C95.0359 42.1049 97.398 40.9688 99.9262 40.9683H99.9401Z"
        className="fill-strong-400 stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.4102 54.4847C23.1397 54.4847 21.2991 56.3254 21.2991 58.5959V95.5846C21.2991 97.8551 23.1397 99.6957 25.4102 99.6957H31.4305C32.1874 99.6957 32.8009 100.309 32.8009 101.066V110.03C32.8009 111.251 34.277 111.863 35.1403 110.999L46.0426 100.097C46.2996 99.8401 46.6482 99.6957 47.0116 99.6957H83.7136C85.9841 99.6957 87.8247 97.8551 87.8247 95.5846V58.5959C87.8247 56.3253 85.9841 54.4847 83.7136 54.4847H25.4102Z"
        className="fill-background stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M35.3133 65.5844L41.5669 71.8386L35.3133 78.0922"
        className="stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M73.8094 65.5844L67.5558 71.8386L73.8094 78.0922"
        className="stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M65.0767 88.1443H44.0454" className="stroke-strong-400" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M121.027 124.878C121.027 124.878 114.737 123.004 112.848 129.267C114.737 123.004 108.459 121.088 108.459 121.088C108.459 121.088 114.75 122.961 116.639 116.699C114.75 122.961 121.027 124.878 121.027 124.878Z"
        className="fill-background stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M37.6775 27.0001C37.6775 27.0001 32.3054 33.3024 38.5679 38.6775C32.3053 33.3023 26.8905 39.5679 26.8905 39.5679C26.8905 39.5679 32.2626 33.2656 26 27.8904C32.2626 33.2656 37.6775 27.0001 37.6775 27.0001Z"
        className="fill-background stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
