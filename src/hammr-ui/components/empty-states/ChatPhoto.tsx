import { FC } from 'react';

export const EmptytStateChatPhoto: FC<React.SVGProps<SVGSVGElement>> = (props) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none" {...props}>
      <rect width="100" height="100" rx="50" className="fill-weak-100" />
      <path
        d="M87.1628 73.6488C87.1628 73.6488 83.1087 73.6626 83.1087 77.7028C83.1087 73.6626 79.0547 73.6488 79.0547 73.6488C79.0547 73.6488 83.1087 73.635 83.1087 69.5947C83.1087 73.635 87.1628 73.6488 87.1628 73.6488Z"
        className="fill-background stroke-strong-400"
        strokeWidth="0.675676"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.2175 17.5678C16.2175 17.5678 12.1634 17.5815 12.1634 21.6218C12.1634 17.5815 8.10938 17.5678 8.10938 17.5678C8.10938 17.5678 12.1634 17.5539 12.1634 13.5137C12.1634 17.5539 16.2175 17.5678 16.2175 17.5678Z"
        className="fill-background stroke-strong-400"
        strokeWidth="0.675676"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect
        x="18.2188"
        y="30.5762"
        width="47.4776"
        height="53.2075"
        rx="3.0184"
        className="fill-soft-200 stroke-strong-400"
        strokeWidth="0.675676"
        strokeLinejoin="round"
      />
      <rect
        x="22.7031"
        y="24.2759"
        width="47.4776"
        height="53.2075"
        rx="3.0184"
        className="fill-weak-100 stroke-strong-400"
        strokeWidth="0.675676"
        strokeLinejoin="round"
      />
      <rect
        x="28.3125"
        y="30.5762"
        width="36.2527"
        height="35.9214"
        className="className-stroke-strong-400 fill-soft-200"
        strokeWidth="0.675676"
        strokeLinejoin="round"
      />
      <rect
        x="28.3125"
        y="30.5762"
        width="36.2527"
        height="35.9214"
        className="fill-background stroke-strong-400"
        strokeWidth="0.675676"
        strokeLinejoin="round"
      />
      <ellipse
        cx="46.4928"
        cy="43.0536"
        rx="7.19593"
        ry="7.48377"
        className="fill-strong-400 stroke-strong-400"
        strokeWidth="0.675676"
        strokeMiterlimit="10"
      />
      <path
        d="M61.9909 66.0911C61.9909 64.0485 61.5885 62.026 60.8069 60.1389C60.0252 58.2518 58.8795 56.5371 57.4352 55.0928C55.9909 53.6485 54.2762 52.5028 52.3891 51.7211C50.502 50.9394 48.4794 50.5371 46.4368 50.5371C44.3942 50.5371 42.3717 50.9394 40.4846 51.7211C38.5975 52.5028 36.8828 53.6485 35.4385 55.0928C33.9942 56.5371 32.8485 58.2518 32.0668 60.1389C31.2851 62.026 30.8828 64.0485 30.8828 66.0911L61.9909 66.0911Z"
        className="fill-strong-400 stroke-strong-400"
        strokeWidth="0.675676"
        strokeMiterlimit="10"
      />
      <rect
        x="54.8516"
        y="17.999"
        width="29.4396"
        height="23.8754"
        rx="2.57732"
        className="fill-soft-200 stroke-strong-400"
        strokeWidth="0.675676"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M61.4037 25.9055L63.5961 28.0835L61.3408 30.3538C61.1108 30.5854 61.112 30.9595 61.3435 31.1895C61.575 31.4194 61.9492 31.4182 62.1792 31.1867L64.8509 28.4971C65.0809 28.2656 65.0797 27.8915 64.8481 27.6615L62.2365 25.0671C62.005 24.8371 61.6309 24.8384 61.4009 25.0699C61.1709 25.3014 61.1721 25.6756 61.4037 25.9055Z"
        className="fill-strong-400"
      />
      <path
        d="M77.5956 25.0159L75.0012 27.6275C74.8916 27.7379 74.8291 27.8903 74.8296 28.0459C74.8301 28.2015 74.8936 28.3535 75.004 28.4631L77.6936 31.1349C77.9251 31.3649 78.2992 31.3636 78.5292 31.1321C78.7592 30.9006 78.7579 30.5265 78.5264 30.2965L76.2561 28.0412L78.434 25.8487C78.664 25.6172 78.6628 25.2431 78.4313 25.0131C78.1998 24.7831 77.8256 24.7844 77.5956 25.0159Z"
        className="fill-strong-400"
      />
      <path
        d="M65.7466 35.7115L74.1754 35.6836C74.5017 35.6825 74.7654 35.4171 74.7643 35.0907C74.7632 34.7644 74.4978 34.5007 74.1715 34.5018L65.7427 34.5297C65.4164 34.5308 65.1527 34.7962 65.1538 35.1226C65.1549 35.4489 65.4203 35.7126 65.7466 35.7115Z"
        className="fill-strong-400"
      />
    </svg>
  );
};
