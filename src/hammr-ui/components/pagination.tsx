import * as React from 'react';
import { cn } from '@hammr-ui/lib/utils';
import { ButtonProps, buttonVariants } from '@hammr-ui/components/button';
import ArrowLeftSLine from '@hammr-icons/ArrowLeftSLine';
import ArrowRightSLine from '@hammr-icons/ArrowRightSLine';
import ArrowLeftDoubleLine from '@hammr-icons/ArrowLeftDoubleLine';
import ArrowRightDoubleLine from '@hammr-icons/ArrowRightDoubleLine';

export const Pagination = ({ className, ...props }: React.ComponentProps<'nav'>) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn('mx-auto flex w-full justify-center', className)}
    {...props}
  />
);
Pagination.displayName = 'Pagination';

export const PaginationContent = React.forwardRef<HTMLUListElement, React.ComponentProps<'ul'>>(
  ({ className, ...props }, ref) => (
    <ul ref={ref} className={cn('flex flex-row items-center gap-1', className)} {...props} />
  )
);
PaginationContent.displayName = 'PaginationContent';

export const PaginationItem = React.forwardRef<HTMLLIElement, React.ComponentProps<'li'>>(
  ({ className, ...props }, ref) => <li ref={ref} className={cn('', className)} {...props} />
);
PaginationItem.displayName = 'PaginationItem';

export type PaginationLinkProps = {
  isActive?: boolean;
} & Pick<ButtonProps, 'size'> &
  React.ComponentProps<'a'>;

export const PaginationLink = ({ className, isActive, size = 'default', ...props }: PaginationLinkProps) => (
  <a
    aria-current={isActive ? 'page' : undefined}
    className={cn(
      buttonVariants({
        variant: isActive ? 'lighter' : 'stroke',
        color: 'neutral',
        size,
      }),
      { 'pointer-events-none': isActive },
      'h-8 w-8 p-0 text-sub-600',
      className
    )}
    {...props}
  />
);
PaginationLink.displayName = 'PaginationLink';

export const PaginationPrevious = ({
  className,
  size = 'default',
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to previous page"
    size="default"
    className={cn(
      '',
      className,
      buttonVariants({
        variant: 'ghost',
        color: 'neutral',
        size,
      }),
      'h-8 w-8 p-0 text-sub-600'
    )}
    {...props}
  >
    <ArrowLeftSLine />
  </PaginationLink>
);
PaginationPrevious.displayName = 'PaginationPrevious';

export const PaginationNext = ({
  className,
  size = 'default',
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to next page"
    size="default"
    className={cn(
      className,
      buttonVariants({
        variant: 'ghost',
        color: 'neutral',
        size,
      }),
      'h-8 w-8 p-0 text-sub-600'
    )}
    {...props}
  >
    <ArrowRightSLine />
  </PaginationLink>
);
PaginationNext.displayName = 'PaginationNext';

export const PaginationFirst = ({
  className,
  size = 'default',
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to first page"
    size={size}
    className={cn(
      className,
      buttonVariants({
        variant: 'ghost',
        color: 'neutral',
        size,
      }),
      'h-8 w-8 p-0 text-sub-600'
    )}
    {...props}
  >
    <ArrowLeftDoubleLine />
  </PaginationLink>
);
PaginationFirst.displayName = 'PaginationFirst';

export const PaginationLast = ({ className, size, ...props }: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to last page"
    size={size}
    className={cn(
      '',
      className,
      buttonVariants({
        variant: 'ghost',
        color: 'neutral',
        size,
      }),
      'h-8 w-8 p-0 text-sub-600'
    )}
    {...props}
  >
    <ArrowRightDoubleLine />
  </PaginationLink>
);
PaginationLast.displayName = 'PaginationLast';

export const PaginationEllipsis = ({
  className,
  size = 'default',
  ...props
}: React.ComponentProps<'span'> & Pick<ButtonProps, 'size'>) => (
  <span
    aria-hidden
    className={cn(
      'flex h-9 w-9',
      className,
      buttonVariants({
        variant: 'outline',
        color: 'neutral',
        size,
      }),
      'h-8 w-8 justify-center'
    )}
    {...props}
  >
    ...
    <span className="sr-only">More pages</span>
  </span>
);
PaginationEllipsis.displayName = 'PaginationEllipsis';
