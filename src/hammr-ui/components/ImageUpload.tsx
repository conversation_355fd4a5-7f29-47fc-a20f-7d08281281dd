import { FC, useRef } from 'react';
import avatarPlaceholder from '@/assets/imgs/avatar.svg';
import Button from './button';
import { Avatar } from './Avatar';

const readFileContent = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve(content);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

interface ImageUploadProps {
  title: string;
  subTitle?: string;
  alignment?: 'vertical' | 'horizontal';
  imageUrl?: string;
  onRemove?: () => void;
  onUpload?: (file: File, content: string) => void;
  onChange?: (file: File, content: string) => void;
  isUploading?: boolean;
}

export const ImageUpload: FC<ImageUploadProps> = ({
  title,
  subTitle,
  alignment = 'vertical',
  imageUrl,
  onRemove,
  onUpload,
  onChange,
  isUploading,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const background = imageUrl ? imageUrl : avatarPlaceholder.src;

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) {
      return;
    }

    const content = await readFileContent(file);

    if (imageUrl) {
      onChange?.(file, content);
    } else {
      onUpload?.(file, content);
    }
  };

  return (
    <div className="flex gap-5">
      <Avatar imageUrl={background} size={alignment === 'vertical' ? 64 : 56} />
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-1">
          <div className="text-base text-strong-950">{title}</div>
          {!!subTitle && <div className="text-sm text-sub-600">{subTitle}</div>}
        </div>
        <div className="flex gap-3">
          {!!imageUrl ? (
            <>
              <Button size="2x-small" color="error" variant="outline" type="button" onClick={onRemove}>
                Remove
              </Button>
              <Button
                size="2x-small"
                color="neutral"
                variant="outline"
                type="button"
                onClick={() => inputRef.current?.click()}
                loading={isUploading}
                loadingText="Uploading"
              >
                Change
              </Button>
            </>
          ) : (
            <Button
              size="2x-small"
              color="neutral"
              variant="outline"
              type="button"
              onClick={() => inputRef.current?.click()}
              loading={isUploading}
              loadingText="Uploading"
            >
              Upload
            </Button>
          )}
        </div>
      </div>
      <input ref={inputRef} type="file" accept="image/*" className="hidden" onChange={handleFileSelect} />
    </div>
  );
};
