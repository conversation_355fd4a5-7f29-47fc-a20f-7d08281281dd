import * as React from 'react';
import * as TabsPrimitive from '@radix-ui/react-tabs';

import { cn } from '../lib/utils';

const Tabs = TabsPrimitive.Root;

const TabList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      'inline-flex h-9 items-center justify-center gap-1 rounded-10 p-1', // layout
      'bg-weak-50', // typography
      className
    )}
    {...props}
  />
));

TabList.displayName = TabsPrimitive.List.displayName;

const TabItem = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
    beforeContent?: React.ReactNode;
    afterContent?: React.ReactNode;
  }
>(({ className, beforeContent, afterContent, children, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      'inline-flex min-w-24 items-center justify-center gap-1.5 whitespace-nowrap rounded-6 px-3 py-1', // layout
      'text-sm font-medium text-soft-400', // typography
      'ring-offset-background transition-all',
      'focus-visible:ring-ring focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
      'disabled:pointer-events-none disabled:opacity-50',
      'data-[state=active]:bg-background data-[state=active]:text-strong-950 data-[state=active]:shadow',
      className
    )}
    {...props}
  >
    {!!beforeContent && <span className="h-5 w-5">{beforeContent}</span>}
    {!!children && <span>{children}</span>}
    {!!afterContent && <span className="h-5 w-5">{afterContent}</span>}
  </TabsPrimitive.Trigger>
));
TabItem.displayName = TabsPrimitive.Trigger.displayName;

const TabContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content ref={ref} className={cn(className)} {...props} />
));

TabContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabList, TabItem, TabContent };
