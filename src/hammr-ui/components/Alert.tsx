import InformationFill from '@/hammr-icons/InformationFill';
import { VariantProps, cva } from 'class-variance-authority';
import { cn } from '../lib/utils';
import ErrorWarningFill from '@/hammr-icons/ErrorWarningFill';
import AlertFill from '@/hammr-icons/AlertFill';
import CheckboxCircleFill from '@/hammr-icons/CheckboxCircleFill';
import MagicFill from '@/hammr-icons/MagicFill';

const alert = cva('text-strong-950 flex items-start', {
  variants: {
    status: {
      error: 'bg-error-lighter [&>svg]:text-error-base',
      warning: 'bg-warning-lighter [&>svg]:text-warning-base',
      success: 'bg-success-lighter [&>svg]:text-success-base',
      information: 'bg-information-lighter [&>svg]:text-information-base',
      feature: 'bg-faded-lighter [&>svg]:text-faded-base',
    },
    size: {
      'x-small': 'p-2 text-xs gap-2 rounded-8',
      small: 'py-2 px-2.5 text-sm gap-2 rounded-8',
      large: 'p-3.5 text-sm font-medium gap-3 rounded-xl',
    },
  },
  defaultVariants: {
    status: 'information',
    size: 'x-small',
  },
});

type AlertProps = VariantProps<typeof alert>;

interface Props extends AlertProps {
  className?: string;
  children?: React.ReactNode;
  icon?: React.ReactNode;
}

export default function Alert({ status = 'information', size, className, children, icon }: Props) {
  return (
    <div className={cn('[&>svg]:size-4 [&>svg]:shrink-0', alert({ status, size }), className)}>
      {icon ? icon : <AlertIcon status={status} />}
      <span>{children}</span>
    </div>
  );
}

interface AlertIconProps extends Pick<VariantProps<typeof alert>, 'status'> {}

function AlertIcon({ status }: AlertIconProps) {
  switch (status) {
    case 'error':
      return <ErrorWarningFill />;
    case 'warning':
      return <AlertFill />;
    case 'success':
      return <CheckboxCircleFill />;
    case 'information':
      return <InformationFill />;
    case 'feature':
      return <MagicFill />;
  }
}
