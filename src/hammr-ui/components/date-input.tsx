import { useRef, useState } from 'react';
import { cn } from '@/utils/cn';
import { Calendar } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { DayPickerProps } from 'react-day-picker';
import dayjs from 'dayjs';
import { RiCalendarLine } from '@remixicon/react';

interface Props {
  value: Date | null;
  onChange: (date: Date | null) => void;
  className?: string;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  dayPickerProps?: DayPickerProps;
  disabled?: boolean;
  'aria-invalid'?: boolean;
}

export function DateInput({
  value,
  onChange,
  className,
  inputProps,
  dayPickerProps,
  disabled,
  'aria-invalid': ariaInvalid,
}: Props) {
  const [open, setOpen] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild onClick={(e) => disabled && e.preventDefault()}>
        <div
          aria-invalid={!!ariaInvalid}
          className={cn(
            'flex cursor-pointer gap-2 rounded-10 border border-soft-200 px-3 py-[9px] text-soft-400',
            'hover:bg-weak-50 hover:text-sub-600',
            'ring-strong-400/25 ring-offset-2 ring-offset-white-0 focus-within:border-strong-950 focus-within:text-sub-600 focus-within:ring-2',
            value && 'text-strong-950 hover:text-strong-950',
            'disabled-within:cursor-not-allowed disabled-within:border-transparent disabled-within:bg-weak-50 disabled-within:text-disabled-300',
            'aria-[invalid=true]:border-error-base aria-[invalid=true]:ring-error-light',
            className
          )}
          onClick={() => inputRef.current?.focus()}
        >
          <RiCalendarLine className={cn('size-5 shrink-0', value && 'text-sub-600', disabled && 'text-disabled-300')} />
          <input
            ref={inputRef}
            disabled={disabled}
            {...inputProps}
            type="date"
            className={cn(
              'h-5 w-full cursor-[inherit] bg-inherit text-sm uppercase focus:text-strong-950 focus:outline-none',
              '[&::-webkit-calendar-picker-indicator]:hidden',
              inputProps?.className
            )}
            value={value ? dayjs(value).format('YYYY-MM-DD') : ''}
            onChange={(e) => {
              const _value = e.target.value;

              if (_value) {
                const date = new Date(_value + 'T00:00:00'); // this intermediate step because dayjs interprets year of the format '0020' as 1920
                onChange(dayjs(date).toDate());
              } else {
                onChange(null);
              }
            }}
            max="9999-12-31"
            onKeyDown={(event) => {
              if (event.code === 'Space') {
                event.preventDefault();
                event.stopPropagation();
              }
              if (event.key === 'Enter') {
                event.preventDefault();
                setOpen(false);
              }
            }}
          />
        </div>
      </PopoverTrigger>
      <PopoverContent className="mt-1 w-auto" align="start" onOpenAutoFocus={(e) => e.preventDefault()}>
        <Calendar
          mode="single"
          value={value}
          autoApply
          onApply={(date) => {
            onChange(date);
            setOpen(false);
          }}
          dayPickerProps={dayPickerProps}
        />
      </PopoverContent>
    </Popover>
  );
}
