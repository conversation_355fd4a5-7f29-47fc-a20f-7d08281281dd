import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import listPlugin from '@fullcalendar/list';
import { Badge } from '@/hammr-ui/components/badge';
import TimeLine from '@/hammr-icons/TimeLine';
import SelectBoxCircleLine from '@/hammr-icons/SelectBoxCircleLine';
import { createPortal } from 'react-dom';
import { useEffect, useRef, useState } from 'react';
import { Item, MultiSelect } from '@/hammr-ui/components/multi-select';
import { HammrUser } from '@/interfaces/user';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import dayjs from 'dayjs';
import { cn } from '@/utils/cn';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { TimeOffRequest } from '@/interfaces/timeoff';
import { PaginatedData } from '@/interfaces/pagination';

export const policyTypeColors = {
  PTO: {
    bg: 'rgb(var(--raw-information-lighter))',
    text: 'rgb(var(--raw-information-base))',
  },
  UNPAID: {
    bg: 'rgb(var(--raw-faded-lighter))',
    text: 'rgb(var(--raw-faded-base))',
  },
  SICK: {
    bg: 'rgb(var(--raw-error-lighter))',
    text: 'rgb(var(--raw-error-base))',
  },
  PERSONAL: {
    bg: 'rgb(var(--raw-away-lighter))',
    text: 'rgb(var(--raw-away-base))',
  },
  VOLUNTEER: {
    bg: 'rgb(var(--raw-success-lighter))',
    text: 'rgb(var(--raw-success-base))',
  },
  JURY_DUTY: {
    bg: 'rgb(var(--raw-feature-lighter))',
    text: 'rgb(var(--raw-feature-base))',
  },
  BEREAVEMENT: {
    bg: 'rgb(var(--raw-warning-lighter))',
    text: 'rgb(var(--raw-warning-base))',
  },
};

interface Props {
  employees: HammrUser[];
}

export default function TimeOffCalendar({ employees }: Props) {
  const [allEmployees, setAllEmployees] = useState<Item[]>([]);

  useEffect(() => {
    setAllEmployees(
      employees
        .map((employee) => ({ label: employee.fullName, value: employee.id, isSelected: false }))
        .sort((a, b) => a.label.localeCompare(b.label))
    );
  }, [employees]);

  const [allStatuses, setAllStatuses] = useState<Item[]>(() => {
    return [
      { label: 'Pending', value: 'PENDING', isSelected: false },
      { label: 'Approved', value: 'APPROVED', isSelected: false },
      { label: 'Paid', value: 'PAID', isSelected: false },
    ];
  });

  const [currentDate, setCurrentDate] = useState<Date>(null);
  const calendarRef = useRef<FullCalendar>(null);

  useEffect(() => {
    if (!calendarRef.current) return;

    const prevButton = document.querySelector('.fc-prev-button');
    const nextButton = document.querySelector('.fc-next-button');
    prevButton?.addEventListener('click', handleClick);
    nextButton?.addEventListener('click', handleClick);

    function handleClick() {
      setDate();
    }

    function setDate() {
      const api = calendarRef.current?.getApi();
      setCurrentDate(api.getDate());
    }

    setDate();

    return () => {
      prevButton?.removeEventListener('click', handleClick);
      nextButton?.removeEventListener('click', handleClick);
    };
  }, []);

  const { isPending, data } = useQuery({
    queryKey: ['time-off-requests', 'month', currentDate],
    async queryFn() {
      return await apiRequest<PaginatedData<TimeOffRequest, 'timeOffRequests'>>('time-off-requests', {
        urlParams: {
          status: ['PENDING', 'APPROVED', 'PAID'],
          startDate: dayjs(currentDate).startOf('month').valueOf(),
          endDate: dayjs(currentDate).endOf('month').valueOf(),
        },
      });
    },
    enabled: dayjs(currentDate).isValid(),
  });

  const filteredRequests = data?.timeOffRequests?.filter((request) => {
    const selectedEmployees = allEmployees.filter((employee) => employee.isSelected);
    const selectedStatuses = allStatuses.filter((status) => status.isSelected);

    if (selectedEmployees.length === 0 && selectedStatuses.length === 0) {
      return true;
    } else if (selectedEmployees.length > 0 && selectedStatuses.length === 0) {
      return selectedEmployees.some((employee) => employee.value === request.userId);
    } else if (selectedEmployees.length === 0 && selectedStatuses.length > 0) {
      return selectedStatuses.some((status) => status.value === request.status);
    } else if (selectedEmployees.length > 0 && selectedStatuses.length > 0) {
      return (
        selectedEmployees.some((employee) => employee.value === request.userId) &&
        selectedStatuses.some((status) => status.value === request.status)
      );
    }
  });

  return (
    <>
      <h2 className="font-medium text-strong-950">Time Off Calendar</h2>
      <section className="mt-6 [&_.fc-header-toolbar]:static [&_.fc-header-toolbar]:flex [&_.fc-header-toolbar]:justify-start">
        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, listPlugin]}
          headerToolbar={{
            start: 'dayGridMonth,listMonth',
            center: 'prev,title,next',
            end: '',
          }}
          events={filteredRequests?.map((request) => ({
            title: `${request.user.firstName} ${request.user.lastName}`,
            start: request.startDate,
            end: dayjs(request.endDate).add(1, 'day').format('YYYY-MM-DD'),
            allDay: true,
            extendedProps: {
              isApproved: request.status === 'APPROVED' || request.status === 'PAID',
              policyType: request.timeOffPolicy.type,
            },
            backgroundColor: 'rgba(255, 255, 255, 0)',
          }))}
          eventContent={renderEventContent}
        />
      </section>
      {createPortal(
        <MultiSelect
          buttonProps={{ className: 'w-[12.5rem]' }}
          popoverProps={{ align: 'start' }}
          label="All Employees"
          items={allEmployees}
          onChange={(newItems) => {
            setAllEmployees(newItems);
          }}
        />,
        document.querySelector('.fc-header-toolbar') ?? document.body
      )}
      {document.querySelector('.fc-header-toolbar') &&
        createPortal(
          <MultiSelect
            buttonProps={{ className: 'w-[12.5rem]' }}
            popoverProps={{ align: 'start' }}
            label="All Statuses"
            items={allStatuses}
            onChange={(newItems) => {
              setAllStatuses(newItems);
            }}
          />,
          document.querySelector('.fc-header-toolbar')
        )}
      {document.querySelector('.fc-view-harness') &&
        createPortal(
          <div
            className={cn(`absolute inset-0 z-10 flex items-center justify-center rounded-xl bg-foreground/5`, {
              hidden: !isPending,
            })}
          >
            <LoadingIndicator />
          </div>,
          document.querySelector('.fc-view-harness')
        )}
    </>
  );
}

function renderEventContent({ event, view }) {
  const backgroundColor = policyTypeColors[event.extendedProps.policyType].bg;
  const color = policyTypeColors[event.extendedProps.policyType].text;

  if (view.type === 'dayGridMonth') {
    return (
      <div className="rounded-8 border border-soft-200 bg-white-0 px-3 py-2 shadow-xs">
        <span className="font-medium text-strong-950">{event.title}</span>
        <div className="mt-1 flex items-center justify-between">
          <Badge style={{ backgroundColor, color }} size="small">
            {event.extendedProps.policyType}
          </Badge>
          {event.extendedProps.isApproved ? (
            <SelectBoxCircleLine className="size-4 text-success-base" />
          ) : (
            <TimeLine className="size-4 text-soft-400" />
          )}
        </div>
      </div>
    );
  } else if (view.type === 'listMonth') {
    return (
      <div className="flex items-center gap-3 py-2">
        {event.extendedProps.isApproved ? (
          <SelectBoxCircleLine className="size-5 text-success-base" />
        ) : (
          <TimeLine className="size-5 text-soft-400" />
        )}

        <span className="text-sm font-medium text-strong-950">{event.title}</span>

        <Badge style={{ backgroundColor, color }}>{event.extendedProps.policyType}</Badge>
      </div>
    );
  }
}
