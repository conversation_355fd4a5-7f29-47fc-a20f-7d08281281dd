import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { PaginationOptions, UpdatedTable } from '../shared/UpdatedTable';
import { TimeOffRequest } from '@/interfaces/timeoff';
import dayjs from 'dayjs';
import { Badge } from '@/hammr-ui/components/badge';
import { getPolicyTypeBadgeProps } from '@/services/time-off-policies';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import CompactButton from '@/hammr-ui/components/CompactButton';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import CheckFill from '@/hammr-icons/CheckFill';
import TimeOffRequestDetailsModal from './TimeOffRequestDetailsModal';
import TimeOffRequestApproveModal from './TimeOffRequestApproveModal';
import TimeOffRequestDeclineModal from './TimeOffRequestDeclineModal';
import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { useToast } from '@/hooks/useToast';
import { DeleteTimeOffRequestModal } from '@/components/time-off-requests/DeleteTimeOffRequestModal';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { formatHours } from '@/utils/format';

interface Props {
  rowData: TimeOffRequest[];
  requestStatus: 'pending' | 'approved' | 'declined' | 'paid';
  pagination: Partial<PaginationOptions>;
  showEmployeeName?: boolean;
}

export default function TimeOffRequestsTable({ rowData, showEmployeeName, requestStatus }: Props) {
  const [selectedTimeOffRequest, setSelectedTimeOffRequest] = useState<TimeOffRequest>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showApproveConfirmationModal, setShowApproveConfirmationModal] = useState(false);
  const [showDeclineConfirmationModal, setShowDeclineConfirmationModal] = useState(false);
  const [showTimeOffRequestDeleteModal, setShowTimeOffRequestDeleteModal] = useState(false);

  const { addToast } = useToast();
  const queryClient = useQueryClient();

  const approveRequestMutation = useMutation({
    mutationFn(requestId: number) {
      return apiRequest(`time-off-requests/${requestId}/approve`, {
        method: 'POST',
      });
    },
    onSuccess(approvedRequest) {
      queryClient.invalidateQueries({ queryKey: ['time-off-requests'] });

      addToast({
        type: 'success',
        title: 'Approved Time Off Request',
        description: (
          <>
            Successfully approved a Time Off request for{' '}
            <strong className="font-medium">
              {approvedRequest.user.firstName} {approvedRequest.user.lastName}
            </strong>
            .
          </>
        ),
      });
    },
  });

  const declineRequestMutation = useMutation({
    mutationFn({ requestId, declineNotes }: { requestId: number; declineNotes: string }) {
      return apiRequest(`time-off-requests/${requestId}/decline`, {
        method: 'POST',
        body: {
          declineNotes,
        },
      });
    },
    onSuccess(declinedRequest) {
      queryClient.invalidateQueries({ queryKey: ['time-off-requests'] });

      addToast({
        type: 'success',
        title: 'Declined Time Off Request',
        description: (
          <>
            Successfully denied a Time Off request for{' '}
            <strong className="font-medium">
              {declinedRequest.user.firstName} {declinedRequest.user.lastName}
            </strong>
            .
          </>
        ),
      });
    },
  });

  const mappedRowData = rowData.map((row) => ({
    ...row,
    endDateTimestamp: dayjs(row.endDate).valueOf(),
    startDateTimestamp: dayjs(row.startDate).valueOf(),
    policyName: row.timeOffPolicy.name,
    employeeLastName: row.user.lastName,
  }));

  const baseColumns: ColDef[] = [
    {
      headerName: 'Employee',
      field: 'employeeLastName',
      minWidth: 150,
      hide: !showEmployeeName,
      cellRenderer: (params: ICellRendererParams<TimeOffRequest>) => {
        return `${params.data.user.firstName} ${params.data.user.lastName}`;
      },
    },
    {
      headerName: 'Time-Off Dates',
      minWidth: 200,
      field: 'endDateTimestamp',
      cellRenderer: (params: ICellRendererParams<TimeOffRequest>) => {
        const startDate = dayjs(params.data.startDate);
        const endDate = dayjs(params.data.endDate);

        if (startDate.isSame(endDate, 'day')) {
          return startDate.format('MMM D, YYYY');
        }

        if (startDate.year() !== endDate.year()) {
          return `${startDate.format('MMM D, YYYY')} - ${endDate.format('MMM D, YYYY')}`;
        }

        return `${startDate.format('MMM D')} - ${endDate.format('MMM D, YYYY')}`;
      },
    },
    {
      headerName: 'Time-Off Hours',
      field: 'totalHours',
      minWidth: 160,
      valueFormatter: (params) => formatHours(params.value),
    },
    {
      headerName: 'Policy Type',
      field: 'timeOffPolicyId',
      cellRenderer: (params: ICellRendererParams<TimeOffRequest>) => {
        const { color, label } = getPolicyTypeBadgeProps(params.data.timeOffPolicy.type);
        return (
          <Badge className="w-fit" variant="lighter" color={color} size="small">
            {label}
          </Badge>
        );
      },
    },
    {
      headerName: 'Policy Name',
      field: 'policyName',
      minWidth: 160,
    },
  ];

  // Then, create the tab-specific columns
  const getTabSpecificColumns = (tab: string): ColDef[] => {
    switch (tab) {
      case 'pending':
        return [
          {
            headerName: '',
            field: 'actions',
            pinned: 'right',
            sortable: false,
            maxWidth: 100,
            cellRenderer: (params: ICellRendererParams<TimeOffRequest>) => {
              return (
                <div className="flex justify-center gap-1">
                  <Tooltip content="Decline">
                    <CompactButton
                      variant="ghost"
                      size="large"
                      onClick={() => {
                        setShowDeclineConfirmationModal(true);
                        setSelectedTimeOffRequest(params.data);
                      }}
                      disabled={declineRequestMutation.isPending}
                      className="text-error-base hover:text-error-base"
                    >
                      <CloseCircleLine />
                    </CompactButton>
                  </Tooltip>

                  <Tooltip content="Approve">
                    <CompactButton
                      variant="ghost"
                      size="large"
                      onClick={() => {
                        setShowApproveConfirmationModal(true);
                        setSelectedTimeOffRequest(params.data);
                      }}
                      disabled={approveRequestMutation.isPending}
                      className="text-success-base hover:text-success-base"
                    >
                      <CheckFill />
                    </CompactButton>
                  </Tooltip>
                </div>
              );
            },
          },
        ];
      case 'approved':
        return [
          {
            headerName: 'Approved By',
            field: 'reviewedBy',
            valueGetter(params) {
              return `${params.data.reviewer.firstName} ${params.data.reviewer.lastName}`;
            },
          },
          {
            headerName: '',
            field: 'actions',
            pinned: 'right',
            sortable: false,
            maxWidth: 100,
            cellRenderer: (params: ICellRendererParams<TimeOffRequest>) => {
              return (
                <div className="flex justify-center gap-1">
                  <Tooltip content="Delete">
                    <CompactButton
                      variant="ghost"
                      size="large"
                      onClick={() => {
                        setShowTimeOffRequestDeleteModal(true);
                        setSelectedTimeOffRequest(params.data);
                      }}
                      className="hover:text-error-base"
                    >
                      <DeleteBinLine />
                    </CompactButton>
                  </Tooltip>
                </div>
              );
            },
          },
        ];
      case 'declined':
        return [
          {
            headerName: 'Declined By',
            field: 'reviewedBy',
            valueGetter(params) {
              return `${params.data.reviewer.firstName} ${params.data.reviewer.lastName}`;
            },
          },
        ];
      case 'paid':
        return [
          {
            headerName: 'Approved By',
            field: 'reviewedBy',
            valueGetter(params) {
              return `${params.data.reviewer.firstName} ${params.data.reviewer.lastName}`;
            },
          },
          {
            headerName: 'Notes',
            field: 'requestNotes',
            cellRenderer: (params: ICellRendererParams<TimeOffRequest>) => {
              const notes = params.data.requestNotes;
              if (!notes) return null;

              return (
                <div className="max-w-[200px]">
                  <Tooltip content={notes}>
                    <div className="truncate">{notes}</div>
                  </Tooltip>
                </div>
              );
            },
          },
        ];
      default:
        return [];
    }
  };

  // Update the colDefs to use both base and tab-specific columns
  const colDefs = [...baseColumns, ...getTabSpecificColumns(requestStatus)];

  return (
    <>
      <UpdatedTable
        colDefs={colDefs}
        rowData={mappedRowData}
        onRowClicked={(event) => {
          setSelectedTimeOffRequest(event.data);
          setShowDetailsDialog(true);
        }}
      />
      <TimeOffRequestDetailsModal
        request={selectedTimeOffRequest}
        open={showDetailsDialog}
        onApprove={() => {
          setShowDetailsDialog(false);
          setShowApproveConfirmationModal(true);
        }}
        onDecline={() => {
          setShowDetailsDialog(false);
          setShowDeclineConfirmationModal(true);
        }}
        onDelete={() => {
          setShowDetailsDialog(false);
          setShowTimeOffRequestDeleteModal(true);
        }}
        onOpenChange={setShowDetailsDialog}
      />
      <TimeOffRequestApproveModal
        open={showApproveConfirmationModal}
        setOpen={setShowApproveConfirmationModal}
        selectedTimeOffRequest={selectedTimeOffRequest}
        mutation={approveRequestMutation}
      />
      <TimeOffRequestDeclineModal
        open={showDeclineConfirmationModal}
        setOpen={setShowDeclineConfirmationModal}
        selectedTimeOffRequest={selectedTimeOffRequest}
        mutation={declineRequestMutation}
      />
      {selectedTimeOffRequest ? (
        <DeleteTimeOffRequestModal
          open={showTimeOffRequestDeleteModal}
          setOpen={setShowTimeOffRequestDeleteModal}
          timeOffRequestId={selectedTimeOffRequest.id}
          onDelete={() => {
            queryClient.invalidateQueries({ queryKey: ['time-off-requests'] });
          }}
        />
      ) : undefined}
    </>
  );
}
