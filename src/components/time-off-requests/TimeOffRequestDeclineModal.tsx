import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import dayjs from 'dayjs';
import { FormV2 } from '../elements/Form';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { Controller, useForm } from 'react-hook-form';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { Dispatch, SetStateAction } from 'react';
import { TimeOffRequest } from '@/interfaces/timeoff';
import { UseMutationResult } from '@tanstack/react-query';

interface DeclineFormData {
  declineNotes: string;
}

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  selectedTimeOffRequest: TimeOffRequest;
  mutation: UseMutationResult;
}

export default function TimeOffRequestDeclineModal({ open, setOpen, selectedTimeOffRequest, mutation }: Props) {
  const {
    handleSubmit,
    reset,
    formState: { errors },
    control,
  } = useForm<DeclineFormData>({
    defaultValues: {
      declineNotes: '',
    },
  });

  function onDeclineSubmit(formData: DeclineFormData) {
    if (!selectedTimeOffRequest) return;

    mutation.mutate(
      {
        requestId: selectedTimeOffRequest.id,
        declineNotes: formData.declineNotes,
      },
      {
        onSuccess() {
          setOpen(false);
          reset();
        },
      }
    );
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          setOpen(false);
          reset();
        }
      }}
    >
      <DialogSurface>
        <DialogHeader
          icon={<KeyIcon icon={<CloseCircleLine className="text-error-base" />} />}
          title="Decline Time Off Request"
          subtitle={
            <>
              You{"'"}re about to decline the Time Off request for{' '}
              <span className="font-medium">
                {selectedTimeOffRequest?.user.firstName} {selectedTimeOffRequest?.user.lastName}
              </span>{' '}
              on{' '}
              <span className="font-medium">
                {dayjs(selectedTimeOffRequest?.startDate).format('MMM D')} -{' '}
                {dayjs(selectedTimeOffRequest?.endDate).format('MMM D, YYYY')}
              </span>
              .
            </>
          }
        />
        <FormV2
          onSubmit={handleSubmit(onDeclineSubmit)}
          onCancel={() => {
            setOpen(false);
            reset();
          }}
          isLoading={mutation.isPending}
          submitText="Decline"
          submitProps={{
            color: 'error',
          }}
        >
          <FormItem required>
            <FormLabel>Decline Notes</FormLabel>
            <FormControl>
              <Controller
                name="declineNotes"
                control={control}
                rules={{
                  required: 'Please provide a reason for declining',
                }}
                render={({ field: { onChange, value, ...field } }) => (
                  <Textarea
                    {...field}
                    value={value || ''}
                    onChange={onChange}
                    placeholder="Enter notes"
                    hideCounter
                    aria-invalid={!!errors.declineNotes}
                    aria-errormessage={errors.declineNotes?.message}
                  />
                )}
              />
            </FormControl>
            {errors.declineNotes && <p className="mt-1.5 text-sm text-error-base">{errors.declineNotes.message}</p>}
          </FormItem>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
