import Layout from '@/components/dashboard/Layout';
import CreateTimeOffRequestsModal from '@/components/time-off-requests/CreateTimeOffRequestsModal';
import TimeOffCalendar from '@/components/time-off-requests/TimeOffCalendar';
import AddLine from '@/hammr-icons/AddLine';
import Calendar2Line from '@/hammr-icons/Calendar2Line';
import EmptyStateScheduleHoliday from '@/hammr-icons/EmptyStateScheduleHoliday';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import Button from '@/hammr-ui/components/button';
import { TabContent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { useEmployeesQuery } from '@/hooks/data-fetching/useEmployees';
import { apiRequest } from '@/utils/requestHelpers';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { TimeOffRequest } from '@/interfaces/timeoff';
import { PaginatedData } from '@/interfaces/pagination';
import TimeOffRequestsTable from '@/components/time-off-requests/RequestsTable';
import { ButtonGroup, ButtonGroupItem } from '@/hammr-ui/components/ButtonGroup';
import { RiCalendar2Line, RiListSettingsLine } from '@remixicon/react';

interface Props {
  timeOffTab: 'requests' | 'policies';
  setTimeOffTab: (tab: 'requests' | 'policies') => void;
}

export default function TimeOffRequests({ timeOffTab, setTimeOffTab }: Props) {
  const employeesQuery = useEmployeesQuery({
    simple: true,
    workerClassification: 'EMPLOYEE',
  });

  const queryClient = useQueryClient();

  const [showCreateTimeOffRequestModal, setShowCreateTimeOffRequestModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'pending' | 'approved' | 'declined' | 'paid'>('pending');

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const timeOffRequestsQuery = useQuery({
    queryKey: ['time-off-requests', selectedTab],
    async queryFn() {
      return await apiRequest<PaginatedData<TimeOffRequest, 'timeOffRequests'>>('time-off-requests', {
        urlParams: {
          status: [selectedTab.toUpperCase()],
        },
      });
    },
  });

  const pendingRequestsCount = queryClient.getQueryData<PaginatedData<TimeOffRequest, 'timeOffRequests'>>([
    'time-off-requests',
    'pending',
  ]);

  return (
    <Layout noPadding>
      <PageHeader
        title="Requests"
        icon={<Calendar2Line />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateTimeOffRequestModal(true)}>
            Create Time Off Request
          </Button>
        }
      />

      <Tabs
        defaultValue="pending"
        className="px-8 py-6"
        value={selectedTab}
        onValueChange={(tab) => setSelectedTab(tab as typeof selectedTab)}
      >
        <menu className="flex items-center justify-between">
          <TabList className="[&>*]:w-[130px]">
            <TabItem value="pending">Pending {pendingRequestsCount ? `(${pendingRequestsCount.total})` : ''}</TabItem>
            <TabItem value="approved">Approved</TabItem>
            <TabItem value="declined">Declined</TabItem>
            <TabItem value="paid">Paid</TabItem>
          </TabList>
          <ButtonGroup>
            <ButtonGroupItem
              active={timeOffTab === 'requests'}
              beforeContent={<RiCalendar2Line />}
              onClick={() => setTimeOffTab('requests')}
            >
              Requests
            </ButtonGroupItem>
            <ButtonGroupItem
              active={timeOffTab === 'policies'}
              beforeContent={<RiListSettingsLine />}
              onClick={() => setTimeOffTab('policies')}
            >
              Policies
            </ButtonGroupItem>
          </ButtonGroup>
        </menu>
        {['pending', 'approved', 'declined', 'paid'].map((value) => (
          <TabContent value={value} key={value} className="mt-6">
            {timeOffRequestsQuery.isPending ? (
              <div className="flex h-full min-h-96 items-center justify-center">
                <LoadingIndicator text="Loading Requests..." />
              </div>
            ) : timeOffRequestsQuery.isError ? (
              <div className="flex h-full items-center justify-center text-sm text-error-base">
                An error occured when fetching the time off requests.
              </div>
            ) : timeOffRequestsQuery.data.timeOffRequests.length === 0 ? (
              <section className="flex flex-col items-center py-[92px]">
                <EmptyStateScheduleHoliday />

                {selectedTab === 'pending' ? (
                  <>
                    <p className="my-3 h-fit text-center text-sm text-soft-400">
                      There is no Time Off requested or scheduled yet.
                      <br />
                      Click the button below to add one.
                    </p>
                    <Button
                      beforeContent={<AddLine />}
                      onClick={() => setShowCreateTimeOffRequestModal(true)}
                      className="mt-5"
                    >
                      Create Time Off Request
                    </Button>
                  </>
                ) : selectedTab === 'approved' ? (
                  <p className="mt-5 h-fit text-center text-sm text-soft-400">
                    There are no approved Time Off requests.
                  </p>
                ) : selectedTab === 'declined' ? (
                  <p className="mt-5 h-fit text-center text-sm text-soft-400">
                    There are no declined Time Off requests.
                  </p>
                ) : selectedTab === 'paid' ? (
                  <p className="mt-5 h-fit text-center text-sm text-soft-400">There are no paid Time Off requests.</p>
                ) : undefined}
              </section>
            ) : (
              <TimeOffRequestsTable
                rowData={timeOffRequestsQuery.data.timeOffRequests}
                requestStatus={selectedTab}
                showEmployeeName={true}
                pagination={{
                  total: timeOffRequestsQuery.data.total,
                  page,
                  pageSize,
                  onPageChanged: setPage,
                  onPageSizeChanged: setPageSize,
                }}
              />
            )}
          </TabContent>
        ))}
      </Tabs>

      <section className="px-8 py-6">
        <TimeOffCalendar employees={employeesQuery.data ?? []} />
      </section>

      <CreateTimeOffRequestsModal open={showCreateTimeOffRequestModal} setOpen={setShowCreateTimeOffRequestModal} />
    </Layout>
  );
}
