import Calendar2Line from '@/hammr-icons/Calendar2Line';
import CheckFill from '@/hammr-icons/CheckFill';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { Badge } from '@/hammr-ui/components/badge';
import Button from '@/hammr-ui/components/button';
import { Dialog, DialogFooter, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { TimeOffPolicy, TimeOffRequest } from '@/interfaces/timeoff';
import { getPolicyTypeBadgeProps } from '@/services/time-off-policies';
import { apiRequest } from '@/utils/requestHelpers';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { formatHours } from '@/utils/format';

interface Props {
  request: TimeOffRequest | undefined;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApprove: () => void;
  onDecline: () => void;
  onDelete: () => void;
}

export default function TimeOffRequestDetailsModal({
  request,
  open,
  onOpenChange,
  onApprove,
  onDecline,
  onDelete,
}: Props) {
  const userId = request?.user.id;
  const userPoliciesQuery = useQuery({
    queryKey: ['user', userId, 'time-off-policies', 'all'],
    queryFn: () =>
      apiRequest<{
        timeOffPolicies: TimeOffPolicy[];
      }>(`users/${userId}/time-off-policies?includeArchived=true`).then((response) => response.timeOffPolicies),
    enabled: !!userId,
  });

  if (!request) return null;

  const { color, label } = getPolicyTypeBadgeProps(request.timeOffPolicy.type);

  const policy = userPoliciesQuery.data?.find((policy) => policy.id === request.timeOffPolicyId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogSurface>
        <DialogHeader title="Time Off Request Details" icon={<Calendar2Line />} />
        <div className="m-5 grid grid-cols-2 gap-5">
          <div className="col-span-2">
            <div className="text-xs text-sub-600">Employee</div>
            <div className="mt-2 text-sm">
              {request.user.firstName} {request.user.lastName}
            </div>
          </div>
          <div>
            <div className="text-xs text-sub-600">Policy Type</div>
            <Badge className="mt-2" variant="lighter" color={color} size="small">
              {label}
            </Badge>
          </div>
          <div>
            <div className="text-xs text-sub-600">Policy name</div>
            <div className="mt-2 text-sm">{request.timeOffPolicy.name}</div>
          </div>
          <div>
            <div className="text-xs text-sub-600">Start Date</div>
            <div className="mt-2 text-sm">{dayjs(request.startDate).format('ddd, MMM D, YYYY')}</div>
          </div>
          <div>
            <div className="text-xs text-sub-600">End Date</div>
            <div className="mt-2 text-sm">{dayjs(request.endDate).format('ddd, MMM D, YYYY')}</div>
          </div>

          <div>
            <div className="text-xs text-sub-600">Total Hours</div>
            <div className="mt-2 text-sm">{formatHours(request.totalHours)}</div>
          </div>
          <div>
            <div className="text-xs text-sub-600">Balance In Policy</div>
            {request.timeOffPolicy.isLimited ? (
              userPoliciesQuery.isLoading ? (
                <div className="mt-2 h-5 w-24 animate-pulse rounded-full bg-weak-100"></div>
              ) : userPoliciesQuery.isError ? (
                <div className="mt-2 text-sm text-error-base">Error loading available hours</div>
              ) : (
                <div className="mt-2 text-sm">
                  {policy.availableHours ? (Math.floor(policy.availableHours * 100) / 100).toFixed(2) : '0.00'} hours{' '}
                  {policy.endDate ? '(Archived)' : ''}
                </div>
              )
            ) : (
              <div className="mt-2 text-sm">Unlimited</div>
            )}
          </div>
          <div className="col-span-2">
            <div className="text-xs text-sub-600">Created On</div>
            <div className="mt-2 text-sm">{dayjs(request.createdAt).format('ddd, MMM D, YYYY [at] h:mm A')}</div>
          </div>
          <div className="col-span-2">
            <div className="text-xs text-sub-600">Notes</div>
            <div className="mt-2 text-sm">{request.requestNotes || '-'}</div>
          </div>
          {request.status === 'APPROVED' && (
            <>
              <hr className="col-span-2 border-soft-200" />

              <div className="col-span-1">
                <div className="text-xs text-sub-600">Status</div>
                <div className="mt-2 flex items-center text-sm capitalize">
                  {request.status.toLowerCase()} <CheckFill className="ml-1 size-4 text-success-base" />
                </div>
              </div>
              <div className="col-span-1">
                <div className="text-xs text-sub-600">Approved By</div>
                <div className="mt-2 text-sm capitalize">
                  {request.reviewer.firstName} {request.reviewer.lastName}
                </div>
              </div>
            </>
          )}
          {request.status === 'DECLINED' && (
            <>
              <hr className="col-span-2 border-soft-200" />

              <div className="col-span-1">
                <div className="text-xs text-sub-600">Status</div>
                <div className="mt-2 flex items-center text-sm capitalize">
                  {request.status.toLowerCase()}
                  <CloseCircleLine className="ml-1 size-4 text-error-base" />
                </div>
              </div>
              <div className="col-span-1">
                <div className="text-xs text-sub-600">Declined By</div>
                <div className="mt-2 text-sm capitalize">
                  {request.reviewer.firstName} {request.reviewer.lastName}
                </div>
              </div>
              <div className="col-span-2">
                <div className="text-xs text-sub-600">Decline Notes</div>
                <div className="mt-2 text-sm capitalize">{request.declineNotes || '-'}</div>
              </div>
            </>
          )}
        </div>
        {request.status === 'PENDING' && (
          <DialogFooter>
            <Button type="button" fullWidth color="error" onClick={onDecline}>
              Decline
            </Button>
            <Button type="submit" fullWidth color="success" onClick={onApprove}>
              Approve
            </Button>
          </DialogFooter>
        )}

        {request.status === 'APPROVED' && (
          <DialogFooter>
            <Button
              type="button"
              fullWidth
              variant="outline"
              color="neutral"
              onClick={onDelete}
              beforeContent={<DeleteBinLine />}
            >
              Delete Time-Off Request
            </Button>
          </DialogFooter>
        )}
      </DialogSurface>
    </Dialog>
  );
}
