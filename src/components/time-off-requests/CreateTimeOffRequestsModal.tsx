import { <PERSON><PERSON>, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { FormV2 } from '../elements/Form';
import Calendar2Line from '@/hammr-icons/Calendar2Line';
import ControlledSelect from '../elements/form/ControlledSelect';
import { Controller, useForm } from 'react-hook-form';
import { SelectItem } from '@/hammr-ui/components/select';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import ControlledDateInput from '../elements/form/ControlledDateInput';
import { TextField } from '../elements/form/TextField';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { useToast } from '@/hooks/useToast';
import Alert from '@hammr-ui/components/Alert';
import { TimeOffPolicy, TimeOffRequest } from '@/interfaces/timeoff';
import { useEmployeesQuery } from '@/hooks/data-fetching/useEmployees';
import dayjs from 'dayjs';
import ControlledCombobox from '@/components/elements/form/ControlledCombobox';

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  selectedEmployee?: number;
  afterSubmit?: (data: TimeOffRequest) => void;
}

const defaultValues = {
  userId: '',
  timeOffPolicyId: '',
  startDate: '',
  endDate: '',
  totalHours: '',
  requestNotes: '',
};

export default function CreateTimeOffRequestsModal({ open, setOpen, selectedEmployee, afterSubmit }: Props) {
  const { addToast } = useToast();
  const queryClient = useQueryClient();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      ...defaultValues,
      userId: selectedEmployee?.toString() ?? '',
    },
  });

  const userId = watch('userId');
  const startDate = watch('startDate');

  const requestsMutation = useMutation({
    mutationFn(formData: typeof defaultValues) {
      return apiRequest<TimeOffRequest>(`/time-off-requests`, {
        method: 'POST',
        body: {
          ...formData,
          startDate: dayjs(formData.startDate).startOf('day').valueOf(),
          endDate: dayjs(formData.endDate).endOf('day').valueOf(),
        },
      });
    },
    onSuccess(timeOffRequest) {
      queryClient.invalidateQueries({ queryKey: ['time-off-requests'] });
      // to refetch the total hours available for the user
      queryClient.invalidateQueries({ queryKey: ['user', timeOffRequest.userId, 'time-off-policies'] });

      setOpen(false);
      afterSubmit?.(timeOffRequest);
      reset();

      addToast({
        type: 'success',
        title: 'Created Time Off Request',
        description: (
          <>
            Successfully created and approved a Time Off request for{' '}
            <strong className="font-medium">
              {timeOffRequest.user.firstName} {timeOffRequest.user.lastName}
            </strong>
            .
          </>
        ),
      });
    },
  });

  const employeesQuery = useEmployeesQuery({
    simple: true,
    workerClassification: 'EMPLOYEE',
  });

  const policies = useQuery<TimeOffPolicy[]>({
    queryKey: ['user', userId, 'time-off-policies'],
    queryFn: () =>
      apiRequest<{
        timeOffPolicies: TimeOffPolicy[];
      }>(`users/${userId}/time-off-policies`).then((response) => response.timeOffPolicies),
    enabled: Boolean(userId) && open,
  });

  useEffect(() => {
    if (policies.data?.length === 1) {
      setValue('timeOffPolicyId', policies.data[0].id.toString());
    } else {
      setValue('timeOffPolicyId', '');
    }
  }, [policies.data, setValue]);

  async function onSubmit(formData: typeof defaultValues) {
    requestsMutation.mutate(formData);
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        reset();
        setOpen(isOpen);
      }}
    >
      <DialogSurface>
        <DialogHeader icon={<Calendar2Line className="text-sub-600" />} title="Create Time Off Request" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={requestsMutation.isPending}
          submitText="Create & Approve"
        >
          <Alert status="information" size="x-small" className="mb-5">
            PTO requests created by admins on behalf of employees are automatically approved.
          </Alert>

          <ControlledCombobox
            name="userId"
            label="Employee"
            control={control}
            rules={{ required: 'Please select an employee' }}
            error={errors['userId']?.message as string}
            disabled={!!selectedEmployee}
            required
            items={employeesQuery.data?.map((employee) => ({
              label: <div key={employee.id}>{employee.fullName}</div>,
              value: employee.id.toString(),
            }))}
          />

          <ControlledSelect
            name="timeOffPolicyId"
            label="Policy"
            control={control}
            rules={{ required: 'Please select a policy' }}
            error={errors['timeOffPolicyId']?.message as string}
            required
            disabled={!userId || policies.isFetching}
            placeholder={policies.isFetching ? 'Loading policies...' : !userId ? 'Select an employee first' : undefined}
            className="mt-5"
          >
            {policies.data?.map((policy) => (
              <SelectItem key={policy.id} value={String(policy.id)}>
                {policy.name}
              </SelectItem>
            ))}
          </ControlledSelect>
          {policies.data?.length === 0 && (
            <FormMessage className="mt-1" messageType="error">
              This employee is not enrolled in any active Time Off policy.
            </FormMessage>
          )}

          <ControlledDateInput
            control={control}
            name="startDate"
            label="Start Date"
            rules={{ required: 'Start date is required' }}
            required
            className="mt-5"
          />

          <ControlledDateInput
            control={control}
            name="endDate"
            label="End Date"
            rules={{ required: 'End date is required' }}
            required
            className="mt-5"
            dayPickerProps={{
              disabled: startDate ? { before: new Date(startDate) } : undefined,
            }}
          />

          <TextField
            name="totalHours"
            label="Total Hours"
            control={control}
            rules={{ required: 'Total hours is required' }}
            error={errors['totalHours']?.message as string}
            required
            className="mt-5"
            afterContent="hours"
            placeholder="Enter number"
          />

          <FormItem className="mt-5">
            <FormLabel>Notes</FormLabel>
            <FormControl>
              <Controller
                name="requestNotes"
                control={control}
                render={({ field }) => <Textarea hideCounter {...field} />}
              />
            </FormControl>
          </FormItem>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
