import { useForm } from 'react-hook-form';
import { Dispatch, SetStateAction, useState } from 'react';
import CloseCircleLine from '@hammr-icons/CloseCircleLine';
import ConfirmDialog from '@hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@hammr-ui/components/KeyIcon';
import { useToast } from '@/hooks/useToast';
import { apiRequest } from '@/utils/requestHelpers';
import { logError, showErrorToast } from '@/utils/errorHandling';

export function DeleteTimeOffRequestModal({
  timeOffRequestId,
  open,
  setOpen,
  onDelete,
}: {
  timeOffRequestId: number;
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  onDelete?: () => void;
}) {
  const { handleSubmit } = useForm();

  const { addToast } = useToast();
  const [isDeleting, setIsDeleting] = useState(false);

  const onSubmit = async () => {
    setIsDeleting(true);

    try {
      setIsDeleting(true);
      await apiRequest(`time-off-requests/${timeOffRequestId}`, {
        method: 'DELETE',
      }).finally(() => setIsDeleting(false));

      addToast({
        title: 'Time-Off Request deleted',
        type: 'success',
      });
      setOpen(false);

      onDelete?.();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to delete the Time-Off Request');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <ConfirmDialog
        open={open}
        setOpen={setOpen}
        onConfirm={handleSubmit(onSubmit)}
        icon={<KeyIcon icon={<CloseCircleLine />} color="red" />}
        title="Delete Time-Off Request"
        subtitle={<>Are you sure you want to delete this Time-Off Request?</>}
        confirmButton={{
          color: 'error',
          loading: isDeleting,
        }}
        confirmButtonText="Delete"
      ></ConfirmDialog>
    </>
  );
}
