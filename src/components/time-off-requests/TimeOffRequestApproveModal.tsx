import CheckFill from '@/hammr-icons/CheckFill';
import { ConfirmDialog } from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { TimeOffRequest } from '@/interfaces/timeoff';
import { UseMutationResult } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction } from 'react';

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  selectedTimeOffRequest: TimeOffRequest;
  mutation: UseMutationResult;
}

export default function TimeOffRequestApproveModal({ open, setOpen, selectedTimeOffRequest, mutation }: Props) {
  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      title="Approve Time Off Request"
      icon={<KeyIcon icon={<CheckFill className="text-success-base" />} />}
      subtitle={
        selectedTimeOffRequest ? (
          <>
            You{"'"}re about to approve the{' '}
            <span className="font-medium">{selectedTimeOffRequest.timeOffPolicy.type}</span> request for{' '}
            <span className="font-medium">
              {selectedTimeOffRequest.user.firstName} {selectedTimeOffRequest.user.lastName}
            </span>{' '}
            on{' '}
            <span className="font-medium">
              {dayjs(selectedTimeOffRequest.startDate).format('MMM D')} -{' '}
              {dayjs(selectedTimeOffRequest.endDate).format('MMM D, YYYY')}
            </span>
            .
          </>
        ) : undefined
      }
      confirmButtonText="Approve"
      confirmButton={{
        color: 'success',
        loading: mutation.isPending,
      }}
      onConfirm={() => mutation.mutate(selectedTimeOffRequest.id)}
    />
  );
}
