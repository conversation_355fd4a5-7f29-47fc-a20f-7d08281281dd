import { FC } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';

import { Dialog, DialogSurface } from '@/hammr-ui/components/dialog';
import { MediaViewerBodyUI, MediaViewerItem } from '../MediaViewer';
import { useToast } from '@/hammr-ui/hooks/use-toast';

interface MediaViewerProps {
  isOpen: boolean;
  onClose: () => void;
  onDeleted: (id: number | string) => void;
  items: MediaViewerItem[];
  selectedItem: MediaViewerItem | null;
  onSelectedItemChange: (item: MediaViewerItem) => void;
}

export const MediaViewer: FC<MediaViewerProps> = ({
  isOpen,
  onClose,
  items,
  onSelectedItemChange,
  selectedItem,
  onDeleted,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const deletePhotoMutation = useMutation({
    mutationFn: (id: number | string) => apiRequest(`project-photos/${id}`, { method: 'DELETE' }),
    onSuccess: () => {
      toast({
        title: 'Project Photo deleted',
        description: 'The Project Photo has been successfully deleted.',
        toastVariant: 'success',
      });

      const updatedItems = items.filter((item) => item.id !== selectedItem?.id);

      if (updatedItems.length === 0) {
        onClose();
        queryClient.invalidateQueries({ queryKey: ['projectPhotosCollection'] });
      } else {
        onSelectedItemChange(updatedItems[0]);
      }
      onDeleted(selectedItem.id);
    },
  });

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(value) => {
        if (!value) {
          onClose?.();
        }
      }}
    >
      <DialogSurface size="4xl" className="h-[700px]" onOpenAutoFocus={(e) => e.preventDefault()}>
        <MediaViewerBodyUI
          items={items}
          currentItem={selectedItem}
          onChange={(item) => {
            onSelectedItemChange?.(item);
          }}
          onDelete={() => {
            if (selectedItem) {
              deletePhotoMutation.mutate(selectedItem.id);
            }
          }}
          isDeleting={deletePhotoMutation.isPending}
          deleteConfirmationDialog={{
            title: 'Delete Project Photo',
            description: 'Are you sure you want to delete this Project Photo?',
          }}
          onClose={onClose}
        />
      </DialogSurface>
    </Dialog>
  );
};
