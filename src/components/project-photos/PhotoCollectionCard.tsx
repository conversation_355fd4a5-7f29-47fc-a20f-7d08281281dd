import Image from 'next/image';
import moment from 'moment';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { useMemo, useState } from 'react';
import { saveAs } from 'file-saver';

import { ProjectPhoto, ProjectPhotosCollection } from 'interfaces/project-photo';

import { rgbDataURL } from 'utils/utils';
import { validateNetworkResponse } from 'utils/errorHandling';
import FoldersLine from '@/hammr-icons/FoldersLine';
import Button from '@/hammr-ui/components/button';
import { MediaViewerItem } from '../MediaViewer';
import { HammrUser } from '@/interfaces/user';
import { MediaViewer } from './MediaViewer';

function transformToMediaViewerItem(photo: ProjectPhoto, user: Partial<HammrUser>) {
  const url = photo.imageUrl;
  return {
    id: photo.id,
    type: 'image' as const,
    url,
    name: photo.objectId,
    deletable: true,
    createdAt: photo.createdAt,
    createdBy: user.fullName || `${user.firstName} ${user.lastName}`,
  } as MediaViewerItem;
}

interface PhotoCollectionCardProps {
  collectionData: ProjectPhotosCollection;
  onDeleted: (id: number | string) => void;
}

const PhotoCollectionCard = ({ collectionData, onDeleted }: PhotoCollectionCardProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const zip = new JSZip();
  const { project, note, projectPhotos, user, createdAt } = collectionData;

  const sortedPhotos = useMemo(() => {
    // Sort photos by createdAt in descending order
    return projectPhotos.sort((a, b) => moment(b.createdAt).diff(moment(a.createdAt)));
  }, [projectPhotos]);

  const transformedItems = useMemo(() => {
    return sortedPhotos.map((photo) => transformToMediaViewerItem(photo, user));
  }, [sortedPhotos, user]);

  const [selectedItem, setSelectedItem] = useState<MediaViewerItem | null>(null);
  const [isMediaViewerOpen, setIsMediaViewerOpen] = useState(false);

  const fetchImageAsBlob = async (url) => {
    const response = await fetch(url, {
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
    await validateNetworkResponse(response);
    const blob = await response.blob();
    return blob;
  };

  const addImagesToZip = async (imageUrls) => {
    const imagePromises = imageUrls.map(async (url, index) => {
      const blob = await fetchImageAsBlob(url);
      zip.file(`image-${index}.jpg`, blob);
    });

    await Promise.all(imagePromises);
  };

  const generateZip = async () => {
    setIsLoading(true);
    const imageUrls = sortedPhotos.map((photo) => photo.imageUrl);
    await addImagesToZip(imageUrls);
    const content = await zip.generateAsync({ type: 'blob' });
    saveAs(content, 'images.zip');
    setIsLoading(false);
  };

  return (
    <div className="flex flex-row gap-6 rounded-16 border border-soft-200 p-4">
      {/* Header row */}
      <div className="flex w-60 shrink-0 flex-col">
        <div className="flex flex-col items-start gap-3">
          <div className="flex flex-col gap-1">
            <div className="flex flex-wrap items-center gap-3">
              <div className="text-sm font-medium text-strong-950">{`${user.firstName} ${user.lastName}`}</div>
              <div className="text-xs font-normal text-sub-600">{moment(createdAt).format('ddd, MMM D')}</div>
            </div>
            <div className="flex items-center gap-1 text-sm font-normal text-sub-600">
              <FoldersLine className="h-5 w-5" />
              <span>{project.name}</span>
            </div>
          </div>
          <Button
            variant="link"
            color="primary"
            className="px-0"
            onClick={generateZip}
            disabled={isLoading}
            loading={isLoading}
          >
            Download All
          </Button>
        </div>
      </div>
      <div className="flex flex-col gap-3">
        {/* Note */}
        {note && <div className="text-sm font-normal text-strong-950">{note}</div>}
        {/* Photos row(s) */}
        <div className="flex flex-wrap justify-start gap-3">
          {sortedPhotos.map((photo) => {
            return (
              <div key={photo.id} className="h-72 w-72 sm:h-56 sm:w-56 md:h-44 md:w-44 lg:h-36 lg:w-36">
                <a
                  href={photo.imageUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={(e) => {
                    e.preventDefault();
                    setSelectedItem(transformToMediaViewerItem(photo, user));
                    setIsMediaViewerOpen(true);
                  }}
                >
                  <Image
                    className="h-full w-full rounded-md object-cover"
                    height={288}
                    width={288}
                    placeholder="blur"
                    blurDataURL={rgbDataURL(141, 141, 141)}
                    src={photo.imageUrl}
                    alt={`${photo.objectId} image`}
                  />
                </a>
              </div>
            );
          })}
        </div>
      </div>
      <MediaViewer
        isOpen={isMediaViewerOpen}
        onClose={() => setIsMediaViewerOpen(false)}
        onDeleted={onDeleted}
        items={transformedItems}
        selectedItem={selectedItem}
        onSelectedItemChange={setSelectedItem}
      />
    </div>
  );
};

export default PhotoCollectionCard;
