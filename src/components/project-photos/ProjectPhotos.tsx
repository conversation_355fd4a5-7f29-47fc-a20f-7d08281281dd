import { useCallback, useMemo, useState } from 'react';

import { useAuth } from 'hooks/useAuth';
import { useAwsS3 } from 'hooks/useAwsS3';
import { useAwsSts } from 'hooks/useAwsSts';

import { getProjectPhotoCollections } from 'services/project-photos';

import { GetProjectPhotosCollection } from 'interfaces/project-photo';

import PhotoCollectionCard from 'components/project-photos/PhotoCollectionCard';
import { useProjects } from 'hooks/data-fetching/useProjects';
import Button from '@/hammr-ui/components/button';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { MultiSelect } from '@/hammr-ui/components/multi-select';
import { userService } from '@/services/user';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { EmptytStateChatPhoto } from '@/hammr-ui/components/empty-states/ChatPhoto';
import { useInfiniteQuery, useQuery, useQueryClient } from '@tanstack/react-query';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

const RESULTS_PER_PAGE = 10; // AKA LIMIT

export default function ProjectPhotos({ projectId }: { projectId?: number }) {
  const { user } = useAuth();
  const { getStsCredentials, stsCredentials } = useAwsSts();
  const { s3 } = useAwsS3();
  const queryClient = useQueryClient();

  const [selectedTab, setSelectedTab] = useState('active');

  const usersQuery = useQuery({
    queryKey: ['users', user?.companyId],
    queryFn: async () => {
      const users = await userService.list({
        organizationId: user.companyId,
        simple: true,
      });
      return users;
    },
  });

  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>(projectId ? [projectId] : []);
  const allProjects = useProjects(user?.companyId, {
    includeIsArchived: true,
  });

  const projects = useMemo(() => {
    return allProjects.filter((x) => (selectedTab === 'archived' ? x.isArchived : !x.isArchived));
  }, [allProjects, selectedTab]);

  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);

  const generateSignedUrl = useCallback(
    async (key = '1.png') => {
      if (!s3) return;

      return new Promise((resolve, reject) => {
        const params = {
          Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/project-photos`,
          Key: key,
          Expires: 21600, // 6 hours - input in seconds
        };

        s3.getSignedUrl('getObject', params, (err, url) => {
          if (err) {
            reject(err);
          }
          resolve(url);
        });
      });
    },
    [s3, user?.companyId]
  );

  const photosKey = [
    'projectPhotosCollection',
    user?.companyId,
    selectedTab,
    projectId,
    selectedProjectIds,
    selectedUserIds,
  ];

  const {
    data,
    hasNextPage,
    fetchNextPage,
    isPending: isLoading,
  } = useInfiniteQuery({
    queryKey: photosKey,
    queryFn: async ({ pageParam }) => {
      if (stsCredentials?.Expiration) {
        const expiration = new Date(stsCredentials.Expiration);
        const currentTime = new Date();
        if (currentTime >= expiration) {
          // if expired, get new credentials
          await getStsCredentials(user?.companyId);
        }
      }
      const queryParams: Partial<GetProjectPhotosCollection> = {
        organizationId: user?.companyId,
        limit: RESULTS_PER_PAGE,
      };

      if (!projectId) {
        queryParams.projectArchived = selectedTab === 'archived';
        if (selectedProjectIds.length) {
          queryParams.projectId = selectedProjectIds.join(',');
        }
      } else {
        queryParams.projectId = projectId.toString();
      }

      if (selectedUserIds.length) {
        queryParams.userId = selectedUserIds.join(',');
      }

      // build offset
      queryParams.offset = RESULTS_PER_PAGE * pageParam.offsetCounter;

      const photoCollectionData = await getProjectPhotoCollections(queryParams as Record<string, string>);

      // Step 1: Loop through each projectPhotosCollection
      const collectionPromises = photoCollectionData.projectPhotosCollections.map(async (collection) => {
        // Step 2: Loop through photos in each collection and generate signed URLs
        const photoPromises = collection.projectPhotos.map(async (photo) => {
          const signedUrl = await generateSignedUrl(photo.objectId);
          return { ...photo, imageUrl: signedUrl };
        });

        // Step 3: Await for all photo promises to resolve
        const updatedPhotos = await Promise.all(photoPromises);

        // Update the current collection's photos
        return { ...collection, projectPhotos: updatedPhotos };
      });

      // Step 4: Await for all collection promises to resolve
      const updatedCollections = await Promise.all(collectionPromises);

      // Update the original projectPhotosCollections array
      photoCollectionData.projectPhotosCollections = updatedCollections;

      return photoCollectionData;
    },
    getNextPageParam: (lastPage, allPages, lastPageParam) => {
      const totalCount = lastPage.totalCount;
      const loadedCount = allPages.reduce((acc, page) => acc + page.projectPhotosCollections.length, 0);

      if (loadedCount >= totalCount) {
        return undefined;
      }

      return {
        offsetCounter: lastPageParam.offsetCounter + 1,
      };
    },
    initialPageParam: {
      offsetCounter: 0,
    },
    enabled: !!s3,
  });

  const totalCount = data?.pages?.[0]?.totalCount || 0;

  const mergedItems = useMemo(() => {
    return data?.pages?.map((page) => page.projectPhotosCollections).flat() || [];
  }, [data]);

  if (usersQuery.isFetching) {
    return (
      <div className="flex items-center justify-center py-20">
        <LoadingIndicator text="Loading users..." />
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="flex gap-3">
          {!projectId && (
            <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value)}>
              <TabList>
                <TabItem value="active" className="w-full">
                  Active Projects
                </TabItem>
                <TabItem value="archived" className="w-full">
                  Archived Projects
                </TabItem>
              </TabList>
            </Tabs>
          )}
          {!projectId && (
            <MultiSelect
              items={projects.map((x) => ({
                label: x.name + (x.projectNumber ? ` (${x.projectNumber})` : ''),
                value: x.id,
                isSelected: selectedProjectIds.includes(x.id),
              }))}
              label="All Projects"
              onChange={(items) => {
                setSelectedProjectIds(items.filter((x) => x.isSelected).map((x) => x.value as number));
              }}
              buttonProps={{ className: 'min-w-52 max-w-60' }}
            />
          )}
          <MultiSelect
            items={usersQuery.data?.map((x) => ({
              label: x.firstName + ' ' + x.lastName,
              value: x.id,
              isSelected: selectedUserIds.includes(x.id),
            }))}
            label="All Users"
            onChange={(items) => {
              setSelectedUserIds(items.filter((x) => x.isSelected).map((x) => x.value as number));
            }}
            buttonProps={{ className: 'min-w-52 max-w-60' }}
          />
        </div>
        <>
          {mergedItems?.length ? (
            <>
              {mergedItems.map((collection) => {
                return (
                  <PhotoCollectionCard
                    key={collection.id}
                    collectionData={collection}
                    onDeleted={(id) => {
                      // manually delete photos on the UI side because reloading photos from the API makes the images re-render and blink
                      data.pages = data.pages.map((page) => {
                        page.projectPhotosCollections = page.projectPhotosCollections.map((collection) => {
                          collection.projectPhotos = collection.projectPhotos.filter((item) => item.id !== id);

                          return collection;
                        });

                        return page;
                      });

                      queryClient.setQueryData(photosKey, data);
                    }}
                  />
                );
              })}
            </>
          ) : isLoading ? (
            <div className="flex size-full items-center justify-center">
              <LoadingIndicator />
            </div>
          ) : null}
        </>
        {totalCount === 0 && !isLoading && (
          <div className="mt-32 flex flex-1 flex-col items-center justify-center gap-5">
            <EmptytStateChatPhoto className="h-[100px] w-[100px]" />
            <p className="text-center text-sm font-normal text-soft-400">
              There are no project photos yet.
              <br />
              They will be visible here once your employees and contractors
              <br />
              upload photos from their Hammr app.
            </p>
          </div>
        )}
        {hasNextPage && (
          <div className="flex justify-center">
            <Button variant="link" color="primary" afterContent={<ArrowDownSLine />} onClick={() => fetchNextPage()}>
              Show {RESULTS_PER_PAGE} more
            </Button>
          </div>
        )}
      </div>
    </>
  );
}
