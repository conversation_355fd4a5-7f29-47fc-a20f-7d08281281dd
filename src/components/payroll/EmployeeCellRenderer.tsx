import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import InfoCustomFill from '@/hammr-icons/InfoCustomFill';
import CompactButton from '@/hammr-ui/components/CompactButton';
import { Tooltip } from '@/hammr-ui/components/tooltip';

const EmployeeCellRenderer = ({
  params,
  handleDeleteEarning,
}: {
  params: any;
  handleDeleteEarning: (params: { id: string; type: string; amount: number }) => void;
}) => {
  const parentsNonVisibleGroup = params.node.parent.group && !params.node.parent.displayed;
  if (params.node.group) {
    return params.node.key;
  } else if (parentsNonVisibleGroup) {
    if (params.data.skip) {
      return <div className="line-through">{params.value}</div>;
    }
    return (
      <div className="flex flex-col">
        <div className="text-sm">{params.value}</div>
        {params.data.employeeSubtext && (
          <div className="relative top-0.5 text-xs text-gray-500">{params.data.employeeSubtext}</div>
        )}
      </div>
    );
  } else {
    const showDelete = params.data.canDelete;
    const showPtoTooltip = params.data.showPtoTooltip;
    const showPerDiemTooltip = params.data.showPerDiemTooltip;

    const name = params.data.type === 'Reimbursement' ? 'Reimbursement/Per Diem' : params.data.type;

    return (
      <div style={{ marginLeft: '20px' }} className="flex">
        <div className="font-normal">{name}</div>
        {showDelete && (
          <div className="ml-1 flex items-center">
            <CompactButton
              size="large"
              onClick={() => {
                handleDeleteEarning({
                  id: params.data.checkEmployeeId,
                  type: params.data.type,
                  amount: params.data.grossPay,
                });
              }}
            >
              <DeleteBinLine />
            </CompactButton>
          </div>
        )}
        {showPtoTooltip && (
          <div className="ml-1 flex items-center">
            <Tooltip
              content="This earning is calculated from approved time off requests. 
To remove it, delete approved time off entries  in this pay period for this employee."
            >
              <div>
                <InfoCustomFill height={20} width={20} />
              </div>
            </Tooltip>
          </div>
        )}
        {showPerDiemTooltip && (
          <div className="ml-1 flex items-center">
            <Tooltip content="This Reimbursement/Per Diem earning is set in the employee's profile. To remove it, go to the employee's profile and update it.">
              <div>
                <InfoCustomFill height={20} width={20} />
              </div>
            </Tooltip>
          </div>
        )}
      </div>
    );
  }
};

export default EmployeeCellRenderer;
