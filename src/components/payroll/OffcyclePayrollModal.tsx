import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import dayjs from 'dayjs';
import { createDraftPayroll } from 'services/payroll';
import { useRouter } from 'next/router';
import { useCompany } from 'hooks/useCompany';
import { ModalV2 } from '../elements/ModalV2';
import ControlledDateInput from '../elements/form/ControlledDateInput';
import ControlledSwitch from '../elements/form/ControlledSwitch';
import FlashlightLine from '@/hammr-icons/FlashlightLine';

interface OffcyclePayrollModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
}

// TODO -> add validation for pay date
export default function OffcyclePayrollModal({ open, setOpen, callback }: OffcyclePayrollModalProps) {
  const form = useForm();
  const {
    formState: { errors },
    control,
    handleSubmit,
    reset,
  } = form;
  const { checkCompany } = useCompany();
  const router = useRouter();

  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    try {
      const res = await createDraftPayroll({
        payday: dayjs(data.payday).format('YYYY-MM-DD'),
        periodStart: dayjs(data.payPeriodStart).format('YYYY-MM-DD'),
        periodEnd: dayjs(data.payPeriodEnd).format('YYYY-MM-DD'),
        type: 'off_cycle',
        offCycleOptions: {
          force_supplemental_withholding: data.taxAllEarnings,
          apply_benefits: data.applyBenefits,
          apply_post_tax_deductions: data.applyPostTaxDeductions,
        },
      });

      router.push(`payroll/${res.payrollId}`);

      callback?.();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to submit off-cycle payroll');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ModalV2 icon={<FlashlightLine />} title="Run Off-Cycle Payroll" open={open} setOpen={setOpen}>
      <FormV2
        onCancel={() => setOpen(false)}
        onSubmit={handleSubmit(onSubmit)}
        isLoading={isProcessing}
        submitText="Create Payroll"
      >
        <div className="space-y-5">
          <ControlledDateInput
            label="Pay Period Start Date"
            control={control}
            name="payPeriodStart"
            rules={{ required: 'Please select a pay period start date' }}
            className="w-full"
            required
          />

          <ControlledDateInput
            label="Pay Period End Date"
            control={control}
            name="payPeriodEnd"
            rules={{ required: 'Please select a pay period end date' }}
            className="w-full"
            required
          />

          <ControlledDateInput
            label="Pay Date"
            control={control}
            name="payday"
            rules={{ required: 'Please select a pay date' }}
            className="w-full"
            required
          />

          <ControlledSwitch label="Tax All Earnings As Supplemental Earnings" control={control} name="taxAllEarnings" />

          <ControlledSwitch label="Apply Benefits" control={control} name="applyBenefits" />

          <ControlledSwitch label="Apply Post-Tax Deductions" control={control} name="applyPostTaxDeductions" />
        </div>
      </FormV2>
    </ModalV2>
  );
}
