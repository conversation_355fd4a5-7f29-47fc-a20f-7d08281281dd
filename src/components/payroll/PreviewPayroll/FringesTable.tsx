import { ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { PayrollPreviewResponse } from 'interfaces/payroll';
import { HammrUser } from 'interfaces/user';
import { formatUSD } from 'utils/format';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from 'utils/requestHelpers';
import { formatHoursWorked } from '../utils';

type FringeRow = {
  employee: string;
  name: string;
  category: string;
  companyContribution: number;
};

const FringesTable = ({
  visible,
  previewData,
  allEmployees,
  isLoading,
}: {
  visible: boolean;
  previewData: PayrollPreviewResponse;
  allEmployees: HammrUser[];
  isLoading: boolean;
}) => {
  const { data: fringeBenefitsData, isLoading: isFringeDataLoading } = useQuery({
    queryKey: ['payroll-fringe-benefits', previewData?.payroll.id],
    queryFn: async () => {
      if (!previewData?.payroll.id) return [];
      return apiRequest(`payrolls/${previewData.payroll.id}/fringe-benefits`);
    },
    enabled: !!previewData?.payroll.id && visible,
  });

  const colDefs = [
    {
      headerName: 'Employee',
      field: 'employee',
      initialSort: 'asc' as const,
      comparator: (a: string, b: string) => {
        if (!a || !b) {
          return 0;
        }

        return a.localeCompare(b, 'en', { sensitivity: 'base' });
      },
    },
    {
      headerName: 'Name',
      field: 'name',
    },
    {
      headerName: 'Category',
      field: 'category',
      valueFormatter: (params: ValueFormatterParams) => {
        return params.value?.replaceAll('_', ' ') || '';
      },
      cellRenderer: (params: ICellRendererParams) => {
        return <div className="capitalize">{params.valueFormatted}</div>;
      },
    },
    {
      headerName: 'Company Contribution',
      field: 'companyContribution',
      minWidth: 190,
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.data.fringeBenefitAmount === undefined || params.data.fringeBenefitAmount === 0) {
          return ' ';
        }
        return formatUSD.format((params.data.fringeBenefitAmount * params.data?.totalMinutes) / 60);
      },
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="items-left flex flex-col justify-center gap-1">
            <div className="text-sm">{params.valueFormatted}</div>
            <div className="text-xs text-sub-600">
              {formatHoursWorked(params.data?.totalMinutes / 60)}h x ${params.data?.fringeBenefitAmount}/hour
            </div>
          </div>
        );
      },
    },
  ];

  // Transform the API response into table rows
  const rows: FringeRow[] =
    fringeBenefitsData?.map((timesheet: any) => {
      const employeeObject = allEmployees.find(
        (employee) => employee.checkEmployeeId === timesheet.user?.checkEmployeeId
      );
      const employeeName = employeeObject ? employeeObject.firstName + ' ' + employeeObject.lastName : 'N/A';

      return {
        employee: employeeName,
        name: timesheet?.fringeBenefitClassification.name,
        category: timesheet?.fringeBenefitClassification.category.toLowerCase(),
        fringeBenefitAmount: timesheet.fringeBenefitClassification.amount || 0,
        totalMinutes: timesheet.totalMinutes - timesheet.driveTimeMinutes || 0,
      };
    }) || [];

  if (!visible) {
    return null;
  }

  return <UpdatedTable<any> colDefs={colDefs} rowData={rows} isLoading={isLoading || isFringeDataLoading} />;
};

export default FringesTable;
