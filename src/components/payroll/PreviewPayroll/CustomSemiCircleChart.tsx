import { formatUSD } from '@/utils/format';
import React, { useState } from 'react';

interface ChartDataItem {
  name: string;
  value: number;
  color: string;
}

interface CustomSemiCircleChartProps {
  data: ChartDataItem[];
  total: number;
  size?: number;
  innerRadius?: number;
  outerRadius?: number;
  className?: string;
  showLegend?: boolean;
}

const CustomSemiCircleChart: React.FC<CustomSemiCircleChartProps> = ({
  data,
  total,
  size = 248,
  innerRadius,
  outerRadius,
  className = '',
  showLegend = false,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [showTooltip, setShowTooltip] = useState(false);

  const center = size / 2;
  const startAngle = Math.PI; // 180 degrees
  const endAngle = 0; // 0 degrees

  // Scale radii proportionally to size if not provided
  const scaledInnerRadius = innerRadius ?? size * 0.35;
  const scaledOuterRadius = outerRadius ?? size * 0.45;

  const handleSegmentHover = (index: number, event: React.MouseEvent) => {
    setHoveredIndex(index);
    setShowTooltip(true);

    // Calculate tooltip position based on mouse position
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    setTooltipPosition({ x, y });
  };

  const handleSegmentMove = (event: React.MouseEvent) => {
    if (showTooltip && hoveredIndex !== null) {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      setTooltipPosition({ x, y });
    }
  };

  const handleSegmentLeave = () => {
    setHoveredIndex(null);
    setShowTooltip(false);
  };

  // Calculate tooltip position with bounds checking
  const getTooltipPosition = () => {
    const tooltipWidth = 200; // Approximate tooltip width
    const tooltipHeight = 60; // Approximate tooltip height

    let left = tooltipPosition.x + 10;
    let top = tooltipPosition.y - tooltipHeight - 10;

    // Prevent tooltip from going off the right edge
    if (left + tooltipWidth > size) {
      left = tooltipPosition.x - tooltipWidth - 10;
    }

    // Prevent tooltip from going off the left edge
    if (left < 0) {
      left = 10;
    }

    // Prevent tooltip from going off the top edge
    if (top < 0) {
      top = tooltipPosition.y + 20;
    }

    return { left, top };
  };

  const createArcPath = (startAngle: number, endAngle: number, innerRadius: number, outerRadius: number) => {
    const x1 = center + innerRadius * Math.cos(startAngle);
    const y1 = center + innerRadius * Math.sin(startAngle);
    const x2 = center + outerRadius * Math.cos(startAngle);
    const y2 = center + outerRadius * Math.sin(startAngle);
    const x3 = center + outerRadius * Math.cos(endAngle);
    const y3 = center + outerRadius * Math.sin(endAngle);
    const x4 = center + innerRadius * Math.cos(endAngle);
    const y4 = center + innerRadius * Math.sin(endAngle);

    const largeArcFlag = Math.abs(endAngle - startAngle) > Math.PI ? 1 : 0;

    const outerArc = `M ${x2} ${y2} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x3} ${y3}`;
    const line1 = `L ${x4} ${y4}`;
    const innerArc = `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x1} ${y1}`;
    const line2 = 'Z';

    return `${outerArc} ${line1} ${innerArc} ${line2}`;
  };

  const renderSegments = () => {
    if (total === 0) return null;

    const segments = [];
    let currentAngle = startAngle;

    // Calculate gap angle (1.5px converted to radians)
    const gapAngle = (1.5 / scaledOuterRadius) * 2; // 1.5px gap converted to radians
    const totalGaps = data.filter((item) => item.value > 0).length - 1;
    const totalGapAngle = totalGaps * gapAngle;

    // Adjust total angle to account for gaps
    const adjustedTotalAngle = Math.PI - totalGapAngle;

    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      if (item.value === 0) continue;

      const segmentAngle = (item.value / total) * adjustedTotalAngle;
      const segmentEndAngle = currentAngle - segmentAngle;

      const path = createArcPath(currentAngle, segmentEndAngle, scaledInnerRadius, scaledOuterRadius);

      const isHovered = hoveredIndex === i;
      const hoverScale = isHovered ? 1.05 : 1;
      const hoverRadius = scaledOuterRadius * hoverScale;

      segments.push(
        <g key={item.name}>
          {/* Hover background for better interaction */}
          <path
            d={createArcPath(currentAngle, segmentEndAngle, scaledInnerRadius, hoverRadius)}
            fill="transparent"
            stroke="none"
            onMouseOver={(event) => handleSegmentHover(i, event)}
            onMouseMove={(event) => handleSegmentMove(event)}
            onMouseOut={handleSegmentLeave}
            style={{ cursor: 'pointer' }}
            className="hover:opacity-20"
          />
          {/* Actual segment */}
          <path
            d={path}
            fill={item.color}
            stroke="none"
            className="transition-all duration-150 ease-in-out"
            onMouseOver={(event) => handleSegmentHover(i, event)}
            onMouseMove={(event) => handleSegmentMove(event)}
            onMouseOut={handleSegmentLeave}
            style={{
              transform: isHovered ? `scale(${hoverScale})` : 'scale(1)',
              transformOrigin: 'center',
              cursor: 'pointer',
            }}
          />
        </g>
      );

      currentAngle = segmentEndAngle - gapAngle; // Add gap after each segment
    }

    return segments;
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative m-auto overflow-hidden" style={{ height: size / 2, width: size }}>
        <svg
          width={size}
          height={size}
          style={{ transform: 'rotateX(180deg)' }}
          className="absolute z-10 transition-all duration-150 ease-in-out hover:scale-105"
          onMouseEnter={() => setHoveredIndex(null)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          {/* Background semi-circle */}
          <path
            d={createArcPath(startAngle, endAngle, scaledInnerRadius, scaledOuterRadius)}
            stroke="none"
            className="fill-background"
          />
          {/* Data segments */}
          {renderSegments()}
        </svg>

        <div className="absolute bottom-0 z-0 flex w-full items-center justify-center">
          <div className="text-2xl font-medium text-strong-950">{formatUSD.format(total)}</div>
        </div>
      </div>

      {/* Tooltip */}
      {showTooltip && hoveredIndex !== null && (
        <div
          className="absolute z-50 rounded-md border border-soft-200 bg-background px-3 py-2 text-sm text-strong-950 shadow-lg"
          style={{
            ...getTooltipPosition(),
            pointerEvents: 'none',
          }}
        >
          <div className="text-sm font-medium text-sub-600">{data[hoveredIndex].name}</div>
          <div className="text-strong-950">
            {formatUSD.format(data[hoveredIndex].value)} ({((data[hoveredIndex].value / total) * 100).toFixed(0)}%)
          </div>
        </div>
      )}

      {showLegend && (
        <div className="mt-9 space-y-1">
          <div className="mb-4 border-b border-soft-200" />
          {data.map((item, index) => {
            const isHovered = hoveredIndex === index;
            return (
              <div
                key={item.name}
                className={`flex w-full cursor-pointer items-center justify-between rounded-md p-2 text-sm transition-all duration-150 ease-in-out ${
                  isHovered ? 'bg-sub-100' : ''
                }`}
                onMouseOver={() => setHoveredIndex(index)}
                onMouseOut={() => setHoveredIndex(null)}
                style={{ width: '100%' }}
              >
                <div className="flex flex-1 items-center gap-2">
                  <div
                    className={`h-3 w-3 rounded-full transition-all duration-150 ease-in-out ${
                      isHovered ? 'scale-125' : ''
                    }`}
                    style={{ backgroundColor: item.color }}
                  />
                  <span
                    className={`transition-all duration-150 ease-in-out ${
                      isHovered ? 'font-medium text-strong-950' : 'text-sub-600'
                    }`}
                  >
                    {item.name}
                  </span>
                </div>
                <span className={`flex-shrink-0 font-medium text-strong-950 transition-all duration-150 ease-in-out`}>
                  {formatUSD.format(item.value)}
                </span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default CustomSemiCircleChart;
