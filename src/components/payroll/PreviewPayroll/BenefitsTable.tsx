import { ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { PayrollPreviewResponse } from 'interfaces/payroll';
import { HammrUser } from 'interfaces/user';
import { flatten } from 'lodash';
import { formatUSD } from 'utils/format';

type BenefitRow = {
  employee: string;
  benefitType: string;
  description: string;
  contribution: number;
  companyContribution: number;
};

const BenefitsTable = ({
  visible,
  previewData,
  allEmployees,
  isLoading,
}: {
  visible: boolean;
  previewData: PayrollPreviewResponse;
  allEmployees: HammrUser[];
  isLoading: boolean;
}) => {
  const colDefs = [
    {
      headerName: 'Employee',
      field: 'employee',
      initialSort: 'asc' as const,
      comparator: (a: string, b: string) => {
        if (!a || !b) {
          return 0;
        }

        return a.localeCompare(b, 'en', { sensitivity: 'base' });
      },
    },
    {
      headerName: 'Benefit type',
      field: 'benefitType',
      valueFormatter: (params: ValueFormatterParams) => {
        return params.value.replaceAll('_', ' ');
      },
      cellRenderer: (params: ICellRendererParams) => {
        return <div className="capitalize">{params.valueFormatted}</div>;
      },
    },
    {
      headerName: 'Description',
      field: 'description',
      valueFormatter: (params: ValueFormatterParams) => {
        if (!params.value) return '';
        return params.value;
      },
    },
    {
      headerName: 'Employee contribution',
      field: 'contribution',
      minWidth: 190,
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined || params.value === 0) {
          return ' ';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'Company contribution',
      field: 'companyContribution',
      minWidth: 190,
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined || params.value === 0) {
          return ' ';
        }
        return formatUSD.format(params.value);
      },
    },
  ];

  const rows: (BenefitRow | undefined)[] | undefined = flatten(
    previewData?.payroll.items.map((item) => {
      const employeeObject = allEmployees.find((employee) => employee.checkEmployeeId === item.employee);
      const employeeName = employeeObject ? employeeObject.firstName + ' ' + employeeObject.lastName : '';

      const benefits = item.benefits?.map((benefit) => {
        return {
          employee: employeeName,
          benefitType: benefit.benefit,
          description: benefit.description,
          contribution: benefit.employee_contribution_amount,
          companyContribution: benefit.company_contribution_amount,
        };
      });

      return benefits;
    })
  );

  if (!visible) {
    return null;
  }

  return <UpdatedTable<any> colDefs={colDefs} rowData={rows} isLoading={isLoading} />;
};

export default BenefitsTable;
