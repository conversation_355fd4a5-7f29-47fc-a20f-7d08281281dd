import { PayrollPreviewResponse } from '@/interfaces/payroll';
import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import { RiCashLine, RiDownload2Line, RiFileList2Line, RiInformationLine } from '@remixicon/react';
import CustomSemiCircleChart from './CustomSemiCircleChart';
import Button from '@/hammr-ui/components/button';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

const Summary = ({
  visible,
  previewData,
  handleExportCashRequirement,
  handleExportPayrollJournal,
  isExportingCashRequirement = false,
  isExportingPayrollJournal = false,
}: {
  visible: boolean;
  previewData: PayrollPreviewResponse;
  handleExportCashRequirement: () => void;
  handleExportPayrollJournal: () => void;
  isExportingCashRequirement?: boolean;
  isExportingPayrollJournal?: boolean;
}) => {
  if (!visible) {
    return null;
  }

  if (!previewData) {
    return (
      <div className="mt-1/5 flex items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  // Calculate liability breakdown
  const liabilityData = [
    { name: 'Employee Gross', value: parseFloat(previewData?.payroll.totals.employee_gross || '0'), color: '#335CFF' },
    {
      name: 'Employee Reimbursements',
      value: parseFloat(previewData?.payroll.totals.employee_reimbursements || '0'),
      color: '#47C2FF',
    },
    {
      name: 'Contractor Gross',
      value: parseFloat(previewData?.payroll.totals.contractor_gross || '0'),
      color: '#7D52F4',
    },
    {
      name: 'Contractor Reimbursements',
      value: parseFloat(previewData?.payroll.totals.contractor_reimbursements || '0'),
      color: '#FB4BA3',
    },
    { name: 'Company Taxes', value: parseFloat(previewData?.payroll.totals.company_taxes || '0'), color: '#1FC16B' },
    {
      name: 'Company Benefit Contributions',
      value: parseFloat(previewData?.payroll.totals.company_benefits || '0'),
      color: '#22D3BB',
    },
  ];

  const employeeDirectDepositAmount = previewData?.payroll.items
    .filter((item) => item.payment_method === 'direct_deposit')
    .reduce((acc, item) => acc + parseFloat(item.net_pay), 0);
  const contractorDirectDepositAmount = previewData?.payroll.contractor_payments
    .filter((item) => item.payment_method === 'direct_deposit')
    .reduce((acc, item) => acc + parseFloat(item.net_pay), 0);

  const directDeposits = employeeDirectDepositAmount + contractorDirectDepositAmount;
  let managedPostTaxDeductionsAmount = 0;

  previewData?.payroll.items?.forEach((item) => {
    if (item.post_tax_deductions && item.post_tax_deductions.length > 0) {
      item.post_tax_deductions.forEach((deduction) => {
        if (deduction.managed) {
          managedPostTaxDeductionsAmount += parseFloat(deduction.amount);
        }
      });
    }
  });

  // Calculate cash requirement breakdown
  const cashRequirementData = [
    { name: 'Direct Deposits', value: directDeposits, color: '#F6B51E' },
    { name: 'Employee Taxes', value: parseFloat(previewData?.payroll.totals.employee_taxes || '0'), color: '#EE6023' },
    { name: 'Company Taxes', value: parseFloat(previewData?.payroll.totals.company_taxes || '0'), color: '#FB3748' },
    {
      name: 'Managed Post-Tax Deductions',
      value: managedPostTaxDeductionsAmount.toFixed(2),
      color: '#717784',
    },
  ];

  const totalLiability = parseFloat(previewData?.payroll.totals.liability || '0');
  const totalCashRequirement = parseFloat(previewData?.payroll.totals.cash_requirement || '0');

  return (
    <div className="grid grid-cols-2 gap-6">
      <Card>
        <CardHeader className="p-4">
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <RiFileList2Line className="text-sub-600" />
                <span className="font-semibold">Total Liability</span>
                <Tooltip content="The total amount owed for this payroll including wages, reimbursements, employer taxes, and benefit contributions.">
                  <div>
                    <RiInformationLine className="size-4 text-sub-600" />
                  </div>
                </Tooltip>
              </div>
              <Button
                className="min-w-36"
                size="small"
                variant="outline"
                color="neutral"
                loading={isExportingPayrollJournal}
                onClick={() => {
                  handleExportPayrollJournal();
                }}
              >
                <div className="flex items-center gap-1">
                  <span className="text-strong-950">Payroll Journal</span>
                  <RiDownload2Line className="size-4" />
                </div>
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <CustomSemiCircleChart data={liabilityData} total={totalLiability} showLegend={true} />
        </CardContent>
      </Card>

      <Card className="h-fit">
        <CardHeader className="p-4">
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <RiCashLine className="text-sub-600" />
                <span className="font-semibold">Cash Requirement</span>
                <Tooltip content="The amount withdrawn via ACH to cover net wages and liabilities handled by Hammr on your behalf.">
                  <div>
                    <RiInformationLine className="size-4 text-sub-600" />
                  </div>
                </Tooltip>
              </div>
              <Button
                className="min-w-36"
                size="small"
                variant="outline"
                color="neutral"
                loading={isExportingCashRequirement}
                onClick={() => {
                  handleExportCashRequirement();
                }}
              >
                <div className="flex items-center gap-1">
                  <span className="text-strong-950">Cash Requirement Report</span>
                  <RiDownload2Line className="size-4" />
                </div>
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <CustomSemiCircleChart data={cashRequirementData} total={totalCashRequirement} showLegend={true} />
        </CardContent>
      </Card>
    </div>
  );
};

export default Summary;
