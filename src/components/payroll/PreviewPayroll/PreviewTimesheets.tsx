import React, { useRef, useState } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { PayrollPreviewResponse } from '@/interfaces/payroll';
import TimesheetsTable from '@/components/timesheets/TimesheetsTable';
import { useCompany } from '@/hooks/useCompany';

interface PreviewTimesheetsProps {
  previewData: PayrollPreviewResponse;
}

const PreviewTimesheets = ({ previewData }: PreviewTimesheetsProps) => {
  const gridRef = useRef<AgGridReact>(null);
  const { company } = useCompany();
  const isExportingRef = useRef(false);

  if (!company) {
    return null;
  }

  return (
    <div className="flex flex-grow flex-col">
      <TimesheetsTable
        isLoading={false}
        parentRef={gridRef}
        userTimesheets={previewData.timesheets}
        useDecimalHours={company?.timeTrackingSettings?.useDecimalHours || false}
        rowGroupBy="employee"
        isExportingRef={isExportingRef}
        showAddTimesheetButton={false}
        showCheckboxes={false}
      />
    </div>
  );
};

export default PreviewTimesheets;
