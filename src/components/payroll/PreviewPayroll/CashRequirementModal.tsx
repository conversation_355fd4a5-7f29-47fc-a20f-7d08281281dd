import { ModalV2 } from '@/components/elements/ModalV2';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import Alert from '@/hammr-ui/components/Alert';
import { FormV2 } from 'components/elements/Form';

const CashRequirementModal = ({
  cashRequirement,
  isOpen,
  setIsOpen,
  onSubmit,
}: {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onSubmit: () => void;
  cashRequirement: string;
}) => {
  return (
    <ModalV2 icon={<FlashlightLine />} title="Submit Payroll" open={isOpen} setOpen={setIsOpen}>
      <FormV2
        submitText="Submit"
        onCancel={() => setIsOpen(false)}
        onSubmit={() => {
          setIsOpen(false);
          onSubmit();
        }}
      >
        <div className="text-hammr-black">
          <h1 className="text-sm font-medium text-strong-950">Cash requirement: ${cashRequirement}</h1>

          <Alert status="information" size="x-small" className="mt-5">
            <div>
              To ensure successful payroll processing, the required funds of ${cashRequirement} must be available in
              your account.
            </div>
            <div className="mt-5">
              Please check that your bank account has sufficient funds before submitting this payroll.
            </div>
          </Alert>
        </div>
      </FormV2>
    </ModalV2>
  );
};

export default CashRequirementModal;
