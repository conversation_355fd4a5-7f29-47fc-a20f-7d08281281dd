import { HammrUser } from '@/interfaces/user';
import { ColDef, ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { PayrollPreviewResponse } from 'interfaces/payroll';
import { flatten } from 'lodash';
import { formatUSD } from 'utils/format';

interface PostTaxDeductionsRow {
  employee: string;
  type: string;
  amount: number;
  description: string;
}

const PostTaxDeductions = ({
  visible,
  previewData,
  allEmployees,
  isLoading,
}: {
  previewData: PayrollPreviewResponse;
  visible: boolean;
  allEmployees: HammrUser[];
  isLoading: boolean;
}) => {
  const colDefs: ColDef<PostTaxDeductionsRow>[] = [
    {
      headerName: 'Employee',
      field: 'employee',
    },
    {
      headerName: 'Type',
      field: 'type',
      valueFormatter: (params: ValueFormatterParams) => {
        return params.value.replace('_', ' ');
      },
      cellRenderer: (params: ICellRendererParams) => {
        return <div className="capitalize">{params.valueFormatted}</div>;
      },
    },
    {
      headerName: 'Description',
      field: 'description',
    },
    {
      headerName: 'Amount',
      field: 'amount',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined || params.value === 0) {
          return ' ';
        }
        return formatUSD.format(params.value);
      },
    },
  ];

  const data = previewData?.payroll.items.map((item) => {
    const employeeObject = allEmployees.find((employee) => employee.checkEmployeeId === item.employee);
    const employeeName = employeeObject ? employeeObject.firstName + ' ' + employeeObject.lastName : '';

    return (item.post_tax_deductions ?? []).map((tax) => {
      return {
        employee: employeeName,
        type: tax.type,
        amount: tax.amount,
        description: tax.description || '',
      };
    });
  });

  const rows = flatten(data).filter((item) => item);

  if (!visible) {
    return null;
  }

  return <UpdatedTable colDefs={colDefs} rowData={rows} isLoading={isLoading} />;
};

export default PostTaxDeductions;
