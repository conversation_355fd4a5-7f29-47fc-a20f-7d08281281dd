import { useState } from 'react';
import { TabI<PERSON>, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import MoneyDollarCircleFill from '@/hammr-icons/MoneyDollarCircleFill';
import { formatUSD } from '@/utils/format';
import { Payroll, PayrollPreviewResponse } from 'interfaces/payroll';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { cn } from '@/utils/cn';
import { useDate } from 'hooks/useDate';
import { useEmployeesQuery } from 'hooks/data-fetching/useEmployees';
import PayBreakdownTable from './PayBreakdownTable';
import TaxesTable from './TaxesTable';
import BenefitsTable from './BenefitsTable';
import PostTaxDeductions from './PostTaxDeductions';
import FringesTable from './FringesTable';
import Summary from './Summary';
import { RiUser<PERSON>ill, RiTeam<PERSON>ill } from '@remixicon/react';

const PayDifference = ({
  prevAmount,
  currentAmount,
  percent = false,
  tooltipSingularWord,
  tooltipPluralWord,
  payrollDate,
}: {
  prevAmount: number;
  currentAmount: number;
  percent?: boolean;
  tooltipSingularWord: string;
  tooltipPluralWord: string;
  payrollDate: string;
}) => {
  const floatDifference = currentAmount - prevAmount;
  const { dayjs } = useDate();

  let difference = Math.floor(floatDifference);

  if (percent) {
    if (prevAmount === 0) return '';
    difference = Math.round((difference / prevAmount) * 100);
  }

  if (difference === 0) return '';

  let message = '';

  if (percent) {
    const stringDifference = Math.abs(difference).toFixed(0);
    if (difference > 0) {
      message = `${tooltipSingularWord} is ${stringDifference}% higher`;
    } else {
      message = `${tooltipSingularWord} is ${stringDifference}% lower`;
    }
  } else {
    message = difference > 0 ? '+' : '';
    if (Math.abs(difference) === 1) {
      message += `${difference} ${tooltipSingularWord}`;
    } else {
      message += `${difference} ${tooltipPluralWord}`;
    }
  }

  message += ' in this payroll';

  return (
    <Tooltip
      contentClassName="max-w-[300px]"
      content={
        <div>
          <div>{message}</div>
          <div>vs last payroll on {dayjs(payrollDate).format('MMM D, YYYY')}.</div>
        </div>
      }
    >
      <div
        className={cn(
          'rounded-full px-2 py-0.5 text-xs font-medium',
          difference > 0 ? 'bg-success-lighter text-success-base' : 'bg-error-lighter text-error-base'
        )}
      >
        {difference > 0 ? '+' : ''}
        {difference}
        {percent ? '%' : ''}
      </div>
    </Tooltip>
  );
};

const PayrollTables = ({
  statusKey,
  previewData,
  refetchCallback,
  isLoading,
  handleExportCashRequirement,
  handleExportPayrollJournal,
  isExportingCashRequirement,
  isExportingPayrollJournal,
}: {
  statusKey: string;
  previewData: PayrollPreviewResponse;
  refetchCallback: () => void;
  isLoading: boolean;
  handleExportCashRequirement: () => void;
  handleExportPayrollJournal: () => void;
  isExportingCashRequirement: boolean;
  isExportingPayrollJournal: boolean;
}) => {
  const employeesQuery = useEmployeesQuery({
    simple: true,
    includeIsArchived: true,
  });

  const { data: employees } = employeesQuery;

  if (!employees) {
    return null;
  }

  return (
    <>
      <Summary
        handleExportCashRequirement={handleExportCashRequirement}
        handleExportPayrollJournal={handleExportPayrollJournal}
        visible={statusKey === 'summary'}
        previewData={previewData}
        isExportingCashRequirement={isExportingCashRequirement}
        isExportingPayrollJournal={isExportingPayrollJournal}
      />
      <PayBreakdownTable
        visible={statusKey === 'pay_breakdown'}
        previewData={previewData}
        allEmployees={employees}
        refetchCallback={refetchCallback}
        isLoading={isLoading}
      />
      <TaxesTable visible={statusKey === 'taxes'} previewData={previewData} isLoading={isLoading} />
      <PostTaxDeductions
        visible={statusKey === 'post_tax_deductions'}
        previewData={previewData}
        allEmployees={employees}
        isLoading={isLoading}
      />
      <BenefitsTable
        visible={statusKey === 'benefits'}
        previewData={previewData}
        allEmployees={employees}
        isLoading={isLoading}
      />
      <FringesTable
        visible={statusKey === 'fringes'}
        previewData={previewData}
        allEmployees={employees}
        isLoading={isLoading}
      />
    </>
  );
};

const earningsTabs = [
  { key: 'summary', name: 'Summary' },
  { key: 'pay_breakdown', name: 'Pay Breakdown' },
  { key: 'taxes', name: 'Taxes' },
  { key: 'post_tax_deductions', name: 'Post-Tax Deductions' },
  { key: 'benefits', name: 'Benefits' },
  { key: 'fringes', name: 'Fringe Contributions' },
];

interface PayrollEarningsProps {
  previewData: PayrollPreviewResponse;
  previousPayroll: Payroll | null;
  refetch: () => void;
  isLoading: boolean;
  isSubmitting: boolean;
  handleExportCashRequirement: () => void;
  handleExportPayrollJournal: () => void;
  isExportingCashRequirement: boolean;
  isExportingPayrollJournal: boolean;
}

const PayrollEarnings = ({
  previewData,
  previousPayroll,
  refetch,
  isLoading,
  isSubmitting,
  handleExportCashRequirement,
  handleExportPayrollJournal,
  isExportingCashRequirement,
  isExportingPayrollJournal,
}: PayrollEarningsProps) => {
  const [selectedEarningsTab, setSelectedEarningsTab] = useState<string>('summary');

  return (
    <>
      <Tabs value={selectedEarningsTab ?? ''} onValueChange={(value) => setSelectedEarningsTab(value)}>
        <TabList>
          {earningsTabs.map((tab) => (
            <TabItem className="px-9" key={tab.key} value={tab.key}>
              {tab.name}
            </TabItem>
          ))}
        </TabList>
      </Tabs>

      <div className="mt-5 rounded-2xl border border-soft-200 p-5">
        <div className="grid grid-cols-3 gap-4">
          <div className="flex gap-3 border-r border-soft-200 pr-4">
            <div className="self-center">
              <KeyIcon icon={<MoneyDollarCircleFill />} color="grey" variant="lighter" />
            </div>
            <div className="flex flex-col justify-center gap-1">
              <div className="text-2xs font-medium uppercase text-soft-400">LIABILITY</div>
              <div className="flex items-center gap-2 text-base font-medium text-strong-950">
                <span>{formatUSD.format(parseFloat(previewData?.payroll.totals.liability ?? '0'))}</span>
                {previousPayroll && (
                  <span>
                    <PayDifference
                      prevAmount={parseFloat(previousPayroll?.totals.liability ?? '0')}
                      currentAmount={parseFloat(previewData?.payroll.totals.liability ?? '0')}
                      percent={true}
                      tooltipSingularWord="Liability"
                      tooltipPluralWord="Liability"
                      payrollDate={previousPayroll.payday}
                    />
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-3 border-r border-soft-200 pr-4">
            <div className="self-center">
              <KeyIcon icon={<RiUserFill />} color="grey" variant="lighter" />
            </div>
            <div className="flex flex-col justify-center gap-1">
              <div className="text-2xs font-medium uppercase text-soft-400">EMPLOYEES</div>
              <div className="flex items-center gap-2 text-base font-medium text-strong-950">
                <span>{previewData?.payroll.items.length ?? 0}</span>
                {previousPayroll && (
                  <span>
                    <PayDifference
                      prevAmount={previousPayroll?.items?.length ?? 0}
                      currentAmount={previewData?.payroll.items?.length ?? 0}
                      tooltipSingularWord="employee"
                      tooltipPluralWord="employees"
                      payrollDate={previousPayroll.payday}
                    />
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-3">
            <div className="self-center">
              <KeyIcon icon={<RiTeamFill />} color="grey" variant="lighter" />
            </div>
            <div className="flex flex-col justify-center gap-1">
              <div className="text-2xs font-medium uppercase text-soft-400">CONTRACTORS</div>
              <div className="flex items-center gap-2 text-base font-medium text-strong-950">
                <span>{previewData?.payroll.contractor_payments?.length ?? 0}</span>
                {previousPayroll && (
                  <span>
                    <PayDifference
                      prevAmount={previousPayroll?.contractor_payments?.length ?? 0}
                      currentAmount={previewData?.payroll.contractor_payments?.length ?? 0}
                      tooltipSingularWord="contractor"
                      tooltipPluralWord="contractors"
                      payrollDate={previousPayroll.payday}
                    />
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-5">
        <PayrollTables
          refetchCallback={async () => {
            refetch();
          }}
          isLoading={isLoading || isSubmitting}
          statusKey={selectedEarningsTab}
          previewData={previewData}
          handleExportCashRequirement={handleExportCashRequirement}
          handleExportPayrollJournal={handleExportPayrollJournal}
          isExportingCashRequirement={isExportingCashRequirement}
          isExportingPayrollJournal={isExportingPayrollJournal}
        />
      </div>
    </>
  );
};

export default PayrollEarnings;
