import { ColDef, ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { PatchPayrollEarningPayload, PayrollPreviewResponse } from 'interfaces/payroll';
import { formatUSD } from 'utils/format';
import { getContractorEarnings, getEmployeeEarnings, populatePayload } from '../utils';
import { HammrUser } from '@/interfaces/user';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import { showErrorToast } from '@/utils/errorHandling';
import { addToast } from '@/hooks/useToast';
import { useState } from 'react';
import Spinner from '@/hammr-ui/components/spinner';
import { updateDraftPayroll } from '@/services/payroll';

interface PayBreakdownRow {
  payrollItemId: string;
  type: string;
  employee: string;
  paymentMethod: string;
  baseEarnings: number;
  additionalEarnings: number;
  totalReimbursements: number;
  ptoSickLeave: number;
  grossPay: number;
  netPay: number;
}

function PaymentMethodCell({
  paymentMethod,
  onUpdate,
  isDirectDepositDisabled,
  disabled,
}: {
  paymentMethod: string;
  onUpdate: (value: string) => void;
  isDirectDepositDisabled: boolean;
  disabled: boolean;
}) {
  return (
    <Select value={paymentMethod} onChange={onUpdate} disabled={disabled}>
      <SelectItem disabled={isDirectDepositDisabled} value="direct_deposit">
        Direct Deposit
      </SelectItem>
      <SelectItem value="manual">Manual</SelectItem>
    </Select>
  );
}

const PayBreakdownTable = ({
  previewData,
  visible,
  allEmployees,
  refetchCallback,
  isLoading,
}: {
  previewData: PayrollPreviewResponse;
  visible: boolean;
  allEmployees: HammrUser[];
  refetchCallback: () => void;
  isLoading: boolean;
}) => {
  const [updatingPaymentMethodId, setUpdatingPaymentMethodId] = useState<string | null>('');

  const paymentMethodOverride = [
    previewData?.payroll.metadata?.manualDepositOverrides1,
    previewData?.payroll.metadata?.manualDepositOverrides2,
    previewData?.payroll.metadata?.manualDepositOverrides3,
    previewData?.payroll.metadata?.manualDepositOverrides4,
    previewData?.payroll.metadata?.manualDepositOverrides5,
  ]
    .map((item) => {
      return JSON.parse(item ?? '[]');
    })
    .flat()
    .reduce((acc, empId) => {
      acc[empId] = 'manual';
      return acc;
    }, {});

  const handlePaymentMethodChange = async (value: string, employeeId: string, employeeName: string) => {
    try {
      setUpdatingPaymentMethodId(employeeId);

      const payload: PatchPayrollEarningPayload = populatePayload(previewData);

      await updateDraftPayroll(previewData.payroll.id, {
        ...payload,
        paymentMethodOverride: {
          [employeeId]: value as 'direct_deposit' | 'manual',
        },
      });

      addToast({
        title: 'Updated Payment Method',
        description: (
          <>
            <span className="font-medium">{employeeName}</span> payment method successfully changed to{' '}
            <span className="font-medium">{value === 'direct_deposit' ? 'Direct Deposit' : 'Manual'}</span>
          </>
        ),
        type: 'success',
      });

      refetchCallback();
    } catch (error) {
      showErrorToast(error, 'Failed to update payment method');
    } finally {
      setUpdatingPaymentMethodId(null);
    }
  };

  const colDefs: ColDef<PayBreakdownRow>[] = [
    {
      headerName: 'type',
      field: 'type',
      rowGroup: true,
      hide: true,
    },
    {
      headerName: 'Employee',
      field: 'employee',
      initialSort: 'asc' as const,
      minWidth: 160,
      comparator: (a: string, b: string) => {
        if (!a || !b) {
          return 0;
        }

        return a.localeCompare(b, 'en', { sensitivity: 'base' });
      },
      cellRenderer: (params: ICellRendererParams) => {
        if (params.node.group) {
          return <div className="text-sub-600">{params.node.key?.replace('Contractors', 'Contractor')}</div>;
        }
        return <div>{params.value}</div>;
      },
    },
    {
      headerName: 'Payment method',
      field: 'paymentMethod',
      minWidth: 150,
      cellRenderer: (params: ICellRendererParams) => {
        if (params.node.group) {
          return null;
        }

        // this is the Payroll data loading and not the Select loading
        if (isLoading) {
          return null;
        }

        if (previewData.payroll.status !== 'draft') {
          return params.value === 'manual' ? 'Manual' : 'Direct Deposit';
        }

        if (updatingPaymentMethodId === params.data.employeeId) {
          return (
            <div className="flex items-center justify-start">
              <Spinner />
            </div>
          );
        }

        // only enable direct deposit if we've done a payroll specific override to manual0
        const isDirectDepositDisabled = params.value === 'manual' && !paymentMethodOverride[params.data.employeeId];

        return (
          <PaymentMethodCell
            paymentMethod={params.value}
            onUpdate={(value) => handlePaymentMethodChange(value, params.data.employeeId, params.data.employee)}
            isDirectDepositDisabled={isDirectDepositDisabled}
            disabled={Boolean(updatingPaymentMethodId)}
          />
        );
      },
    },
    {
      headerName: 'Earnings',
      field: 'baseEarnings',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return '';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'Add. Earnings',
      field: 'additionalEarnings',
      minWidth: 130,
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return '';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'Total Reimb.',
      field: 'totalReimbursements',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return '';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'PTO/Sick leave',
      field: 'ptoSickLeave',
      minWidth: 140,
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return '';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'Gross pay',
      field: 'grossPay',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return '';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'Net pay',
      field: 'netPay',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return '';
        }
        return formatUSD.format(params.value);
      },
    },
  ];
  const employeeData = previewData?.payroll.items.map((item, index) => {
    return getEmployeeEarnings({ item, allEmployees });
  });

  let rows: any[] = employeeData ?? [];

  const contractorData =
    previewData?.payroll.contractor_payments.map((item) => {
      return getContractorEarnings({ item, allEmployees });
    }) ?? [];

  rows = [...rows, ...contractorData];

  if (!visible) {
    return null;
  }

  return (
    <UpdatedTable<PayBreakdownRow>
      tableProps={{ groupAllowUnbalanced: true }}
      colDefs={colDefs}
      rowData={rows}
      disableCustomRendering
      gridOptions={{
        groupDisplayType: 'custom',
      }}
      isLoading={isLoading}
    />
  );
};

export default PayBreakdownTable;
