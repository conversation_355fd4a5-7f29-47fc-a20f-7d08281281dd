import { useMemo, useState } from 'react';
import { addToast } from 'hooks/useToast';
import { showErrorToast } from 'utils/errorHandling';
import { Input } from '@/hammr-ui/components/input';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import { useEmployees } from '@/hooks/data-fetching/useEmployees';
import { useCompany } from '@/hooks/useCompany';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef } from '@ag-grid-community/core';
import { useRef } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { PayrollPreviewResponse } from '@/interfaces/payroll';
import { updatePayrollItem } from '@/services/payroll-item';
import { updateContractorPaymentItem } from '@/services/contractor-payment';

interface SetCheckNumbersModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  previewData: PayrollPreviewResponse;
  onSuccess: () => void;
}

interface CheckNumberRow {
  id: string;
  employeeName: string;
  checkNumber: string;
  employeeType: 'employee' | 'contractor';
}

export const SetCheckNumbersModal = ({ isOpen, setIsOpen, previewData, onSuccess }: SetCheckNumbersModalProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { company } = useCompany();
  const employees = useEmployees(Number(company?.id), {
    simple: true,
    includeIsArchived: true,
  });

  const gridRef = useRef<AgGridReact>(null);

  const employeeRowData = useMemo(
    () =>
      previewData.payroll.items
        .filter((item) => item.payment_method === 'manual')
        .map((item) => {
          const employee = employees.find((e) => e.checkEmployeeId === item.employee);
          return {
            id: item.id,
            employeeName: `${employee?.firstName} ${employee?.lastName}`,
            checkNumber: item.paper_check_number || '',
            employeeType: 'employee' as const,
          };
        }),
    [previewData.payroll.items, employees]
  );

  const contractorRowData = useMemo(
    () =>
      previewData.payroll.contractor_payments
        .filter((item) => item.payment_method === 'manual')
        .map((item) => {
          const contractor = employees.find((e) => e.checkContractorId === item.contractor);
          return {
            id: item.id,
            employeeName: `${contractor?.firstName} ${contractor?.lastName}`,
            checkNumber: item.paper_check_number || '',
            employeeType: 'contractor' as const,
          };
        }),
    [previewData.payroll.contractor_payments, employees]
  );

  // Transform payroll items into row data
  const rowData: CheckNumberRow[] = [...employeeRowData, ...contractorRowData];

  const colDefs: ColDef[] = [
    {
      field: 'employeeName',
      headerName: 'Employee',
      flex: 1,
      cellRenderer: (params: any) => <div className="text-sm font-medium text-strong-950">{params.value}</div>,
    },
    {
      field: 'checkNumber',
      headerName: 'Check Number',
      flex: 1,
      editable: true,
      cellRenderer: (params: any) => (
        <Input
          type="text"
          value={params.value}
          onChange={(e) => {
            params.node.setDataValue('checkNumber', e.target.value);
          }}
          placeholder="Check number"
        />
      ),
    },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    try {
      setIsSaving(true);

      // Get the current row data from the grid
      const rowData = [];
      gridRef.current?.api.forEachNode((node) => {
        if (node.data) {
          rowData.push(node.data);
        }
      });

      // TODO -> filter out any rows that have not changed or are empty

      const patchPayrollRequests = rowData.map((row) => {
        if (!row.checkNumber) {
          return;
        }
        if (row.employeeType === 'employee') {
          return updatePayrollItem(row.id, { paper_check_number: row.checkNumber });
        } else {
          return updateContractorPaymentItem(row.id, { paper_check_number: row.checkNumber });
        }
      });

      await Promise.all(patchPayrollRequests);

      addToast({
        title: 'Success',
        description: 'Check numbers have been saved.',
        type: 'success',
      });
      setIsOpen(false);
      onSuccess();
    } catch (error) {
      showErrorToast(error, 'Error saving check numbers');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <ModalV2 open={isOpen} setOpen={setIsOpen} title="Set Check Numbers" icon={<FlashlightLine />}>
      <FormV2 onCancel={() => setIsOpen(false)} onSubmit={handleSubmit} isLoading={isSaving} submitText="Save">
        <div className="min-h-[200px] w-full">
          <UpdatedTable
            parentRef={gridRef}
            rowData={rowData}
            colDefs={colDefs}
            gridOptions={{
              suppressRowHoverHighlight: true,
              suppressCellFocus: true,
            }}
            defaultColDef={{
              sortable: false,
              suppressMenu: true,
            }}
          />
        </div>
      </FormV2>
    </ModalV2>
  );
};
