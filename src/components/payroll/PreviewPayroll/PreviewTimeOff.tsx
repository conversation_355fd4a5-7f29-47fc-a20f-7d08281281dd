import React from 'react';
import { PayrollPreviewResponse } from '@/interfaces/payroll';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef, ValueFormatterParams, ICellRendererParams } from '@ag-grid-community/core';
import { Badge } from '@/hammr-ui/components/badge';
import dayjs from 'dayjs';
import { getPolicyTypeBadgeProps } from '@/services/time-off-policies';
import { TimeOffPolicyType } from '@/interfaces/timeoff';
import EmptyStateScheduleHoliday from '@/hammr-icons/EmptyStateScheduleHoliday';

interface PreviewTimeOffProps {
  previewData: PayrollPreviewResponse;
}

interface TimeOffRow {
  employee: string;
  startDate: string;
  endDate: string;
  hours: number;
  policyType: TimeOffPolicyType;
  policyName: string;
}

const PreviewTimeOff = ({ previewData }: PreviewTimeOffProps) => {
  // Column definitions for the time off table
  const colDefs: ColDef[] = [
    {
      headerName: 'Employee',
      field: 'employee',
      minWidth: 200,
      flex: 1,
    },
    {
      headerName: 'Time-Off Dates',
      field: 'dates',
      minWidth: 180,
      valueFormatter: (params: ValueFormatterParams) => {
        const startDate = dayjs(params.data.startDate);
        const endDate = dayjs(params.data.endDate);
        if (startDate.isSame(endDate, 'day')) {
          return dayjs(params.data.startDate).format('MMM D, YYYY');
        }
        return `${dayjs(params.data.startDate).format('MMM D')} - ${dayjs(params.data.endDate).format('MMM D, YYYY')}`;
      },
    },
    {
      headerName: 'Time Off Hours',
      field: 'hours',
      minWidth: 120,
      valueFormatter: (params: ValueFormatterParams) => {
        return `${params.value}h`;
      },
    },
    {
      headerName: 'Policy Type',
      field: 'policyType',
      minWidth: 120,
      cellRenderer: (params: ICellRendererParams<TimeOffRow>) => {
        const { color, label } = getPolicyTypeBadgeProps(params.value);
        return (
          <Badge className="w-fit" variant="lighter" color={color} size="small">
            {label}
          </Badge>
        );
      },
    },
    {
      headerName: 'Policy Name',
      field: 'policyName',
      minWidth: 200,
      flex: 1,
    },
  ];

  const timeOffRows: TimeOffRow[] = React.useMemo(() => {
    if (!previewData?.timeOffRequests) return [];

    const rows: TimeOffRow[] = [];

    previewData.timeOffRequests.forEach((item) => {
      rows.push({
        employee: `${item.user.firstName} ${item.user.lastName}`,
        startDate: item.startDate,
        endDate: item.endDate,
        hours: item.totalHours || 0,
        policyType: item.timeOffPolicy.type,
        policyName: item.timeOffPolicy.name,
      });
    });

    return rows;
  }, [previewData]);

  if (timeOffRows.length === 0) {
    return (
      <div className="flex flex-grow flex-col">
        <section className="mt-32 flex flex-col items-center">
          <EmptyStateScheduleHoliday />

          <p className="mt-5 h-fit text-center text-sm text-soft-400">No time off records for this payroll period.</p>
        </section>
      </div>
    );
  }

  return (
    <div className="flex flex-grow flex-col">
      <UpdatedTable<TimeOffRow>
        colDefs={colDefs}
        rowData={timeOffRows}
        isLoading={false}
        emptyRowsText="No time off records for this payroll period"
        fitToWindow
      />
    </div>
  );
};

export default PreviewTimeOff;
