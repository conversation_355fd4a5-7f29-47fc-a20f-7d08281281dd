import { useDate } from 'hooks/useDate';
import { Payroll, PayrollPreviewResponse, PayrollStatus } from 'interfaces/payroll';
import { useEffect, useState } from 'react';
import {
  approvePayroll,
  exportCashRequirement,
  exportPayrollJournal,
  exportPaystubs,
  getPayrollDataStatic,
  listPayrolls,
} from 'services/payroll';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { addToast } from 'hooks/useToast';
import { showErrorToast } from 'utils/errorHandling';
import { useRouter } from 'next/router';
import CashRequirementModal from './CashRequirementModal';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import Button from '@/hammr-ui/components/button';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/utils/cn';
import { StatusBadge } from '../StatusBadge';
import dayjs from 'dayjs';
import { getPaperChecksUrl, downloadPaperChecksPdf } from '@/utils/checkApiHelpers';
import { SetCheckNumbersModal } from './SetCheckNumbersModal';
import EditPaydayPopover from '../EditPaydayPopover';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Menu, MenuContent, MenuItem, MenuTrigger } from '@/hammr-ui/components/Menu';
import Spinner from '@/hammr-ui/components/spinner';
import { RiCashLine, RiEdit2Line, RiFileList2Line, RiMoneyDollarCircleLine, RiPrinterLine } from '@remixicon/react';
import { apiRequest } from '@/utils/requestHelpers';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/hammr-ui/components/FlatTabs';
import PayrollEarnings from './PayrollEarnings';
import PreviewTimesheets from './PreviewTimesheets';
import PreviewTimeOff from './PreviewTimeOff';

const getPreviousPayroll = async ({
  payFrequency,
  periodStart,
  checkCompanyId,
  payday,
}: {
  payFrequency: string;
  periodStart: string;
  checkCompanyId: string;
  payday: string;
}) => {
  if (payFrequency === 'off_cycle') {
    return null;
  }

  const startDate = dayjs(periodStart).subtract(60, 'day').format('YYYY-MM-DD');

  const payrolls = await listPayrolls({
    companyId: checkCompanyId,
    payday_after: startDate,
    type: 'regular',
    status: 'paid',
  });

  const payrollsSorted = payrolls.results.sort((a, b) => (dayjs(b.payday).diff(dayjs(a.payday), 'day') > 0 ? -1 : 1));

  const payrollsBeforePayday = payrollsSorted.filter((_payroll) => {
    return dayjs(_payroll.payday).isBefore(dayjs(payday));
  });

  const lastPayrollBeforePayday = payrollsBeforePayday[payrollsBeforePayday.length - 1];

  return lastPayrollBeforePayday;
};

const PreviewPayroll = ({ payrollId, isDraft }: { payrollId: string; isDraft: boolean }) => {
  const { parseInOrgTimezone, dayjs, formatTimezone } = useDate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedMainTab, setSelectedMainTab] = useState<string>('earnings');
  const router = useRouter();
  const [showCashRequirementModal, setShowCashRequirementModal] = useState(false);
  const { user } = useAuth();
  const [previousPayroll, setPreviousPayroll] = useState<Payroll | null>(null);
  const [isPrintingChecks, setIsPrintingChecks] = useState(false);
  const [isSettingCheckNumbers, setIsSettingCheckNumbers] = useState(false);
  const [isEditPaydayModalOpen, setIsEditPaydayModalOpen] = useState(false);

  const {
    data: previewData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['payroll-data', payrollId],
    queryFn: async () => {
      return getPayrollDataStatic(payrollId);
    },
    enabled: !!payrollId,
    retry: false,
  });

  const formattedPeriodStart = dayjs(previewData?.payroll.period_start).format('MMM D');
  const formattedPeriodEnd = dayjs(previewData?.payroll.period_end).format('MMM D, YYYY');
  const payFrequencyHeader =
    previewData?.payroll.type === 'off_cycle' ? 'Offcycle' : previewData?.payroll.pay_frequency;

  const isManualPayrollItemsPresent = previewData?.payroll.items.some((item) => item.payment_method === 'manual');

  const canShowChecksLogicFromPayrollState =
    previewData?.payroll.status === 'paid' ||
    previewData?.payroll.status === 'pending' ||
    previewData?.payroll.status === 'processing' ||
    previewData?.payroll.status === 'partially_paid';

  const shouldShowChecksFeature = canShowChecksLogicFromPayrollState && isManualPayrollItemsPresent;

  useEffect(() => {
    const fetchPreviousPayroll = async () => {
      if (!previewData) return null;

      if (previewData.payroll.type === 'off_cycle') {
        return null;
      }

      const previousPayroll = await getPreviousPayroll({
        payFrequency: previewData?.payroll.pay_frequency,
        periodStart: previewData?.payroll.period_start,
        checkCompanyId: user?.checkCompanyId,
        payday: previewData?.payroll.payday,
      });

      setPreviousPayroll(previousPayroll);
    };

    fetchPreviousPayroll();
  }, [previewData]);

  const onSubmitPayrollCashRequirement = () => {
    if (previewData && parseFloat(previewData.payroll.totals.cash_requirement) > 0) {
      setShowCashRequirementModal(true);
    } else {
      onSubmitPayroll();
    }
  };

  const onSubmitPayroll = async () => {
    setIsSubmitting(true);

    let data;

    try {
      data = await approvePayroll(payrollId);

      addToast({
        title: 'Submitted Payroll',
        description: (
          <>
            Successfully submitted the payroll for the period{' '}
            <span className="font-medium">{`${formattedPeriodStart} - ${formattedPeriodEnd}`}</span>.
          </>
        ),
        type: 'success',
      });

      router.push(`/payroll/${payrollId}/details`);
    } catch (e) {
      if (e.type === 'payroll_approval_deadline_expired') {
        addToast({
          title: 'Missed Approval Deadline',
          description: (
            <>
              The approval deadline of{' '}
              {previewData?.payroll.approval_deadline
                ? parseInOrgTimezone(previewData?.payroll.approval_deadline).format('MMMM D, YYYY, [at] h:mmA z')
                : '-'}{' '}
              has passed.
              <br />
              <br />
              To proceed, update payday and try again.
            </>
          ),
          type: 'error',
        });
      } else {
        showErrorToast(e, 'Payroll Submit Error');
      }

      setIsSubmitting(false);
    }
  };

  const handlePrintChecks = async () => {
    try {
      setIsPrintingChecks(true);
      // First get the polling URL
      const pollingUrl = await getPaperChecksUrl(payrollId);

      // Download the PDF through our proxy
      await downloadPaperChecksPdf(pollingUrl);

      addToast({
        title: 'Success',
        description: 'Paper Checks have been downloaded.',
        type: 'success',
      });
    } catch (error) {
      showErrorToast(error, 'Error Printing Checks');
    } finally {
      setIsPrintingChecks(false);
    }
  };

  const handleSetCheckNumbers = () => {
    setIsSettingCheckNumbers(true);
  };

  const exportPayrollJournalMutation = useMutation({
    mutationFn: () => exportPayrollJournal(payrollId, user?.checkCompanyId, isDraft),
    onSuccess: () => {
      addToast({
        title: 'Success',
        description: 'Payroll Journal has been downloaded.',
        type: 'success',
      });
    },
    onError: (error) => {
      showErrorToast(error, 'Error Exporting Payroll Journal');
    },
  });

  const exportCashRequirementMutation = useMutation({
    mutationFn: () => exportCashRequirement(payrollId),
    onSuccess: () => {
      addToast({
        title: 'Success',
        description: 'Cash Requirement report has been downloaded.',
        type: 'success',
      });
    },
    onError: (error) => {
      showErrorToast(error, 'Error Exporting Cash Requirement');
    },
  });

  const reopenPayrollMutation = useMutation({
    mutationFn: () => apiRequest(`payrolls/${payrollId}/reopen`, { method: 'PATCH' }),
    onSuccess: () => {
      addToast({
        title: 'Payroll Reopened',
        description: 'The payroll has been successfully reopened.',
        type: 'success',
      });

      router.push(`/payroll/${payrollId}`);
    },
    onError: (error) => {
      showErrorToast(error, 'Error Reopening Payroll');
    },
  });

  const exportPaystubsMutation = useMutation({
    mutationFn: () =>
      exportPaystubs(
        payrollId,
        `paystubs-${dayjs(previewData?.payroll.period_start).format('MM-DD-YYYY')}-${dayjs(
          previewData?.payroll.period_end
        ).format('MM-DD-YYYY')}.pdf`
      ),
    onSuccess: () => {
      addToast({
        title: 'Success',
        description: 'Paystubs have been downloaded.',
        type: 'success',
      });
    },
    onError: (error) => {
      showErrorToast(error, 'Error Downloading Paystubs');
    },
  });

  const handleExportPayrollJournal = () => {
    exportPayrollJournalMutation.mutate();
  };

  const handleExportCashRequirement = () => {
    exportCashRequirementMutation.mutate();
  };

  const handleExportPaystubs = () => {
    exportPaystubsMutation.mutate();
  };

  if (error) {
    showErrorToast(error, 'Unable to Preview Payroll');
    router.push('/payroll');
    return;
  }

  const shouldShowDownloadPaystubs =
    ['paid', 'pending', 'processing', 'partially_paid'].includes(previewData?.payroll.status ?? '') &&
    previewData?.payroll.managed;

  return (
    <>
      <div className="flex items-end justify-between capitalize">
        <div className="flex gap-2">
          <PageHeader
            badge={<StatusBadge status={previewData?.payroll.status as PayrollStatus} className="self-center" />}
            title={
              previewData ? (
                `${formattedPeriodStart} - ${formattedPeriodEnd} (${payFrequencyHeader})`
              ) : (
                <SkeletonLoader className="h-8 w-80" />
              )
            }
            icon={<FlashlightLine />}
            noPadding
            breadcrumb={
              <Breadcrumbs>
                <BreadcrumbItem text="Payroll" onClick={() => router.push('/payroll')} />
                {isLoading ? (
                  <BreadcrumbItem text={<SkeletonLoader className="w-32" />} active />
                ) : (
                  <BreadcrumbItem text={previewData ? `${formattedPeriodStart} - ${formattedPeriodEnd}` : ''} active />
                )}
              </Breadcrumbs>
            }
            subtitle={
              previewData ? (
                <>
                  Payday: {dayjs(previewData?.payroll.payday).format('MMM DD, YYYY')}
                  {previewData?.payroll.approval_deadline && previewData?.payroll.managed && (
                    <>
                      {' | '}
                      Approval Deadline:{' '}
                      {formatTimezone(
                        parseInOrgTimezone(previewData?.payroll.approval_deadline ?? '').format('MMM DD, YYYY, h:mmA z')
                      )}
                    </>
                  )}
                </>
              ) : (
                <SkeletonLoader className="h-4 w-80" />
              )
            }
          />
        </div>

        <div className="flex gap-3">
          {isDraft && previewData && (
            <EditPaydayPopover
              isOpen={isEditPaydayModalOpen}
              setIsOpen={setIsEditPaydayModalOpen}
              payroll={previewData.payroll}
              callback={() => {
                refetch();
                setIsEditPaydayModalOpen(false);
              }}
              showFullButton={true}
            />
          )}
          <Menu>
            <MenuTrigger asChild>
              <Button variant="outline" color="neutral" afterContent={<ArrowDownSLine />}>
                {isDraft ? 'Reports' : 'Actions'}
              </Button>
            </MenuTrigger>
            <MenuContent className={cn('min-w-80')} align="end">
              <MenuItem
                beforeContent={
                  exportPayrollJournalMutation.isPending ? (
                    <Spinner className="size-4 animate-spin" />
                  ) : (
                    <RiFileList2Line className="size-4" />
                  )
                }
                title="Payroll Journal Report"
                onClick={() => {
                  handleExportPayrollJournal();
                }}
              />
              <MenuItem
                beforeContent={
                  exportCashRequirementMutation.isPending ? (
                    <Spinner className="size-4 animate-spin" />
                  ) : (
                    <RiCashLine className="size-4" />
                  )
                }
                title="Cash Requirement Report"
                onClick={() => {
                  handleExportCashRequirement();
                }}
              />
              {shouldShowDownloadPaystubs && (
                <>
                  <div className="my-1 h-px w-full bg-soft-200" />
                  <MenuItem
                    beforeContent={
                      exportPaystubsMutation.isPending ? (
                        <Spinner className="size-4 animate-spin" />
                      ) : (
                        <RiMoneyDollarCircleLine className="size-4" />
                      )
                    }
                    title="Download Paystubs"
                    onClick={() => {
                      handleExportPaystubs();
                    }}
                  />
                  <div className="my-1 h-px w-full bg-soft-200" />
                </>
              )}
              {shouldShowChecksFeature && (
                <MenuItem
                  beforeContent={
                    isPrintingChecks ? (
                      <Spinner className="size-4 animate-spin" />
                    ) : (
                      <RiPrinterLine className="size-4" />
                    )
                  }
                  title="Print Checks"
                  onClick={() => {
                    handlePrintChecks();
                  }}
                />
              )}
              {shouldShowChecksFeature && previewData?.payroll.managed && (
                <MenuItem
                  beforeContent={<RiEdit2Line className="size-4" />}
                  title="Set Check Numbers"
                  onClick={() => {
                    handleSetCheckNumbers();
                  }}
                />
              )}
            </MenuContent>
          </Menu>

          {previewData?.payroll.status === 'pending' && (
            <Button
              onClick={() => reopenPayrollMutation.mutate()}
              variant="outline"
              loading={reopenPayrollMutation.isPending}
            >
              Reopen Payroll
            </Button>
          )}

          {isDraft && (
            <>
              <Button
                onClick={() => {
                  router.push(`/payroll/${payrollId}`);
                }}
                variant="outline"
                color="neutral"
                disabled={isSubmitting || isLoading}
              >
                Edit Payroll
              </Button>
              <Button loading={isSubmitting} disabled={isLoading} onClick={onSubmitPayrollCashRequirement}>
                Submit Payroll
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="mt-6 flex flex-grow">
        <div className="flex flex-grow flex-col overflow-auto">
          {previewData && (
            <Tabs value={selectedMainTab} onValueChange={setSelectedMainTab} className="flex flex-grow flex-col">
              <TabsList>
                <TabsTrigger value="earnings">Earnings</TabsTrigger>
                {/* <TabsTrigger value="payments">Payments</TabsTrigger> */}
                <TabsTrigger value="timesheets">Timesheets</TabsTrigger>
                <TabsTrigger value="time_off">Time Off</TabsTrigger>
              </TabsList>

              <TabsContent value="earnings" className={selectedMainTab === 'earnings' ? 'flex flex-grow flex-col' : ''}>
                <PayrollEarnings
                  previewData={previewData}
                  previousPayroll={previousPayroll}
                  refetch={refetch}
                  isLoading={isLoading}
                  isSubmitting={isSubmitting}
                  handleExportCashRequirement={handleExportCashRequirement}
                  handleExportPayrollJournal={handleExportPayrollJournal}
                  isExportingCashRequirement={exportCashRequirementMutation.isPending}
                  isExportingPayrollJournal={exportPayrollJournalMutation.isPending}
                />
              </TabsContent>

              <TabsContent value="payments">
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="text-soft-600 text-lg font-medium">Payments</div>
                    <div className="mt-1 text-sm text-soft-400">Coming soon...</div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent
                value="timesheets"
                className={selectedMainTab === 'timesheets' ? 'flex flex-grow flex-col' : ''}
              >
                <PreviewTimesheets previewData={previewData} />
              </TabsContent>

              <TabsContent value="time_off" className={selectedMainTab === 'time_off' ? 'flex flex-grow flex-col' : ''}>
                <PreviewTimeOff previewData={previewData} />
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
      <CashRequirementModal
        isOpen={showCashRequirementModal}
        setIsOpen={setShowCashRequirementModal}
        onSubmit={onSubmitPayroll}
        cashRequirement={previewData?.payroll?.totals?.cash_requirement ?? ''}
      />
      {previewData && (
        <SetCheckNumbersModal
          isOpen={isSettingCheckNumbers}
          setIsOpen={setIsSettingCheckNumbers}
          previewData={previewData}
          onSuccess={() => {
            refetch();
          }}
        />
      )}
    </>
  );
};

export default PreviewPayroll;

function SkeletonLoader({ className }: { className?: string }) {
  return <div className={cn('h-5 w-24 animate-pulse rounded-full bg-weak-100', className)}></div>;
}
