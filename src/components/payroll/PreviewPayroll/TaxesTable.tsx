import { ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { PayrollPreviewResponse, PayrollResponse } from 'interfaces/payroll';
import { formatUSD } from 'utils/format';

const TaxesTable = ({
  previewData,
  visible,
  isLoading,
}: {
  previewData: PayrollPreviewResponse;
  visible: boolean;
  isLoading: boolean;
}) => {
  const colDefs = [
    {
      headerName: 'Tax description',
      field: 'tax',
    },
    {
      headerName: 'by Employees',
      field: 'employeeTax',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined || params.value === 0) {
          return ' ';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      headerName: 'by Company',
      field: 'companyTax',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined || params.value === 0) {
          return ' ';
        }
        return formatUSD.format(params.value);
      },
    },
  ];

  const allTaxesHash: Record<
    string,
    {
      employee: number;
      company: number;
    }
  > = {};

  previewData?.payroll.items.forEach((item) => {
    item.taxes.forEach((tax) => {
      const prevValue = allTaxesHash[tax.description] || {
        employee: 0,
        company: 0,
      };
      allTaxesHash[tax.description] = {
        ...prevValue,
        [tax.payer]: prevValue[tax.payer] + parseFloat(tax.amount),
      };
    });
  });

  const rows = Object.keys(allTaxesHash).map((key) => {
    return {
      tax: key,
      employeeTax: allTaxesHash[key].employee,
      companyTax: allTaxesHash[key].company,
    };
  });

  if (!visible) {
    return null;
  }

  return <UpdatedTable colDefs={colDefs} rowData={rows} isLoading={isLoading} />;
};

export default TaxesTable;
