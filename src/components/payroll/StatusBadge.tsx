import { cn } from '@/utils/cn';
import { PayrollStatus } from '@/interfaces/payroll';
import { getTextColor, getBackgroundColor } from './utils';

export const StatusBadge = ({ status, className }: { status: PayrollStatus; className?: string }) => {
  return (
    <div
      className={cn(
        getBackgroundColor(status),
        getTextColor(status),
        'flex h-6 items-center gap-2 self-end rounded-full px-2 py-1',
        className
      )}
    >
      {status && <span className="text-xs font-medium uppercase">{status.split('_').join(' ')}</span>}
    </div>
  );
};
