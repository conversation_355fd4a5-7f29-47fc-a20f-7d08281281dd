import { IDetailCellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { amountFormatterCellRenderer, formatHoursWorked } from './utils';
import EmployeeCellRenderer from './EmployeeCellRenderer';
import { getAllVisibleChildren } from '@/utils/table';
import { useCompany } from '@/hooks/useCompany';

const HourlyEmployees = ({
  employees,
  handleDeleteEarning,
  isLoading,
}: {
  employees: any[];
  handleDeleteEarning: (params: { id: string; type: string; amount: number; resourceId: string }) => void;
  isLoading: boolean;
}) => {
  const { company } = useCompany();

  const colDefs = [
    {
      headerName: 'Employee',
      field: 'employee',
      minWidth: 240,
      rowGroup: true,
      style: {
        overflow: 'visible',
      },
      cellRenderer: (params: ValueFormatterParams) => (
        <EmployeeCellRenderer
          params={params}
          handleDeleteEarning={() =>
            handleDeleteEarning({
              id: params.data.checkEmployeeId,
              type: params.data.type,
              amount: params.data.grossPay,
              resourceId: params.data.checkEmployeeId,
            })
          }
        />
      ),
    },
    {
      headerName: 'Gross pay',
      field: 'grossPay',
      valueFormatter: (params) => amountFormatterCellRenderer('grossPay', params, 'currency'),
    },
    {
      headerName: 'Reg Pay',
      field: 'regPay',
      valueFormatter: (params: ValueFormatterParams) => amountFormatterCellRenderer('regPay', params, 'currency'),
      cellRenderer: (params: IDetailCellRendererParams) => {
        if (!params.valueFormatted || params.value === 0 || params.valueFormatted === '$0.00') {
          return null;
        }
        return (
          <div className="flex flex-col items-start justify-center gap-1">
            <div className="text-sm">{params.valueFormatted}</div>
            {!params.node.group && (
              <div className="text-xs text-sub-600">
                {formatHoursWorked(params.data?.regHours)}h x ${params.data?.regRate.toFixed(2)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      headerName: 'OT Pay',
      field: 'otPay',
      valueFormatter: (params: ValueFormatterParams) => amountFormatterCellRenderer('otPay', params, 'currency'),
      cellRenderer: (params: IDetailCellRendererParams) => {
        if (!params.valueFormatted || params.value === 0 || params.valueFormatted === '$0.00') {
          return null;
        }
        return (
          <div className="flex flex-col items-start justify-center gap-1">
            <div className="text-sm">{params.valueFormatted}</div>
            {!params.node.group && (
              <div className="text-xs text-sub-600">
                {formatHoursWorked(params.data?.otHours)}h x ${parseFloat(params.data?.otRate).toFixed(2)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      headerName: 'DOT Pay',
      field: 'dotPay',
      valueFormatter: (params: ValueFormatterParams) => amountFormatterCellRenderer('dotPay', params, 'currency'),
      cellRenderer: (params: IDetailCellRendererParams) => {
        if (!params.valueFormatted || params.value === 0 || params.valueFormatted === '$0.00') {
          return null;
        }
        return (
          <div className="flex flex-col items-start justify-center gap-1">
            <div className="text-sm">{params.valueFormatted}</div>
            {!params.node.group && (
              <div className="text-xs text-sub-600">
                {formatHoursWorked(params.data?.dotHours)}h x ${parseFloat(params.data?.dotRate).toFixed(2)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      headerName: 'Drive Time Pay',
      field: 'driveTimePay',
      hide: !company?.timeTrackingSettings.isDriveTimeEnabled,
      valueFormatter: (params: ValueFormatterParams) => amountFormatterCellRenderer('driveTimePay', params, 'currency'),
      cellRenderer: (params: IDetailCellRendererParams) => {
        if (!params.valueFormatted || params.value === 0 || params.valueFormatted === '$0.00') {
          return null;
        }

        let aggregatedDriveTimeHours = 0;
        // driveTimeRate is set on the company level setting and is the same for all employees
        let driveTimeRate = 0;

        if (params.node.group) {
          getAllVisibleChildren(params.node).forEach((child: any) => {
            // we need to check for type number as there's also custom earnings data that won't have driveTime information
            if (typeof child.data.driveTimeHours === 'number') {
              aggregatedDriveTimeHours += child.data.driveTimeHours;
            }

            if (typeof child.data.driveTimeRate === 'number') {
              driveTimeRate = child.data.driveTimeRate;
            }
          });
        }

        return (
          <div className="items-left flex flex-col justify-center gap-1">
            <div className="text-sm">{params.valueFormatted}</div>
            {!params.node.group ? (
              <div className="text-xs text-sub-600">
                {formatHoursWorked(params.data?.driveTimeHours)}h x ${params.data.driveTimeRate.toFixed(2)}
              </div>
            ) : null}
          </div>
        );
      },
    },
  ];

  const groupHash = {};

  employees.forEach((employee) => {
    if (!groupHash[employee.employee]) {
      groupHash[employee.employee] = 1;
    } else {
      groupHash[employee.employee] = groupHash[employee.employee] + 1;
    }
  });

  const sortedEmployees = employees.sort((a, b) => {
    const empDiff = a.employee !== b.employee;
    const groupDiff = groupHash[a.employee] - groupHash[b.employee];
    if (groupDiff !== 0 && empDiff) {
      return groupDiff > 0 ? -1 : 1;
    } else if (empDiff) {
      return a.employee.localeCompare(b.employee);
    }

    if (a.employee.canDelete !== b.employee.canDelete) {
      if (a.employee.canDelete) {
        return 1;
      }
      if (b.employee.canDelete) {
        return -1;
      }
    }
  });

  return (
    <div className="mt-6">
      <UpdatedTable
        colDefs={colDefs}
        rowData={sortedEmployees}
        gridOptions={{
          groupDisplayType: 'custom',
        }}
        defaultColDef={{
          sortable: false,
        }}
        tableProps={{
          groupRemoveSingleChildren: true,
        }}
        disableCustomRendering
        isLoading={isLoading}
        getRowHeight={() => 64}
      />
    </div>
  );
};

export default HourlyEmployees;
