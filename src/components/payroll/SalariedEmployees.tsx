import { ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { formatUSD } from 'utils/format';
import { amountFormatterCellRenderer, populatePayload } from './utils';
import EmployeeCellRenderer from './EmployeeCellRenderer';
import { updateDraftPayroll } from '@/services/payroll';
import { useState } from 'react';
import { showErrorToast } from '@/utils/errorHandling';
import Spinner from '@/hammr-ui/components/spinner';
import { Payroll } from '@/interfaces/payroll';

const SalariedEmployees = ({
  employees,
  handleDeleteEarning,
  payroll,
  onEmployeeUnskip,
  isLoading,
}: {
  employees: any[];
  handleDeleteEarning: (params: { id: string; type: string; amount: number; resourceId: string }) => void;
  payroll: Payroll;
  onEmployeeUnskip: () => void;
  isLoading: boolean;
}) => {
  const [revertingSkipEmployeeId, setRevertingSkipEmployeeId] = useState('');

  const handleUnskip = async (checkEmployeeId: string) => {
    setRevertingSkipEmployeeId(checkEmployeeId);

    const employeesAlreadySkipped = employees.filter((emp) => emp.skip);
    const employeesToSkip = employeesAlreadySkipped.filter((emp) => emp.checkEmployeeId !== checkEmployeeId);
    const selectedEmployeeIds = employeesToSkip.map((emp) => emp.checkEmployeeId);

    const payload = populatePayload(payroll, selectedEmployeeIds);

    try {
      await updateDraftPayroll(payroll.id, {
        ...payload,
        salariedEmployeesToSkip: selectedEmployeeIds,
      });
      onEmployeeUnskip();
    } catch (err) {
      showErrorToast(err, 'Unable to unskip employee');
    } finally {
      setRevertingSkipEmployeeId('');
    }
  };

  const colDefs = [
    {
      headerName: 'Employee',
      field: 'employee',
      minWidth: 240,
      rowGroup: true,
      cellRenderer: (params: ValueFormatterParams) => (
        <EmployeeCellRenderer
          params={params}
          handleDeleteEarning={() =>
            handleDeleteEarning({
              id: params.data.checkEmployeeId,
              type: params.data.type,
              amount: params.data.grossPay,
              resourceId: params.data.checkEmployeeId,
            })
          }
        />
      ),
    },
    {
      headerName: 'Gross pay',
      field: 'grossPay',
      valueFormatter: (params: ValueFormatterParams) => {
        return amountFormatterCellRenderer('grossPay', params, 'currency');
      },
      cellRenderer: (params: ICellRendererParams) => {
        if (params.data?.skip) {
          return <div className="line-through">{params.valueFormatted}</div>;
        }

        return params.valueFormatted;
      },
    },
    {
      headerName: 'Annual salary',
      field: 'salary',
      valueFormatter: (params: ValueFormatterParams) => {
        if (params.value === undefined) {
          return null;
        }

        return formatUSD.format(params.value) + '/yr';
      },
    },
    {
      width: 90,
      pinned: 'right' as const,
      cellRenderer: (params: ICellRendererParams) => {
        if (revertingSkipEmployeeId === params.data?.checkEmployeeId) {
          return <Spinner className="text-strong-950" />;
        }

        if (params.data?.skip) {
          return (
            <div className="cursor-pointer text-strong-950" onClick={() => handleUnskip(params.data.checkEmployeeId)}>
              Unskip
            </div>
          );
        }

        return null;
      },
    },
  ];

  const groupHash = {};

  employees.forEach((employee) => {
    if (!groupHash[employee.employee]) {
      groupHash[employee.employee] = 1;
    } else {
      groupHash[employee.employee] = groupHash[employee.employee] + 1;
    }
  });

  const sortedEmployees = employees.sort((a, b) => {
    const empDiff = a.employee !== b.employee;
    const groupDiff = groupHash[a.employee] - groupHash[b.employee];
    if (groupDiff !== 0 && empDiff) {
      return groupDiff > 0 ? -1 : 1;
    } else if (empDiff) {
      return a.employee.localeCompare(b.employee);
    }

    if (a.employee.canDelete !== b.employee.canDelete) {
      if (a.employee.canDelete) {
        return 1;
      }
      if (b.employee.canDelete) {
        return -1;
      }
    }
  });

  return (
    <div className="mt-6">
      <UpdatedTable
        colDefs={colDefs}
        rowData={sortedEmployees}
        defaultColDef={{
          sortable: false,
          rowDrag: false,
          resizable: false,
          suppressMovable: true,
        }}
        tableProps={{
          groupRemoveSingleChildren: true,
        }}
        disableCustomRendering
        gridOptions={{
          groupDisplayType: 'custom',
          getRowClass: (params) => {
            if (params.data?.skip) {
              return 'text-soft-400 !text-soft-400';
            }
            return '';
          },
        }}
        isLoading={isLoading}
      />
    </div>
  );
};

export default SalariedEmployees;
