import { ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { amountFormatterCellRenderer } from './utils';
import EmployeeCellRenderer from './EmployeeCellRenderer';

const ContractorTable = ({
  employees,
  handleDeleteEarning,
  isLoading,
}: {
  employees: any[];
  handleDeleteEarning: (params: { id: string; type: string; amount: number; resourceId: string }) => void;
  isLoading: boolean;
}) => {
  const colDefs = [
    {
      headerName: 'Contractor',
      field: 'employee',
      minWidth: 240,
      rowGroup: true,
      cellRenderer: (params: ValueFormatterParams) => {
        return (
          <EmployeeCellRenderer
            params={params}
            handleDeleteEarning={() =>
              handleDeleteEarning({
                id: params.data.checkContractorId,
                type: params.data.type,
                amount: params.data.grossPay,
                resourceId: params.data.checkContractorId,
              })
            }
          />
        );
      },
    },
    {
      headerName: 'Payment amount',
      field: 'grossPay',
      valueFormatter: (params: ValueFormatterParams) => {
        return amountFormatterCellRenderer('grossPay', params, 'currency');
      },
    },
  ];

  const groupHash = {};

  employees.forEach((employee) => {
    if (!groupHash[employee.employee]) {
      groupHash[employee.employee] = 1;
    } else {
      groupHash[employee.employee] = groupHash[employee.employee] + 1;
    }
  });

  const sortedEmployees = employees.sort((a, b) => {
    const empDiff = a.employee !== b.employee;
    const groupDiff = groupHash[a.employee] - groupHash[b.employee];
    if (groupDiff !== 0 && empDiff) {
      return groupDiff > 0 ? -1 : 1;
    } else if (empDiff) {
      return a.employee.localeCompare(b.employee);
    }

    if (a.employee.canDelete !== b.employee.canDelete) {
      if (a.employee.canDelete) {
        return 1;
      }
      if (b.employee.canDelete) {
        return -1;
      }
    }
  });

  return (
    <div className="mt-6">
      <UpdatedTable
        colDefs={colDefs}
        rowData={sortedEmployees}
        gridOptions={{
          groupDisplayType: 'custom',
        }}
        defaultColDef={{
          sortable: false,
        }}
        tableProps={{
          groupRemoveSingleChildren: true,
        }}
        disableCustomRendering
        isLoading={isLoading}
      />
    </div>
  );
};

export default ContractorTable;
