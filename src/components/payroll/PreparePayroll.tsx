import { useDate } from 'hooks/useDate';
import { useMemo, useState } from 'react';
import HourlyEmployees from './HourlyEmployees';
import { formatUSD } from 'utils/format';
import SalariedEmployees from './SalariedEmployees';
import { useRouter } from 'next/router';
import EditPaydayPopover from './EditPaydayPopover';
import ContractorTable from './ContractorTable';
import { PayrollResponse, PayrollStatus, TransformedEmployeeSummary } from 'interfaces/payroll';
import AddEarningModal from './AddEarningModal';
import { useEmployees } from 'hooks/data-fetching/useEmployees';
import { useCompany } from 'hooks/useCompany';
import DeletePayrollModal from './DeletePayrollModal';
import { HOURLY_EARNING_TYPES, NON_HOURLY_EARNING_TYPES, TWO_PERCENT_SHAREHOLDER_EARNING_TYPES } from './constants';
import DeleteEarningModal from './DeleteEarningModal';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import MoneyDollarCircleFill from '@/hammr-icons/MoneyDollarCircleFill';
import CalendarCheckFill from '@/hammr-icons/CalendarCheckFill';
import CalendarScheduleFill from '@/hammr-icons/CalendarScheduleFill';
import Button from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import ErrorWarningFill from '@/hammr-icons/ErrorWarningFill';
import AlertFill from '@/hammr-icons/AlertFill';
import { getPayrollTotals } from './utils';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { StatusBadge } from './StatusBadge';
import { PayrollItemEarning } from '@/interfaces/payroll-item';
import SkipSalariedEmployees from './SkipSalariedEmployees';
import { cn } from '@/utils/cn';

// Import the TimesheetSummary interface from the payroll.ts file
import { TimesheetSummary, EmployeeSummary, Reimbursement } from '@/interfaces/payroll';

const PreparePayroll = ({
  payrollData,
  refreshPayroll,
  isLoading,
}: {
  payrollData: PayrollResponse;
  refreshPayroll: () => void;
  isLoading: boolean;
}) => {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState('hourly');
  const { dayjs, parseInOrgTimezone } = useDate();
  const [isEditPaydayModalOpen, setIsEditPaydayModalOpen] = useState(false);
  const [isAddEarningModalOpen, setIsAddEarningModalOpen] = useState(false);
  const {
    payroll,
    hourlyEmployeeSummaries,
    contractorSummaries,
    salariedSummaries,
    hasUnapprovedTimesheets,
    hasMissingPayrollUsers,
  } = payrollData ?? {};
  const { period_start, period_end, pay_frequency, totals, payday, approval_deadline, type } = payroll ?? {};
  const formattedPeriodStart = dayjs(period_start).format('MMM D');
  const formattedPeriodEnd = dayjs(period_end).format('MMM D, YYYY');
  const tabs = [
    { key: 'hourly', name: 'Hourly' },
    { key: 'salaried', name: 'Salaried' },
    { key: 'contractors', name: 'Contractors' },
  ];
  const [isDeletePayrollModalOpen, setIsDeletePayrollModalOpen] = useState(false);
  const [isDeleteEarningModalOpen, setIsDeleteEarningModalOpen] = useState(false);
  const [selectedEarning, setSelectedEarning] = useState<{
    id: string;
    type: string;
    amount: number;
    resourceId: string;
  } | null>(null);
  const [isSkipSalariedEmployeesModalOpen, setIsSkipSalariedEmployeesModalOpen] = useState(false);

  const mapRowData = (employees: EmployeeSummary[], type: 'hourly' | 'salaried' | 'contractors') => {
    let allEmployeeData: Array<TransformedEmployeeSummary> = [];
    employees?.map((employee) => {
      if (employee.compensationType === 'ANNUALLY') {
        allEmployeeData = [
          ...allEmployeeData,
          {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: employee.totalWages,
            salary: employee.salary,
            employmentType: type,
            type: 'Salary',
            skip: employee.skip,
            checkEmployeeId: employee.checkEmployeeId,
          },
        ];
      }

      if (employee.nonPWTimesheetsSummary) {
        const nonPwSummary = employee.nonPWTimesheetsSummary.map((timesheet: any) => {
          const driveTimeHours = timesheet.driveTimeMinutes / 60;
          const driveTimePay = timesheet.driveTimeWages;
          const driveTimeRate = driveTimePay / driveTimeHours;
          return {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: timesheet.totalWages,
            regRate: timesheet.hourlyWage,
            otRate: timesheet.otHourlyWage,
            regHours: timesheet.regularMinutes / 60,
            otHours: timesheet.overtimeMinutes / 60,
            otPay: timesheet.overtimeWages,
            dotHours: timesheet.doubleOvertimeMinutes / 60,
            dotPay: timesheet.doubleOvertimeWages,
            dotRate: timesheet.dotHourlyWage,
            regPay: timesheet.regularWages,
            type: employee.checkContractorId ? 'Default Earnings' : 'Default Hourly Rate',
            employmentType: type,
            canDelete: false,
            driveTimeHours,
            driveTimePay,
            driveTimeRate,
          };
        });

        if (nonPwSummary.length > 0) {
          allEmployeeData = [...allEmployeeData, ...nonPwSummary];
        }
      }

      if (employee.pwTimesheetsSummary) {
        const pwSummary = employee.pwTimesheetsSummary.map((timesheet: any, index: number) => {
          const driveTimeHours = timesheet.driveTimeMinutes / 60;
          const driveTimePay = timesheet.driveTimeWages;
          const driveTimeRate = driveTimePay / driveTimeHours;

          const showEmployeeSubtext =
            employee.nonPWTimesheetsSummary.length === 0 &&
            type === 'hourly' &&
            employee.pwTimesheetsSummary.length === 1;

          return {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: timesheet.totalWages,
            regRate: timesheet.hourlyWage,
            otRate: timesheet.otHourlyWage,
            regHours: timesheet.regularMinutes / 60,
            otHours: timesheet.overtimeMinutes / 60,
            otPay: timesheet.overtimeWages,
            dotHours: timesheet.doubleOvertimeMinutes / 60,
            dotPay: timesheet.doubleOvertimeWages,
            dotRate: timesheet.dotHourlyWage,
            regPay: timesheet.regularWages,
            type: timesheet.classificationName,
            employmentType: type,
            canDelete: false,
            driveTimeHours,
            driveTimePay,
            driveTimeRate,
            employeeSubtext: showEmployeeSubtext ? timesheet.classificationName : '',
          };
        });

        if (pwSummary.length > 0) {
          allEmployeeData = [...allEmployeeData, ...pwSummary];
        }
      }

      const employeeEarnings = payroll.items?.find((item) => item.employee === employee.checkEmployeeId);

      if (type === 'contractors') {
        const defaultRate = {
          id: employee.id,
          employee: employee.firstName + ' ' + employee.lastName,
          grossPay: 0,
          regRate: 0,
          otRate: 0,
          regHours: 0,
          otHours: 0,
          otPay: 0,
          regPay: 0,
          type: 'Default Earnings',
          employmentType: type,
          canDelete: false,
        };

        // reimbursements for contractors are handled differently
        const contractorReimbursements = employee.reimbursements
          .filter((_reimbursement: any) => _reimbursement.totalWages > 0)
          .map((reimbursement: any) => ({
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: reimbursement.totalWages,
            checkContractorId: employee.checkContractorId,
            type: 'Reimbursement',
            employmentType: type,
            canDelete: parseFloat(reimbursement.metadata?.per_diem) !== parseFloat(reimbursement.totalWages),
            showPerDiemTooltip: parseFloat(reimbursement.metadata?.per_diem) === parseFloat(reimbursement.totalWages),
          }));

        // contractors have one reimbursement item in the summaries and that's where the metadata is
        const contractorCustomReimbursement = employee.reimbursements[0].metadata.contractor_payment;

        const noTimesheetsAndReimbursements =
          employee.nonPWTimesheetsSummary.length === 0 && employee.reimbursements.length > 0;
        const noTimesheetsAndCustomReimbursement =
          employee.nonPWTimesheetsSummary.length === 0 && contractorCustomReimbursement;
        if (noTimesheetsAndReimbursements || noTimesheetsAndCustomReimbursement) {
          // sometimes there are reimbursements but no timesheets, we need to add a dub rate so that the ag-grid row expands and the reimbursement can be deleted
          allEmployeeData = [...allEmployeeData, defaultRate];
        }

        if (contractorCustomReimbursement) {
          const contractorCustomReimbursementData = {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: contractorCustomReimbursement,
            type: 'Custom Earnings',
            employmentType: type,
            canDelete: true,
            checkContractorId: employee.checkContractorId,
          };

          allEmployeeData = [...allEmployeeData, contractorCustomReimbursementData];
        }

        if (contractorReimbursements.length > 0) {
          allEmployeeData = [...allEmployeeData, ...contractorReimbursements];
        }
      }

      if (employeeEarnings?.reimbursements) {
        const addDefaultRate =
          employee.pwTimesheetsSummary.length === 0 &&
          employee.nonPWTimesheetsSummary.length === 0 &&
          type === 'hourly';
        if (addDefaultRate) {
          const defaultRate = {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: 0,
            regRate: employee.hourlyWage,
            otRate: employee.otHourlyWage,
            regHours: 0,
            otHours: 0,
            otPay: 0,
            regPay: 0,
            type: 'Default Hourly Rate',
            employmentType: type,
            canDelete: false,
          };
          allEmployeeData = [...allEmployeeData, defaultRate];
        }

        const reimbursements = employeeEarnings.reimbursements.map((reimbursement: Reimbursement) => {
          const isPerDiem = reimbursement.code === 'per_diem';

          return {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: parseFloat(reimbursement.amount),
            checkEmployeeId: employee.checkEmployeeId,
            type: 'Reimbursement',
            employmentType: type,
            canDelete: !isPerDiem,
            showPerDiemTooltip: isPerDiem,
          };
        });

        if (reimbursements.length > 0) {
          allEmployeeData = [...allEmployeeData, ...reimbursements];
        }
      }

      // extended to include two percent shareholder earnings
      const nonHourlyEarnings = employeeEarnings?.earnings?.filter((earning) => {
        const isNonHourly =
          NON_HOURLY_EARNING_TYPES.findIndex((_earningType) => _earningType.key === earning.type) > -1;

        const isDefaultSalaried = earning.type === 'salaried' && !earning.metadata?.is_custom_salaried;
        const isValidNonHourly = isNonHourly && !isDefaultSalaried;

        const isValidTwoPercent =
          TWO_PERCENT_SHAREHOLDER_EARNING_TYPES.findIndex((_earningType) => _earningType.key === earning.type) > -1;
        return isValidNonHourly || isValidTwoPercent;
      });

      if (nonHourlyEarnings?.length && nonHourlyEarnings.length > 0) {
        const nonHourlyEarningsData = nonHourlyEarnings.map((earning) => {
          const earningName =
            NON_HOURLY_EARNING_TYPES.find((_earningType) => _earningType.key === earning.type)?.label ||
            TWO_PERCENT_SHAREHOLDER_EARNING_TYPES.find((_earningType) => _earningType.key === earning.type)?.label;
          return {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: parseFloat(earning.amount),
            checkContractorId: employee.checkContractorId,
            checkEmployeeId: employee.checkEmployeeId,
            type: earningName,
            employmentType: type,
            canDelete: true,
          };
        });
        allEmployeeData = [...allEmployeeData, ...nonHourlyEarningsData];
      }

      const extraHourlyEarnings = employeeEarnings?.earnings?.filter((earning: any) => {
        return HOURLY_EARNING_TYPES.findIndex((_earningType) => _earningType.key === earning.type) > -1;
      });

      if (extraHourlyEarnings?.length && extraHourlyEarnings.length > 0) {
        const extraHourlyEarningsData = extraHourlyEarnings.map((earning: PayrollItemEarning) => {
          const earningName = HOURLY_EARNING_TYPES.find((_earningType) => _earningType.key === earning.type)?.label;
          const canDelete = earning.metadata.timeOffPolicyId ? false : true;

          return {
            id: employee.id,
            employee: employee.firstName + ' ' + employee.lastName,
            grossPay: parseFloat(earning.amount),
            checkContractorId: employee.checkContractorId,
            checkEmployeeId: employee.checkEmployeeId,
            type: earningName,
            employmentType: type,
            canDelete: canDelete,
            showPtoTooltip: earning.metadata.timeOffPolicyId ? true : false,
          };
        });
        allEmployeeData = [...allEmployeeData, ...extraHourlyEarningsData];
      }
    });

    return allEmployeeData;
  };

  const handleDeleteEarning = ({
    id,
    type,
    amount,
    resourceId,
  }: {
    id: string;
    type: string;
    amount: number;
    resourceId: string;
  }) => {
    setSelectedEarning({ id, type, amount, resourceId });
    setIsDeleteEarningModalOpen(true);
  };

  const mappedRowData = useMemo(() => {
    const employeeData = mapRowData(hourlyEmployeeSummaries, 'hourly');
    const contractorData = mapRowData(contractorSummaries, 'contractors');
    const salariedEmployeeData = mapRowData(salariedSummaries, 'salaried');
    return [...employeeData, ...contractorData, ...salariedEmployeeData];
  }, [hourlyEmployeeSummaries, contractorSummaries, salariedSummaries]);

  const { company } = useCompany();
  const allEmployees = useEmployees(Number(company?.id) ?? null, { simple: true });

  const eligibleUsers = allEmployees.filter((employee) => employee.checkEmployeeId || employee.checkContractorId);

  const mappedEmployeeSummaries = mappedRowData.filter((row) => row.employmentType === 'hourly');

  const mappedContractorSummaries = mappedRowData.filter((row) => row.employmentType === 'contractors');

  const mappedSalariedSummaries = mappedRowData.filter((row) => row.employmentType === 'salaried');

  const payFrequencyHeader = type === 'off_cycle' ? 'Offcycle' : pay_frequency;

  const salariedEmployees = mappedSalariedSummaries.filter((employee) => employee.type === 'Salary');

  const disabledUserIds = mappedSalariedSummaries.filter((row) => row.skip).map((row) => row.checkEmployeeId);

  // this calculation is done here using the totals from the payroll object and in the /preview page we rely on the totals from the payroll object but should be the same data
  const { totalGross } = getPayrollTotals({
    payroll: payroll ?? { items: [], contractor_payments: [] },
    allEmployees,
  });

  return (
    <>
      <div className="flex justify-between capitalize">
        <div className="flex gap-2">
          <div className="pl-8 pr-0 pt-5">
            <PageHeader
              noPadding
              title={
                payroll ? (
                  `${formattedPeriodStart} - ${formattedPeriodEnd} (${payFrequencyHeader})`
                ) : (
                  <SkeletonLoader className="h-8 w-80" />
                )
              }
              icon={<FlashlightLine />}
              breadcrumb={
                <Breadcrumbs>
                  <BreadcrumbItem text="Payroll" onClick={() => router.push('/payroll')} />
                  {isLoading ? (
                    <BreadcrumbItem text={<SkeletonLoader className="w-36" />} active />
                  ) : (
                    <BreadcrumbItem text={payroll ? `${formattedPeriodStart} - ${formattedPeriodEnd}` : ''} active />
                  )}
                </Breadcrumbs>
              }
            />
          </div>
          {payroll && <StatusBadge className="mb-2.5" status={payroll?.status as PayrollStatus} />}
        </div>

        <div className="flex items-end gap-2 pr-8 lg:gap-3">
          <Button
            variant="outline"
            onClick={() => setIsDeletePayrollModalOpen(true)}
            beforeContent={<DeleteBinLine />}
            color="error"
            disabled={isLoading}
          >
            Delete Payroll
          </Button>
          <Button
            onClick={() => {
              router.push(`/payroll/${payroll?.id}/preview`);
            }}
            disabled={isLoading}
          >
            Preview Payroll
          </Button>
        </div>
      </div>

      <div className="px-8">
        <div className="mt-6 flex justify-between rounded-2xl border border-soft-200 p-5">
          <div className="flex justify-center gap-3">
            <div className="flex flex-col justify-center">
              <KeyIcon icon={<MoneyDollarCircleFill />} color="grey" variant="lighter" />
            </div>
            <div className="flex flex-col justify-center gap-1">
              <div className="text-2xs font-medium uppercase text-soft-400">Total gross</div>
              <div className="text-base font-medium text-strong-950">
                {totalGross ? formatUSD.format(totalGross) : '-'}
              </div>
            </div>
          </div>

          <div className="flex justify-center gap-3">
            <div className="flex flex-col justify-center">
              <KeyIcon icon={<CalendarCheckFill />} color="grey" variant="lighter" />
            </div>
            <div className="flex flex-col justify-center gap-1">
              <div className="text-2xs font-medium uppercase text-soft-400">Pay day</div>
              <div className="flex gap-2 text-base font-medium text-strong-950">
                <span>{payday ? dayjs(payday).format('MMM D, YYYY') : '-'}</span>

                {payroll && (
                  <EditPaydayPopover
                    isOpen={isEditPaydayModalOpen}
                    setIsOpen={setIsEditPaydayModalOpen}
                    payroll={payroll}
                    callback={() => {
                      refreshPayroll();
                      setIsEditPaydayModalOpen(false);
                    }}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-center gap-3">
            <div className="flex flex-col justify-center">
              <KeyIcon icon={<CalendarScheduleFill />} color="grey" variant="lighter" />
            </div>
            <div className="flex flex-col justify-center gap-1">
              <div className="text-2xs font-medium uppercase text-soft-400">Approval deadline</div>
              <div className="text-base font-medium text-strong-950">
                {approval_deadline ? parseInOrgTimezone(approval_deadline).format('MMM D, YYYY h:mmA z') : '-'}
              </div>
            </div>
          </div>
        </div>

        {hasUnapprovedTimesheets && (
          <div className="mt-6 flex rounded-lg bg-warning-lighter p-2">
            <div className="mr-2">
              <AlertFill color="#EE6023" />
            </div>
            <div className="flex-grow">
              <div className="text-sm text-strong-950">
                <span className="font-medium leading-6">Unapproved timesheets for this period</span>
                <span> To include all employee hours in payroll, please approve them before proceeding.</span>
              </div>
            </div>

            <div
              onClick={() => {
                router.push('/timesheets');
              }}
              className="ml-3 mr-2 flex cursor-pointer items-center text-nowrap text-sm font-medium text-primary-base underline hover:text-primary-darker"
            >
              Review Timesheets
            </div>
          </div>
        )}

        {hasMissingPayrollUsers && (
          <div className="mt-6 flex rounded-lg bg-error-lighter p-2">
            <div className="relative mr-2">
              <ErrorWarningFill className="text-error-base" />
            </div>
            <div className="flex-grow">
              <div className="mb-1 text-sm font-medium text-strong-950">
                Employees not set up for payroll processing
              </div>
              <div className="text-sm text-strong-950">
                Some employees are not onboarded to payroll and will not receive payments if you proceed.
              </div>
              <div className="mb-1 text-sm text-strong-950">
                To pay them, finish their payroll onboarding process before proceeding.
              </div>
            </div>

            <div
              onClick={() => {
                router.push('/people');
              }}
              className="ml-3 mr-2 flex cursor-pointer items-center text-nowrap text-sm font-medium text-error-base underline hover:text-error-darker"
            >
              Onboard missing employees
            </div>
          </div>
        )}

        <div className="mt-7 flex justify-between">
          <div className="w-[700px]">
            <Tabs value={selectedTab ?? ''} onValueChange={(value) => setSelectedTab(value)}>
              <TabList>
                {tabs.map((tab) => (
                  <TabItem className="px-9" key={tab.key} value={tab.key}>
                    {tab.name}
                  </TabItem>
                ))}
              </TabList>
            </Tabs>
          </div>

          <div className="flex gap-3">
            {selectedTab === 'salaried' && (
              <Button
                disabled={isLoading}
                variant="outline"
                color="neutral"
                onClick={() => setIsSkipSalariedEmployeesModalOpen(true)}
              >
                Skip Employees
              </Button>
            )}

            <Button
              disabled={isLoading}
              beforeContent={<AddLine />}
              variant="outline"
              onClick={() => setIsAddEarningModalOpen(true)}
              color="neutral"
            >
              Add Earning
            </Button>
          </div>
        </div>

        {selectedTab === 'hourly' && (
          <HourlyEmployees
            employees={mappedEmployeeSummaries}
            handleDeleteEarning={handleDeleteEarning}
            isLoading={isLoading}
          />
        )}

        {selectedTab === 'salaried' && (
          <SalariedEmployees
            isLoading={isLoading}
            employees={mappedSalariedSummaries}
            handleDeleteEarning={handleDeleteEarning}
            payroll={payroll}
            onEmployeeUnskip={() => {
              refreshPayroll();
            }}
          />
        )}

        {selectedTab === 'contractors' && (
          <ContractorTable
            employees={mappedContractorSummaries}
            handleDeleteEarning={handleDeleteEarning}
            isLoading={isLoading}
          />
        )}
      </div>

      <AddEarningModal
        isOpen={isAddEarningModalOpen}
        setIsOpen={setIsAddEarningModalOpen}
        employees={eligibleUsers}
        callback={(payload) => {
          setIsAddEarningModalOpen(false);
          refreshPayroll();

          let targetTab = '';
          if (payload.isSalariedEmployee) {
            targetTab = 'salaried';
          } else if (payload.isContractor) {
            targetTab = 'contractors';
          } else {
            targetTab = 'hourly';
          }
          setSelectedTab(targetTab);
        }}
        payroll={payroll}
        disabledUserIds={disabledUserIds}
      />

      <DeleteEarningModal
        earning={selectedEarning}
        isOpen={isDeleteEarningModalOpen}
        setIsOpen={setIsDeleteEarningModalOpen}
        payroll={payroll}
        callback={() => {
          refreshPayroll();
        }}
        allEmployees={allEmployees}
      />
      {payroll && (
        <DeletePayrollModal
          payroll={payroll}
          isOpen={isDeletePayrollModalOpen}
          setIsOpen={setIsDeletePayrollModalOpen}
          callback={() => {
            setIsDeletePayrollModalOpen(false);
            router.push('/payroll');
          }}
        />
      )}

      <SkipSalariedEmployees
        isOpen={isSkipSalariedEmployeesModalOpen}
        setIsOpen={setIsSkipSalariedEmployeesModalOpen}
        callback={() => {
          setIsSkipSalariedEmployeesModalOpen(false);
          refreshPayroll();
        }}
        // we filter this in order to get the reimbursements and other earnings
        employees={salariedEmployees}
        payroll={payroll}
      />
    </>
  );
};

export default PreparePayroll;

function SkeletonLoader({ className }: { className?: string }) {
  return <div className={cn('h-5 w-24 animate-pulse rounded-full bg-weak-100', className)}></div>;
}
