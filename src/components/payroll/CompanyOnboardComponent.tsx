import { useEffect, useState } from 'react';
import { generateCheckCompanyOnboardLink } from 'services/company';
import importScript from 'hooks/importScript';
import { OnboardEvent } from 'interfaces/onboard';
import { useAuth } from 'hooks/useAuth';
import SetSignerTitleModal from '../company/SetSignerTitleModal';
import { logError } from 'utils/errorHandling';
import Button from '@/hammr-ui/components/button';
import EmptyStateTrainingAnalyis from '@/hammr-icons/EmptyStateTrainingAnalyis';

const CompanyOnboardComponent: React.FC<{
  status: 'needs_attention' | 'blocking';
}> = ({ status }) => {
  importScript('https://cdn.checkhq.com/onboard-initialize.js');

  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [closedSetupNotComplete, setClosedSetupNotComplete] = useState(false);
  const [signerTitle, setSignerTitle] = useState(null);
  const [email, setEmail] = useState(null);
  const [signerTitleModalOpen, setSignerTitleModalOpen] = useState(false);
  const [onboardOpen, setOnboardOpen] = useState(null);

  const handleCheckOnboard = async () => {
    setIsLoading(true);
    setClosedSetupNotComplete(false);
    if (!user.signer_title) {
      setSignerTitleModalOpen(true);
    } else {
      openCompanyOnboard();
    }
  };

  const openCompanyOnboard = async (userSignerTitle?: string, userEmail?: string) => {
    const onboardLink = await generateCheckCompanyOnboardLink(
      user?.checkCompanyId,
      user?.name,
      userSignerTitle || user.signer_title,
      userEmail || user?.email
    );

    try {
      const handler = window.Check.create({
        link: onboardLink.url,
        onClose: () => {
          setOnboardOpen(false);
          setIsLoading(false);
          window.location.reload();
        },
        onEvent: (event: OnboardEvent) => {
          if (event === 'check-onboard-app-loaded') {
            setIsLoading(false);
          }
        },
      });
      handler.open();
      setOnboardOpen(true);
    } catch (err) {
      logError(err);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (signerTitle) {
      setSignerTitleModalOpen(false);
      openCompanyOnboard(signerTitle, email);
    }
  }, [signerTitle, email]);

  useEffect(() => {
    if (onboardOpen === false) {
      setClosedSetupNotComplete(true);
    }
  }, [onboardOpen]);

  return (
    <div className="mt-32">
      <div className="flex h-full flex-col items-center justify-start">
        <EmptyStateTrainingAnalyis />
        <div className="mt-5 text-center text-sm text-soft-400">
          <div className="text-lg font-medium text-strong-950">Your company onboarding status is {status}</div>
          <div className="mt-2 text-soft-400">Before running payroll, follow the onboarding setup</div>
          <div className="text-soft-400">instructions by clicking the button below.</div>

          <div className="mt-5">
            <Button onClick={handleCheckOnboard}>Finish Onboarding</Button>
          </div>
        </div>
      </div>

      {signerTitleModalOpen && (
        <SetSignerTitleModal
          isOpen={signerTitleModalOpen}
          setIsOpen={setSignerTitleModalOpen}
          handleComplete={(signerTitle, email) => {
            setSignerTitle(signerTitle);
            setEmail(email);
          }}
        />
      )}
    </div>
  );
};

export default CompanyOnboardComponent;
