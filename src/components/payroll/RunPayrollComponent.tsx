import { useAuth } from 'hooks/useAuth';
import { useEffect, useState } from 'react';
import { getCheckCompany } from 'services/company';
import CompanyOnboardComponent from 'components/payroll/CompanyOnboardComponent';
import { logError, showErrorToast } from 'utils/errorHandling';
import { getPayDays } from 'services/pay-schedule';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween'; // ES 2015
import OffcyclePayrollModal from './OffcyclePayrollModal';
import PayrollHistory from './PayrollHistory';
import { createDraftPayroll, listPayrolls } from 'services/payroll';
import { useForm } from 'react-hook-form';
import { PaydayResponse, Payroll } from 'interfaces/payroll';
import { useCompany } from 'hooks/useCompany';
import { useDate } from 'hooks/useDate';
import { useRouter } from 'next/router';
import { checkRequestPaginated } from 'utils/requestHelpers';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import ControlledDateInput from '../elements/form/ControlledDateInput';
import Button from '@/hammr-ui/components/button';
import PayrollCharts from './PayrollCharts';
import { CheckCompany } from '@/interfaces/company';
import Spinner from '@/hammr-ui/components/spinner';
import { useMutation } from '@tanstack/react-query';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

const RunPayrollBody = (props: {
  selectedPayDay: any;
  setSelectedPayDay: any;
  setIsOffcyclePayrollModalOpen: any;
  selectedDraftPayroll?: Payroll;
  payrollHistory: Payroll[];
  paydayError: boolean;
}) => {
  const form = useForm({
    defaultValues: {
      payday: null,
    },
  });
  const { handleSubmit } = form;
  const { dayjs } = useDate();
  const { selectedPayDay, setIsOffcyclePayrollModalOpen, selectedDraftPayroll, paydayError } = props;
  const isDraft = !!selectedDraftPayroll;

  const payPeriodString = selectedPayDay
    ? `${dayjs(selectedPayDay?.period_start).format('MMM D')} - ${dayjs(selectedPayDay?.period_end).format(
        'MMM D, YYYY'
      )}`
    : '-';

  const router = useRouter();

  const createDraftPayrollMutation = useMutation({
    mutationFn(data: { payday: string }) {
      if (!selectedPayDay) {
        showErrorToast('Unable to run payroll');
        return;
      }

      return createDraftPayroll({
        payday: dayjs(data.payday).format('YYYY-MM-DD'),
        periodStart: selectedPayDay.period_start,
        periodEnd: selectedPayDay.period_end,
      });
    },
    onSuccess(res) {
      router.push(`payroll/${res.payrollId}`);
    },
  });

  return (
    <div>
      <div className="mt-6 flex flex-col gap-6 lg:flex-row">
        <div className="min-w-96 lg:w-96">
          <div className="rounded-2xl border border-soft-200 p-5">
            <div className="flex justify-between">
              <h2 className="font-medium text-strong-950">Upcoming Regular Payroll</h2>
              {isDraft && (
                <span className="ml-7 flex items-center rounded-xl bg-neutral-200 px-2 py-0.5 text-xs font-medium  text-neutral-800">
                  DRAFT
                </span>
              )}
            </div>

            <div className="mt-5 text-xs text-sub-600">Pay period</div>
            <div className="mt-1.5 text-sm text-strong-950">
              {paydayError ? 'No pay period found' : payPeriodString}
            </div>

            <div className="mt-1">
              <form onSubmit={handleSubmit((data) => createDraftPayrollMutation.mutate(data))}>
                <div className={`${!isDraft && 'mt-5'} flex items-start justify-between gap-2`}>
                  <div className="flex-grow lg:max-w-[230px]">
                    {!isDraft && (
                      <ControlledDateInput
                        control={form.control}
                        name="payday"
                        label="Payday"
                        disabled={paydayError || !selectedPayDay}
                        rules={{ required: 'Payday date is required' }}
                      />
                    )}

                    {isDraft && (
                      <div className="text-sm text-sub-600">
                        <div className="mt-5 text-xs text-sub-600">Payday</div>
                        <div className="mt-2 text-sm text-strong-950">
                          {dayjs(selectedDraftPayroll?.payday).format('MMM D, YYYY')}
                        </div>
                      </div>
                    )}
                  </div>

                  {isDraft ? (
                    <Button
                      className="mt-6"
                      type="button"
                      onClick={() => {
                        router.push(`payroll/${selectedDraftPayroll.id}`);
                      }}
                    >
                      Resume Payroll
                    </Button>
                  ) : (
                    <Button
                      className="mt-6"
                      disabled={
                        Object.keys(form.formState.errors).length > 0 ||
                        paydayError ||
                        !selectedPayDay ||
                        createDraftPayrollMutation.isPending
                      }
                      type="submit"
                      beforeContent={createDraftPayrollMutation.isPending ? <Spinner /> : undefined}
                    >
                      Run Payroll
                    </Button>
                  )}
                </div>
              </form>
            </div>
          </div>

          <div className="mt-5 flex items-center justify-between rounded-2xl border border-soft-200 p-5">
            <div className="font-medium text-strong-950">Off-Cycle Payroll</div>
            <div>
              <Button className="font-medium" variant="outline" onClick={() => setIsOffcyclePayrollModalOpen(true)}>
                Run Off-Cycle Payroll
              </Button>
            </div>
          </div>
        </div>

        <div className="flex-grow">
          <PayrollCharts />
        </div>
      </div>
    </div>
  );
};

const RunPayrollComponent = () => {
  const { user } = useAuth();
  const { company: hammrCompany } = useCompany();
  const [company, setCompany] = useState<CheckCompany | null>(null);
  const [paydayError, setPaydayError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPayDay, setSelectedPayDay] = useState<PaydayResponse | null>(null);
  const [isOffcyclePayrollModalOpen, setIsOffcyclePayrollModalOpen] = useState(false);
  const [payrollHistory, setPayrollHistory] = useState([]);

  const [selectedDraftPayroll, setSelectedDraftPayroll] = useState<Payroll | undefined>(undefined);

  useEffect(() => {
    const refreshPayrollData = async () => {
      setSelectedDraftPayroll(undefined);
      setIsLoading(true);
      setPaydayError(false);
      try {
        const company = await getCheckCompany(user?.checkCompanyId);

        if (company) {
          setCompany(company);
        }

        // The reason to pick 45 days is so we have enough buffer to choose pay schedules for weekly, bi-weekly, semi-monthly or even monthly pay cycles.
        const startDate = dayjs().subtract(45, 'day').format('YYYY-MM-DD');
        const payDays = await getPayDays(hammrCompany.paySchedule.checkPayScheduleId, startDate);

        if (payDays && payDays.length > 0) {
          const payDaysInThePast = payDays.filter((_payDay: PaydayResponse) => {
            return dayjs(_payDay.period_end).isBefore(dayjs());
          });

          const mostRecentPayDayInThePastIndex = payDaysInThePast.length - 1;

          // pick the most recent payDay in the past, as they are sorted it should be the last one
          const mostRecentPayDayInThePast = payDaysInThePast[mostRecentPayDayInThePastIndex];

          const payrolls = await listPayrolls({
            companyId: user?.checkCompanyId,
            payday_after: startDate,
            type: 'regular',
          });

          const allPayrolls = await checkRequestPaginated(payrolls, 1);

          const draftPayrolls = allPayrolls.filter((payroll: Payroll) => payroll.status === 'draft');

          // these are payrolls that have been paid, pending or processing
          const filteredPayrolls = allPayrolls.filter(
            (payroll: Payroll) =>
              (payroll.status === 'paid' || payroll.status === 'pending' || payroll.status === 'processing') &&
              payroll.pay_frequency === company.pay_frequency
          );

          let latestPaidPayrollPayDayIndex = -1;

          payDays.forEach((payDay: PaydayResponse, index: number) => {
            const selectedPaidPayroll = filteredPayrolls.find(
              (payroll: Payroll) =>
                payroll.period_end === payDay.period_end && payroll.period_start === payDay.period_start
            );
            if (selectedPaidPayroll) {
              latestPaidPayrollPayDayIndex = index;
            }
          });

          if (latestPaidPayrollPayDayIndex >= mostRecentPayDayInThePastIndex) {
            const selectedPayDay = payDays[latestPaidPayrollPayDayIndex + 1];

            const draftPayroll = draftPayrolls.find(
              (payroll: Payroll) =>
                payroll.period_end === selectedPayDay.period_end && payroll.period_start === selectedPayDay.period_start
            );

            if (draftPayroll) {
              setSelectedDraftPayroll(draftPayroll);
            }

            setSelectedPayDay(selectedPayDay);
          } else {
            const draftPayroll = draftPayrolls.find(
              (payroll: Payroll) =>
                payroll.period_end === mostRecentPayDayInThePast.period_end &&
                payroll.period_start === mostRecentPayDayInThePast.period_start
            );

            if (draftPayroll) {
              setSelectedDraftPayroll(draftPayroll);
            }
            setSelectedPayDay(mostRecentPayDayInThePast);
          }
        }
      } catch (err) {
        logError(err);
        showErrorToast(err, 'Payroll data failed to load.');
        setPaydayError(true);
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.checkCompanyId && hammrCompany?.paySchedule) {
      refreshPayrollData();
    }
  }, [user?.checkCompanyId, hammrCompany?.paySchedule]);

  useEffect(() => {
    const fetchPayrollHistory = async () => {
      const payrolls = await listPayrolls({
        companyId: user?.checkCompanyId,
      });

      // fetch the next payrolls to show up to 100 payrolls
      const allPayrolls = await checkRequestPaginated(payrolls, 3);
      setPayrollHistory(allPayrolls);
    };

    fetchPayrollHistory();
  }, [user?.checkCompanyId]);

  if (company?.onboard.status === 'blocking') {
    return (
      <>
        <PageHeader title="Payroll" icon={<FlashlightLine />} />
        <CompanyOnboardComponent status={company?.onboard.status} />
      </>
    );
  }

  return (
    <>
      <PageHeader noPadding title="Payroll" icon={<FlashlightLine />} />
      <div className="flex flex-grow flex-col">
        <RunPayrollBody
          selectedPayDay={selectedPayDay}
          setSelectedPayDay={setSelectedPayDay}
          setIsOffcyclePayrollModalOpen={setIsOffcyclePayrollModalOpen}
          selectedDraftPayroll={selectedDraftPayroll}
          payrollHistory={payrollHistory}
          paydayError={paydayError}
        />

        <PayrollHistory payrollHistory={payrollHistory} isLoading={isLoading} />
      </div>
      <OffcyclePayrollModal open={isOffcyclePayrollModalOpen} setOpen={setIsOffcyclePayrollModalOpen} />
    </>
  );
};

export default RunPayrollComponent;
