import { PayrollStatus } from '@/interfaces/payroll';
import { ValueFormatterParams } from '@ag-grid-community/core';
import { formatUSD } from 'utils/format';
import { getAllVisibleChildren } from 'utils/table';

export const amountFormatterCellRenderer = (name, params: ValueFormatterParams, type: 'number' | 'currency') => {
  if (params.node.group) {
    const children = getAllVisibleChildren(params.node).map((child) => {
      const value = child.data[name];
      if (value === undefined) {
        return 0;
      }
      return parseFloat(value);
    });
    const totalAmount = children.reduce((acc, child) => acc + child, 0);
    return type === 'currency' ? formatUSD.format(totalAmount) : totalAmount.toFixed(2);
  }

  if (params.value === undefined) {
    return null;
  }
  return type === 'currency' ? formatUSD.format(params.value) : params.value.toFixed(2);
};

/**
 * Right now the only people we skip are Salaried employees therefore we expect it to always be a string of Check string id's
 */
export const populatePayload = (payroll, checkIdsToSkip = []) => {
  const reimbursements = [];
  const earnings = [];
  const perDiemReimbursements = {};
  const additionalContractorPayments = {};

  payroll.contractor_payments?.forEach((contractor) => {
    if (parseFloat(contractor.metadata.per_diem) > 0) {
      perDiemReimbursements[contractor.contractor] = parseFloat(contractor.metadata.per_diem);
    }

    if (parseFloat(contractor.metadata.contractor_payment) > 0) {
      additionalContractorPayments[contractor.contractor] = parseFloat(contractor.metadata.contractor_payment);
    }
  });

  // Reimbursements and Contractor Payments are the only kind of additional payment available to contractors.
  payroll.contractor_payments?.forEach((contractor) => {
    if (contractor.reimbursement_amount > 0) {
      if (perDiemReimbursements[contractor.contractor]) {
        reimbursements.push({
          resourceId: contractor.contractor,
          amount: Math.max(0, contractor.reimbursement_amount - perDiemReimbursements[contractor.contractor]),
        });
      } else {
        reimbursements.push({
          resourceId: contractor.contractor,
          amount: contractor.reimbursement_amount,
        });
      }
    }

    if (additionalContractorPayments[contractor.contractor]) {
      // this we add as earning but it gets translated to contractor wages in the backend as contractors can't have earnings
      earnings.push({
        resourceId: contractor.contractor,
        amount: String(additionalContractorPayments[contractor.contractor]),
        type: 'contractor_payment',
        metadata: {},
      });
    }
  });

  // Employee Reimbursements
  payroll.items?.forEach((item) => {
    item.reimbursements?.forEach((reimbursement) => {
      const resourceId = item.employee ?? item.contractor;
      if (checkIdsToSkip.includes(resourceId)) {
        return;
      }

      reimbursements.push({
        resourceId,
        amount: reimbursement.amount,
        description: reimbursement.description,
        code: reimbursement.code,
      });
    });
  });

  /**
   * Earnings that are not part of their regular payroll
   */
  payroll.items?.forEach((item) => {
    item.earnings
      .filter((earning) => {
        const isDefaultSalaried = earning.type === 'salaried' && !earning.metadata?.is_custom_salaried;
        return earning.type && earning.type !== 'hourly' && !isDefaultSalaried;
      })
      .forEach((earning) => {
        const resourceId = item.employee ?? item.contractor;
        if (checkIdsToSkip.includes(resourceId)) {
          return;
        }
        earnings.push({
          resourceId,
          amount: earning.amount,
          hours: earning.hours,
          description: earning.description,
          type: earning.type,
          metadata: earning.metadata,
        });
      });
  });

  return { reimbursements, earnings };
};

export const getEmployeeEarnings = ({ item, allEmployees }) => {
  const employeeObject = allEmployees.find((employee) => employee.checkEmployeeId === item.employee);
  const employeeName = employeeObject ? employeeObject.firstName + ' ' + employeeObject.lastName : '';

  // These are needed in the PayBreakdown Table as the base earnings are separate from the additional earnings and reimbursements
  const baseEarnings = item.earnings
    .filter(
      (_earning) =>
        _earning.type === 'hourly' ||
        _earning.type === 'salaried' ||
        _earning.type === 'overtime' ||
        _earning.type === 'double_overtime' ||
        _earning.type === 'paid_holiday' ||
        _earning.type === 'rest_and_recovery' ||
        _earning.type === 'non_productive' ||
        (_earning.type === null && Boolean(_earning.earning_code))
    )
    .reduce((acc, curr) => {
      return acc + parseFloat(curr.amount);
    }, 0);

  const totalEarnings = item.earnings.reduce((acc, curr) => {
    return acc + parseFloat(curr.amount);
  }, 0);

  const totalReimbursements = item.reimbursements.reduce((acc, curr) => {
    return acc + parseFloat(curr.amount);
  }, 0);

  let totalEmployeeTaxes = 0;
  let totalCompanyTaxes = 0;

  // taxes are only available once the payroll is Previewed
  item.taxes?.forEach((tax) => {
    if (tax.payer === 'company') {
      totalCompanyTaxes += parseFloat(tax.amount);
    } else {
      totalEmployeeTaxes += parseFloat(tax.amount);
    }
  });

  const grossPay = totalEarnings + totalReimbursements;

  const additionalEarnings = item.earnings
    .filter(
      (_earning) =>
        _earning.type === 'bonus' ||
        _earning.type === 'commission' ||
        _earning.type === 'paycheck_tips' ||
        _earning.type === 'cash_tips' ||
        _earning.type === 'group_term_life' ||
        _earning.type === 'other_imputed' ||
        _earning.type === '2_percent_shareholder_benefits' ||
        _earning.type === '2_percent_shareholder_hsa'
    )
    .map((_earning) => parseFloat(_earning.amount))
    .reduce((acc, curr) => acc + curr, 0);

  const ptoSickLeave = item.earnings
    .filter((_earning) => _earning.type === 'pto' || _earning.type === 'sick')
    .map((_earning) => parseFloat(_earning.amount))
    .reduce((acc, curr) => acc + curr, 0);

  return {
    employeeId: item.employee,
    payrollItemId: item.id,
    employee: employeeName,
    paymentMethod: item.payment_method,
    baseEarnings: baseEarnings.toFixed(2),
    additionalEarnings: additionalEarnings.toFixed(2),
    totalReimbursements: totalReimbursements.toFixed(2),
    ptoSickLeave: ptoSickLeave.toFixed(2),
    grossPay: grossPay.toFixed(2),
    netPay: item.net_pay,
    totalEmployeeTaxes: totalEmployeeTaxes.toFixed(2),
    totalCompanyTaxes: totalCompanyTaxes.toFixed(2),
  };
};

export const getContractorEarnings = ({ item, allEmployees }) => {
  const contractorObject = allEmployees.find((contractor) => contractor.checkContractorId === item.contractor);

  const grossPay = parseFloat(item.amount) + parseFloat(item.reimbursement_amount);

  const contractorName = contractorObject ? contractorObject.firstName + ' ' + contractorObject.lastName : '';

  return {
    employeeId: item.contractor,
    payrollItemId: item.id,
    employee: contractorName,
    paymentMethod: item.payment_method,
    baseEarnings: item.amount,
    additionalEarnings: undefined,
    totalReimbursements: item.reimbursement_amount,
    ptoSickLeave: undefined,
    grossPay: grossPay,
    netPay: item.net_pay,
    type: 'Contractors',
  };
};

export const getPayrollTotals = ({ payroll, allEmployees }) => {
  const employeeData = payroll.items.map((item) => getEmployeeEarnings({ item, allEmployees }));

  const contractorData = payroll.contractor_payments.map((item) => getContractorEarnings({ item, allEmployees }));

  const employeeTotals = employeeData.reduce((acc, curr) => {
    Object.keys(curr).forEach((key) => {
      acc[key] = (acc[key] || 0) + parseFloat(curr[key]);
    });
    return acc;
  }, {});

  const contractorTotals = contractorData.reduce((acc, curr) => {
    Object.keys(curr).forEach((key) => {
      acc[key] = (acc[key] || 0) + parseFloat(curr[key]);
    });
    return acc;
  }, {});

  const employeeTotalPay = employeeTotals.grossPay || 0;
  const contractorTotalPay = contractorTotals.grossPay || 0;
  const companyTotalTaxes = employeeTotals.totalCompanyTaxes || 0;
  const employeeTotalTaxes = employeeTotals.totalEmployeeTaxes || 0;

  const totalGross = employeeTotalPay + contractorTotalPay;
  const totalNet = totalGross - companyTotalTaxes - employeeTotalTaxes;

  return { employeeTotals, contractorTotals, totalGross, totalNet };
};

export const getTextColor = (status: PayrollStatus) => {
  switch (status) {
    case 'draft':
      return 'text-faded-base';
    case 'processing':
      return 'text-information-base';
    case 'paid':
    case 'partially_paid':
      return 'text-success-base';
    case 'pending':
      return 'text-warning-base';
    case 'failed':
      return 'text-error-base';
    default:
      return '';
  }
};

export const getBackgroundColor = (status: PayrollStatus) => {
  switch (status) {
    case 'draft':
      return 'bg-faded-lighter';
    case 'processing':
      return 'bg-information-lighter';
    case 'paid':
    case 'partially_paid':
      return 'bg-success-lighter';
    case 'pending':
      return 'bg-warning-lighter';
    case 'failed':
      return 'bg-error-lighter';
    default:
      return '';
  }
};

// takes an hour number and returns a formatted string
// 1.00 -> 1
// 1.10 -> 1.1
// 1.11 -> 1.11
export const formatHoursWorked = (hours: number) => {
  if (!hours || hours === 0) {
    return '0';
  }

  const hoursString = hours.toFixed(2);
  const [whole, decimal] = hoursString.split('.');
  if (decimal === '00') {
    return whole;
  }
  if (decimal[1] === '0') {
    return `${whole}.${decimal[0]}`;
  }
  return hoursString;
};
