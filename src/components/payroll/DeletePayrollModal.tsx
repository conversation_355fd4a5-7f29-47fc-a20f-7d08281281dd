import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import dayjs from 'dayjs';
import { addToast } from 'hooks/useToast';
import { useState } from 'react';
import { deletePayroll } from 'services/payroll';
import { showErrorToast } from 'utils/errorHandling';

const DeletePayrollModal = ({ isOpen, setIsOpen, callback, payroll }) => {
  const onSubmit = async () => {
    try {
      await deletePayroll(payroll.id);
      callback();
      setIsOpen(false);
      addToast({
        title: 'Deleted Payroll',
        description: (
          <>
            Successfully deleted the payroll for the period{' '}
            <span className="font-medium">
              {dayjs(payroll.period_start).format('MMM D')} - {dayjs(payroll.period_end).format('MMM D, YYYY')}.
            </span>
          </>
        ),
        type: 'success',
      });
    } catch (error) {
      showErrorToast(error, 'Error deleting payroll');
    }
  };

  return (
    <ConfirmDialog
      open={isOpen}
      setOpen={setIsOpen}
      onConfirm={() => onSubmit()}
      data={[]}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      confirmButtonText="Delete"
      confirmButton={{
        color: 'error',
      }}
      title="Delete Payroll"
      subtitle={
        <div>
          {`You're about to delete the payroll for the period `}
          <span className="font-medium">
            {dayjs(payroll.period_start).format('MMM D')} - {dayjs(payroll.period_end).format('MMM D, YYYY')}
          </span>
          . Do you want to proceed?
        </div>
      }
    />
  );
};

export default DeletePayrollModal;
