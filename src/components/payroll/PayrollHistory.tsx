import { formatUSD } from '@/utils/format';
import { ColDef } from '@ag-grid-community/core';
import { cn } from '@/utils/cn';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { useDate } from 'hooks/useDate';
import { Payroll, PayrollStatus } from 'interfaces/payroll';
import { useRouter } from 'next/router';
import { formatLocaleUsa } from 'utils/dateHelper';
import { capitalize } from 'utils/stringHelper';
import { getTextColor, getBackgroundColor } from './utils';

export const StatusCell = ({ status }: { status: PayrollStatus }) => {
  return (
    <div className="relative flex h-full items-center justify-between">
      <span
        style={{ padding: '2px 8px' }}
        className={cn(getBackgroundColor(status), getTextColor(status), 'rounded-xl text-xs font-medium uppercase')}
      >
        {status.split('_').join(' ')}
      </span>
    </div>
  );
};

const PayrollHistory = ({ payrollHistory, isLoading }: { payrollHistory: Payroll[]; isLoading?: boolean }) => {
  const router = useRouter();
  const { formatUsa, dayjs } = useDate();
  const colDefs: ColDef[] = [
    {
      field: 'payPeriod',
      headerName: 'Pay Period',
      minWidth: 210,
      cellRenderer: (params) => {
        return `${formatUsa(params.data.periodStart)} - ${formatUsa(params.data.periodEnd)}`;
      },
    },
    {
      field: 'payDay',
      headerName: 'Payday',
      minWidth: 120,
      cellRenderer: (params) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      field: 'type',
      headerName: 'Type',
      valueFormatter: (params) => params.value.replace('_', '-'),
      minWidth: 120,
    },
    {
      field: 'payrollTotal',
      headerName: 'Total Payroll',
      minWidth: 120,
      valueFormatter: (params) => {
        if (params.value === 0) {
          return ' ';
        }
        return formatUSD.format(params.value);
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      minWidth: 130,
      maxWidth: 140,
      cellRenderer: (params) => {
        return <StatusCell status={params.value} />;
      },
    },
    {
      field: 'approved_at',
      headerName: 'Approved On',
      minWidth: 120,
      cellRenderer: (params) => {
        return params.value ? formatUsa(params.value, { showTime: true, showTimezone: true }) : '';
      },
    },
  ];

  const mapPayrolls = () => {
    return payrollHistory.map((payroll) => {
      return {
        periodStart: dayjs(payroll.period_start).valueOf(),
        periodEnd: dayjs(payroll.period_end).valueOf(),
        payPeriod: dayjs(payroll.period_start).valueOf(),
        payDay: dayjs(payroll.payday).valueOf(),
        type: capitalize(payroll.type),
        status: payroll.status,
        approved_at: payroll.approved_at ? dayjs(payroll.approved_at).valueOf() : 0,
        id: payroll.id,
        payrollTotal: parseFloat(payroll.totals?.liability ?? 0),
      };
    });
  };

  return (
    <div className="mt-6 flex flex-grow">
      <UpdatedTable
        colDefs={colDefs}
        rowData={mapPayrolls()}
        emptyRowsText="No payrolls found"
        enablePagination
        onRowClicked={(params) => {
          if (params.data.status === 'draft') {
            router.push(`/payroll/${params.data.id}`);
          } else {
            router.push(`/payroll/${params.data.id}/details`);
          }
        }}
        isLoading={isLoading}
      />
    </div>
  );
};

export default PayrollHistory;
