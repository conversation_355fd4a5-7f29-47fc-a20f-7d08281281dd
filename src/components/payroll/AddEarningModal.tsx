import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { patchPayrollEarning } from 'services/payroll';
import { HOURLY_EARNING_TYPES, NON_HOURLY_EARNING_TYPES, TWO_PERCENT_SHAREHOLDER_EARNING_TYPES } from './constants';
import { populatePayload } from './utils';
import { PatchPayrollEarningPayload } from 'interfaces/payroll';
import { ModalV2 } from '../elements/ModalV2';
import { NumberFieldV2 } from '../elements/form/NumberFieldV2';
import { TextField } from '../elements/form/TextField';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Combobox } from '@/hammr-ui/components/combobox';
import { getActiveEarningRate, validateAndNormalizeDate } from '@/utils/temporalUtils';
import { useCompany } from '@/hooks/useCompany';

const AddEarningModal = ({ isOpen, setIsOpen, callback, employees, payroll, disabledUserIds = [] }) => {
  const { addToast } = useToast();
  const { company } = useCompany();
  const {
    formState: { errors },
    control,
    handleSubmit,
    watch,
    setError,
    reset,
  } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  const earningType = watch('earningType');

  const isReimbursementSelected = earningType === 'reimbursement';
  const isHourlySelected = HOURLY_EARNING_TYPES.findIndex((_earningType) => _earningType.key === earningType) > -1;
  const isNonHourlySelected =
    NON_HOURLY_EARNING_TYPES.findIndex((_earningType) => _earningType.key === earningType) > -1;
  const isTwoPercentShareholderSelected =
    TWO_PERCENT_SHAREHOLDER_EARNING_TYPES.findIndex((_earningType) => _earningType.key === earningType) > -1;

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    let payload: PatchPayrollEarningPayload = populatePayload(payroll);

    const employeeObject = employees.find((_employee) => _employee.id === Number(data.userId));

    const { checkContractorId, checkEmployeeId, hourlyRate } = employeeObject;

    // Validate and normalize the payroll period start date
    const normalizedPeriodStart = validateAndNormalizeDate(payroll.period_start);

    // use timezone-aware logic to get the active earning rate at the payroll period start date
    const regularEarningRate = getActiveEarningRate(
      employeeObject.earningRates || [],
      company?.timezone || 'America/Los_Angeles',
      'REG',
      normalizedPeriodStart // Use the validated payroll period start date
    );

    // Check if the currently active earning rate is salaried (ANNUALLY)
    const isCurrentSalariedEmployee = regularEarningRate?.period === 'ANNUALLY';

    const selectedId = checkContractorId || checkEmployeeId;

    // validate for non-hourly earnings on employees who have hourly earnings in this payroll cycle
    const payrollItem = payroll.items.find((_item) => _item.employee === selectedId);

    if (isCurrentSalariedEmployee && isHourlySelected) {
      setError('earningType', {
        type: 'manual',
        message: 'Hourly earnings are not allowed for salaried employees.',
      });
      setIsProcessing(false);
      return;
    }

    if (checkContractorId) {
      // contractors can only be added reimbursements and contractor payments
      const isNonHourlyAndNotContractorPayment = isNonHourlySelected && earningType !== 'contractor_payment';
      if (isHourlySelected || isNonHourlyAndNotContractorPayment || isTwoPercentShareholderSelected) {
        setError('earningType', {
          type: 'manual',
          message: 'Reimbursements and Contractor Payments are the only earnings allowed for contractors.',
        });
        setIsProcessing(false);
        return;
      }

      if (earningType === 'contractor_payment') {
        const contractorHasCustomPaymentAlready = payload.earnings
          .filter((_earning) => _earning.type === 'contractor_payment')
          .some((_earning) => _earning.resourceId === checkContractorId);

        if (contractorHasCustomPaymentAlready) {
          setError('earningType', {
            type: 'manual',
            message: 'Custom Payment already exists for this contractor.',
          });
          setIsProcessing(false);
          return;
        }
      }
    } else if (earningType === 'contractor_payment') {
      // contractor payments can only be added for employees who have a contractor id
      setError('earningType', {
        type: 'manual',
        message:
          'Contractor payments are only allowed for 1099 contractors. To continue, please choose a contractor instead of a W-2 employee.',
      });
      setIsProcessing(false);
      return;
    }

    if (isHourlySelected) {
      payload = {
        ...payload,
        earnings: [
          ...payload.earnings,
          {
            resourceId: selectedId,
            description: data.description,
            type: earningType,
            hours: Number(data.hours),
          },
        ],
      };
    }

    if (isNonHourlySelected) {
      // calculatedHours is only needed for "salaried" earnings and we do it because Check requires
      // that all earnings of type "salaried" will need to have a value amount and number of hours
      const calculatedHourlyRate = isCurrentSalariedEmployee ? hourlyRate / regularEarningRate.weeklyHours : hourlyRate;

      // we have to set a minimum of 1 hour in case the amount is lower than their hourly rate as Check requires the hour amount to be non-zero
      const calculatedHours = Math.max(1, Math.round(Number(data.amount) / calculatedHourlyRate));

      payload = {
        ...payload,
        earnings: [
          ...payload.earnings,
          {
            resourceId: selectedId,
            description: data.description,
            amount: String(data.amount),
            type: earningType,
            metadata: {
              ...(earningType === 'salaried' && { is_custom_salaried: 'true' }),
            },
            ...(earningType === 'salaried' && { hours: calculatedHours }),
          },
        ],
      };
    }

    if (isReimbursementSelected) {
      payload = {
        ...payload,
        reimbursements: [
          ...payload.reimbursements,
          {
            resourceId: selectedId,
            amount: String(data.amount),
            // TODO -> add code as optional param
            // code: data.code,
            ...(data.description && { description: data.description }),
          },
        ],
      };
    }

    // there isnt a bulk endpoint to determine if someone is a 2% shareholder
    // what we could do is validate against this before submitting
    if (isTwoPercentShareholderSelected) {
      payload = {
        ...payload,
        earnings: [
          ...payload.earnings,
          {
            resourceId: selectedId,
            description: data.description,
            amount: String(data.amount),
            type: earningType,
          },
        ],
      };
    }

    try {
      await patchPayrollEarning(payroll.id, payload);

      addToast({
        title: 'Added Earning',
        description: (
          <div>
            Successfully added an earning for <strong className="font-medium">{employeeObject.fullName}</strong>.
          </div>
        ),
        type: 'success',
      });

      callback?.({ isSalariedEmployee: isCurrentSalariedEmployee, isContractor: !!checkContractorId });
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to add earning');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ModalV2 icon={<FlashlightLine />} title="Add Earning" open={isOpen} setOpen={setIsOpen}>
      <FormV2
        onCancel={() => setIsOpen(false)}
        onSubmit={handleSubmit(onSubmit)}
        isLoading={isProcessing}
        submitText="Add Earning"
      >
        <div className="space-y-4">
          <FormItem required error={!!errors['userId']}>
            <FormLabel>Employee</FormLabel>
            <FormControl>
              <Controller
                rules={{ required: 'Please select an employee' }}
                control={control}
                name="userId"
                render={({ field }) => (
                  <Combobox
                    items={employees.map((employee) => ({
                      value: String(employee.id),
                      label: employee.fullName,
                      disabled: disabledUserIds.includes(employee.checkEmployeeId),
                    }))}
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select an option"
                  />
                )}
              />
            </FormControl>
            <FormMessage>{errors['userId']?.message as string}</FormMessage>
          </FormItem>

          <FormItem required error={!!errors['earningType']}>
            <FormLabel>Earning type</FormLabel>
            <FormControl>
              <Controller
                rules={{ required: 'Please select an earning type' }}
                control={control}
                name="earningType"
                render={({ field }) => (
                  <Combobox
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select an option"
                    items={{
                      '': [{ label: 'Reimbursement/Per Diem', value: 'reimbursement' }],
                      HOURLY: [...HOURLY_EARNING_TYPES.map((option) => ({ value: option.key, label: option.label }))],
                      'NON-HOURLY': [
                        ...NON_HOURLY_EARNING_TYPES.map((option) => ({ value: option.key, label: option.label })),
                      ],
                      '2% SHAREHOLDER': [
                        ...TWO_PERCENT_SHAREHOLDER_EARNING_TYPES.map((option) => ({
                          value: option.key,
                          label: option.label,
                        })),
                      ],
                    }}
                  />
                )}
              />
            </FormControl>
            <FormMessage>{errors['earningType']?.message as string}</FormMessage>
          </FormItem>

          {(isNonHourlySelected || isReimbursementSelected || isTwoPercentShareholderSelected) && (
            <NumberFieldV2
              label="Amount"
              control={control}
              name="amount"
              rules={{ required: 'Please enter an amount' }}
              className="w-full"
              prefix="$"
              error={errors['amount']?.message}
              required
              isFloat
            />
          )}

          {isHourlySelected && (
            <NumberFieldV2
              label="Hours"
              name="hours"
              rules={{ required: 'Please enter hours' }}
              className="w-full"
              error={errors['hours']?.message}
              control={control}
              required
              placeholder="Enter number of hours"
              isFloat
            />
          )}

          <TextField
            name="description"
            control={control}
            label="Description"
            className="w-full"
            error={errors['description']?.message}
            placeholder="Enter description"
          />
        </div>
      </FormV2>
    </ModalV2>
  );
};

export default AddEarningModal;
