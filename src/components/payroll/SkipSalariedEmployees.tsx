import { useEffect, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { ModalV2 } from '../elements/ModalV2';
import { Input } from '@/hammr-ui/components/input';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import { Checkbox } from '@/hammr-ui/components/checkbox';
import Alert from '@/hammr-ui/components/Alert';
import Search2Line from '@/hammr-icons/Search2Line';
import { updateDraftPayroll } from '@/services/payroll';
import dayjs from 'dayjs';
import { populatePayload } from './utils';
import { uniqBy } from 'lodash';

const SkipSalariedEmployees = ({ isOpen, setIsOpen, callback, employees, payroll }) => {
  const { addToast } = useToast();

  const alreadySkippedEmployeeIds = employees.filter((emp) => emp.skip).map((emp) => emp.id);

  const [isProcessing, setIsProcessing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>(alreadySkippedEmployeeIds);

  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');

      if (alreadySkippedEmployeeIds.length !== selectedEmployees.length) {
        setSelectedEmployees(alreadySkippedEmployeeIds);
      }
    }
  }, [isOpen, alreadySkippedEmployeeIds]);

  const handleSubmit = async () => {
    setIsProcessing(true);

    try {
      const selectedEmployeeObjects = employees.filter((emp) => selectedEmployees.includes(emp.id));
      const selectedEmployeeIds = selectedEmployeeObjects.map((emp) => emp.checkEmployeeId);

      const payload = populatePayload(payroll, selectedEmployeeIds);

      await updateDraftPayroll(payroll.id, {
        ...payload,
        salariedEmployeesToSkip: selectedEmployeeIds,
      });

      addToast({
        title: 'Skipped Employees',
        description:
          selectedEmployeeObjects.length === 0 ? (
            'Successfully included salaried employees.'
          ) : (
            <div>
              Successfully skipped <strong className="font-medium">{selectedEmployeeObjects.length}</strong>{' '}
              {selectedEmployeeObjects.length === 1 ? 'employee' : 'employees'} from the{' '}
              <strong className="font-medium">
                {dayjs(payroll.period_start).format('MMM D')} - {dayjs(payroll.period_end).format('MMM D, YYYY')}
              </strong>{' '}
              payroll.
            </div>
          ),
        type: 'success',
      });

      callback?.();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update employee status');
    } finally {
      setIsProcessing(false);
    }
  };

  const filteredEmployees = employees.filter((emp) => emp.employee.toLowerCase().includes(searchTerm.toLowerCase()));

  const uniqueFilteredEmployees = [...new Set(filteredEmployees.map((emp) => emp.employee))].map((employee) =>
    filteredEmployees.find((emp) => emp.employee === employee)
  );

  const toggleEmployee = (employeeId: number) => {
    setSelectedEmployees((prev) => {
      if (prev.includes(employeeId)) {
        return prev.filter((id) => id !== employeeId);
      }
      return [...prev, employeeId];
    });
  };

  const skipText =
    selectedEmployees.length === 0
      ? `Skip Employees`
      : selectedEmployees.length === 1
        ? `Skip 1 Employee`
        : `Skip ${selectedEmployees.length} Employees`;

  return (
    <ModalV2 icon={<FlashlightLine />} title="Skip Salaried Employees" open={isOpen} setOpen={setIsOpen}>
      <FormV2
        onSubmit={handleSubmit}
        onCancel={() => setIsOpen(false)}
        isLoading={isProcessing}
        submitText={isProcessing ? 'Updating...' : skipText}
      >
        <Alert>
          Skipping a salaried employee will remove any custom earnings that may have been added to them for this payroll
          period.
        </Alert>

        <div className="mt-5 space-y-4">
          <div className="text-sm font-medium text-strong-950">Employees</div>

          <div className="rounded-16 border border-soft-200 p-1">
            <Input
              beforeContent={<Search2Line className="size-5" />}
              noBorder
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {uniqueFilteredEmployees.length === 0 ? (
              <div className="text-strong-500 h-[400px] pt-3 text-center text-sm">No employees found</div>
            ) : (
              <div className="h-[400px] space-y-2 overflow-auto pt-1">
                {uniqueFilteredEmployees.map((employee) => (
                  <div key={employee.id} className="hover:bg-strong-50 flex items-center gap-3 rounded px-3 py-2">
                    <label key={employee.id} className="flex items-center gap-3">
                      <Checkbox
                        checked={selectedEmployees.includes(employee.id)}
                        onCheckedChange={() => toggleEmployee(employee.id)}
                      />
                      <div className="flex-1 text-sm">{employee.employee}</div>
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </FormV2>
    </ModalV2>
  );
};

export default SkipSalariedEmployees;
