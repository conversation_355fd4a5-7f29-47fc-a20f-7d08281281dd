import dayjs from 'dayjs';
import { addToast } from 'hooks/useToast';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { updateDraftPayrollCheck } from 'services/payroll';
import { showErrorToast } from 'utils/errorHandling';
import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { Calendar } from '@/hammr-ui/components/calendar';
import Button from '@/hammr-ui/components/button';
import CompactButton from '@/hammr-ui/components/CompactButton';
import { Payroll, PayrollPreviewResponseCheck } from '@/interfaces/payroll';
import { RiPencilLine } from '@remixicon/react';

interface EditPaydayPopoverProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  callback: () => void;
  payroll: PayrollPreviewResponseCheck | Payroll;
  showFullButton?: boolean;
}

const EditPaydayPopover = ({
  isOpen,
  setIsOpen,
  callback,
  payroll,
  showFullButton = false,
}: EditPaydayPopoverProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm({
    defaultValues: {
      payday: dayjs(payroll.payday).toDate(),
    },
  });
  const {
    formState: { errors },
    reset,
  } = form;

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen]);

  const onSubmit = async (data: { payday: Date }) => {
    setIsLoading(true);
    const { payday } = data;
    try {
      await updateDraftPayrollCheck(payroll.id, {
        payday: dayjs(payday).format('YYYY-MM-DD'),
      });
      addToast({
        title: 'Updated Payday',
        description: (
          <>
            Successfully updated the payday to{' '}
            <span className="font-medium">{dayjs(payday).format('MMM D, YYYY')}</span>.
          </>
        ),
        type: 'success',
      });
      callback();
      setIsOpen(false);
    } catch (error) {
      showErrorToast(error, 'Unable to update payday');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Popover
      open={isOpen}
      onOpenChange={(open) => {
        setIsOpen(open);
      }}
    >
      <PopoverTrigger asChild>
        {showFullButton ? (
          <Button variant="outline" color="primary" size="small" afterContent={<RiPencilLine className="size-4" />}>
            Edit Payday
          </Button>
        ) : (
          <CompactButton size="large" variant="ghost">
            <RiPencilLine className="text-primary-base" />
          </CompactButton>
        )}
      </PopoverTrigger>
      <PopoverContent className="max-h-[386px] w-[276px]">
        <Calendar
          isLoading={isLoading}
          error={errors.payday?.message as string}
          mode="single"
          onCancel={() => {
            setIsOpen(false);
          }}
          value={form.getValues('payday') ?? null}
          onApply={(value) => {
            onSubmit({ payday: value });
          }}
        />
      </PopoverContent>
    </Popover>
  );
};

export default EditPaydayPopover;
