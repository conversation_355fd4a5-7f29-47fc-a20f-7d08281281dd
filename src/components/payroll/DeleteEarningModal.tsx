import { addToast } from 'hooks/useToast';
import { useState } from 'react';
import { patchPayrollEarning } from 'services/payroll';
import { showErrorToast } from 'utils/errorHandling';
import { populatePayload } from './utils';
import { EmployeeSummary, PatchPayrollEarningPayload, Payroll } from 'interfaces/payroll';
import { HOURLY_EARNING_TYPES, NON_HOURLY_EARNING_TYPES, TWO_PERCENT_SHAREHOLDER_EARNING_TYPES } from './constants';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { HammrUser } from '@/interfaces/user';

type DeleteEarningModalProps = {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  callback: () => void;
  earning: { id: string; type: string; amount: number; resourceId: string };
  payroll: Payroll;
  allEmployees: HammrUser[];
};
const DeleteEarningModal = ({
  isOpen,
  setIsOpen,
  callback,
  earning,
  payroll,
  allEmployees,
}: DeleteEarningModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);

  if (!earning) return null;

  const { firstName, lastName } = allEmployees.find(
    (_employee) => _employee.checkContractorId === earning.id || _employee.checkEmployeeId === earning.id
  ) ?? { firstName: '', lastName: '' };

  const employeeName = `${firstName} ${lastName}`;

  const onSubmit = async () => {
    setIsDeleting(true);

    let payload: PatchPayrollEarningPayload = populatePayload(payroll);

    if (earning.type === 'Reimbursement') {
      let updatedReimbursements;

      if (earning.id.startsWith('ctr_')) {
        // when dealing with contractors, as reimbursements are added fully we won't check if the values are the same
        // this will just remove all the reimbursements for the contractor
        updatedReimbursements = payload.reimbursements.filter(
          (_reimbursement) => _reimbursement.resourceId !== earning.resourceId
        );
      } else {
        updatedReimbursements = payload.reimbursements.filter((_reimbursement) => {
          const idsMatch = _reimbursement.resourceId === earning.resourceId;
          const amountsMatch = parseFloat(_reimbursement.amount) === earning.amount;
          return !idsMatch || !amountsMatch;
        });
      }

      payload = { ...payload, reimbursements: updatedReimbursements };
    } else {
      const ALL_EARNING_TYPES = HOURLY_EARNING_TYPES.concat(NON_HOURLY_EARNING_TYPES).concat(
        TWO_PERCENT_SHAREHOLDER_EARNING_TYPES
      );
      let typeKey = ALL_EARNING_TYPES.find((_earningType) => _earningType.label === earning.type)?.key;

      if (earning.type === 'Custom Earnings') {
        typeKey = 'contractor_payment';
      }

      const updatedEarnings = payload.earnings.filter(
        (_earning) =>
          _earning.resourceId !== earning.resourceId ||
          parseFloat(_earning.amount) !== earning.amount ||
          _earning.type !== typeKey
      );

      payload = { ...payload, earnings: updatedEarnings };
    }

    try {
      await patchPayrollEarning(payroll.id, payload);

      addToast({
        title: 'Deleted Earning',
        description: (
          <>
            We have successfully deleted a <span className="font-medium">${earning.amount} </span>
            earning for <span className="font-medium">{employeeName}</span>.
          </>
        ),
        type: 'success',
      });

      callback?.();
      setIsOpen(false);
    } catch (err) {
      showErrorToast(err, 'Unable to delete earning');
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ConfirmDialog
      open={isOpen}
      setOpen={setIsOpen}
      onConfirm={() => onSubmit()}
      data={earning}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      confirmButtonText="Delete"
      confirmButton={{
        color: 'error',
      }}
      title="Delete Earning"
      subtitle={
        <div>
          {`You're about to delete a `}
          <span className="font-medium">${earning.amount} </span>
          earning for <span className="font-medium">{employeeName}</span>. Do you want to want to proceed?
        </div>
      }
    />
  );
};

export default DeleteEarningModal;
