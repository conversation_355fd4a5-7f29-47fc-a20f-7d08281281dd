'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/hammr-ui/components/card';
import { BarChart } from '../charts';

import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { listPayrolls } from '@/services/payroll';
import { checkRequestPaginated } from '@/utils/requestHelpers';
import { useAuth } from '@/hooks/useAuth';
import EmptyStateFinanceBanking from '@/hammr-icons/EmptyStateFinanceBanking';
import { Select, SelectItem } from '@/hammr-ui/components/select';

const options = [
  { label: 'Last 3 Months', days: 90 },
  { label: 'Last 6 Months', days: 180 },
  { label: 'Last 12 Months', days: 365 },
];

const PayrollCharts = () => {
  const { user } = useAuth();
  const [chartSize, setChartSize] = useState(options[0]);
  const [paidPayrolls, setPaidPayrolls] = useState([]);
  const [showEmptyState, setShowEmptyState] = useState(false);

  useEffect(() => {
    const setPayrolls = async () => {
      const payrolls = await listPayrolls({
        companyId: user?.checkCompanyId,
        payday_after: dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
        status: 'paid',
      });

      if (payrolls.results.length) {
        setShowEmptyState(false);
        setPaidPayrolls(payrolls.results);

        const allPayrolls = await checkRequestPaginated(payrolls, 5);

        setPaidPayrolls(allPayrolls);
      } else {
        setShowEmptyState(true);
      }
    };

    setPayrolls();
  }, []);

  const visiblePayrolls = paidPayrolls
    .filter((payroll) => dayjs(payroll.period_end).isAfter(dayjs().subtract(chartSize.days, 'days')))
    .sort((a, b) => (dayjs(a.period_end).isAfter(dayjs(b.period_end)) ? 1 : -1));

  const mappedChartData = visiblePayrolls.map((payroll) => ({
    period: `${dayjs(payroll.period_start).format('M/DD')} - ${dayjs(payroll.period_end).format('M/DD')}`,
    date: payroll.approved_at,
    amount: parseFloat(payroll.totals?.employee_gross || '0') + parseFloat(payroll.totals?.contractor_gross || '0'),
  }));

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-col items-stretch space-y-0 p-0 sm:flex-row">
        <div className="flex flex-1 flex-col justify-center gap-1 px-4 py-5">
          <CardTitle>Payroll History</CardTitle>
        </div>
        <div className="mr-3.5 flex pt-3.5">
          <Select
            value={chartSize.days.toString()}
            onChange={(value) => setChartSize(options.find((o) => o.days === Number(value)))}
            className="h-[32px]  text-sub-600"
            disabled={showEmptyState}
          >
            {options.map((size) => (
              <SelectItem key={size.days} value={size.days.toString()}>
                {size.label}
              </SelectItem>
            ))}
          </Select>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {showEmptyState && (
          <div className="mt-8 flex flex-col items-center justify-center">
            <div className="">
              <EmptyStateFinanceBanking height={108} width={108} />
            </div>
            <div className="mt-5 text-center text-sm">
              <div className="text-soft-400">There is no payroll history yet.</div>
              <div className="text-soft-400">Run a payroll to see the payroll history.</div>
            </div>
          </div>
        )}

        {!showEmptyState && <BarChart mappedChartData={mappedChartData} />}
      </CardContent>
    </Card>
  );
};

export default PayrollCharts;
