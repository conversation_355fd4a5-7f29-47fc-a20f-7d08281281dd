import { Dispatch, SetStateAction, useState } from 'react';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';
import PencilLine from '@/hammr-icons/PencilLine';

interface MultiScheduleUpdateModalProps {
  modalOpen: boolean;
  setModalOpen: Dispatch<SetStateAction<boolean>>;
  processUpdate: (all: boolean) => void;
  callback: () => void;
}

export default function MultiScheduleUpdateModal({
  modalOpen,
  setModalOpen,
  processUpdate,
  callback,
}: MultiScheduleUpdateModalProps) {
  const invokeUpdates = async (shouldUpdateAll) => {
    try {
      await processUpdate(shouldUpdateAll);
      // should call refresh (callback) and then close modal
      if (callback) {
        callback();
      }
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update scheduled event(s)');
    } finally {
      setModalOpen(false);
    }
  };

  const [shouldUpdateDays, setShouldUpdateDays] = useState('this-day');

  return (
    <ConfirmDialog
      open={modalOpen}
      setOpen={setModalOpen}
      onConfirm={async () => {
        invokeUpdates(shouldUpdateDays === 'all-days');
      }}
      icon={<PencilLine className="text-sub-600" />}
      title="Edit Schedule Event"
      confirmButtonText="Save"
    >
      <h3 className="text-sm font-medium text-strong-950">What schedule event do you want to update?</h3>

      <RadioGroup value={shouldUpdateDays} onValueChange={setShouldUpdateDays} className="mt-3">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="this-day" id="this-day" />
          <Label className="font-normal" htmlFor="this-day">
            This selected day
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="all-days" id="all-days" />
          <Label className="font-normal" htmlFor="all-days">
            All days
          </Label>
        </div>
      </RadioGroup>
    </ConfirmDialog>
  );
}
