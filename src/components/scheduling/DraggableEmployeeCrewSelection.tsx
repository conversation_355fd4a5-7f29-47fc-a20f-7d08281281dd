import { useState } from 'react';
import { cn } from '@/utils/cn';
import { Crew } from 'interfaces/crew';
import { TabContent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';

const ONE_DAY = 86400000; // 24 hours in milliseconds

interface DraggableCrewElementProps {
  crew: any;
  crewId: number;
  objectName: string;
  alreadyAssigned: boolean;
  shouldManage?: boolean;
}

function DraggableCrewElement({
  crew,
  crewId,
  objectName,
  alreadyAssigned,
  shouldManage = false,
}: DraggableCrewElementProps) {
  const { name: crewName } = crew;

  const crewMembers = crew.crewMembers.map((member) => {
    return {
      fullName: `${member.crewMemberUser.firstName} ${member.crewMemberUser.lastName}`,
      crewWasDragged: true,
      crewName,
      ...member.crewMemberUser,
    };
  });

  return (
    <div
      className={cn(`draggable-${objectName}`, {
        'line-through': alreadyAssigned && shouldManage,
        'cursor-pointer': (!alreadyAssigned && shouldManage) || !shouldManage,
        'cursor-default': alreadyAssigned && shouldManage,
      })}
      title={`${crewName}`}
      data-event={JSON.stringify(crewMembers)}
      data-crew-id={crewId}
      draggable={alreadyAssigned && shouldManage ? false : true}
      onDragStart={(e) => {
        const draggedElement = e.target as HTMLElement;
        const extractedData = draggedElement.getAttribute('data-event');

        e.dataTransfer.setData('application/json', extractedData);
      }}
    >
      {`${crewName}`}
    </div>
  );
}
interface DraggableUserElementProps {
  user: any; // really should be a HammrUser with scheduling data
  objectName: string; // 'employee' or 'crew' / etc
  alreadyAssigned: boolean;
  className?: string;
  shouldManage?: boolean;
}

function DraggableUserElement({
  user,
  objectName,
  alreadyAssigned,
  className = '',
  shouldManage = false,
}: DraggableUserElementProps) {
  const { firstName, lastName, id: userId, email, phone, position } = user;

  return (
    <div
      className={cn(
        `${className} draggable-${objectName} flex flex-col content-center justify-center border-b border-soft-200 px-3 py-4 text-sm text-sub-600`,
        alreadyAssigned && shouldManage && 'line-through',
        ((!alreadyAssigned && shouldManage) || !shouldManage) && 'cursor-grab',
        alreadyAssigned && shouldManage && 'cursor-default'
      )}
      title={`${firstName} ${lastName}`}
      data-event={JSON.stringify([
        {
          id: userId,
          email: email || '',
          firstName: firstName,
          lastName: lastName,
          fullName: `${firstName} ${lastName}`,
          phone: phone,
          position: position || '',
        },
      ])}
      data-user-id={userId}
      draggable={alreadyAssigned && shouldManage ? false : true}
      onDragStart={(e) => {
        const draggedElement = e.target as HTMLElement;
        const extractedData = draggedElement.getAttribute('data-event');

        e.dataTransfer.setData('application/json', extractedData);
      }}
    >
      {`${user.firstName} ${user.lastName}`}
    </div>
  );
}

const employeeCrewTabs = ['employees', 'crews'];

interface DraggableEmployeeCrewSelectionProps {
  resourceTypeSelected: string;
  innerRef: React.RefObject<HTMLDivElement>;
  availableUsers: any[]; // this is really HammrUsers with scheduling data
  availableCrews: Crew[];
  activeStartTimestamp?: string; // this is really moment timestamp string
  activeEndTimestamp?: string; // this is really moment timestamp string
  shouldManage?: boolean; // should manage is essentially used for crossing out already assigned employees/crews and disabling dragging
}

export default function DraggableEmployeeCrewSelection({
  innerRef,
  availableUsers,
  availableCrews,
  activeStartTimestamp,
  activeEndTimestamp,
  shouldManage = false,
}: DraggableEmployeeCrewSelectionProps) {
  const [selectedTab, setSelectedTab] = useState(employeeCrewTabs[0]);

  return (
    <div
      className="hidden max-h-full w-[258px] overflow-y-hidden rounded-16 border border-soft-200 bg-white-0 p-4 lg:flex"
      id="external-events"
      ref={innerRef}
    >
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex w-full flex-col">
        <TabList className="w-full">
          <TabItem className="grow" value="employees">
            Employees
          </TabItem>
          <TabItem className="grow" value="crews">
            Crews
          </TabItem>
        </TabList>
        <TabContent value="employees" className="-mx-4 mt-4 overflow-hidden">
          <ScrollArea className="h-full px-4">
            {availableUsers.map((user, index) => {
              const alreadyAssigned = user.scheduleEvents.some((event) => {
                return event.startTime >= +activeStartTimestamp && event.endTime <= +activeEndTimestamp + ONE_DAY;
              });

              return (
                <DraggableUserElement
                  key={`${user.id}-${index}`}
                  user={user}
                  objectName="employee"
                  // use alreadyAssigned value if shouldManage is true
                  alreadyAssigned={shouldManage ? alreadyAssigned : false}
                  shouldManage={shouldManage}
                />
              );
            })}
          </ScrollArea>
        </TabContent>
        <TabContent value="crews" className="-mx-4 mt-4 overflow-hidden">
          <ScrollArea className="h-full px-4">
            {availableCrews.map((crew) => {
              // in order to make crew draggable, need to check if any of the crewMembers are already assigned
              const crewAlreadyAssigned = crew.crewMembers.every((member) => {
                const foundUser = availableUsers.find((user) => {
                  return user.id === member.crewMemberUser.id;
                });

                if (!foundUser) return false;

                return foundUser.scheduleEvents.some((event) => {
                  return event.startTime >= +activeStartTimestamp && event.endTime <= +activeEndTimestamp + ONE_DAY;
                });
              });

              return (
                <CrewAccordion
                  key={crew.name}
                  crew={crew}
                  crewAlreadyAssigned={shouldManage ? crewAlreadyAssigned : false}
                  availableUsers={availableUsers}
                  activeStartTimestamp={activeStartTimestamp}
                  activeEndTimestamp={activeEndTimestamp}
                  shouldManage={shouldManage}
                />
              );
            })}
          </ScrollArea>
        </TabContent>
      </Tabs>
    </div>
  );
}

interface CrewAccordionProps {
  crew: Crew;
  crewAlreadyAssigned: boolean;
  availableUsers: any[];
  activeStartTimestamp?: string;
  activeEndTimestamp?: string;
  shouldManage?: boolean;
}

function CrewAccordion({
  crew,
  crewAlreadyAssigned,
  availableUsers,
  activeStartTimestamp,
  activeEndTimestamp,
  shouldManage = false,
}: CrewAccordionProps) {
  const [open, setOpen] = useState(false);

  return (
    <div>
      <div
        className="flex w-full cursor-pointer items-center justify-between border-b border-soft-200 px-3 py-4 text-sm font-medium text-sub-600"
        data-crew-id={crew.id}
        onClick={() => setOpen((prevOpen) => !prevOpen)}
      >
        <DraggableCrewElement
          crew={crew}
          crewId={crew.id}
          objectName="crew"
          alreadyAssigned={crewAlreadyAssigned}
          shouldManage={shouldManage}
        />
        <ArrowDownSLine className={`size-5 ${open ? 'rotate-180' : ''}`} />
      </div>
      {open && (
        <ul>
          {crew.crewMembers.map((member, index) => {
            // need to take member and find that member in availableUsers
            // then look at that member's scheduleEvents and see if any of them overlap with the activeStartTimestamp and activeEndTimestamp
            const foundUser = availableUsers.find((user) => {
              return user.id === member.crewMemberUser.id;
            });

            let alreadyAssigned = false;
            if (foundUser) {
              alreadyAssigned = foundUser.scheduleEvents.some((event) => {
                return event.startTime >= +activeStartTimestamp && event.endTime <= +activeEndTimestamp;
              });
            }

            const draggableClasses = 'pl-6';

            return (
              <DraggableUserElement
                key={`${member.crewMemberUser.id}-${index}`}
                user={member.crewMemberUser}
                className={draggableClasses}
                objectName="employee"
                // use alreadyAssigned value if shouldManage is true
                alreadyAssigned={shouldManage ? alreadyAssigned : false}
                shouldManage={shouldManage}
              />
            );
          })}
        </ul>
      )}
    </div>
  );
}
