import { Dispatch, SetStateAction, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { deleteScheduleEvent, deleteManyScheduleEvents } from 'services/schedule-event';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';

interface MultiScheduleDeleteModalProps {
  modalOpen: boolean;
  setModalOpen: Dispatch<SetStateAction<boolean>>;
  projectName: string;
  currentEventData: any;
  callback: () => void;
}

export default function MultiScheduleDeleteModal({
  modalOpen,
  setModalOpen,
  projectName,
  currentEventData,
  callback,
}: MultiScheduleDeleteModalProps) {
  const [shouldUpdateDays, setShouldUpdateDays] = useState('this-day');
  const { addToast } = useToast();

  const invokeUpdates = async (shouldUpdateAll) => {
    try {
      if (shouldUpdateAll && currentEventData?.extendedProps?.linkedEventId) {
        await deleteManyScheduleEvents(currentEventData.extendedProps.linkedEventId);
      } else {
        await deleteScheduleEvent(currentEventData.id);
      }

      addToast({
        title: 'Deleted Schedule Event',
        description: (
          <>
            Successfully deleted {shouldUpdateAll ? 'schedule events' : 'a schedule event'} for the project{' '}
            <strong className="font-medium">{projectName}</strong>.
          </>
        ),
        type: 'success',
      });
      // should call refresh (callback) and then close modal
      if (callback) {
        callback();
      }
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to delete scheduled event(s)');
    } finally {
      setModalOpen(false);
    }
  };

  return (
    <ConfirmDialog
      open={modalOpen}
      setOpen={setModalOpen}
      onConfirm={async () => {
        invokeUpdates(shouldUpdateDays === 'all-days');
      }}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      title="Delete Schedule Event"
      subtitle={
        <>
          You’re about to delete a schedule event for the project <strong className="font-medium">{projectName}</strong>
          . Do you want to proceed?
        </>
      }
      confirmButton={{
        color: 'error',
      }}
      confirmButtonText="Delete"
    >
      <h3 className="text-sm font-medium text-strong-950">What schedule event do you want to delete?</h3>

      <RadioGroup value={shouldUpdateDays} onValueChange={setShouldUpdateDays} className="mt-3">
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="this-day" id="this-day" />
          <Label className="font-normal" htmlFor="this-day">
            This selected day
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="all-days" id="all-days" />
          <Label className="font-normal" htmlFor="all-days">
            All days
          </Label>
        </div>
      </RadioGroup>
    </ConfirmDialog>
  );
}
