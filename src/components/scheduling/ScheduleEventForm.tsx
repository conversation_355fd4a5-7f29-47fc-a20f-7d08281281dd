import { Controller, UseFormReturn } from 'react-hook-form';
import ControlledSwitch from '../elements/form/ControlledSwitch';
import ControlledDateInput from '../elements/form/ControlledDateInput';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { apiRequest } from '@/utils/requestHelpers';
import { useMemo } from 'react';
import { TimeOffRequest } from '@/interfaces/timeoff';
import ControlledCombobox from '../elements/form/ControlledCombobox';
import { sortListAlphabetically } from '@/utils/collectionHelpers';
import { Project } from '@/interfaces/project';
import { CostCode } from '@/interfaces/cost-code';
import EquipmentSelect from '@/hammr-ui/components/EquipmentSelect';
import { ControlledTextarea } from '../elements/form/ControlledTextarea';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';
import { DateTimeInput } from '@/hammr-ui/components/datetime-input';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { Badge } from '@/hammr-ui/components/badge';
import { TimeInput } from '@/hammr-ui/components/time-input';
import { minutesAfterMidnight } from '@/utils/dateHelper';
import { DateInput } from '@/hammr-ui/components/date-input';
import { FormValues, getNotificationSettings } from './utils';
import Alert from '@/hammr-ui/components/Alert';
import { Notification } from '@/interfaces/notification';

interface Props {
  form: UseFormReturn<FormValues>;
  notification?: Notification;
}

export default function ScheduleEventForm({ form, notification }: Props) {
  const isEditing = !!notification;
  const notificationPreviouslySent = notification
    ? dayjs(notification.notificationScheduledAt).isBefore(dayjs())
    : false;

  const isMultipleDays = form.watch('isMultipleDays');

  const projectsQuery = useQuery({
    queryKey: ['projects'],
    async queryFn() {
      const res = await apiRequest<{ projects: Project[] }>('projects');
      return sortListAlphabetically(res.projects, 'name');
    },
  });

  const costCodesQuery = useQuery({
    queryKey: ['costCodes'],
    async queryFn() {
      const res = await apiRequest<{ costCodes: CostCode[] }>('cost-codes');
      return sortListAlphabetically(res.costCodes, 'name');
    },
  });

  return (
    <>
      {!isEditing && (
        <ControlledSwitch
          control={form.control}
          name="isMultipleDays"
          label="Multiple Days"
          onChange={(isMultipleDays) => {
            if (isMultipleDays === false) {
              form.setValue('endDate', null);
            }
          }}
        />
      )}

      <div className={`${isMultipleDays && 'grid grid-cols-2 gap-3'}`}>
        <Controller
          name="startDate"
          control={form.control}
          rules={{ required: 'Please select a start date' }}
          render={({ field, fieldState }) => (
            <FormItem required error={!!fieldState.error}>
              <FormLabel>{isMultipleDays ? 'Start Date' : 'Date'}</FormLabel>
              <FormControl>
                <DateInput
                  value={field.value}
                  onChange={(value) => {
                    field.onChange(value);

                    // If multiple days is enabled and the end date is before the start date, remove the prev end date to make the user select a new one
                    const endDate = form.getValues('endDate');
                    if (isMultipleDays && endDate && dayjs(endDate).isBefore(dayjs(value))) {
                      form.setValue('endDate', null);
                    }

                    // When start date changes, reset the notification settings if notifyEmployees is true
                    const notifyEmployees = form.getValues('notifyEmployees');
                    const startTime = form.getValues('startTime');
                    const notificationTimestamp = form.getValues('notificationTimestamp');
                    if (notifyEmployees && form.getValues('notificationTiming') === 'later' && value && startTime) {
                      const notificationSettings = getNotificationSettings({
                        startDate: value,
                        startTime,
                        ...(notificationTimestamp
                          ? { defaultNotificationTime: dayjs(notificationTimestamp).format('HH:mm:ss') }
                          : {}),
                      });
                      if (notificationSettings.notificationTiming === null) {
                        form.setValue('notifyEmployees', false);
                        form.setValue('textNotification', false);
                        form.setValue('pushNotification', false);
                      } else {
                        form.setValue('notificationTiming', notificationSettings.notificationTiming);
                        form.setValue('notificationTimestamp', notificationSettings.notificationTimestamp);
                      }
                    }
                  }}
                />
              </FormControl>
              <FormMessage className="mt-1">{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />
        {isMultipleDays && (
          <ControlledDateInput
            control={form.control}
            name="endDate"
            label="End Date"
            required
            rules={{ required: 'Please select an end date' }}
            dayPickerProps={{
              disabled: { before: form.watch('startDate') ?? new Date() },
            }}
          />
        )}
      </div>

      {isMultipleDays && <ControlledSwitch control={form.control} name="skipWeekends" label="Skip Weekends" />}

      <div className="grid grid-cols-2 gap-3">
        <Controller
          name="startTime"
          control={form.control}
          rules={{ required: 'Start time is required' }}
          render={({ field, fieldState }) => (
            <FormItem required error={!!fieldState.error}>
              <FormLabel>Start Time</FormLabel>
              <FormControl>
                <TimeInput value={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />
        <Controller
          name="endTime"
          control={form.control}
          rules={{ required: 'End time is required' }}
          render={({ field, fieldState }) => {
            const startTime = form.getValues('startTime');
            const startTimeDayjs = dayjs(startTime);
            const endTime = field.value;
            const endTimeDayjs = dayjs(endTime);
            const isNightShift =
              startTime && endTime && minutesAfterMidnight(endTimeDayjs) < minutesAfterMidnight(startTimeDayjs);

            return (
              <FormItem required error={!!fieldState.error}>
                <FormLabel>End Time {isNightShift && 'Night Shift 💤'}</FormLabel>
                <FormControl>
                  <TimeInput value={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage>{fieldState.error?.message}</FormMessage>
              </FormItem>
            );
          }}
        />
      </div>

      <hr className="border-soft-200" />

      <WorkerCrewSelectSection form={form} />

      <ControlledCombobox
        control={form.control}
        name="selectedProject"
        label="Project"
        required
        items={
          projectsQuery.data?.map((project) => ({
            label: (
              <span className="flex gap-1">
                <span className="truncate">
                  {project.name + (project.projectNumber ? ` (${project.projectNumber})` : '')}
                </span>
                {project.isPrevailingWage && (
                  <Tooltip content="Prevailing Wage">
                    <Badge variant="outline" color="gray">
                      PW
                    </Badge>
                  </Tooltip>
                )}
              </span>
            ),
            value: String(project.id),
          })) ?? []
        }
        rules={{ required: 'Please select a project' }}
      />

      <ControlledCombobox
        control={form.control}
        name="selectedCostCode"
        label="Cost Code"
        items={costCodesQuery.data?.map((costCode) => ({ label: costCode.name, value: String(costCode.id) })) ?? []}
      />

      <Controller
        name="selectedEquipmentIds"
        control={form.control}
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel>
              Equipment{' '}
              {field.value.length > 0 && (
                <span className="font-normal text-sub-600">({field.value.length} selected)</span>
              )}
            </FormLabel>
            <FormControl>
              <EquipmentSelect
                className={!!fieldState.error ? 'border-error-base' : ''}
                selectedEquipmentIds={field.value}
                onChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />

      <ControlledTextarea control={form.control} name="notes" label="Notes" placeholder="Enter notes" />

      <hr className="border-soft-200" />

      {notificationPreviouslySent ? (
        <>
          <Alert>Employees were previously notified. Would you like to notify them about this change?</Alert>
          <ControlledSwitch control={form.control} name="notifyAgain" label="Notify Again" />
        </>
      ) : (
        <>
          <ControlledSwitch
            control={form.control}
            name="notifyEmployees"
            label="Notify Employees"
            onChange={(value) => {
              // sync notification settings when toggling notifyEmployees
              form.setValue('textNotification', value);
              form.setValue('pushNotification', value);
            }}
          />

          {form.watch('notifyEmployees') && (
            <Controller
              control={form.control}
              name="notificationTiming"
              render={({ field }) => (
                <>
                  <RadioGroup
                    value={field.value as string}
                    onValueChange={(val) => {
                      field.onChange(val);

                      // when changing from 'immediately' to 'later', set the notification timestamp to the default time
                      const notificationTimestamp = form.getValues('notificationTimestamp');
                      if (val === 'later' && notificationTimestamp === null) {
                        form.setValue('notificationTimestamp', dayjs().toDate());
                      }
                    }}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="immediately" id="immediately" />
                      <Label className="font-normal" htmlFor="immediately">
                        Immediately
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="later" id="later" />
                      <Label className="font-normal" htmlFor="later">
                        Later
                      </Label>
                    </div>

                    {field.value === 'later' && (
                      <Controller
                        control={form.control}
                        name="notificationTimestamp"
                        render={({ field }) => (
                          <DateTimeInput
                            value={field.value}
                            onChange={field.onChange}
                            dayPickerProps={{ disabled: { before: dayjs(form.watch('startDate')).toDate() } }}
                          />
                        )}
                      />
                    )}
                  </RadioGroup>

                  <hr className="border-soft-200" />
                </>
              )}
            />
          )}

          <ControlledSwitch
            control={form.control}
            name="textNotification"
            label="Text Notification"
            disabled={!form.watch('notifyEmployees')}
          />

          <ControlledSwitch
            control={form.control}
            name="pushNotification"
            label="Push Notification"
            disabled={!form.watch('notifyEmployees')}
          />
        </>
      )}
    </>
  );
}

function WorkerCrewSelectSection({ form }: { form: UseFormReturn<FormValues> }) {
  const startDate = form.watch('startDate');
  const endDate = form.watch('endDate');
  const selectedEmployees = form.watch('selectedEmployeeIds');

  const timeOffQuery = useQuery({
    queryKey: ['time-off-requests', startDate, endDate],
    queryFn: () => {
      const queryStartDate = dayjs(startDate).startOf('day').valueOf();
      const queryEndDate = dayjs(endDate || startDate)
        .endOf('day')
        .valueOf();

      return apiRequest<{ timeOffRequests: TimeOffRequest[] }>('/time-off-requests', {
        urlParams: {
          status: ['APPROVED', 'PAID'],
          startDate: queryStartDate,
          endDate: queryEndDate,
        },
      });
    },
    enabled: !!startDate,
  });

  const disabledWorkers = useMemo(() => {
    if (!timeOffQuery.data?.timeOffRequests) return [];

    return timeOffQuery.data.timeOffRequests.map((request) => ({
      workerId: request.userId,
      selected: selectedEmployees.includes(request.userId),
      reason: '(Time Off)',
    }));
  }, [timeOffQuery.data?.timeOffRequests, selectedEmployees]);

  return (
    <Controller
      control={form.control}
      name="selectedEmployeeIds"
      rules={{
        validate(value) {
          if (value.length === 0) {
            return 'Please select at least one employee';
          }
        },
      }}
      render={({ field, fieldState }) => (
        <FormItem error={!!fieldState.error}>
          <FormLabel>
            Employees{' '}
            {field.value.length > 0 && (
              <span className="font-normal text-sub-600">({field.value.length} selected)</span>
            )}
          </FormLabel>
          <FormControl>
            <WorkerCrewSelect
              className={!!fieldState.error ? 'border-error-base' : ''}
              selectedWorkerIds={field.value}
              onChange={field.onChange}
              showCrews
              disabledWorkers={disabledWorkers}
            />
          </FormControl>
          <FormMessage>{fieldState.error?.message as string}</FormMessage>
        </FormItem>
      )}
    />
  );
}
