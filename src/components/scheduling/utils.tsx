import dayjs from 'dayjs';

export type NotificationTiming = 'immediately' | 'later' | null;

type Params = {
  startDate: Date;
  startTime: Date;
  defaultNotificationTime?: string;
};
type ReturnType = { notificationTiming: NotificationTiming; notificationTimestamp: Date | null };

export function getNotificationSettings({ startDate, startTime, defaultNotificationTime }: Params): ReturnType {
  const defaultTime = defaultNotificationTime
    ? dayjs(defaultNotificationTime, 'HH:mm:ss')
    : dayjs('07:00:00', 'HH:mm:ss');
  const shouldBeNotifiedAt = dayjs(startDate)
    .subtract(1, 'day')
    .hour(defaultTime.hour())
    .minute(defaultTime.minute())
    .second(0);

  const eventStartTime = dayjs(startDate).hour(dayjs(startTime).hour()).minute(dayjs(startTime).minute()).second(0);

  if (dayjs().isBefore(shouldBeNotifiedAt)) {
    return { notificationTiming: 'later', notificationTimestamp: shouldBeNotifiedAt.toDate() };
  } else if (dayjs().isBefore(eventStartTime)) {
    return { notificationTiming: 'immediately', notificationTimestamp: null };
  } else {
    return { notificationTiming: null, notificationTimestamp: null };
  }
}

export interface FormValues {
  isMultipleDays: boolean;
  startDate: Date | null;
  endDate: Date | null;
  skipWeekends: boolean;
  startTime: Date | null;
  endTime: Date | null;
  selectedEmployeeIds: number[];
  selectedProject: string | null;
  selectedCostCode: string | null;
  selectedEquipmentIds: number[];
  notes: string;
  notifyEmployees: boolean;
  notificationTiming: NotificationTiming;
  notificationTimestamp: Date | null;
  textNotification: boolean;
  pushNotification: boolean;
  notifyAgain: boolean; // used in edit modal
}
