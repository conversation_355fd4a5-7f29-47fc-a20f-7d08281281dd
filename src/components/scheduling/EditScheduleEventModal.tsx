import { <PERSON><PERSON>, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { RiCalendarLine } from '@remixicon/react';
import { useForm } from 'react-hook-form';
import ScheduleEventForm from './ScheduleEventForm';
import { FormV2 } from '../elements/Form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FormValues } from './utils';
import { useEffect, useState } from 'react';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';
import { UpdateScheduleEvent } from '@/interfaces/schedule-event';
import dayjs from 'dayjs';
import { minutesAfterMidnight } from '@/utils/dateHelper';
import { resendEventNotification, updateScheduleEvent } from '@/services/schedule-event';
import { addToast } from '@/hooks/useToast';
import { apiRequest } from '@/utils/requestHelpers';
import { Project } from '@/interfaces/project';
import { sortListAlphabetically } from '@/utils/collectionHelpers';
import { Notification } from '@/interfaces/notification';

interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
  eventData: {
    extendedProps: {
      startTime: Date;
      endTime: Date;
      assigned: { id: number; firstName: string; lastName: string }[];
      projectId: number;
      costCodeId: number | null;
      equipment: { id: number; name: string }[];
      note: string | null;
      notification: Notification | undefined;
      linkedEventId: string | null;
      eventId: number;
    };
  };
}

export default function EditScheduleEventModal({ open, setOpen, eventData }: Props) {
  const form = useForm<FormValues>();

  useEffect(() => {
    if (open) {
      const notificationScheduledAt = eventData.extendedProps.notification?.notificationScheduledAt;
      const notificationPreviouslySent = dayjs(notificationScheduledAt).isBefore(dayjs());

      form.reset({
        isMultipleDays: false,
        startDate: eventData.extendedProps.startTime,
        endDate: null,
        skipWeekends: false,
        startTime: eventData.extendedProps.startTime,
        endTime: eventData.extendedProps.endTime,
        selectedEmployeeIds: eventData.extendedProps.assigned.map((emp) => emp.id),
        selectedProject: eventData.extendedProps.projectId.toString(),
        selectedCostCode: eventData.extendedProps.costCodeId?.toString(),
        selectedEquipmentIds: eventData.extendedProps.equipment.map((eq) => eq.id),
        notes: eventData.extendedProps.note || '',
        notifyEmployees: !!eventData.extendedProps.notification,
        notificationTiming: 'later',
        notificationTimestamp: notificationScheduledAt ? dayjs(notificationScheduledAt).toDate() : null,
        textNotification: eventData.extendedProps.notification?.notifyViaText || false,
        pushNotification: eventData.extendedProps.notification?.notifyViaPush || false,
        notifyAgain: notificationPreviouslySent,
      });
    }
  }, [open, form, eventData.extendedProps]);

  const queryClient = useQueryClient();

  const projectsQuery = useQuery({
    queryKey: ['projects'],
    async queryFn() {
      const res = await apiRequest<{ projects: Project[] }>('projects');
      return sortListAlphabetically(res.projects, 'name');
    },
  });

  const editScheduleMutation = useMutation({
    async mutationFn(formData: FormValues) {
      const notificationScheduledAt = eventData.extendedProps.notification?.notificationScheduledAt;
      const notificationPreviouslySent = dayjs(notificationScheduledAt).isBefore(dayjs());

      const startTimeDayjs = dayjs(formData.startTime);
      const endTimeDayjs = dayjs(formData.endTime);
      const isNightShift =
        formData.startTime &&
        formData.endTime &&
        minutesAfterMidnight(endTimeDayjs) < minutesAfterMidnight(startTimeDayjs);

      const userIds = eventData.extendedProps.assigned.map((emp) => emp.id);
      const addedUserIds = formData.selectedEmployeeIds.filter((id) => !userIds.includes(id));
      const removedUserIds = userIds.filter((id) => !formData.selectedEmployeeIds.includes(id));

      const scheduleEvent: UpdateScheduleEvent = {
        startTime: dayjs(formData.startDate)
          .hour(startTimeDayjs.hour())
          .minute(startTimeDayjs.minute())
          .second(0)
          .valueOf(),
        endTime: dayjs(formData.startDate)
          .add(isNightShift ? 1 : 0, 'day')
          .hour(endTimeDayjs.hour())
          .minute(endTimeDayjs.minute())
          .second(0)
          .valueOf(),
        addUsers: addedUserIds.join(','),
        removeUsers: removedUserIds.join(','),
        projectId: Number(formData.selectedProject),
        costCodeId: formData.selectedCostCode ? Number(formData.selectedCostCode) : null,
        equipmentIds: formData.selectedEquipmentIds.join(','),
        note: formData.notes,
        ...(!notificationPreviouslySent
          ? {
              notificationScheduledAt: formData.notifyEmployees
                ? formData.notificationTiming === 'later'
                  ? formData.notificationTimestamp?.valueOf()
                  : dayjs().valueOf()
                : null,
              notifyViaText: formData.textNotification,
              notifyViaPush: formData.pushNotification,
            }
          : {}),
        ...(shouldUpdateDays === 'all-days'
          ? {
              linkedEventId: eventData.extendedProps.linkedEventId,
              timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
              scheduleEventId: eventData.extendedProps.eventId,
            }
          : {}),
      };

      await updateScheduleEvent(eventData.extendedProps.eventId, scheduleEvent);
      if (notificationPreviouslySent && formData.notifyAgain) {
        await resendEventNotification(eventData.extendedProps.eventId);
      }
    },
    onSuccess(_, formData) {
      queryClient.invalidateQueries({ queryKey: ['scheduleEvents'] });

      const projectName = projectsQuery.data?.find((p) => p.id === Number(formData.selectedProject))?.name;
      addToast({
        title: 'Edited Schedule Event',
        description: (
          <>
            Successfully edited a schedule event for the project <strong className="font-medium">{projectName}</strong>.
          </>
        ),
        type: 'success',
      });

      setOpen(false);
    },
  });

  const [isMultiScheduleUpdatePickerOpen, setIsMultiScheduleUpdatePickerOpen] = useState(false);
  const [shouldUpdateDays, setShouldUpdateDays] = useState('this-day');

  useEffect(() => {
    if (!open) {
      form.reset();
      setIsMultiScheduleUpdatePickerOpen(false);
      setShouldUpdateDays('this-day');
    }
  }, [open, form]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<RiCalendarLine className="text-sub-600" />} title="Edit Schedule Event" />
        <FormV2
          onCancel={() => setOpen(false)}
          onSubmit={form.handleSubmit((formData) => {
            if (eventData.extendedProps.linkedEventId && !isMultiScheduleUpdatePickerOpen) {
              setIsMultiScheduleUpdatePickerOpen(true);
              return;
            }
            editScheduleMutation.mutate(formData);
          })}
          submitText="Save"
          isLoading={editScheduleMutation.isPending}
          className="flex flex-col gap-5"
        >
          {!isMultiScheduleUpdatePickerOpen ? (
            <ScheduleEventForm form={form} notification={eventData.extendedProps.notification} />
          ) : (
            <div>
              <h3 className="text-sm font-medium text-strong-950">What schedule event do you want to update?</h3>

              <RadioGroup value={shouldUpdateDays} onValueChange={setShouldUpdateDays} className="mt-3">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="this-day" id="this-day" />
                  <Label className="font-normal" htmlFor="this-day">
                    This selected day
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all-days" id="all-days" />
                  <Label className="font-normal" htmlFor="all-days">
                    All days
                  </Label>
                </div>
              </RadioGroup>
            </div>
          )}
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
