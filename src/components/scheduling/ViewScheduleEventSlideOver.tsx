import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import moment from 'moment';
import { labelDate, minutesAfterMidnight } from 'utils/dateHelper';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import CalendarLine from '@/hammr-icons/CalendarLine';
import Button from '@/hammr-ui/components/button';
import Alert from '@/hammr-ui/components/Alert';

interface ViewScheduleEventSlideOverProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentEventData: any;
  editCallback: Dispatch<SetStateAction<boolean>>;
  deleteCallback: () => void;
  persistDataCallback: Dispatch<SetStateAction<any>>;
}

export default function ViewScheduleEventSlideOver({
  open,
  setOpen,
  currentEventData,
  editCallback,
  deleteCallback,
  persistDataCallback,
}: ViewScheduleEventSlideOverProps) {
  const [currentEventColor, setCurrentEventColor] = useState(currentEventData?.extendedProps?.eventColor || '#000000');

  const [notificationTimeLabel, setNotificationTimeLabel] = useState('');
  const [allowNotificationTimeEdit, setAllowNotificationTimeEdit] = useState(false);
  const [isNightShift, setIsNightShift] = useState(false);

  const resetLocalState = () => {
    setNotificationTimeLabel('');
  };

  useEffect(() => {
    if (currentEventData?.extendedProps?.eventColor) {
      setCurrentEventColor(currentEventData?.extendedProps?.eventColor);
    }

    // set night shift flag
    if (currentEventData?.extendedProps?.startTime && currentEventData?.extendedProps?.endTime) {
      const momentStartTime = moment(currentEventData?.extendedProps?.startTime);
      const momentEndTime = moment(currentEventData?.extendedProps?.endTime);
      setIsNightShift(minutesAfterMidnight(momentEndTime) < minutesAfterMidnight(momentStartTime));
    }

    if (currentEventData?.extendedProps?.notification) {
      // also set notification data
      const scheduleEventNotificationObj = currentEventData?.extendedProps?.notification;

      // now check scheduleEventNotificationObj for sentAt, else
      if (scheduleEventNotificationObj?.sentAt) {
        // implies past
        setAllowNotificationTimeEdit(false);
        setNotificationTimeLabel(`Employees were notified ${labelDate(scheduleEventNotificationObj.sentAt)}.`);
      } else {
        const scheduledAt = scheduleEventNotificationObj?.notificationScheduledAt;
        setAllowNotificationTimeEdit(true);
        setNotificationTimeLabel(`Employees will be notified ${labelDate(scheduledAt)}.`);
      }
    }
  }, [currentEventData]);

  // reset the local state if the slide over closes
  useEffect(() => {
    if (!open) {
      resetLocalState();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<CalendarLine className="text-sub-600" />} title="Schedule Event Details" />

        <article className="p-5">
          <section className="flex items-center justify-between">
            <header>
              <h3 className="text-xs text-sub-600">Project</h3>
              <p className="mt-1.5 h-fit text-strong-950">
                {currentEventData?.extendedProps?.projectName || 'Placeholder'}
              </p>
            </header>
            <span
              className="inline-block size-7 rounded border border-soft-200"
              style={{ backgroundColor: currentEventColor }}
            ></span>
          </section>

          <section className="mt-5 grid grid-cols-2 gap-3">
            <div>
              <h3 className="text-xs text-sub-600">Start Time</h3>
              <p className="mt-1.5 h-fit text-strong-950">
                {isNightShift
                  ? moment(currentEventData?.extendedProps?.startTime).format('ddd, MMM D, YYYY h:mm A')
                  : moment(currentEventData?.extendedProps?.startTime).format('ddd, MMM D, YYYY h:mm A')}
              </p>
            </div>
            <div>
              <h3 className="text-xs text-sub-600">End Time</h3>
              <p className="mt-1.5 h-fit text-strong-950">
                {isNightShift
                  ? moment(currentEventData?.extendedProps?.endTime).format('ddd, MMM D, YYYY h:mm A')
                  : moment(currentEventData?.extendedProps?.endTime).format('ddd, MMM D, YYYY h:mm A')}
              </p>
            </div>
          </section>

          <section className="mt-5">
            <h3 className="text-xs text-sub-600">Address</h3>
            <p className="mt-1.5 h-fit text-strong-950">{currentEventData?.extendedProps?.address || '-'}</p>
          </section>

          <section className="mt-5">
            <h3 className="text-xs text-sub-600">Cost Code</h3>
            <p className="mt-1.5 h-fit text-strong-950">{currentEventData?.extendedProps?.costCodeName || '-'}</p>
          </section>

          <section className="mt-5">
            <h3 className="text-xs text-sub-600">Employees Assigned</h3>
            <ul className="mt-1.5">
              {currentEventData?.extendedProps?.assigned.map((assignee, index) => (
                <li
                  key={`assignee-${index}`}
                  className="text-sm text-strong-950"
                >{`${assignee.firstName} ${assignee.lastName}`}</li>
              ))}
            </ul>
          </section>

          <section className="mt-5">
            <h3 className="text-xs text-sub-600">Equipment</h3>
            <ul className="mt-1.5">
              {currentEventData?.extendedProps?.equipment.length > 0 ? (
                currentEventData?.extendedProps?.equipment.map((equipment, index) => (
                  <li key={`equipment-${index}`} className="text-sm text-strong-950">
                    {`${equipment.name}`}
                  </li>
                ))
              ) : (
                <li className="text-sm text-strong-950">-</li>
              )}
            </ul>
          </section>

          <section className="mt-5">
            <h3 className="text-xs text-sub-600">Notes</h3>
            <p className="mt-1.5 h-fit text-strong-950">{currentEventData?.extendedProps?.note || '-'}</p>
          </section>

          {notificationTimeLabel && <Alert className="mt-5">{notificationTimeLabel}</Alert>}
        </article>

        <footer className="grid h-[68px] grid-cols-2 items-center gap-3 border-t border-soft-200 px-5">
          <Button
            color="error"
            variant="outline"
            onClick={() => {
              deleteCallback();
              setOpen(false);
            }}
          >
            Delete Event
          </Button>
          <Button
            onClick={() => {
              // set data
              if (persistDataCallback) {
                persistDataCallback(currentEventData);
              }
              // close view slide over
              setOpen(false);
              // open edit slide over
              editCallback(true);
            }}
          >
            Edit Event
          </Button>
        </footer>
      </DialogSurface>
    </Dialog>
  );
}
