import { Dispatch, SetStateAction } from 'react';
import { useToast } from 'hooks/useToast';
import { deleteScheduleEvent } from 'services/schedule-event';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';

interface ConfirmDeleteEventModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  projectName: string;
  currentEventData: any;
  callback: () => void;
}

export default function ConfirmDeleteEventModal({
  open,
  setOpen,
  projectName,
  currentEventData,
  callback,
}: ConfirmDeleteEventModalProps) {
  const { addToast } = useToast();

  const confirmDeleteEventHandler = async () => {
    try {
      await deleteScheduleEvent(`${currentEventData?.extendedProps?.eventId}`);
      addToast({
        title: 'Deleted Schedule Event',
        description: (
          <>
            Successfully deleted a schedule event for the project <strong className="font-medium">{projectName}</strong>
            .
          </>
        ),
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to delete scheduled event');
    } finally {
      setOpen(false);
    }
  };

  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      onConfirm={async () => {
        confirmDeleteEventHandler();
      }}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      title="Delete Schedule Event"
      subtitle={
        <>
          You’re about to delete a schedule event for the project <strong className="font-medium">{projectName}</strong>
          . Do you want to proceed?
        </>
      }
      confirmButton={{
        color: 'error',
      }}
      confirmButtonText="Delete"
    />
  );
}
