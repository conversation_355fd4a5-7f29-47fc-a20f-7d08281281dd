import React, { Dispatch, SetStateAction, useRef } from 'react';
import { createPortal } from 'react-dom';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import scrollgridPlugin from '@fullcalendar/scrollgrid';
import resourceTimelinePlugin from '@fullcalendar/resource-timeline';
import resourceDayGridPlugin from '@fullcalendar/resource-daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { cn } from '@/utils/cn';
import moment from 'moment';
import dayjs from 'dayjs';

import { weekStartDateToInt } from 'utils/dataTransformers';

import DraggableEmployeeCrewSelection from 'components/scheduling/DraggableEmployeeCrewSelection';
import { useToast } from 'hooks/useToast';

import { updateScheduleEvent } from 'services/schedule-event';

import { UpdateScheduleEvent } from 'interfaces/schedule-event';
import { DraggableHammrUser } from 'interfaces/user';
import { Crew } from 'interfaces/crew';
import { logError, showErrorToast } from 'utils/errorHandling';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import UserLine from '@/hammr-icons/UserLine';
import { formatMinutesToHoursWorked } from '@/utils/format';

interface TimeOffEventCardProps {
  title: string;
  hours: number;
}

function TimeOffEventCard({ title, hours }: TimeOffEventCardProps) {
  function clickDragHandler(e: React.MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
  }

  return (
    <div className="cursor-default overflow-hidden rounded-8 bg-white-0">
      <div
        className="bg-faded-lighter px-3 py-2"
        onClick={clickDragHandler}
        onMouseDown={clickDragHandler}
        style={{
          backgroundImage: `repeating-linear-gradient(
          120deg,
          transparent,
          transparent 10px,
          rgb(var(--raw-soft-200)) 10px,
          rgb(var(--raw-soft-200)) 11px
        )`,
        }}
      >
        <div className="text-xs font-medium text-strong-950">{formatMinutesToHoursWorked(hours * 60)}</div>
        <div className="mt-1 text-xs text-sub-600">{title}</div>
      </div>
    </div>
  );
}

function renderEventContent(eventInfo, resourceTypeSelected) {
  if (eventInfo.event.extendedProps.isTimeOff) {
    return <TimeOffEventCard title={eventInfo.event.title} hours={eventInfo.event.extendedProps.hours} />;
  }

  const startTime =
    moment(eventInfo.event.start).minute() === 0
      ? moment(eventInfo.event.start).format('h a')
      : moment(eventInfo.event.start).format('h:mm a');
  const endTime =
    moment(eventInfo.event.end).minute() === 0
      ? moment(eventInfo.event.end).format('h a')
      : moment(eventInfo.event.end).format('h:mm a');

  return (
    <div className="px-3 py-2">
      <div className="text-xs font-medium uppercase text-strong-950">{`${startTime} - ${endTime}`}</div>
      <div className="text-sub-600">
        {resourceTypeSelected === 'projects' ? (
          <div className="mt-1 flex items-center gap-1">
            <UserLine className="size-4" />
            <span>{eventInfo.event.title}</span>
          </div>
        ) : (
          <div className="mt-1 text-xs">
            {resourceTypeSelected === 'projects' ? (
              <div className="mt-2 flex pb-2 pr-2">
                {eventInfo.event.extendedProps?.costCodeName ? eventInfo.event.extendedProps?.costCodeName : null}
              </div>
            ) : (
              <span className="">{eventInfo.event.title}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

function renderMonthlyEventContent(eventInfo, resourceTypeSelected) {
  if (eventInfo.event.extendedProps.isTimeOff) {
    return <TimeOffEventCard title={eventInfo.event.title} hours={eventInfo.event.extendedProps.hours} />;
  }

  const startTime =
    moment(eventInfo.event.start).minute() === 0
      ? moment(eventInfo.event.start).format('h a')
      : moment(eventInfo.event.start).format('h:mm a');
  const endTime =
    moment(eventInfo.event.end).minute() === 0
      ? moment(eventInfo.event.end).format('h a')
      : moment(eventInfo.event.end).format('h:mm a');

  return (
    <div
      className="w-full rounded-8 px-3 py-2"
      style={{
        backgroundColor: eventInfo.event.backgroundColor,
      }}
    >
      <div className="text-xs font-medium text-strong-950">{`${startTime} - ${endTime}`}</div>
      <div className="mt-1 whitespace-normal text-sub-600">
        {resourceTypeSelected === 'projects' ? (
          <>
            <div className="mt-1 flex">{eventInfo.event.extendedProps.projectName}</div>
            {eventInfo.event.extendedProps?.costCodeName ? (
              <div className="mt-1 flex">{eventInfo.event.extendedProps?.costCodeName}</div>
            ) : null}
            <div className="mt-1 flex items-center gap-1 text-sub-600">
              <UserLine className="size-4" />
              <span className="pl-1 pr-1">{eventInfo.event.title}</span>
            </div>
          </>
        ) : (
          <>
            <div className="mt-1">{eventInfo.event.title}</div>
            <div className="mt-1 flex items-center gap-1 text-sub-600">
              <UserLine className="size-4" />
              <span className="pl-1 pr-1">{`${eventInfo.event.extendedProps.fullName}`}</span>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

interface ScheduleCalendarProps {
  dateSetHandler: Dispatch<SetStateAction<{ start: Date; end: Date }>>;
  resourceSelectHandler: Dispatch<SetStateAction<string>>;
  resourceTypeSelected: string;
  weekStartDay: string;
  resourcesData: any[];
  availableUsers: any[]; // this is really HammrUsers enhanced with scheduling data
  availableCrews: Crew[];
  eventsData: any[];
  isLoading: boolean;
  dateClickHandler?: Dispatch<SetStateAction<any>>;
  eventClickHandler?: Dispatch<SetStateAction<any>>;
  callback?: (...args) => void;
  timeOffEvents?: any[];
}

export default function ScheduleCalendar({
  dateSetHandler,
  resourceSelectHandler,
  resourceTypeSelected,
  weekStartDay = 'SUNDAY',
  resourcesData,
  availableUsers,
  availableCrews,
  eventsData,
  isLoading,
  dateClickHandler,
  eventClickHandler,
  callback,
  timeOffEvents,
}: ScheduleCalendarProps) {
  const calendarRef = useRef(null);

  const draggableEl = useRef(null);
  const { addToast } = useToast();

  const calendarView = calendarRef.current?.getApi().view.type;
  const momentActiveStartTimestamp = moment(calendarRef.current?.getApi().view.activeStart).format('x');
  const momentActiveEndTimestamp = moment(calendarRef.current?.getApi().view.activeEnd).format('x');

  const handleDatesSet = (arg) => {
    dateSetHandler({ start: arg.start, end: arg.end });
  };

  const handleRenderEventContent = (eventInfo) => {
    if (eventInfo.view.type === 'dayGridMonth') {
      return renderMonthlyEventContent(eventInfo, resourceTypeSelected);
    } else {
      return renderEventContent(eventInfo, resourceTypeSelected);
    }
  };

  const handleEventClick = (eventClickInfo) => {
    // trigger the event click callback if it exists
    if (eventClickHandler) {
      eventClickHandler(eventClickInfo.event);
    }
  };

  const handleDateTimeSelect = (selectionInfo) => {
    // set the resource selected and then set the data within the sldie over, open slide over
    const selectedDatesDetails = {};
    selectedDatesDetails['start'] = selectionInfo.start;
    selectedDatesDetails['startStr'] = selectionInfo.startStr;
    selectedDatesDetails['end'] = selectionInfo.end;
    selectedDatesDetails['endStr'] = selectionInfo.endStr;
    // depending on view, resource might not be available
    if (selectionInfo.resource) {
      selectedDatesDetails['resource'] = selectionInfo.resource.toPlainObject();
      selectedDatesDetails['resourceType'] = resourceTypeSelected;
    }
    dateClickHandler(selectedDatesDetails);
  };

  const handleEventDrop = async (info) => {
    if (info.event.extendedProps.isTimeOff) {
      info.revert();
      return;
    }

    // Check for time off conflicts
    const droppedStartDate = dayjs(info.event.start).format('YYYY-MM-DD');
    const droppedEndDate = dayjs(info.event.end).format('YYYY-MM-DD');

    // Get affected users
    const affectedUsers = info.event.extendedProps.assigned.map((user) => user.id);

    // If moving to a different resource (employee), add that employee to check list
    if (info.newResource && resourceTypeSelected === 'employees') {
      affectedUsers.push(info.newResource.id);
    }

    // Find any time off events that conflict
    const conflicts = timeOffEvents?.filter((timeOff) => {
      const timeOffDate = dayjs(timeOff.startTime).format('YYYY-MM-DD');
      const isDateInRange = timeOffDate >= droppedStartDate && timeOffDate <= droppedEndDate;
      const isAffectedUser = affectedUsers.includes(timeOff.resourceId);
      return isDateInRange && isAffectedUser;
    });

    if (conflicts?.length > 0) {
      // Get unique user names from conflicts
      const userNames = [
        ...new Set(
          conflicts.map((conflict) => {
            const user = availableUsers.find((u) => u.id === conflict.resourceId);
            return user ? `${user.firstName} ${user.lastName}` : 'Unknown User';
          })
        ),
      ];

      addToast({
        title: 'Schedule Conflict',
        description: `Cannot schedule on these dates. ${userNames.join(', ')} ${userNames.length > 1 ? 'have' : 'has'} time off.`,
        type: 'error',
      });

      info.revert();
      return;
    }

    const updateScheduleEventPayload: UpdateScheduleEvent = {};

    // configure payload
    updateScheduleEventPayload.startTime = +moment(info.event.start).format('x');
    updateScheduleEventPayload.endTime = +moment(info.event.end).format('x');

    // Get current assigned user IDs
    const currentUserIds = info.event.extendedProps.assigned.map((user) => user.id);

    // if the drag and drop moved to a different row, we need to look at info.newResource for the new resource
    if (info.newResource) {
      // if it is an event with multiple users, it should take existing users and only mutate the changed user
      if (resourceTypeSelected === 'employees') {
        // When moving to a different employee row, we need to replace the old resource user with the new one
        const oldResourceId = parseInt(info.oldResource.id);
        const newResourceId = parseInt(info.newResource.id);

        // Check if the old resource was in the assigned users
        if (currentUserIds.includes(oldResourceId)) {
          // Remove the old user and add the new user
          updateScheduleEventPayload.removeUsers = oldResourceId.toString();
          updateScheduleEventPayload.addUsers = newResourceId.toString();
        } else {
          // Just add the new user if old wasn't assigned
          updateScheduleEventPayload.addUsers = newResourceId.toString();
        }
      } else if (resourceTypeSelected === 'projects') {
        updateScheduleEventPayload.projectId = parseInt(info.newResource.id, 10);
      }
    }
    // If no resource change, just update the time (no user changes needed)

    try {
      // make the call to updateScheduleEvent
      await updateScheduleEvent(info.event.extendedProps.eventId, updateScheduleEventPayload);

      // toast
      addToast({
        title: 'Updated Schedule Event',
        type: 'success',
      });

      // if the api call is successful, we need to update the eventsData in calendar via refresh
      // call backback or the data in the view event will be stale
      if (callback) {
        const calendarApi = calendarRef.current?.getApi().view;
        callback(calendarApi.activeStart, calendarApi.activeEnd);
      }
    } catch (err) {
      // else we need to revert the change by calling info.revert()
      console.error(`Error updating schedule event: ${err}`);
      showErrorToast(err);
      logError(err);
      info.revert();
    }
  };

  // external event drop handler
  const handleCustomEventDrop = async (e: DragEvent) => {
    e.preventDefault();
    const eventId = (e.target as HTMLElement).closest('.fc-event').getAttribute('data-event-id');
    const eventData = calendarRef.current?.getApi().getEventById(eventId);

    // if it's a time off event, ignore the drop
    if (eventData.extendedProps.isTimeOff) {
      return;
    }

    const transferredUserData: Partial<DraggableHammrUser>[] = JSON.parse(e.dataTransfer.getData('application/json'));

    const allTransferredMemberIds = transferredUserData.map((member) => {
      return member.id;
    });

    // Check for time off conflicts
    const eventStartDate = dayjs(eventData.start).format('YYYY-MM-DD');
    const eventEndDate = dayjs(eventData.end).format('YYYY-MM-DD');

    const conflicts = timeOffEvents?.filter((timeOff) => {
      const timeOffDate = dayjs(timeOff.startTime).format('YYYY-MM-DD');
      const isDateInRange = timeOffDate >= eventStartDate && timeOffDate <= eventEndDate;
      const isAffectedUser = allTransferredMemberIds.includes(timeOff.resourceId);
      return isDateInRange && isAffectedUser;
    });

    if (conflicts?.length > 0) {
      // Get unique user names from conflicts
      const userNames = [
        ...new Set(
          conflicts.map((conflict) => {
            const user = availableUsers.find((u) => u.id === conflict.resourceId);
            return user ? `${user.firstName} ${user.lastName}` : 'Unknown User';
          })
        ),
      ];

      // Check if it's a crew being dragged
      const isCrew = transferredUserData.every((member) => member.hasOwnProperty('crewWasDragged'));

      addToast({
        title: 'Schedule Conflict',
        description: isCrew
          ? `Cannot add crew. Some crew members have time off during these dates: ${userNames.join(', ')}`
          : `Cannot schedule on these dates. ${userNames.join(', ')} ${userNames.length > 1 ? 'have' : 'has'} time off.`,
        type: 'error',
      });

      return;
    }

    // Get existing assigned user IDs from the event
    const existingUserIds = eventData.extendedProps.assigned.map((user) => user.id);

    // Find which users need to be added (not already assigned)
    const usersToAdd = allTransferredMemberIds.filter((id) => !existingUserIds.includes(id));

    const updateScheduleEventPayload: UpdateScheduleEvent = {};

    // Only add users that aren't already assigned
    if (usersToAdd.length > 0) {
      updateScheduleEventPayload.addUsers = usersToAdd.join(',');
    } else {
      // If all users are already assigned, no update needed
      addToast({
        title: 'Users already assigned',
        description: 'All selected users are already assigned to this event.',
        type: 'info',
      });
      return;
    }

    try {
      // make the call to updateScheduleEvent
      await updateScheduleEvent(eventData.extendedProps.eventId, updateScheduleEventPayload);

      // toast
      const crewWasDragged = transferredUserData.every((member) => {
        return member.hasOwnProperty('crewWasDragged');
      });

      let toastMsg = `${transferredUserData
        .map((member) => member.fullName)
        .join(', ')} has been added to ${eventData.extendedProps.projectName}`;
      if (crewWasDragged) {
        toastMsg = `${transferredUserData[0].crewName} has been added to ${eventData.extendedProps.projectName}`;
      }

      addToast({
        title: toastMsg,
        type: 'success',
      });

      // Update the local event data with combined users
      let copiedAssignedUsersProp = [...eventData.extendedProps.assigned];
      // Filter out existing users from transferred data to avoid duplicates
      const newUsers = transferredUserData.filter((user) => !existingUserIds.includes(user.id));
      copiedAssignedUsersProp = copiedAssignedUsersProp.concat(newUsers);
      eventData.setExtendedProp('assigned', copiedAssignedUsersProp);

      // if the api call is successful, we need to update the eventsData in calendar via refresh
      if (callback) {
        const calendarApi = calendarRef.current?.getApi().view;
        callback(calendarApi.activeStart, calendarApi.activeEnd);
      }
    } catch (err) {
      showErrorToast(err);
      console.error(`Error updating schedule event: ${err}`);
      logError(err);
    }
  };

  return (
    <div className="grow overflow-hidden px-8 py-6">
      <div className="relative flex h-full items-start gap-6 pt-[52px]">
        <DraggableEmployeeCrewSelection
          resourceTypeSelected={resourceTypeSelected}
          innerRef={draggableEl}
          availableUsers={availableUsers}
          availableCrews={availableCrews}
          activeStartTimestamp={momentActiveStartTimestamp}
          activeEndTimestamp={momentActiveEndTimestamp}
          shouldManage={calendarView === 'resourceTimelineDay' ? true : false}
        />
        <div className="relative h-full grow">
          <FullCalendar
            ref={calendarRef}
            plugins={[
              resourceTimelinePlugin,
              resourceDayGridPlugin,
              scrollgridPlugin,
              dayGridPlugin,
              interactionPlugin,
              timeGridPlugin,
            ]}
            headerToolbar={{
              left: 'dayGridMonth,resourceTimelineWeek,resourceTimelineDay',
              center: 'prev,title,next',
              right: '',
            }}
            datesSet={handleDatesSet}
            firstDay={weekStartDateToInt(weekStartDay)}
            slotDuration={{ days: 1 }}
            nextDayThreshold="08:00:00"
            initialView="resourceTimelineWeek"
            nowIndicator={false}
            editable={true}
            droppable={true}
            eventDurationEditable={false}
            selectable={true}
            selectMirror={true}
            resources={resourcesData}
            events={eventsData}
            resourceAreaColumns={[{}]}
            slotLabelFormat={{
              weekday: 'short', // Display the abbreviated weekday (e.g., "Sun")
              month: 'short', // Display the abbreviated month name (e.g., "Jul")
              day: 'numeric', // Display the numeric day (e.g., "18")
            }}
            select={handleDateTimeSelect}
            eventContent={handleRenderEventContent}
            eventClick={handleEventClick}
            eventDrop={handleEventDrop}
            eventAllow={() => {
              return true;
            }}
            eventDidMount={(info) => {
              // store id in event element(s)
              info.el.setAttribute('data-event-id', info.event.id);

              // Only add drag/drop handlers if it's not a time off event
              if (!info.event.extendedProps.isTimeOff) {
                info.el.addEventListener('dragover', (ev) => {
                  ev.preventDefault();
                });

                info.el.addEventListener('drop', (ev) => {
                  handleCustomEventDrop(ev);
                });
              }
            }}
            eventWillUnmount={(info) => {
              // Only remove listeners if it's not a time off event
              if (!info.event.extendedProps.isTimeOff) {
                info.el.removeEventListener('dragover', (ev) => {
                  ev.preventDefault();
                });

                info.el.removeEventListener('drop', (ev) => {
                  handleCustomEventDrop(ev);
                });
              }
            }}
            resourceAreaWidth="150px"
            slotMinWidth={175}
            eventMinWidth={175}
            dayMinWidth={175}
            schedulerLicenseKey={process.env.NEXT_PUBLIC_FC_LICENSE}
          />
          {createPortal(
            <DropdownPicker
              placeholder="Select an option"
              className="h-9 w-full pl-2.5 font-normal shadow-none ring-0 [&>span]:grow-0 [&>span]:text-sub-600"
              items={[
                { label: 'Projects', value: 'projects' },
                { label: 'Employees', value: 'employees' },
              ]}
              value={resourceTypeSelected}
              onChange={(value: string) => resourceSelectHandler(value)}
            />,
            document.querySelector('.fc-header-toolbar') ?? document.body
          )}
          <div
            className={cn(`absolute inset-0 z-10 flex items-center justify-center rounded-xl bg-foreground/5`, {
              hidden: !isLoading,
            })}
          >
            <LoadingIndicator />
          </div>
        </div>
      </div>
    </div>
  );
}
