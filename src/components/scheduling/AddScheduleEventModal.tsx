import { <PERSON><PERSON>, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { RiCalendarLine } from '@remixicon/react';
import { useForm } from 'react-hook-form';
import ScheduleEventForm from './ScheduleEventForm';
import { FormV2 } from '../elements/Form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FormValues, getNotificationSettings } from './utils';
import dayjs from 'dayjs';
import { useCompany } from '@/hooks/useCompany';
import { useEffect } from 'react';
import { CreateScheduleEvent } from '@/interfaces/schedule-event';
import { minutesAfterMidnight } from '@/utils/dateHelper';
import { createScheduleEvent } from '@/services/schedule-event';
import { addToast } from '@/hooks/useToast';
import { apiRequest } from '@/utils/requestHelpers';
import { Project } from '@/interfaces/project';
import { sortListAlphabetically } from '@/utils/collectionHelpers';

interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
  currentResourceData: {
    end: Date;
    endStr: string; // YYYY-MM-DD
    resource: {
      extendedProps: object;
      id: string;
      title: string;
    };
    resourceType: 'projects' | 'employees';
    start: Date;
    startStr: string; // YYYY-MM-DD
  } | null;
  equipmentId?: number;
}

export default function AddScheduleEventModal({ open, setOpen, currentResourceData, equipmentId }: Props) {
  const { company } = useCompany();

  const form = useForm<FormValues>();

  useEffect(() => {
    const startDate = currentResourceData?.start
      ? dayjs(currentResourceData.start).toDate()
      : dayjs().add(1, 'day').toDate(); // Default to tomorrow

    const startTime = company?.timeTrackingSettings.defaultClockInTime
      ? dayjs(company.timeTrackingSettings.defaultClockInTime, 'HH:mm:ss').toDate()
      : dayjs().hour(7).minute(0).second(0).toDate(); // Default to 7:00 AM

    let notifyEmployees = true;
    const notificationSettings = getNotificationSettings({
      startDate,
      startTime,
      defaultNotificationTime: company?.schedulingSettings.defaultNotificationTime,
    });
    if (notificationSettings.notificationTiming === null) {
      notifyEmployees = false;
    }

    form.reset({
      isMultipleDays: false,
      startDate,
      endDate: null,
      skipWeekends: false,
      startTime,
      endTime: company?.timeTrackingSettings.defaultClockOutTime
        ? dayjs(company.timeTrackingSettings.defaultClockOutTime, 'HH:mm:ss').toDate()
        : dayjs().hour(16).minute(0).second(0).toDate(), // Default to 4:00 PM
      selectedEmployeeIds: currentResourceData?.resourceType === 'employees' ? [+currentResourceData.resource.id] : [],
      selectedProject: currentResourceData?.resourceType === 'projects' ? currentResourceData.resource.id : null,
      selectedCostCode: null,
      selectedEquipmentIds: equipmentId ? [equipmentId] : [],
      notes: '',
      notifyEmployees,
      notificationTiming: notificationSettings.notificationTiming,
      notificationTimestamp: notificationSettings.notificationTimestamp,
      textNotification: notifyEmployees,
      pushNotification: notifyEmployees,
    });
  }, [form, company, currentResourceData, equipmentId, open]);

  const queryClient = useQueryClient();

  const projectsQuery = useQuery({
    queryKey: ['projects'],
    async queryFn() {
      const res = await apiRequest<{ projects: Project[] }>('projects');
      return sortListAlphabetically(res.projects, 'name');
    },
  });

  const createScheduleMutation = useMutation({
    async mutationFn(formData: FormValues) {
      const scheduleEvents: CreateScheduleEvent[] = [];

      if (formData.isMultipleDays && formData.endDate) {
        const linkedEventId = crypto.randomUUID();
        let currentDate = dayjs(formData.startDate).startOf('day'); // starOf day to reset time to 00:00:00
        while (currentDate.isSameOrBefore(dayjs(formData.endDate))) {
          if (formData.skipWeekends && (currentDate.day() === 0 || currentDate.day() === 6)) {
            currentDate = currentDate.add(1, 'day');
            continue;
          }

          const startTimeDayjs = dayjs(formData.startTime);
          const endTimeDayjs = dayjs(formData.endTime);
          const isNightShift =
            formData.startTime &&
            formData.endTime &&
            minutesAfterMidnight(endTimeDayjs) < minutesAfterMidnight(startTimeDayjs);

          const scheduleEvent: CreateScheduleEvent = {
            startTime: currentDate.hour(startTimeDayjs.hour()).minute(startTimeDayjs.minute()).second(0).valueOf(),
            endTime: currentDate
              .add(isNightShift ? 1 : 0, 'day')
              .hour(endTimeDayjs.hour())
              .minute(endTimeDayjs.minute())
              .second(0)
              .valueOf(),
            userIds: formData.selectedEmployeeIds.join(','),
            projectId: Number(formData.selectedProject),
            ...(formData.selectedCostCode ? { costCodeId: Number(formData.selectedCostCode) } : {}),
            equipmentIds: formData.selectedEquipmentIds.join(','),
            note: formData.notes,
            notificationScheduledAt: formData.notifyEmployees
              ? formData.notificationTiming === 'later'
                ? dayjs(formData.notificationTimestamp)
                    .add(currentDate.diff(formData.notificationTimestamp, 'day'), 'day')
                    .valueOf()
                : dayjs().add(currentDate.diff(dayjs(), 'day')).valueOf()
              : undefined,
            notifyViaText: formData.textNotification,
            notifyViaPush: formData.pushNotification,
            linkedEventId,
          };

          scheduleEvents.push(scheduleEvent);
          currentDate = currentDate.add(1, 'day');
        }
      } else {
        const startTimeDayjs = dayjs(formData.startTime);
        const endTimeDayjs = dayjs(formData.endTime);
        const isNightShift =
          formData.startTime &&
          formData.endTime &&
          minutesAfterMidnight(endTimeDayjs) < minutesAfterMidnight(startTimeDayjs);

        const scheduleEvent: CreateScheduleEvent = {
          userIds: formData.selectedEmployeeIds.join(','),
          equipmentIds: formData.selectedEquipmentIds.join(','),
          projectId: Number(formData.selectedProject),
          startTime: dayjs(formData.startDate)
            .hour(startTimeDayjs.hour())
            .minute(startTimeDayjs.minute())
            .second(0)
            .valueOf(),
          endTime: dayjs(formData.startDate)
            .add(isNightShift ? 1 : 0, 'day')
            .hour(endTimeDayjs.hour())
            .minute(endTimeDayjs.minute())
            .second(0)
            .valueOf(),
          note: formData.notes,
          notificationScheduledAt: formData.notifyEmployees
            ? formData.notificationTiming === 'later'
              ? formData.notificationTimestamp?.valueOf()
              : dayjs().valueOf()
            : undefined,
          notifyViaText: formData.textNotification,
          notifyViaPush: formData.pushNotification,
        };
        scheduleEvents.push(scheduleEvent);
      }

      const promises = scheduleEvents.map((payload) => createScheduleEvent(payload));
      await Promise.all(promises);
    },
    onSuccess(_, formData) {
      queryClient.invalidateQueries({ queryKey: ['scheduleEvents'] });

      const projectName = projectsQuery.data?.find((p) => p.id === Number(formData.selectedProject))?.name;
      addToast({
        title: 'Added Schedule Event',
        description: (
          <>
            Successfully added a schedule event for the project <strong className="font-medium">{projectName}</strong>.
          </>
        ),
        type: 'success',
      });

      setOpen(false);
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<RiCalendarLine className="text-sub-600" />} title="Add Schedule Event" />
        <FormV2
          onCancel={() => setOpen(false)}
          onSubmit={form.handleSubmit((formData) => createScheduleMutation.mutate(formData))}
          submitText="Add Schedule"
          isLoading={createScheduleMutation.isPending}
          className="flex flex-col gap-5"
        >
          <ScheduleEventForm form={form} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
