import { useState, useEffect, useRef, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';

import { getPayrolls } from 'services/payroll';
import { get401kReport } from 'services/report';
import { apiRequestCheck } from 'utils/requestHelpers';

import { useToast } from '@/hooks/useToast';
import { useAuth } from 'hooks/useAuth';

import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';

import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormV2 } from 'components/elements/Form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { LoadMorePayrollsButton } from '@/components/reports/LoadMorePayrollsButton';
import { SelectItem } from '@/hammr-ui/components/select';
import LineChartLine from '@/hammr-icons/LineChartLine';

const errorMessage = (
  <>
    There was a problem generating your 401k report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

const FourZeroOneKReport = () => {
  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
    watch,
  } = useForm();

  const { addToast } = useToast();
  const { user } = useAuth();

  const currentValues = watch();

  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoadingPayrolls, setIsLoadingPayrolls] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [payrollList, setPayrollList] = useState([]);
  const nextPayrollRef = useRef(null);

  useEffect(() => {
    if (!isModalOpen) {
      reset();
    }
  }, [isModalOpen, reset]);

  const getPayrollList = useCallback(
    async (useNext = false) => {
      setIsLoadingPayrolls(true);
      let payrollResponse;

      if (useNext) {
        const formattedUrl = nextPayrollRef.current
          .replace('https://sandbox.checkhq.com/', '')
          .replace('https://api.checkhq.com/', '');
        payrollResponse = await apiRequestCheck(formattedUrl);
      } else {
        payrollResponse = await getPayrolls(user.checkCompanyId);
      }

      let payrollListResponse;

      if (useNext) {
        const allLists = [...payrollList, ...payrollResponse.results];
        const allIds = new Set();

        allLists.forEach((payroll) => {
          allIds.add(payroll.id);
        });

        const uniqueList = Array.from(allIds).map((payrollId) => allLists.find((payroll) => payroll.id === payrollId));
        payrollListResponse = uniqueList;
      } else {
        payrollListResponse = payrollResponse.results;
      }
      nextPayrollRef.current = payrollResponse.next;

      if (payrollList) {
        // alternatively just query for paid payrolls
        const filteredPayrolls = payrollListResponse.filter((payroll) => {
          return payroll.status !== 'draft';
        });

        // Sort the results in DESC order of period_end and convert from 2024-07-19 to the Jul 19, 2024 format to display.
        const sortedPayrollList = filteredPayrolls.sort((a, b) => {
          return dayjs(b.period_end).diff(dayjs(a.period_end));
        });

        setPayrollList(sortedPayrollList);
      }
      setIsLoadingPayrolls(false);
    },
    [payrollList, user.checkCompanyId]
  );

  useEffect(() => {
    getPayrollList();
  }, []);

  const onSubmit = async (data) => {
    setIsProcessing(true);
    const selectedPayroll = payrollList.find((payroll) => payroll.id === data.payrollId);

    const payload = {
      payrollId: data.payrollId,
    };

    try {
      const res = (await get401kReport(payload)) as Response;
      const blob = await res.blob();

      const binaryData = [];
      binaryData.push(blob);
      const file = window.URL.createObjectURL(new Blob(binaryData, { type: 'text/csv;charset=UTF-8' }));

      const filename = res.headers.get('Content-Disposition').match(/filename="?([^"]+)"?/)[1];

      const a = document.createElement('a');
      a.href = file;
      a.download = `${dayjs(selectedPayroll.period_end).format('MM_DD_YYYY')}-${filename}`;
      document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox

      a.click();
      a.remove(); //afterwards we remove the element again
    } catch (err) {
      addToast({
        type: 'error',
        title: 'Failed To Generate Report',
        description: errorMessage,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">401k Report</h3>
      <p className="mt-1.5 h-fit text-sub-600">
        Detailed breakdown of employee and company contributions across 401(k) plans per payroll.
      </p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setIsModalOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogSurface>
          <DialogHeader icon={<LineChartLine className="text-sub-600" />} title="401k Report" />
          <FormV2
            onSubmit={handleSubmit(onSubmit)}
            onCancel={() => setIsModalOpen(false)}
            isLoading={isProcessing}
            submitText="Generate"
          >
            <ControlledSelect
              name="payrollId"
              label="Payroll Week Ending"
              control={control}
              rules={{ required: 'Please select a payroll week ending' }}
              error={errors['payrollId']?.message as string}
              required
            >
              {payrollList.map((payroll) => (
                <SelectItem key={payroll.id} value={String(payroll.id)}>
                  {dayjs(payroll.period_end).format('MMM DD, YYYY')}
                  <span className="ml-3">({payroll.type === 'off_cycle' ? 'Off-cycle' : 'Regular'})</span>
                </SelectItem>
              ))}
              {nextPayrollRef.current?.length > 0 && (
                <LoadMorePayrollsButton
                  isLoadingPayrolls={isLoadingPayrolls}
                  onLoadMore={() => {
                    getPayrollList(true);
                    reset({ ...currentValues, payrollId: undefined });
                  }}
                />
              )}
            </ControlledSelect>
          </FormV2>
        </DialogSurface>
      </Dialog>
    </section>
  );
};

export default FourZeroOneKReport;
