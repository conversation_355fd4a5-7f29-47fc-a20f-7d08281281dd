import React, { useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { AgGridReact } from '@ag-grid-community/react';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { ModalV2 } from '../elements/ModalV2';
import { yupResolver } from '@/utils/yupResolver';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { FormV2 } from '@/components/elements/Form';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { useToast } from '@/hooks/useToast';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { apiRequest } from '@/utils/requestHelpers';
import { useQuery } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { SelectItem } from '@/hammr-ui/components/select';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { TimeOffPolicy, TimeOffPolicyType } from '@/interfaces/timeoff';
import { exportTableToPDF } from '@/utils/table';
import LineChart from '@/hammr-icons/LineChart';
import { formatHours } from '@/utils/format';

const schema = yup
  .object({
    year: yup.string().required('Year is required'),
    format: yup.string().oneOf(['csv', 'pdf']).required('Format is required'),
  })
  .required();

type FormData = {
  year: string;
  format: string;
};

interface TimeOffPolicyBalance {
  id: number;
  name: string;
  type: TimeOffPolicyType;
  accruedHours?: number | null;
  usedHours: number;
  availableHours?: number | null;
}

interface UserTimeOffBalance {
  id: number;
  firstName: string;
  lastName: string;
  policies: TimeOffPolicyBalance[];
}

type FlattenedTimeOffBalance = Pick<TimeOffPolicyBalance, 'accruedHours' | 'usedHours' | 'availableHours'> & {
  employeeName: string;
  policyName: string;
};

const formatOptions = [
  { label: 'CSV', value: 'csv' },
  { label: 'PDF', value: 'pdf' },
];

export default function TimeOffBalancesReport() {
  const [open, setOpen] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Time-Off Balances</h3>
      <p className="mt-1.5 h-fit text-sub-600">
        Summary of employee time-off balance, including accrued and used hours.
      </p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <ModalV2 open={open} setOpen={setOpen} title="Time-Off Balances" icon={<LineChart />}>
        <ModalContent setOpen={setOpen} />
      </ModalV2>
    </section>
  );
}

const errorMessage = (
  <>
    There was a problem generating your report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

function ModalContent({ setOpen }: { setOpen: (isOpen: boolean) => void }) {
  const { addToast } = useToast();
  const gridRef = useRef<AgGridReact>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<UserTimeOffBalance[]>([]);
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      year: undefined,
      format: 'pdf',
    },
  });

  const policies = useQuery({
    queryKey: ['time-off-policies'],
    queryFn: () =>
      apiRequest<{
        timeOffPolicies: TimeOffPolicy[];
      }>('time-off-policies').then((response) => response.timeOffPolicies),
  });

  const yearOptions = useMemo(() => {
    if (!policies.data?.length) return [];

    const oldestYear = new Date(
      Math.min(...policies.data.map((policy) => new Date(policy.createdAt).getTime()))
    ).getFullYear();
    const currentYear = new Date().getFullYear();

    setValue('year', currentYear.toString());

    return Array.from({ length: currentYear - oldestYear + 1 }, (_, index) => ({
      value: (currentYear - index).toString(),
      label: (currentYear - index).toString(),
    }));
  }, [policies.data, setValue]);

  const flattenedData = useMemo(() => {
    return data.flatMap((user) =>
      user.policies.map((policy) => ({
        employeeName: `${user.firstName} ${user.lastName}`,
        policyName: policy.name,
        accruedHours: policy.accruedHours,
        usedHours: policy.usedHours,
        availableHours: policy.availableHours,
      }))
    );
  }, [data]);

  const columnDefs = useMemo<ColDef<FlattenedTimeOffBalance>[]>(
    () => [
      {
        headerName: 'Employee Name',
        field: 'employeeName',
        minWidth: 200,
      },
      {
        headerName: 'Policy',
        field: 'policyName',
        minWidth: 150,
      },
      {
        headerName: 'Total Accrued Hours',
        field: 'accruedHours',
        minWidth: 150,
        valueFormatter: (
          params: ValueFormatterParams<FlattenedTimeOffBalance, FlattenedTimeOffBalance['accruedHours']>
        ) => {
          return params.value != null ? formatHours(params.value) : '-';
        },
      },
      {
        headerName: 'Total Used Hours',
        field: 'usedHours',
        minWidth: 150,
        valueFormatter: (
          params: ValueFormatterParams<FlattenedTimeOffBalance, FlattenedTimeOffBalance['usedHours']>
        ) => {
          return formatHours(params.value);
        },
      },
      {
        headerName: 'Ending Balance',
        field: 'availableHours',
        minWidth: 150,
        valueFormatter: (
          params: ValueFormatterParams<FlattenedTimeOffBalance, FlattenedTimeOffBalance['availableHours']>
        ) => {
          return params.value != null ? formatHours(params.value) : '-';
        },
      },
    ],
    []
  );

  const defaultColDef = useMemo(
    () => ({
      resizable: true,
      sortable: true,
    }),
    []
  );

  const onSubmit = async (formData: FormData) => {
    setIsLoading(true);
    try {
      const response = await apiRequest<UserTimeOffBalance[]>(`/time-off-policies/balances/${formData.year}`);
      setData(response);

      setTimeout(() => {
        const gridApi = gridRef.current?.api;
        if (gridApi) {
          const fileName = `time_off_balances_${formData.year}`;

          if (formData.format === 'pdf') {
            const title = `Time-Off Balances Report\nYear ${formData.year}`;
            exportTableToPDF(gridApi, fileName + '.pdf', title);
          } else {
            gridApi.exportDataAsCsv({
              fileName: fileName + '.csv',
            });
          }
        }
      }, 1000);
    } catch (error) {
      console.error(error);
      addToast({
        type: 'error',
        title: 'Failed To Generate Report',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (policies.isPending) {
    return (
      <div className="mt-8 flex justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  return (
    <FormV2
      onSubmit={handleSubmit(onSubmit)}
      isLoading={isLoading}
      submitText={isLoading ? 'Generating...' : 'Download'}
      onCancel={() => setOpen(false)}
    >
      <div className="flex flex-col justify-center gap-5 self-stretch">
        <div className="space-y-2">
          <ControlledSelect
            name="year"
            label="Year"
            required
            control={control}
            rules={{ required: 'Year is required' }}
            error={errors.year?.message}
          >
            {yearOptions.map((item) => (
              <SelectItem key={item.value} value={item.value.toString()}>
                {item.label}
              </SelectItem>
            ))}
          </ControlledSelect>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-strong-950">Report Format</label>
          <Controller
            name="format"
            control={control}
            render={({ field }) => (
              <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-col gap-3">
                {formatOptions.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={option.value} />
                    <label htmlFor={option.value} className="text-sm font-medium leading-none text-strong-950">
                      {option.label}
                    </label>
                  </div>
                ))}
              </RadioGroup>
            )}
          />
          {errors.format && <p className="text-sm text-error-base">{errors.format.message}</p>}
        </div>
      </div>

      <div className="hidden">
        <UpdatedTable
          parentRef={gridRef}
          rowData={flattenedData}
          colDefs={columnDefs}
          defaultColDef={defaultColDef}
          isLoading={isLoading}
          emptyRowsText="No data available"
        />
      </div>
    </FormV2>
  );
}
