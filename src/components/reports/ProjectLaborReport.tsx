import React, { useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { AgGridReact } from '@ag-grid-community/react';
import type { GridApi } from '@ag-grid-community/core';
import { ColDef } from '@ag-grid-community/core';
import { ModalV2 } from '../elements/ModalV2';
import LineChart from '@hammr-icons/LineChart';
import { yupResolver } from '@/utils/yupResolver';
import { customCellValueFormatter, exportTableToPDF } from 'utils/table';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { FormV2 } from '@/components/elements/Form';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { useToast } from '@/hooks/useToast';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { apiRequest } from '@/utils/requestHelpers';
import dayjs from 'dayjs';
import { formatMinutesToHoursWorked, formatUSD } from '@/utils/format';
import { useCompany } from '@/hooks/useCompany';

const schema = yup
  .object({
    startDate: yup.date().required('Start date is required'),
    endDate: yup.date().required('End date is required').min(yup.ref('startDate'), 'End date must be after start date'),
    format: yup.string().oneOf(['csv', 'pdf']).required('Format is required'),
  })
  .required();

type FormData = {
  startDate?: Date;
  endDate?: Date;
  format: string;
};

interface ProjectLaborData {
  projectName: string;
  projectNumber: string;
  regMinutes: string;
  regEarnings: string;
  otMinutes: string;
  otEarnings: string;
  dotMinutes: string;
  dotEarnings: string;
  ytdMinutes: string;
  ytdEarnings: string;
}

interface ProjectLaborResponse {
  projects: ProjectLaborData[];
}

export default function ProjectLaborReport() {
  const [open, setOpen] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Project Labor Report</h3>
      <p className="mt-1.5 h-fit text-sub-600">Breakdown of hours and earnings for all projects in a date range</p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <ModalV2 open={open} setOpen={setOpen} title="Project Labor Report" icon={<LineChart />}>
        <ModalContent setOpen={setOpen} />
      </ModalV2>
    </section>
  );
}

const errorMessage = (
  <>
    There was a problem generating your report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

const formatOptions = [
  { label: 'CSV', value: 'csv' },
  { label: 'PDF', value: 'pdf' },
];

function getTotalValue(colId: string, gridAPI: GridApi<unknown>) {
  if (colId === 'projectName') {
    return 'Total';
  }

  if (colId === 'projectNumber') {
    return '';
  }

  // For numeric columns, calculate the sum of all values
  if (
    [
      'regMinutes',
      'regEarnings',
      'otMinutes',
      'otEarnings',
      'dotMinutes',
      'dotEarnings',
      'driveTimeMinutes',
      'driveTimeEarnings',
      'ytdMinutes',
      'ytdEarnings',
    ].includes(colId)
  ) {
    // Get all leaf nodes (non-group rows)
    const allLeafNodes: any[] = [];
    gridAPI.forEachLeafNode((node) => {
      allLeafNodes.push(node);
    });

    // Calculate sum of values for the column
    const sum = allLeafNodes.reduce((total, node) => total + (Number(node.data[colId]) || 0), 0);

    // Format the value based on column type
    if (colId.includes('Earnings')) {
      return formatUSD.format(sum);
    } else if (colId.includes('Minutes')) {
      return formatMinutesToHoursWorked(sum);
    }
  }

  return '';
}

function ModalContent({ setOpen }: { setOpen: (isOpen: boolean) => void }) {
  const { addToast } = useToast();
  const gridRef = useRef<AgGridReact>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<ProjectLaborData[]>([]);
  const { company } = useCompany();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      format: 'pdf',
    },
  });

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        field: 'projectName',
        headerName: 'Project Name',
        minWidth: 150,
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">Total</span>;
          } else if (params.node.group) {
            return params.node.key;
          } else {
            return params.value;
          }
        },
      },
      {
        field: 'projectNumber',
        headerName: 'Project Number',
        minWidth: 120,
      },
      {
        field: 'regMinutes',
        headerName: 'Regular Hours',
        minWidth: 120,
        valueFormatter: (params) => {
          return formatMinutesToHoursWorked(
            customCellValueFormatter({
              params,
              type: 'billableTime',
              skipFormatting: true,
            })
          );
        },
        cellRenderer: (params) => {
          // Check if this is a grand total row
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      {
        field: 'regEarnings',
        headerName: 'Regular Earnings',
        minWidth: 130,
        valueFormatter: (params) => customCellValueFormatter({ params, type: 'currency' }),
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      {
        field: 'otMinutes',
        headerName: 'OT Hours',
        minWidth: 100,
        valueFormatter: (params) => {
          return formatMinutesToHoursWorked(
            customCellValueFormatter({
              params,
              type: 'billableTime',
              skipFormatting: true,
            })
          );
        },
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      {
        field: 'otEarnings',
        headerName: 'OT Earnings',
        minWidth: 110,
        valueFormatter: (params) => customCellValueFormatter({ params, type: 'currency' }),
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      {
        field: 'dotMinutes',
        headerName: 'DOT Hours',
        minWidth: 100,
        valueFormatter: (params) => {
          return formatMinutesToHoursWorked(
            customCellValueFormatter({
              params,
              type: 'billableTime',
              skipFormatting: true,
            })
          );
        },
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      {
        field: 'dotEarnings',
        headerName: 'DOT Earnings',
        minWidth: 110,
        valueFormatter: (params) => customCellValueFormatter({ params, type: 'currency' }),
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      ...(company?.timeTrackingSettings.isDriveTimeEnabled
        ? [
            {
              field: 'driveTimeMinutes',
              headerName: 'Drive Time Hours',
              minWidth: 100,
              valueFormatter: (params) => {
                return formatMinutesToHoursWorked(
                  customCellValueFormatter({
                    params,
                    type: 'billableTime',
                    skipFormatting: true,
                  })
                );
              },
              cellRenderer: (params) => {
                if (params.node.footer) {
                  return <span className="font-medium">{params.valueFormatted}</span>;
                }
                return params.valueFormatted;
              },
            },
            {
              field: 'driveTimeEarnings',
              headerName: 'Drive Time Earnings',
              minWidth: 110,
              valueFormatter: (params) => customCellValueFormatter({ params, type: 'currency' }),
              cellRenderer: (params) => {
                if (params.node.footer) {
                  return <span className="font-medium">{params.valueFormatted}</span>;
                }
                return params.valueFormatted;
              },
            },
          ]
        : []),
      {
        field: 'ytdMinutes',
        headerName: 'YTD Hours',
        minWidth: 100,
        valueFormatter: (params) => {
          return formatMinutesToHoursWorked(
            customCellValueFormatter({
              params,
              type: 'billableTime',
              skipFormatting: true,
            })
          );
        },
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
      {
        field: 'ytdEarnings',
        headerName: 'YTD Earnings',
        minWidth: 110,
        valueFormatter: (params) => customCellValueFormatter({ params, type: 'currency' }),
        cellRenderer: (params) => {
          if (params.node.footer) {
            return <span className="font-medium">{params.valueFormatted}</span>;
          }
          return params.valueFormatted;
        },
      },
    ],
    []
  );

  const defaultColDef = useMemo(
    () => ({
      resizable: true,
      minWidth: 100,
    }),
    []
  );

  const onSubmit = async (formData: FormData) => {
    if (!formData.startDate || !formData.endDate) return;

    setIsLoading(true);
    try {
      const startDate = dayjs(formData.startDate);
      const endDate = dayjs(formData.endDate);

      const response = await apiRequest<ProjectLaborResponse>(`report/project-labor`, {
        method: 'POST',
        body: {
          startDate: startDate.format('YYYY-MM-DD'),
          endDate: endDate.format('YYYY-MM-DD'),
        },
      });

      const sortedData = response.projects.sort((a, b) => a.projectName.localeCompare(b.projectName));

      setData(sortedData);

      // Handle export
      setTimeout(() => {
        const gridApi = gridRef.current?.api;
        if (gridApi) {
          const fileName = `project_labor_${startDate.format('MM_DD_YYYY')}_${endDate.format('MM_DD_YYYY')}`;

          const formattedDateRange = `${dayjs(formData.startDate).format('MM/DD/YYYY')} to ${dayjs(formData.endDate).format('MM/DD/YYYY')}`;

          if (formData.format === 'pdf') {
            const reportTitle = company.name;

            const header = function (currentPage) {
              if (currentPage === 1) {
                return {
                  columns: [
                    { text: dayjs().format('YYYY-MM-DD HH:mm:ss'), alignment: 'left', fontSize: 8 },
                    {
                      stack: [
                        { text: reportTitle, alignment: 'center' },
                        {
                          text: formattedDateRange,
                          alignment: 'center',
                          fontSize: 8,
                        },
                      ],
                      width: 'auto',
                    },
                    { text: '', alignment: 'right' },
                  ],
                  margin: [12, 12, 12, 12],
                  fontSize: 14,
                  bold: true,
                };
              }
            };

            exportTableToPDF(gridRef.current!.api, fileName + '.pdf', header, {
              footerRenderer: (gridAPI) => {
                const columnDefs = gridAPI.getColumnDefs() as any[];
                const fields = columnDefs.filter((col) => !col.hide).map((col) => col.field);

                return fields.map((column) => ({
                  text: getTotalValue(column, gridAPI),
                  fontSize: 9,
                  bold: true,
                }));
              },
            });
          } else {
            const title = `${company.name}\nProject Labor Report\n${formattedDateRange}`;

            const exportParams = {
              prependContent: title,
              fileName: fileName + '.csv',
              allColumns: false,
              skipHeader: false,
              skipFooters: false,
              skipGroups: false,
              skipPinnedTop: false,
              skipPinnedBottom: false,
              processCellCallback: (params: any) => {
                const colId = params.column.getColId();

                // Handle footer/totals row
                if (params.node.footer) {
                  return getTotalValue(colId, gridRef.current!.api);
                }

                // Return the regular value for non-footer cells
                return params.formatValue(params.value);
              },
            };

            gridApi.exportDataAsCsv(exportParams);
          }
        }
      }, 1000);
    } catch (error) {
      console.error(error);
      addToast({
        type: 'error',
        title: 'Failed To Generate Report',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormV2
      onSubmit={handleSubmit(onSubmit)}
      isLoading={isLoading}
      submitText={isLoading ? 'Generating...' : 'Download'}
      onCancel={() => setOpen(false)}
    >
      <>
        <div className="flex flex-col justify-center gap-5 self-stretch">
          <ControlledDateInput
            control={control}
            name="startDate"
            required
            label="Start Date"
            rules={{ required: 'Please select a start date' }}
          />
          <ControlledDateInput
            control={control}
            name="endDate"
            required
            label="End Date"
            rules={{ required: 'Please select an end date' }}
          />

          <div className="space-y-2">
            <label className="text-sm font-medium text-strong-950">Report Format</label>
            <Controller
              name="format"
              control={control}
              render={({ field }) => (
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-col gap-3">
                  {formatOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.value} id={option.value} />
                      <label htmlFor={option.value} className="text-sm font-medium leading-none text-strong-950">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            />
            {errors.format && <p className="text-sm text-error-base">{errors.format.message}</p>}
          </div>
        </div>

        {/* AG Grid */}
        <div className="hidden">
          <UpdatedTable
            parentRef={gridRef}
            rowData={data}
            colDefs={columnDefs}
            defaultColDef={defaultColDef}
            gridOptions={{
              groupDisplayType: 'custom',
              groupDefaultExpanded: 0,
              grandTotalRow: 'bottom',
            }}
            isLoading={isLoading}
            emptyRowsText="No data available"
            disableCustomRendering
          />
        </div>
      </>
    </FormV2>
  );
}
