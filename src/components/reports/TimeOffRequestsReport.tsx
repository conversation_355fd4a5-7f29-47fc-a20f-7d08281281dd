import React, { useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { AgGridReact } from '@ag-grid-community/react';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { ModalV2 } from '../elements/ModalV2';
import LineChart from '@hammr-icons/LineChart';
import { yupResolver } from '@/utils/yupResolver';
import { exportTableToPDF } from 'utils/table';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { FormV2 } from '@/components/elements/Form';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { useToast } from '@/hooks/useToast';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { apiRequest } from '@/utils/requestHelpers';
import dayjs from 'dayjs';
import { TimeOffRequest } from '@/interfaces/timeoff';
import { PaginatedData } from '@/interfaces/pagination';
import { ValueGetterParams } from '@ag-grid-community/core/dist/types/src/entities/colDef';
import { formatHours } from '@/utils/format';

const schema = yup
  .object({
    startDate: yup.date().required('Start date is required'),
    endDate: yup.date().required('End date is required'),
    format: yup.string().oneOf(['csv', 'pdf']).required('Format is required'),
  })
  .required();

type FormData = {
  startDate?: Date;
  endDate?: Date;
  format?: string;
};

const formatOptions = [
  { label: 'CSV', value: 'csv' },
  { label: 'PDF', value: 'pdf' },
];

export default function TimeOffRequestReport() {
  const [open, setOpen] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Time-Off Requests</h3>
      <p className="mt-1.5 h-fit text-sub-600">
        Summary of employee time-off requests, including dates, statuses and time-off types.
      </p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <ModalV2 open={open} setOpen={setOpen} title="Time-Off Requests" icon={<LineChart />}>
        <ModalContent setOpen={setOpen} />
      </ModalV2>
    </section>
  );
}

const errorMessage = (
  <>
    There was a problem generating your report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

function ModalContent({ setOpen }: { setOpen: (isOpen: boolean) => void }) {
  const { addToast } = useToast();
  const gridRef = useRef<AgGridReact>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      format: 'pdf',
    },
  });

  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        field: 'user.firstName',
        headerName: 'Employee',
        valueGetter: (params: ValueGetterParams<TimeOffRequest>) => {
          return `${params.data.user.firstName} ${params.data.user.lastName}`;
        },
      },
      {
        headerName: 'Start Date',
        field: 'startDate',
        valueFormatter: (params: ValueFormatterParams<TimeOffRequest>) =>
          params.value ? dayjs(params.value).format('MM/DD/YYYY') : '',
      },
      {
        headerName: 'End Date',
        field: 'endDate',
        valueFormatter: (params: ValueFormatterParams<TimeOffRequest>) =>
          params.value ? dayjs(params.value).format('MM/DD/YYYY') : '',
      },
      {
        headerName: 'Hours',
        field: 'totalHours',
        valueFormatter: (params: ValueFormatterParams) => {
          return formatHours(params.value);
        },
      },
      {
        headerName: 'Policy',
        field: 'timeOffPolicy.name',
      },
      {
        headerName: 'Status',
        field: 'status',
      },
    ],
    []
  );

  const defaultColDef = useMemo(
    () => ({
      resizable: true,
      minWidth: 100,
    }),
    []
  );

  const onSubmit = async (data: FormData) => {
    if (!data.startDate || !data.endDate) return;

    setIsLoading(true);
    try {
      const startDate = dayjs(data.startDate);
      const endDate = dayjs(data.endDate);

      const searchParams = new URLSearchParams({
        startDate: startDate.valueOf().toString(),
        endDate: endDate.valueOf().toString(),
        limit: '0',
      });

      const timeOffRequests = await apiRequest<PaginatedData<TimeOffRequest, 'timeOffRequests'>>(
        `time-off-requests?${searchParams}`
      );

      const sortedData = timeOffRequests.timeOffRequests.sort((a, b) =>
        `${a.user.firstName} ${a.user.lastName}`.localeCompare(`${b.user.firstName} ${b.user.lastName}`)
      );

      setData(sortedData);

      // Handle export
      setTimeout(() => {
        const gridApi = gridRef.current?.api;
        if (gridApi) {
          const fileName = `time_off_requests_${startDate.format('MM_DD_YYYY')}_${endDate.format('MM_DD_YYYY')}`;

          if (data.format === 'pdf') {
            const title = `Time-Off Requests Report\n${startDate.format('MM/DD/YYYY')} - ${endDate.format('MM/DD/YYYY')}`;
            exportTableToPDF(gridRef.current!.api, fileName + '.pdf', title);
          } else {
            gridRef.current!.api.exportDataAsCsv({
              fileName: fileName + '.csv',
            });
          }
        }
      }, 1000);
    } catch (error) {
      console.error(error);
      addToast({
        type: 'error',
        title: 'Failed To Generate Report',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormV2
      onSubmit={handleSubmit(onSubmit)}
      isLoading={isLoading}
      submitText={isLoading ? 'Generating...' : 'Download'}
      onCancel={() => setOpen(false)}
    >
      <>
        <div className="flex flex-col justify-center gap-5 self-stretch">
          <ControlledDateInput
            control={control}
            name="startDate"
            required
            label="Start Date"
            rules={{ required: 'Please select a start date' }}
          />
          <ControlledDateInput
            control={control}
            name="endDate"
            required
            label="End Date"
            rules={{ required: 'Please select an end date' }}
          />

          <div className="space-y-2">
            <label className="text-sm font-medium text-strong-950">Report Format</label>
            <Controller
              name="format"
              control={control}
              render={({ field }) => (
                <RadioGroup onValueChange={field.onChange} defaultValue={field.value} className="flex flex-col gap-3">
                  {formatOptions.map((option) => (
                    <div key={option.value} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.value} id={option.value} />
                      <label htmlFor={option.value} className="text-sm font-medium leading-none text-strong-950">
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            />
            {errors.format && <p className="text-sm text-error-base">{errors.format.message}</p>}
          </div>
        </div>

        {/* AG Grid */}
        <div className="hidden">
          <UpdatedTable
            parentRef={gridRef}
            rowData={data}
            colDefs={columnDefs}
            defaultColDef={defaultColDef}
            isLoading={isLoading}
            emptyRowsText="No data available"
          />
        </div>
      </>
    </FormV2>
  );
}
