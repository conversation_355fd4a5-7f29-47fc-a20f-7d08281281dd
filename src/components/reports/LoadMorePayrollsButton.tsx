interface LoadMorePayrollsButtonProps {
  isLoadingPayrolls: boolean;
  onLoadMore: () => void;
  className?: string;
}

export const LoadMorePayrollsButton = ({
  isLoadingPayrolls,
  onLoadMore,
  className = 'w-full px-3 py-2.5 text-center text-sm leading-5',
}: LoadMorePayrollsButtonProps) => {
  return (
    <button
      className={className}
      onClick={(e) => {
        e.preventDefault();
        if (!isLoadingPayrolls) {
          onLoadMore();
        }
      }}
    >
      {isLoadingPayrolls ? 'Loading...' : 'Load more payrolls'}
    </button>
  );
};
