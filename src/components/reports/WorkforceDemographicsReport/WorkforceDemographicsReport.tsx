import { useState } from 'react';
import { <PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import dayjs from 'dayjs';
import { useMutation } from '@tanstack/react-query';

import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { TextField } from '@/components/elements/form/TextField';

import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/useToast';
import { apiRequest, downloadFileFromResponse } from '@/utils/requestHelpers';
import { RiArrowRightSLine, RiLineChartLine } from '@remixicon/react';
import { FormV2 } from '@/components/elements/Form';
import { useProjects } from '@/hooks/data-fetching/useProjects';
import { Tooltip } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import ControlledCombobox from '@/components/elements/form/ControlledCombobox';

// --- Validation Schema ---
const schema = yup.object().shape({
  reportFormat: yup.string().required(),
  projectId: yup.number().required('Please select a Project').typeError('Please select a valid project'),
  startDate: yup.date().required('Please select a start date').typeError('Invalid date'),
  endDate: yup
    .date()
    .required('Please select an end date')
    .typeError('Invalid date')
    .min(yup.ref('startDate'), 'End date cannot be before start date'),
  percentageCompleted: yup
    .number()
    .required('Please enter the percentage completed')
    .min(0, 'Percentage must be at least 0')
    .max(100, 'Percentage must not exceed 100')
    .typeError('Please enter a valid number'),
  tradeOrCraft: yup.string().required('Please enter the trade or craft'),
  signatoryName: yup.string().required('Please enter a signatory name'),
  signatoryTitle: yup.string().required('Please enter a signatory title'),
});

// Use yup.InferType to ensure FormData matches the schema
type FormData = yup.InferType<typeof schema>;

// Define the payload structure for the API
interface AA202ReportPayload {
  projectId: number;
  startDate: string;
  endDate: string;
  percentOfWorkCompleted: number;
  tradeOrCraft: string;
  signatoryName: string;
  signatoryTitle: string;
}

const GenerateReportModal = ({ isOpen, setIsOpen }: { isOpen: boolean; setIsOpen: (open: boolean) => void }) => {
  const { addToast } = useToast();
  const { user } = useAuth();

  const projects = useProjects(user.companyId);

  const { handleSubmit, control, reset, formState, watch } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      reportFormat: 'AA-202',
      projectId: undefined,
      startDate: undefined,
      endDate: undefined,
      percentageCompleted: undefined,
      tradeOrCraft: Number(user.companyId) === 76 ? 'Painter' : '',
      signatoryName: '',
      signatoryTitle: '',
    },
  });
  const errors = formState.errors;

  const generateReportMutation = useMutation<Response, Error, AA202ReportPayload>({
    mutationFn: (payload: AA202ReportPayload) =>
      apiRequest<Response>('report/aa202', {
        method: 'POST',
        convertToJson: false,
        body: payload,
      }),
    onSuccess: async (response) => {
      const filenameHeader = response.headers.get('Content-Disposition');
      const filenameMatch = filenameHeader?.match(/filename="?([^;"]+)"?/);
      const filename = filenameMatch ? filenameMatch[1] : 'workforce-demographics-report.pdf';

      // Use the helper function to download the file
      await downloadFileFromResponse(response, filename);

      addToast({
        type: 'success',
        title: 'Report Generated',
        description: 'Your Workforce Demographics report has been downloaded.',
      });
    },
  });

  const onSubmit: SubmitHandler<FormData> = (data) => {
    const payload: AA202ReportPayload = {
      projectId: data.projectId,
      startDate: dayjs(data.startDate).format('YYYY-MM-DD'),
      endDate: dayjs(data.endDate).format('YYYY-MM-DD'),
      percentOfWorkCompleted: data.percentageCompleted,
      tradeOrCraft: data.tradeOrCraft,
      signatoryName: data.signatoryName,
      signatoryTitle: data.signatoryTitle,
    };
    generateReportMutation.mutate(payload);
  };

  const startDate = watch('startDate');

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          reset();
        }
        setIsOpen(isOpen);
      }}
    >
      <DialogSurface>
        <DialogHeader icon={<RiLineChartLine className="text-sub-600" />} title="Workforce Demographics Report" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={generateReportMutation.isPending}
          submitText="Generate"
        >
          <div className="flex flex-col gap-5">
            <ControlledSelect
              name="reportFormat"
              label="Report Format"
              control={control}
              rules={{ required: true }}
              error={errors.reportFormat?.message}
              disabled /*DIsa bled for now since we have only one demographic report */
              required
            >
              <SelectItem value="AA-202">AA-202</SelectItem>
            </ControlledSelect>

            {/* Project Selection */}
            <ControlledCombobox
              name="projectId"
              label="Project"
              control={control}
              rules={{ required: 'Please select a project' }}
              error={errors['projectId']?.message as string}
              required
              items={projects.map((project) => ({
                label: (
                  <span className="flex gap-1">
                    <span className="truncate">
                      {project.name + (project.projectNumber ? ` (${project.projectNumber})` : '')}
                    </span>
                    {project.isPrevailingWage && (
                      <Tooltip content="Prevailing Wage">
                        <Badge variant="outline" color="gray">
                          PW
                        </Badge>
                      </Tooltip>
                    )}
                  </span>
                ),
                value: project.id.toString(),
              }))}
            />

            {/* Start Date */}
            <ControlledDateInput name="startDate" label="Start Date" control={control} required />

            {/* End Date */}
            <ControlledDateInput
              name="endDate"
              label="End Date"
              control={control}
              required
              dayPickerProps={{
                disabled: [(date) => date < new Date(startDate)],
              }}
            />
            <hr className="border-soft-200" />
            {/* Percentage Completed */}
            <Controller
              name="percentageCompleted"
              control={control}
              render={({ field }) => (
                <FormItem required error={!!errors.percentageCompleted}>
                  <FormLabel>Percentage of work completed</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="% Enter percentage of work completed"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === '' ? undefined : parseFloat(value));
                      }}
                      value={field.value}
                      min="0"
                      max="100"
                      step="any"
                    />
                  </FormControl>
                  <FormMessage>{errors.percentageCompleted?.message}</FormMessage>
                </FormItem>
              )}
            />

            {/* Trade or Craft */}
            <TextField
              name="tradeOrCraft"
              label="Trade or Craft"
              control={control}
              capitalizeLabel={false}
              error={errors.tradeOrCraft?.message}
              placeholder="Enter trade or craft"
              required
            />
            <hr className="border-soft-200" />

            {/* Signatory Name */}
            <TextField
              name="signatoryName"
              label="Signatory Name"
              control={control}
              error={errors.signatoryName?.message}
              placeholder="Enter signatory name"
              required
            />

            {/* Signatory Title */}
            <TextField
              name="signatoryTitle"
              label="Signatory Title"
              control={control}
              error={errors.signatoryTitle?.message}
              placeholder="Enter signatory title"
              required
            />
          </div>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

// --- Report Card Component ---
const WorkforceDemographicsReport = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Workforce Demographics Reports</h3>
      <p className="mt-1.5 h-fit text-sub-600">Reports related to demographic diversity of the workforce.</p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setIsModalOpen(true)}>
        Create
        <RiArrowRightSLine className="size-5" />
      </LinkButton>

      <GenerateReportModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} />
    </section>
  );
};

export default WorkforceDemographicsReport;
