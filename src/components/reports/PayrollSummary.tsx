import { FormEvent, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { getPayrollSummary } from 'services/company';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormV2 } from '../elements/Form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { DateInput } from '@/hammr-ui/components/date-input';
import moment from 'moment';
import LineChartLine from '@/hammr-icons/LineChartLine';
import { useToast } from '@/hooks/useToast';

const errorMessage = (
  <>
    There was a problem generating your Payroll Summary report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

const PayrollSummary: React.FC = () => {
  const { addToast } = useToast();
  const [open, setOpen] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [startDateNotSet, setStartDateNotSet] = useState(false);
  const [endDate, setEndDate] = useState(null);
  const [endDateNotSet, setEndDateNotSet] = useState(false);
  const [spinner, setSpinner] = useState(false);

  const { user } = useAuth();

  function resetLocalState() {
    setStartDate(null);
    setStartDateNotSet(false);
    setEndDate(null);
    setEndDateNotSet(false);
  }

  function downloadPayrollSummary(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setSpinner(true);

    if (!startDate) {
      setStartDateNotSet(true);
      setSpinner(false);
      return;
    }

    if (!endDate) {
      setEndDateNotSet(true);
      setSpinner(false);
      return;
    }

    getPayrollSummary(user.checkCompanyId, startDate, endDate).then(async (res) => {
      if (res) {
        setSpinner(false);
      } else {
        addToast({
          type: 'error',
          title: 'Failed To Generate Report',
          description: errorMessage,
        });
        setSpinner(false);
      }
    });
  }

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Payroll Summary</h3>
      <p className="mt-1.5 h-fit text-sub-600">
        View employees&apos; and contractors&apos; past earnings, deductions, and taxes aggregated over a period for
        your company.
      </p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            resetLocalState();
          }
          setOpen(isOpen);
        }}
      >
        <DialogSurface>
          <DialogHeader icon={<LineChartLine className="text-sub-600" />} title="Payroll Summary" />
          <FormV2
            onSubmit={downloadPayrollSummary}
            onCancel={() => {
              resetLocalState();
              setOpen(false);
            }}
            isLoading={spinner}
            submitText="Download"
          >
            <FormItem required error={startDateNotSet}>
              <FormLabel>Start Date</FormLabel>
              <FormControl>
                <DateInput
                  value={startDate ? moment(startDate).toDate() : null}
                  onChange={(value) => {
                    setStartDateNotSet(false);
                    setStartDate(value ? moment(value).format('YYYY-MM-DD') : null);
                  }}
                />
              </FormControl>
              <FormMessage>{startDateNotSet && 'Start date is required.'}</FormMessage>
            </FormItem>

            <FormItem required error={endDateNotSet} className="mt-5">
              <FormLabel>End Date</FormLabel>
              <FormControl>
                <DateInput
                  value={endDate ? moment(endDate).toDate() : null}
                  onChange={(value) => {
                    setEndDateNotSet(false);
                    setEndDate(value ? moment(value).format('YYYY-MM-DD') : null);
                  }}
                />
              </FormControl>
              <FormMessage>{endDateNotSet && 'End date is required.'}</FormMessage>
            </FormItem>
          </FormV2>
        </DialogSurface>
      </Dialog>
    </section>
  );
};

export default PayrollSummary;
