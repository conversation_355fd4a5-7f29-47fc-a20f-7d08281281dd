import React, { useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import moment from 'moment';
import { AgGridReact } from '@ag-grid-community/react';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { ModalV2 } from '../elements/ModalV2';
import { SelectItem } from '@hammr-ui/components/select';
import LineChart from '@hammr-icons/LineChart';
import { yupResolver } from '@/utils/yupResolver';
import { getTimesheetsReport } from 'services/timesheet';
import { customCellValueFormatter, exportTableToPDF } from 'utils/table';
import { UpdatedTable } from 'components/shared/UpdatedTable';
// Removed group renderers since we export a flat, aggregated table
import { formatMinutesToHoursWorked } from '@/utils/format';
import { FormV2 } from '@/components/elements/Form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { useToast } from '@/hooks/useToast';

const schema = yup
  .object({
    startDate: yup.date().required('Start date is required'),
    endDate: yup.date().required('End date is required'),
    groupBy: yup.string().required('Group by is required'),
    format: yup.string().required('Format is required'),
  })
  .required();

type FormData = yup.InferType<typeof schema>;

const groupByOptions = [
  { label: 'Workers Comp Code', value: 'workers_comp_code' },
  { label: 'Employee', value: 'employee' },
];

const formatOptions = [
  { label: 'CSV', value: 'csv' },
  { label: 'PDF', value: 'pdf' },
];

export default function WorkerCompReport() {
  const [open, setOpen] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Workers Comp Report</h3>
      <p className="mt-1.5 h-fit text-sub-600">
        Detailed breakdown of employee hours worked across various workers comp codes
      </p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <ModalV2 open={open} setOpen={setOpen} title="Workers Comp Report" icon={<LineChart />}>
        <ModalContent setOpen={setOpen} />
      </ModalV2>
    </section>
  );
}

const errorMessage = (
  <>
    There was a problem generating your report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

function ModalContent({ setOpen }: { setOpen: (isOpen: boolean) => void }) {
  const { addToast } = useToast();
  const gridRef = useRef<AgGridReact>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [timesheetData, setTimesheetData] = useState([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      groupBy: 'workers_comp_code',
      format: 'pdf',
    },
  });

  const groupBy = watch('groupBy');
  const columnDefs = useMemo<ColDef[]>(
    () => [
      ...(groupBy === 'employee'
        ? [
            {
              field: 'employeeName',
              headerName: 'Employee',
            },
            {
              field: 'workersCompCodeName',
              headerName: 'Workers Comp Code Name',
            },
            {
              field: 'workersCompCodeNumber',
              headerName: 'Workers Comp Code',
            },
          ]
        : [
            {
              field: 'workersCompCodeNumber',
              headerName: 'Workers Comp Code',
            },
            {
              field: 'workersCompCodeName',
              headerName: 'Workers Comp Code Name',
            },
            {
              field: 'employeeName',
              headerName: 'Employee',
            },
          ]),
      {
        field: 'regularMinutes',
        headerName: 'Regular Hours',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours: true,
            isExporting: true,
          }),
        cellRenderer: (params) => formatMinutesToHoursWorked(params.value),
      },
      {
        field: 'overtimeMinutes',
        headerName: 'Overtime Hours',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours: true,
            isExporting: true,
          }),
        cellRenderer: (params) => formatMinutesToHoursWorked(params.value),
      },
      {
        field: 'totalWages',
        headerName: 'Total Wages',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'currency',
            isExporting: true,
          }),
      },
    ],
    [groupBy]
  );

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      filter: true,
      resizable: true,
      minWidth: 100,
    }),
    []
  );

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    try {
      const startDate = moment(data.startDate);
      const endDate = moment(data.endDate);

      const params = {
        from: startDate.valueOf().toString(),
        to: endDate.valueOf().toString(),
      };

      const response = await getTimesheetsReport(params);

      // Filter out contractors and entries without a WC code
      const timesheets = response.timesheets.filter(
        (item: any) => item.user.workerClassification !== 'CONTRACTOR' && item.workersCompCode?.code
      );

      type Row = {
        employeeName: string;
        workersCompCodeNumber: string | null;
        workersCompCodeName: string | null;
        regularMinutes: number;
        overtimeMinutes: number;
        totalWages: number;
        isTotal?: boolean;
        groupKey?: string;
      };

      const rows: Row[] = [];

      if (groupBy === 'employee') {
        const perCombo = new Map<string, Row>();
        const perEmployeeTotals = new Map<string, Row>();

        for (const timesheet of timesheets) {
          const employeeName = `${timesheet.user.firstName} ${timesheet.user.lastName}`;
          const codeNum = timesheet.workersCompCode.code as string;
          const codeName = timesheet.workersCompCode.name as string;
          const comboKey = `${employeeName}|${codeNum}`;

          if (!perCombo.has(comboKey)) {
            perCombo.set(comboKey, {
              employeeName,
              workersCompCodeNumber: codeNum,
              workersCompCodeName: codeName,
              regularMinutes: 0,
              overtimeMinutes: 0,
              totalWages: 0,
              groupKey: employeeName,
            });
          }
          const combo = perCombo.get(comboKey)!;
          combo.regularMinutes += timesheet.regularMinutes || 0;
          combo.overtimeMinutes += timesheet.overtimeMinutes || 0;
          combo.totalWages += timesheet.totalWages || 0;

          if (!perEmployeeTotals.has(employeeName)) {
            perEmployeeTotals.set(employeeName, {
              employeeName,
              workersCompCodeNumber: 'Total',
              workersCompCodeName: null,
              regularMinutes: 0,
              overtimeMinutes: 0,
              totalWages: 0,
              isTotal: true,
              groupKey: employeeName,
            });
          }
          const total = perEmployeeTotals.get(employeeName)!;
          total.regularMinutes += timesheet.regularMinutes || 0;
          total.overtimeMinutes += timesheet.overtimeMinutes || 0;
          total.totalWages += timesheet.totalWages || 0;
        }

        rows.push(...perCombo.values());
        rows.push(...perEmployeeTotals.values());

        // Sort by employee, then WC name, with Total last (the total is last per combo)
        rows.sort((a, b) => {
          const ga = a.groupKey || '';
          const gb = b.groupKey || '';
          if (ga !== gb) return ga.localeCompare(gb);
          if (!!a.isTotal !== !!b.isTotal) return a.isTotal ? 1 : -1;
          const an = a.workersCompCodeName || '';
          const bn = b.workersCompCodeName || '';
          return an.localeCompare(bn);
        });
      } else {
        // groupBy workers_comp_code
        const perCombo = new Map<string, Row>();
        const perCodeTotals = new Map<string, Row>();

        for (const timesheet of timesheets) {
          const employeeName = `${timesheet.user.firstName} ${timesheet.user.lastName}`;
          const codeNum = timesheet.workersCompCode.code as string;
          const codeName = timesheet.workersCompCode.name as string;
          const comboKey = `${codeNum}|${employeeName}`;

          if (!perCombo.has(comboKey)) {
            perCombo.set(comboKey, {
              workersCompCodeNumber: codeNum,
              workersCompCodeName: codeName,
              employeeName,
              regularMinutes: 0,
              overtimeMinutes: 0,
              totalWages: 0,
              groupKey: `${codeNum} ${codeName}`,
            });
          }
          const combo = perCombo.get(comboKey)!;
          combo.regularMinutes += timesheet.regularMinutes || 0;
          combo.overtimeMinutes += timesheet.overtimeMinutes || 0;
          combo.totalWages += timesheet.totalWages || 0;

          if (!perCodeTotals.has(codeNum)) {
            perCodeTotals.set(codeNum, {
              employeeName: 'Total',
              workersCompCodeNumber: codeNum,
              workersCompCodeName: codeName,
              regularMinutes: 0,
              overtimeMinutes: 0,
              totalWages: 0,
              isTotal: true,
              groupKey: `${codeNum} ${codeName}`,
            });
          }
          const total = perCodeTotals.get(codeNum)!;
          total.regularMinutes += timesheet.regularMinutes || 0;
          total.overtimeMinutes += timesheet.overtimeMinutes || 0;
          total.totalWages += timesheet.totalWages || 0;
        }

        rows.push(...perCombo.values());
        rows.push(...perCodeTotals.values());

        // Sort by WC name, then employee, with Total last (the total is last per combo)
        rows.sort((a, b) => {
          const ga = a.groupKey || '';
          const gb = b.groupKey || '';
          if (ga !== gb) return ga.localeCompare(gb);
          if (!!a.isTotal !== !!b.isTotal) return a.isTotal ? 1 : -1;
          return (a.employeeName || '').localeCompare(b.employeeName || '');
        });
      }

      setTimesheetData(rows as any);

      // Ensure no grouping in the underlying grid (flat export rows)
      const gridApi = gridRef.current!.api;
      gridApi.setRowGroupColumns([]);

      const exportColumns =
        groupBy === 'employee'
          ? [
              'employeeName',
              'workersCompCodeNumber',
              'workersCompCodeName',
              'regularMinutes',
              'overtimeMinutes',
              'totalWages',
            ]
          : [
              'workersCompCodeNumber',
              'workersCompCodeName',
              'employeeName',
              'regularMinutes',
              'overtimeMinutes',
              'totalWages',
            ];

      // Ensure grid's displayed column order matches export order
      try {
        gridRef.current!.api.moveColumns(exportColumns, 0);
      } catch {}

      // Handle export
      setTimeout(() => {
        const fileName = `workers_comp_report_${startDate.format('MM_DD_YYYY')}_${endDate.format('MM_DD_YYYY')}`;

        if (data.format === 'pdf') {
          const title = `Workers Compensation Report\n${startDate.format('MM/DD/YYYY')} - ${endDate.format('MM/DD/YYYY')}`;

          // Widths depend on current grouping/column order
          const widths =
            groupBy === 'employee'
              ? // Employee | WC Code | WC Code Name | Regular | Overtime | Total Wages
                ['18%', '12%', '28%', '14%', '12%', '16%']
              : // WC Code | WC Code Name | Employee | Regular | Overtime | Total Wages
                ['12%', '28%', '18%', '14%', '12%', '16%'];

          exportTableToPDF(gridRef.current!.api, fileName + '.pdf', title, {
            columnWidths: widths,
          });
        } else {
          gridRef.current!.api.exportDataAsCsv({
            fileName: fileName + '.csv',
            columnKeys: exportColumns,
          });
        }
      }, 1000);
    } catch (error) {
      console.error('error generating report ->', error);
      addToast({
        type: 'error',
        title: 'Failed To Generate Report',
        description: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormV2
      onSubmit={handleSubmit(onSubmit)}
      isLoading={isLoading}
      submitText={isLoading ? 'Generating...' : 'Download'}
      onCancel={() => setOpen(false)}
    >
      <>
        <div className="flex flex-col justify-center gap-5 self-stretch">
          <ControlledDateInput
            control={control}
            name="startDate"
            required
            label="Start Date"
            rules={{ required: 'Please select a start date' }}
          />
          <ControlledDateInput
            control={control}
            name="endDate"
            required
            label="End Date"
            rules={{ required: 'Please select an end date' }}
          />
          <ControlledSelect
            name="groupBy"
            label="Group By"
            required
            className="w-full"
            control={control}
            rules={{ required: 'Group by is required' }}
            error={errors['groupBy']?.message}
          >
            {groupByOptions.map((item) => (
              <SelectItem key={item.value} value={item.value}>
                {item.label}
              </SelectItem>
            ))}
          </ControlledSelect>
          <ControlledSelect
            name="format"
            label="Format"
            required
            control={control}
            className="w-full"
            rules={{ required: 'Format is required' }}
            error={errors['format']?.message}
          >
            {formatOptions.map((item) => (
              <SelectItem key={item.value} value={item.value}>
                {item.label}
              </SelectItem>
            ))}
          </ControlledSelect>
        </div>

        {/* AG Grid */}
        <div className="hidden">
          <UpdatedTable
            parentRef={gridRef}
            rowData={timesheetData}
            colDefs={columnDefs}
            defaultColDef={defaultColDef}
            isLoading={isLoading}
            emptyRowsText="No data available"
            gridOptions={{
              suppressRowTransform: true,
            }}
          />
        </div>
      </>
    </FormV2>
  );
}
