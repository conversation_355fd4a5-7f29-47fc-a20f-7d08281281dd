import { useState } from 'react';
import GenerateReportModal from './GenerateReportModal';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';

const CertifiedPayrollReport = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
      <h3 className="text-sm font-medium text-strong-950">Certified Payroll Report</h3>
      <p className="mt-1.5 h-fit text-sub-600">
        Employee classifications, wages, fringes and taxes on prevailing wage projects.
      </p>
      <LinkButton style="primary" size="medium" className="mt-5 flex gap-1" onClick={() => setIsModalOpen(true)}>
        Create
        <ArrowRightSLine className="size-5" />
      </LinkButton>

      <GenerateReportModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} />
    </section>
  );
};

export default CertifiedPayrollReport;
