import { FormV2 } from 'components/elements/Form';
import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { Project } from 'interfaces/project';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { getCertifiedPayrollReport } from 'services/report';
import dayjs from 'dayjs';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import InfoCustomFill from '@/hammr-icons/InfoCustomFill';
import { getPayrolls } from 'services/payroll';
import { apiRequest, apiRequestCheck } from 'utils/requestHelpers';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import LineChartLine from '@/hammr-icons/LineChartLine';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { LoadMorePayrollsButton } from '@/components/reports/LoadMorePayrollsButton';
import { SelectItem } from '@/hammr-ui/components/select';
import { Input } from '@/hammr-ui/components/input';
import { Switch } from '@/hammr-ui/components/Switch';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { useToast } from '@/hooks/useToast';
import { calculatePayPeriods } from '@/utils/dateHelper';
import { CertifiedPayrollReportPayload } from '@/interfaces/report';
import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { cn } from '@/utils/cn';
import { RiCloseFill } from '@remixicon/react';
import { useQuery } from '@tanstack/react-query';
import { Checkbox } from '@/hammr-ui/components/checkbox';
import { useProjects } from '@/hooks/data-fetching/useProjects';
import Search2Line from '@/hammr-icons/Search2Line';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';

const GatewayDevelopmentCommissionCompanyID = 76;

const reports = {
  DepartmentOfIndustrialRelations_XML: 'California DIR (XML)',
  DEDepartmentOfLabor_PDF: 'Delaware DOL Sworn Payroll (PDF)',
  DepartmentOfLabor_PDF: 'Federal WH-347 (PDF)',
  DepartmentOfLabor2025_PDF: 'Federal WH-347 2025 (PDF)',
  LCPTracker_XLSX: 'LCP Tracker (XLSX)',
  GatewayDevelopmentCommission_XLSX: 'Gateway Development Commission (XLSX)',
  NJDepartmentOfTransportation_PDF: 'New Jersey DOT CR-347 (PDF)',
  NJDepartmentOfLaborAndWorkforce_CSV: 'New Jersey DOL MW-562 (CSV)',
  NJDepartmentOfLaborAndWorkforce_PDF: 'New Jersey DOL MW-562 (PDF)',
  ORDepartmentOfLaborAndIndustries_PDF: 'Oregon WH-38 (PDF)',
  DepartmentOfLaborAndIndustries_XML: 'Washington L&I (XML)',
};

const errorMessage = (
  <>
    There was a problem generating your Payroll Journal report. Please reach out to our support.
    <a className="mt-2.5 block font-medium text-strong-950 underline" href="mailto:<EMAIL>">
      Write to support
    </a>
  </>
);

const GenerateReportModal = ({ isOpen, setIsOpen }) => {
  const { addToast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isLoadingPayrolls, setIsLoadingPayrolls] = useState(false);
  const [autoSelectAllProjects, setAutoSelectAllProjects] = useState(false);
  const [searchText, setSearchText] = useState('');
  const {
    handleSubmit,
    control,
    formState: { errors },
    register,
    reset,
    watch,
    setValue,
    trigger,
  } = useForm();
  const { company } = useCompany();
  const { user } = useAuth();
  const currentValues = watch();

  const [payrollList, setPayrollList] = useState([]);
  const [selectedPayroll, setSelectedPayroll] = useState(null);
  const [periods, setPeriods] = useState([]);
  const allProjects = useProjects(user.companyId) as Project[];
  const nextPayrollRef = useRef(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Query to fetch projects that have timesheets in the selected pay period
  const projectsWithTimesheetsQuery = useQuery({
    queryKey: ['projectsWithTimesheets', currentValues.payrollId],
    queryFn: async () =>
      await apiRequest<{ projects: Project[] }>(`projects`, {
        urlParams: {
          payrollId: currentValues.payrollId,
        },
      }),
    enabled: !!currentValues.payrollId,
  });

  const filteredProjects = useMemo(
    () => allProjects?.filter((project) => project.isPrevailingWage) || [],
    [allProjects]
  );

  const projectsWithTimesheets = useMemo(
    () => projectsWithTimesheetsQuery.data?.projects?.filter((project) => project.isPrevailingWage) || [],
    [projectsWithTimesheetsQuery.data?.projects]
  );

  // Filter projects based on search text
  const filteredProjectsForSearch = useMemo(() => {
    if (!searchText) {
      return filteredProjects;
    }

    return filteredProjects.filter((project) => {
      const projectName = project.name.toLowerCase();
      const projectNumber = project.projectNumber?.toLowerCase() || '';
      const searchLower = searchText.toLowerCase();

      return projectName.includes(searchLower) || projectNumber.includes(searchLower);
    });
  }, [filteredProjects, searchText]);

  useEffect(() => {
    if (!isOpen) {
      reset();
      setAutoSelectAllProjects(false);
      setSearchText('');
    }
  }, [isOpen, reset]);

  // Focus the search input when popover opens
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  useEffect(() => {
    const selectedPayroll = payrollList.find((payroll) => payroll.id === currentValues.payrollId);
    setSelectedPayroll(selectedPayroll);

    if (selectedPayroll) {
      const periods = calculatePayPeriods({
        fromDate: selectedPayroll?.period_start,
        toDate: selectedPayroll?.period_end,
      });

      setPeriods(periods);
    }
  }, [currentValues.payrollId, company?.paySchedule?.payFrequency, company?.paySchedule?.firstPeriodEnd, payrollList]);

  // Handle auto-select functionality
  useEffect(() => {
    if (autoSelectAllProjects && projectsWithTimesheets.length > 0) {
      const projectsWithTimesheetsIds = projectsWithTimesheets.map((project) => project.id);
      setValue('projectIds', projectsWithTimesheetsIds);
      trigger('projectIds');
    }
  }, [autoSelectAllProjects, projectsWithTimesheets, setValue, trigger]);

  // Handle payroll change - auto-select projects if checkbox is checked
  useEffect(() => {
    if (currentValues.payrollId && autoSelectAllProjects) {
      const projectsWithTimesheetsIds = projectsWithTimesheets.map((project) => project.id);
      setValue('projectIds', projectsWithTimesheetsIds);
    }
  }, [currentValues.payrollId, autoSelectAllProjects, projectsWithTimesheets, setValue, trigger]);

  const getPayrollList = async (useNext = false) => {
    setIsLoadingPayrolls(true);
    let payrollResponse;

    if (useNext) {
      const formattedUrl = nextPayrollRef.current
        .replace('https://sandbox.checkhq.com/', '')
        .replace('https://api.checkhq.com/', '');
      payrollResponse = await apiRequestCheck(formattedUrl);
    } else {
      payrollResponse = await getPayrolls(user.checkCompanyId);
    }

    let payrollListResponse;

    if (useNext) {
      const allLists = [...payrollList, ...payrollResponse.results];
      const allIds = new Set();

      allLists.forEach((payroll) => {
        allIds.add(payroll.id);
      });

      const uniqueList = Array.from(allIds).map((payrollId) => allLists.find((payroll) => payroll.id === payrollId));
      payrollListResponse = uniqueList;
    } else {
      payrollListResponse = payrollResponse.results;
    }
    nextPayrollRef.current = payrollResponse.next;

    if (payrollList) {
      const filteredPayrolls = payrollListResponse.filter((payroll) => {
        return payroll.status !== 'draft';
      });

      // Sort the results in DESC order of period_end and convert from 2024-07-19 to the Jul 19, 2024 format to display.
      const sortedPayrollList = filteredPayrolls.sort((a, b) => {
        return dayjs(b.period_end).diff(dayjs(a.period_end));
      });

      setPayrollList(sortedPayrollList);
    }
    setIsLoadingPayrolls(false);
  };

  useEffect(() => {
    getPayrollList();
  }, []);

  const onSubmit = async (data) => {
    const projectIds = data.projectIds || [];

    if (projectIds.length === 0) {
      addToast({
        type: 'error',
        title: 'No Projects Selected',
        description: 'Please select at least one project to generate the report.',
      });
      return;
    }

    setIsProcessing(true);

    try {
      // parse data.reportFormat to get the reportFormat and the file extension
      if (!data.reportFormat || !data.reportFormat.includes('_')) {
        throw new Error('Invalid report format. Expected format: TYPE_EXTENSION');
      }

      const reportFormat = data.reportFormat.split('_');
      const reportFormatName = reportFormat[0];
      const fileExtension = reportFormat[1].toLowerCase();

      const payload: CertifiedPayrollReportPayload = {
        reportFormat: reportFormatName,
        signatoryName: data.signatoryName,
        signatoryTitle: data.signatoryTitle,
        projectIds,
        isLastCertifiedPayrollReport: data.lastCertifiedPayrollReport,
        payrollId: data.payrollId,
        remarks: data.remarks,
        reportType: fileExtension === 'csv' ? fileExtension : null,
      };

      // if payroll is biweekly, pass the selected workWeek to and from timestamps
      let workWeekData;
      if (periods.length > 1) {
        workWeekData = JSON.parse(data.workWeek);
        payload.payrollWeekEnding = workWeekData.end;
      }

      const res = (await getCertifiedPayrollReport(payload)) as Response;
      const blob = await res.blob();

      const binaryData = [];
      binaryData.push(blob);
      const file = window.URL.createObjectURL(new Blob(binaryData, { type: 'application/pdf' }));

      const filename = res.headers.get('Content-Disposition').match(/filename="?([^"]+)"?/)[1];

      const a = document.createElement('a');
      a.href = file;
      const downloadName =
        selectedPayroll?.pay_frequency === 'biweekly' && workWeekData
          ? dayjs(workWeekData.end).format('MM_DD_YYYY')
          : dayjs(selectedPayroll.period_end).format('MM_DD_YYYY');

      // Generate filename based on number of projects
      let filenameSuffix = '';
      if (projectIds.length === 1) {
        const project = filteredProjects.find((p) => p.id === projectIds[0]);
        filenameSuffix = project?.name || `Project_${projectIds[0]}`;
      } else {
        filenameSuffix = `${projectIds.length}_Projects`;
      }

      a.download = `${downloadName}-${filenameSuffix}-${filename}`;
      document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox

      a.click();
      a.remove(); //afterwards we remove the element again
    } catch (err) {
      let title = 'Failed To Generate Report';
      let description = errorMessage;

      if (err.message === 'Invalid report format. Expected format: TYPE_EXTENSION') {
        description = <>The report format is invalid. Please contact support.</>;
      } else if (err.status === 404) {
        title = 'No Work Week';
        description = <>There was no work performed on the selected project(s) in the selected period.</>;
      }

      addToast({
        type: 'error',
        title,
        description,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // as per this task: https://linear.app/hammr/issue/HAM-573/gdc-certified-payroll-report
  // "On the frontend, we can add logic to only show this when running on localhost or to organization 76 on production (something I had done here)"
  const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
  const availableReports = { ...reports };
  if (company && company.id !== GatewayDevelopmentCommissionCompanyID && !isLocalhost) {
    delete availableReports['GatewayDevelopmentCommission_XLSX'];
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<LineChartLine className="text-sub-600" />} title="Certified Payroll Report" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Generate"
        >
          <ControlledSelect
            name="payrollId"
            label="Pay Period"
            control={control}
            rules={{ required: 'Please select a payroll week ending' }}
            error={errors['payrollId']?.message as string}
            required
          >
            {payrollList.map((payroll) => (
              <SelectItem key={payroll.id} value={String(payroll.id)}>
                <div className="flex items-center">
                  <div className="min-w-[260px] text-start">
                    {dayjs(payroll.period_start).format('MMM D')} - {dayjs(payroll.period_end).format('MMM D, YYYY')}
                  </div>
                  <div className="ml-0.5 min-w-16 text-right text-[10px] font-light text-sub-600">
                    {payroll.type === 'off_cycle' ? 'OFF-CYCLE' : 'REGULAR'}
                  </div>
                  {(payroll.totals?.liability || payroll.items?.length || payroll.contractor_payments?.length) && (
                    <Tooltip
                      content={
                        <div className="space-y-1">
                          {payroll.totals?.liability && (
                            <div>
                              Total Liability: $
                              {parseFloat(payroll.totals.liability).toLocaleString('en-US', {
                                maximumFractionDigits: 0,
                              })}
                            </div>
                          )}
                          <div>Employees: {payroll.items?.length || 0}</div>
                          <div>Contractors: {payroll.contractor_payments?.length || 0}</div>
                        </div>
                      }
                    >
                      <div className="payroll-tooltip-icon ml-2 flex items-center">
                        <InfoCustomFill className="text-sub-400 h-4 w-4" />
                      </div>
                    </Tooltip>
                  )}
                </div>
              </SelectItem>
            ))}
            {nextPayrollRef.current?.length > 0 && (
              <LoadMorePayrollsButton
                isLoadingPayrolls={isLoadingPayrolls}
                onLoadMore={() => {
                  getPayrollList(true);
                  reset({ ...currentValues, payrollId: undefined });
                }}
              />
            )}
          </ControlledSelect>

          <Controller
            name="projectIds"
            control={control}
            rules={{
              required: 'Please select at least one project',
              validate: (value) => {
                if (!value || !Array.isArray(value) || value.length === 0) {
                  return 'Please select at least one project';
                }
                return true;
              },
            }}
            render={({ field: { onChange, value = [] }, fieldState: { error } }) => {
              const handleProjectRemove = (projectId: number) => {
                const newValue = value.filter((id) => id !== projectId);
                onChange(newValue);
                trigger('projectIds');

                // If user removes a project and auto-select is checked, uncheck it
                if (autoSelectAllProjects && newValue.length < projectsWithTimesheets.length) {
                  setAutoSelectAllProjects(false);
                }
              };

              const handleProjectToggle = (projectId: number, isSelected: boolean) => {
                const newValue = isSelected ? [...value, projectId] : value.filter((id) => id !== projectId);
                onChange(newValue);
                trigger('projectIds');

                // If user removes a project and auto-select is checked, uncheck it
                if (autoSelectAllProjects && !isSelected) {
                  setAutoSelectAllProjects(false);
                }
              };

              return (
                <FormItem required error={!!error} className="mt-5">
                  <FormLabel>Project</FormLabel>
                  <FormControl>
                    <Popover
                      onOpenChange={(isOpen) => {
                        if (!isOpen) {
                          setSearchText('');
                        }
                      }}
                    >
                      <PopoverTrigger asChild>
                        <button
                          type="button"
                          className={cn(
                            'flex min-h-[40px] w-full cursor-pointer items-center gap-2 rounded-10 border border-soft-200 px-[11px] py-[7px]',
                            !!error && 'border-error-base'
                          )}
                        >
                          {value.length === 0 && <span className="text-sm text-soft-400">Select projects</span>}
                          <div className="flex flex-wrap gap-2">
                            {filteredProjects
                              .filter((project) => value.includes(project.id))
                              .map((project) => (
                                <span
                                  key={project.id}
                                  className="flex cursor-default items-center gap-0.5 rounded-6 border border-soft-200 px-2 py-1 text-xs font-medium text-sub-600 hover:bg-weak-50"
                                  onClick={(event) => event.stopPropagation()}
                                >
                                  {project.name}
                                  {project.projectNumber ? ` (${project.projectNumber})` : ''}
                                  <span onClick={() => handleProjectRemove(project.id)}>
                                    <RiCloseFill className="size-4 cursor-pointer text-soft-400 hover:text-sub-600" />
                                  </span>
                                </span>
                              ))}
                          </div>
                        </button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[400px]" align="start">
                        <div className="rounded-16 border border-soft-200 bg-white-0 p-[7px]">
                          {filteredProjects.length > 0 ? (
                            <>
                              <Input
                                ref={searchInputRef}
                                className="mb-2 border-none pl-2"
                                beforeContent={<Search2Line className="size-5 text-soft-400" />}
                                placeholder="Search projects..."
                                value={searchText}
                                onChange={(e) => setSearchText(e.target.value)}
                                boxSize="small"
                                noBorder
                              />
                              {filteredProjectsForSearch.length === 0 ? (
                                <div className="mt-1 flex h-[200px] items-center justify-center text-sm">
                                  No results found.
                                </div>
                              ) : (
                                <ScrollArea className="-mr-1.5 mt-1 h-[200px]">
                                  <div className="flex flex-col gap-1">
                                    {filteredProjectsForSearch.map((project) => (
                                      <label
                                        key={project.id}
                                        className="flex cursor-pointer items-center rounded-6 p-2 text-sm hover:bg-weak-50"
                                      >
                                        <Checkbox
                                          checked={value.includes(project.id)}
                                          onCheckedChange={(checked) => handleProjectToggle(project.id, !!checked)}
                                          className="mr-2"
                                        />
                                        {project.name}
                                        {project.projectNumber ? ` (${project.projectNumber})` : ''}
                                      </label>
                                    ))}
                                  </div>
                                </ScrollArea>
                              )}
                              <hr className="mt-1 border-soft-200" />
                              <div className="-mb-1 mt-1 p-2">
                                {value.length === filteredProjects.length ? (
                                  <LinkButton
                                    style="primary"
                                    size="medium"
                                    onClick={() => {
                                      onChange([]);
                                      trigger('projectIds');
                                      setAutoSelectAllProjects(false);
                                    }}
                                  >
                                    Deselect All
                                  </LinkButton>
                                ) : (
                                  <LinkButton
                                    style="primary"
                                    size="medium"
                                    onClick={() => {
                                      const allProjectIds = filteredProjects.map((project) => project.id);
                                      onChange(allProjectIds);
                                      trigger('projectIds');
                                    }}
                                  >
                                    Select All
                                  </LinkButton>
                                )}
                              </div>
                            </>
                          ) : (
                            <div className="py-4 text-center text-soft-400">
                              {currentValues.payrollId
                                ? 'No prevailing wage projects found for this pay period'
                                : 'Select a pay period to see available projects'}
                            </div>
                          )}
                        </div>
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage>{error?.message as string}</FormMessage>
                </FormItem>
              );
            }}
          />

          <div className="my-5">
            <label className="flex cursor-pointer items-center text-sm">
              <Checkbox
                checked={autoSelectAllProjects}
                onCheckedChange={(checked) => {
                  const isChecked = !!checked;
                  setAutoSelectAllProjects(isChecked);
                  if (isChecked && projectsWithTimesheets.length > 0) {
                    const projectsWithTimesheetsIds = projectsWithTimesheets.map((project) => project.id);
                    setValue('projectIds', projectsWithTimesheetsIds);
                    trigger('projectIds');
                  }
                }}
                className="mr-2"
              />
              Auto-select projects with paid timesheets in pay period
            </label>
          </div>
          <hr className="mt-1 border-soft-200" />

          {/* will handle off-cycle differently (TBD) */}
          {periods.length > 1 && (
            <ControlledSelect
              name="workWeek"
              label="Work Week"
              control={control}
              rules={{ required: 'Please select a work week' }}
              error={errors['workWeek']?.message as string}
              className="mt-5"
              required
            >
              {periods.map((period) => (
                <SelectItem key={period.start} value={JSON.stringify(period)}>
                  {dayjs(period.start).format('MMM D')} - {dayjs(period.end).format('MMM D, YYYY')}
                </SelectItem>
              ))}
            </ControlledSelect>
          )}

          <FormItem required error={!!errors['signatoryName']} className="mt-5">
            <FormLabel>Signatory Name</FormLabel>
            <FormControl>
              <Input
                name="signatoryName"
                placeholder="Enter signatory name"
                {...register('signatoryName', {
                  required: 'Please enter a signatory name',
                })}
              />
            </FormControl>
            <FormMessage>{errors['signatoryName']?.message as string}</FormMessage>
          </FormItem>

          <FormItem required error={!!errors['signatoryTitle']} className="mt-5">
            <FormLabel>Signatory Title</FormLabel>
            <FormControl>
              <Input
                name="signatoryTitle"
                placeholder="Enter signatory title"
                {...register('signatoryTitle', {
                  required: 'Please enter a signatory title',
                })}
              />
            </FormControl>
            <FormMessage>{errors['signatoryTitle']?.message as string}</FormMessage>
          </FormItem>

          <ControlledSelect
            name="reportFormat"
            label="Payroll Format"
            control={control}
            rules={{ required: 'Please select a payroll format' }}
            error={errors['reportFormat']?.message as string}
            className="mt-5"
            required
          >
            {Object.entries(availableReports).map(([key, name]) => (
              <SelectItem key={key} value={key}>
                {name}
              </SelectItem>
            ))}
          </ControlledSelect>

          <FormItem className="mt-5 flex-row items-center justify-between">
            <FormLabel htmlFor="lastCertifiedPayrollReport">Last Certified Payroll Report</FormLabel>
            <FormControl>
              <Controller
                name="lastCertifiedPayrollReport"
                control={control}
                render={({ field }) => (
                  <Switch id="lastCertifiedPayrollReport" checked={field.value} onCheckedChange={field.onChange} />
                )}
              />
            </FormControl>
          </FormItem>

          <FormItem error={!!errors['remarks']} className="mt-5">
            <FormLabel>Remarks</FormLabel>
            <FormControl>
              <Textarea
                name="remarks"
                placeholder="Enter remarks"
                {...register('remarks', {
                  maxLength: {
                    value: 200,
                    message: 'Remarks should not exceed 200 characters',
                  },
                })}
              />
            </FormControl>
            <FormMessage>{errors['remarks']?.message as string}</FormMessage>
          </FormItem>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default GenerateReportModal;
