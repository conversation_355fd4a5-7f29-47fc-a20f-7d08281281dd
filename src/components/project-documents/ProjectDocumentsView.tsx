import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { useAwsS3 } from 'hooks/useAwsS3';
import { ProjectDocuments } from 'interfaces/project-documents';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { logError } from 'utils/errorHandling';
import { getFileAndOpen, getFileUrl } from './utils';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { CellClickedEvent, ColDef, GridReadyEvent, SortDirection, ValueFormatterParams } from '@ag-grid-community/core';
import { formatLocaleUsa } from 'utils/dateHelper';
import { AgGridReact } from '@ag-grid-community/react';
import { defaultHammrHeaders } from 'utils/requestHelpers';
import { Tooltip } from '@hammr-ui/components/tooltip';
import { Input } from 'hammr-ui/components/input';
import { Item, MultiSelect } from '@/hammr-ui/components/multi-select';
import { useProjects } from 'hooks/data-fetching/useProjects';
import { getHammrUsers } from 'services/user';
import { IServerSideGetRowsParams } from '@ag-grid-community/core/dist/types/src/interfaces/iServerSideDatasource';
import debounce from 'lodash/debounce';
import FileFormatIcon from '@hammr-icons/FileFormatIcon';
import Search2Line from '@hammr-icons/Search2Line';
import DeleteBinLine from '@hammr-icons/DeleteBinLine';
import Spinner from '@/hammr-ui/components/spinner';
import CompactButton from '@hammr-ui/components/CompactButton';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import Button from '@/hammr-ui/components/button';
import { EmptytStateDocument } from '@/hammr-ui/components/empty-states/Document';
import UploadCloud2Line from '@/hammr-icons/UploadCloud2Line';
import { cn } from '@/hammr-ui/lib/utils';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

interface ProjectDocumentsViewProps {
  handleDelete: (document: ProjectDocuments) => void;
  innerRef: React.RefObject<AgGridReact>;
  onUploadClick?: (files: File[]) => void;
  selectedProjectId?: number;
}

type TableItem = ReturnType<typeof mapDocumentsForTable>[number] & {
  actions: string;
};

export default function ProjectDocumentsView({
  handleDelete,
  innerRef,
  onUploadClick,
  selectedProjectId,
}: ProjectDocumentsViewProps) {
  const { user } = useAuth();
  const { company } = useCompany();
  const { s3 } = useAwsS3();
  const gridRef = innerRef;
  const [openingDocument, setOpeningDocument] = useState<string | undefined>();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedProjects, setSelectedProjects] = useState<Item['value'][] | undefined>();
  const [selectedUsers, setSelectedUsers] = useState<Item['value'][] | undefined>();
  const [users, setUsers] = useState<
    {
      id: number;
      firstName: string;
      lastName: string;
    }[]
  >([]);

  const [selectedTab, setSelectedTab] = useState('active');

  const allProjects = useProjects(user?.companyId, {
    includeIsArchived: true,
  });

  const projects = useMemo(() => {
    return allProjects.filter((x) => (selectedTab === 'archived' ? x.isArchived : !x.isArchived));
  }, [allProjects, selectedTab]);

  const projectItems = useMemo(() => {
    return projects
      .sort((a, b) => a.name.localeCompare(b.name))
      .map((project) => ({
        label: project.name + (project.projectNumber ? ` (${project.projectNumber})` : ''),
        value: project.id,
        isSelected: selectedProjects?.includes(project.id),
      }));
  }, [projects, selectedProjects]);

  const userItems = useMemo(() => {
    return users.map((user) => ({
      label: `${user.firstName} ${user.lastName}`,
      value: user.id,
      isSelected: selectedUsers?.includes(user.id),
    }));
  }, [selectedUsers, users]);

  const handleProjectChange = (items: Item[]) => {
    setSelectedProjects(items.filter((item) => item.isSelected).map((item) => item.value));
  };

  const handleUserChange = (items: Item[]) => {
    const selected = items.filter((item) => item.isSelected && item.value !== 'all').map((item) => item.value);
    setSelectedUsers(selected);
  };

  useEffect(() => {
    if (selectedProjectId) {
      setSelectedProjects([selectedProjectId]);
    }
  }, [selectedProjectId]);

  useEffect(() => {
    const fetchUsers = async () => {
      if (user?.companyId) {
        const fetchedUsers = await getHammrUsers({
          organizationId: user.companyId,
        });
        if (fetchedUsers && fetchedUsers.users) {
          const sortedUsers = fetchedUsers.users.sort((a, b) => a.firstName.localeCompare(b.firstName));
          setUsers(sortedUsers);
        }
      }
    };
    fetchUsers();
  }, [user?.companyId]);

  const handleDeleteRef = useRef(handleDelete);
  handleDeleteRef.current = handleDelete;

  const colDefs: ColDef<TableItem>[] = useMemo(
    () => [
      {
        headerName: 'Document Name',
        field: 'name',
        initialWidth: 300,
        cellRenderer: (params: ValueFormatterParams<TableItem>) => {
          return (
            <div className="flex cursor-pointer flex-row items-center gap-2">
              {openingDocument === params.data.objectId ? (
                <div>
                  <Spinner />
                </div>
              ) : undefined}
              <FileFormatIcon type={params.data.objectId.split('.').at(-1)} />
              <span className="ml-2">{params.value}</span>
            </div>
          );
        },
      },
      {
        headerName: 'Project',
        field: 'project.name',
        hide: Boolean(selectedProjectId),
      },
      {
        headerName: 'Uploaded By',
        field: 'userName',
        cellClass: 'text-sub-600',
      },
      {
        headerName: 'Created On',
        field: 'createdAt',
        cellClass: 'text-sub-600',
        initialSort: 'desc' as SortDirection,
        cellRenderer: (params: ValueFormatterParams) => {
          return formatLocaleUsa(params.value);
        },
      },
      {
        headerName: '',
        field: 'actions',
        sortable: false,
        cellStyle: { overflow: 'visible' },
        maxWidth: 120,
        cellRenderer: (params: ValueFormatterParams) => {
          const isDocumentOwner = params.data.createdBy === parseInt(user.uid);
          const showDelete = isDocumentOwner || user.isCompanyAdmin;
          return (
            <div key={`project-${params.data.id}`} className="flex h-full items-center justify-center space-x-4">
              {showDelete && (
                <Tooltip content="Delete">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      handleDeleteRef.current?.(params.data);
                    }}
                  >
                    <DeleteBinLine />
                  </CompactButton>
                </Tooltip>
              )}
            </div>
          );
        },
      },
    ],
    [openingDocument, user.isCompanyAdmin, user.uid]
  );

  const [gridApi, setGridApi] = useState(null);
  // we need the rows overlay logic instead of using the ag-grid prop because of ssr conflicts
  const [noRowsOverlay, setNoRowsOverlay] = useState(false);

  const createServerSideDatasource = useCallback(
    (gridApi) => {
      return {
        getRows: async (params: IServerSideGetRowsParams<TableItem>) => {
          const updatedFilterModel: Record<string, unknown> = {
            ...params.request.filterModel,
            searchTerm: debouncedSearchTerm || undefined,
            projects: selectedProjects?.length > 0 ? selectedProjects : undefined,
            users: selectedUsers?.length > 0 ? selectedUsers : undefined,
          };

          if (!selectedProjectId) {
            updatedFilterModel.projectArchived = selectedTab === 'archived';
          }

          try {
            const httpResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-documents-ssr`, {
              method: 'post',
              body: JSON.stringify({
                ...params.request,
                filterModel: updatedFilterModel,
              }),
              credentials: 'include',
              headers: {
                ...defaultHammrHeaders,
                'Content-Type': 'application/json',
              },
            });
            const response = await httpResponse.json();
            const mappedRows = mapDocumentsForTable(response.data.rows);

            if (mappedRows.length === 0) {
              setNoRowsOverlay(true);
            } else {
              setNoRowsOverlay(false);
            }

            params.success({
              rowData: mappedRows,
              rowCount: response.data.totalCount,
            });
          } catch (error) {
            console.error(error);
            params.fail();
          }
        },
      };
    },
    [debouncedSearchTerm, selectedProjects, selectedUsers, selectedProjectId, selectedTab]
  );

  const handleViewFile = async (key: string) => {
    setOpeningDocument(key);
    await getFileAndOpen(s3, 'project-documents', key, user?.companyId, CUSTOMER_BUCKET);
    setOpeningDocument(undefined);
  };

  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api);
  }, []);

  // Update the grid when searchTerm changes
  useEffect(() => {
    if (gridApi) {
      const datasource = createServerSideDatasource(gridApi);
      gridApi.setGridOption('serverSideDatasource', datasource);
    }
  }, [debouncedSearchTerm, gridApi, createServerSideDatasource]);

  // Create a debounced function to update debouncedSearchTerm
  const debouncedSetSearchTerm = useCallback(
    debounce((value: string) => {
      setDebouncedSearchTerm(value);
    }, 300),
    []
  );

  // Update the debounced search term when searchTerm changes
  useEffect(() => {
    debouncedSetSearchTerm(searchTerm);
  }, [searchTerm, debouncedSetSearchTerm]);

  if (!user || !company)
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );

  return (
    <>
      <div className="mb-6 flex items-center space-x-4">
        <div>
          {!selectedProjectId && (
            <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value)}>
              <TabList className="">
                <TabItem value="active" className="w-full">
                  Active Projects
                </TabItem>
                <TabItem value="archived" className="w-full">
                  Archived Projects
                </TabItem>
              </TabList>
            </Tabs>
          )}
        </div>
        <div className={cn('flex flex-1 items-center justify-end space-x-4', selectedProjectId && 'justify-start')}>
          <Input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            beforeContent={<Search2Line />}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full max-w-80"
          />
          {!selectedProjectId && (
            <MultiSelect
              items={projectItems}
              label="All Projects"
              onChange={handleProjectChange}
              buttonProps={{ className: 'w-52' }}
            />
          )}
          <MultiSelect
            items={userItems}
            label="All Users"
            onChange={handleUserChange}
            buttonProps={{ className: 'w-52' }}
          />
        </div>
      </div>
      <div className="relative flex flex-grow">
        <div className="relative flex flex-grow">
          {noRowsOverlay && (
            <div className="absolute inset-0 z-10 bg-background">
              <NoDocumentsResultOverlay onUploadClick={onUploadClick} tab={selectedTab} />
            </div>
          )}
          <UpdatedTable<TableItem>
            colDefs={colDefs}
            parentRef={gridRef}
            onGridReady={onGridReady}
            onRowClicked={(event) => {
              handleViewFile(event.data.objectId);
            }}
            fitToWindow
            gridOptions={{
              rowModelType: 'serverSide',
              getRowId: (params) => params.data.id.toString(),
            }}
            enablePagination={true}
          />
        </div>
      </div>
    </>
  );
}

function mapDocumentsForTable(documents: ProjectDocuments[]) {
  return documents.map((document) => {
    return {
      ...document,
      userName: document.user ? `${document.user.firstName} ${document.user.lastName}` : '',
    };
  });
}

export const NoDocumentsResultOverlay: FC<{ onUploadClick?: (files: File[]) => void; tab?: string }> = ({
  onUploadClick,
  tab,
}) => {
  return (
    <div
      className="pointer-events-auto flex flex-col items-center gap-5 pt-32"
      onDrop={(e) => {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files);
        onUploadClick(files);
      }}
      onDragOver={(e) => e.preventDefault()}
    >
      <EmptytStateDocument className="h-36 w-36" />
      {tab === 'active' && (
        <div className="text-center text-sm font-normal text-soft-400">There are no Project Documents yet.</div>
      )}
      {tab === 'archived' && (
        <div className="text-center text-sm font-normal text-soft-400">
          There are no Project Documents from Archived Projects
        </div>
      )}
      {tab === 'active' && (
        <Button beforeContent={<UploadCloud2Line />} onClick={() => onUploadClick([])}>
          Upload Document
        </Button>
      )}
    </div>
  );
};
