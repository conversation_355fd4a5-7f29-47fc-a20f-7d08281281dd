import { logError } from '@/utils/errorHandling';

const supportedExtMimeMap = {
  pdf: 'application/pdf',
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  png: 'image/png',
  gif: 'image/gif',
  bmp: 'image/bmp',
  tiff: 'image/tiff',
  mp4: 'video/mp4',
  mov: 'video/quicktime',
  avi: 'video/x-msvideo',
  wmv: 'video/x-ms-wmv',
  mp3: 'audio/mpeg',
  wav: 'audio/x-wav',
  ogg: 'audio/ogg',
  webm: 'audio/webm',
  aac: 'audio/aac',
  flac: 'audio/flac',
  xls: 'application/vnd.ms-excel',
  xlsx: 'application/vnd.ms-excel',
  doc: 'application/msword',
  docx: 'application/msword',
  ppt: 'application/vnd.ms-powerpoint',
  pptx: 'application/vnd.ms-powerpoint',
  csv: 'text/csv',
  txt: 'text/plain',
  rtf: 'application/rtf',
};

export function isExtSupported(ext: string) {
  return Object.keys(supportedExtMimeMap).includes(ext);
}

function extToMime(ext: string, defaultMime = 'application/octet-stream') {
  return supportedExtMimeMap[ext] || defaultMime;
}

export async function getFileUrl(s3: AWS.S3, bucket: string, key: string) {
  const params = {
    Bucket: bucket,
    Key: key,
    Expires: 21600, // 6 hours - input in seconds
  };

  const url = await s3.getSignedUrlPromise('getObject', params);

  const fileExtension = key.split('.').length > 1 ? key.split('.').pop() : '';

  if (!isExtSupported(fileExtension)) {
    return url;
  }

  const response = await fetch(url);

  if (!response.ok) {
    return null;
  }

  const blob = await response.blob();

  const file = new File([blob], key, {
    type: extToMime(fileExtension, blob.type),
  });

  if (!file) {
    return null;
  }

  return URL.createObjectURL(file);
}

export const getFileAndOpen = async (
  s3: any,
  bucketPrefix: string,
  key: string,
  companyId: string | number | undefined,
  customerBucket: string
): Promise<string | undefined> => {
  if (!s3 || !key || !companyId) return undefined;

  try {
    const fileURL = await getFileUrl(s3, `${customerBucket}/${companyId}/${bucketPrefix}`, key);
    if (!fileURL) {
      return undefined;
    }

    // Open file in new tab
    const fileWindow = window.open(fileURL, '_blank');
    fileWindow?.addEventListener('beforeunload', () => {}, true);
    fileWindow?.addEventListener('load', () => {}, true);

    return fileURL;
  } catch (error) {
    logError(error);
    return undefined;
  }
};
