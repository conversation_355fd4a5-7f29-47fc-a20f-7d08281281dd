import { Dispatch, FC, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { useToast } from 'hooks/useToast';
import { Project } from 'interfaces/project';
import { logError, showErrorToast } from 'utils/errorHandling';
import { v4 as uuidv4 } from 'uuid';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from 'hooks/useAuth';
import { useAwsS3 } from 'hooks/useAwsS3';
import { createProjectDocument } from 'services/project-documents';
import { yupResolver } from 'utils/yupResolver';
import { formSchema } from './schema';
import { FormV2 } from 'components/elements/Form';
import UploadCloud2Line from '@hammr-icons/UploadCloud2Line';
import { FormControl, FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import { Combobox } from '@/hammr-ui/components/combobox';
import { AWS_CUSTOMER_BUCKET } from '@/utils/constants';
import { FileUploaderProgress } from '../elements/form/FileUploader';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { Badge } from '@/hammr-ui/components/badge';

interface UploadDocumentModalProps {
  projects: Project[];
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  defaultFiles?: File[];
  onSuccess?: () => void;
  projectId?: number;
}

export default function UploadDocumentsModal({
  projects,
  open,
  setOpen,
  onSuccess,
  projectId,
}: UploadDocumentModalProps) {
  const form = useForm<FormData>({
    defaultValues: {
      documents: [],
      projectId: projectId || null,
    },
    shouldUnregister: false,
    mode: 'all',
    resolver: yupResolver(formSchema as any),
  });

  const formRef = useRef(form);
  formRef.current = form;

  const { addToast } = useToast();
  const { user } = useAuth();
  const { s3 } = useAwsS3();
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});

  async function uploadDocuments(data: FormData) {
    const updatedProgress = { ...uploadProgress };

    await Promise.all(
      data.documents.filter(Boolean).map(async (document) => {
        const fileExtension = document.name.indexOf('.') ? document.name.split('.').pop() : '';

        const objectId = uuidv4() + (fileExtension ? '.' + fileExtension : '');

        const params = {
          Bucket: `${AWS_CUSTOMER_BUCKET}/${user?.companyId}/project-documents`,
          Key: objectId,
          Body: document,
        };

        await s3
          .putObject(params)
          .on('httpUploadProgress', (evt) => {
            const progress = Math.round((evt.loaded * 100) / evt.total);
            updatedProgress[document.name] = progress;
            setUploadProgress({ ...updatedProgress });
          })
          .promise();

        const payload = {
          name: document.name,
          objectId: objectId,
          projectId: data.projectId,
        };
        await createProjectDocument(payload);
      })
    );

    addToast({
      title: 'Upload Successful',
      description: 'We have successfully uploaded your documents',
      type: 'success',
    });
  }

  const onSubmit = async (data: FormData) => {
    try {
      setUploadProgress({});
      await uploadDocuments(data);
      setOpen(false);
      onSuccess?.();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create project document');
    } finally {
      setUploadProgress({});
    }
  };

  useEffect(() => {
    if (!open) {
      formRef.current.reset({
        documents: [],
        projectId: projectId || null,
      });
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface size="xl">
        <DialogHeader icon={<UploadCloud2Line className="text-sub-600" />} title="Upload Documents" showCloseButton />
        <FormV2
          onSubmit={form.handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={form.formState.isSubmitting}
          submitText="Upload"
          showFooter={form.formState.isValid}
          isDisabled={!form.formState.isValid}
        >
          <UploadDocumentsForm form={form} projects={projects} uploadProgress={uploadProgress} projectId={projectId} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}

interface FormData {
  documents: File[];
  projectId: number | null;
}

interface UploadDocumentsFormProps {
  form: ReturnType<typeof useForm<FormData>>;
  projects: Project[];
  uploadProgress: { [key: string]: number };
  projectId?: number;
}

const UploadDocumentsForm: FC<UploadDocumentsFormProps> = ({ form, projects, uploadProgress, projectId }) => {
  const {
    control,
    register,
    formState: { errors },
    watch,
    setValue,
  } = form;
  const documents = watch('documents');

  const projectItems = useMemo(
    () =>
      projects.map((project) => ({
        value: project.id,
        label: project.name,
      })),
    [projects]
  );

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setValue('documents', [...documents, ...files], { shouldValidate: true });
  };

  const handleFileInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setValue('documents', [...documents, ...files], { shouldValidate: true });
    event.target.value = null;
  };

  return (
    <div className="flex flex-col gap-6">
      <Controller
        name="projectId"
        control={control}
        rules={{ required: 'Please select a Project' }}
        render={({ field: { onChange, value } }) => (
          <FormItem disabled={Boolean(projectId)} required error={!!errors['projectId']}>
            <FormLabel>Project</FormLabel>
            <FormControl>
              <Combobox
                items={projects.map((project) => ({
                  value: project.id.toString(),
                  label: (
                    <span className="flex gap-1">
                      <span className="truncate">
                        {project.name + (project.projectNumber ? ` (${project.projectNumber})` : '')}
                      </span>
                      {project.isPrevailingWage && (
                        <Tooltip content="Prevailing Wage">
                          <Badge variant="outline" color="gray">
                            PW
                          </Badge>
                        </Tooltip>
                      )}
                    </span>
                  ),
                }))}
                onChange={(value) => {
                  onChange(Number(value));
                }}
                value={value?.toString()}
                placeholder="Select a project"
              />
            </FormControl>
            <FormMessage>{errors['projectId']?.message}</FormMessage>
          </FormItem>
        )}
      />

      <FormItem required error={!!errors['documents']}>
        <FormControl>
          <div
            className="cursor-pointer rounded-xl border border-dashed border-sub-300 bg-white-0 p-6 text-center"
            onDrop={handleDrop}
            onDragOver={(e) => e.preventDefault()}
            onClick={() => document.getElementById('fileInput')?.click()}
          >
            <input
              id="fileInput"
              type="file"
              multiple
              className="hidden"
              {...register('documents')}
              onChange={handleFileInput}
            />
            <div className="flex flex-col items-center justify-center">
              <UploadCloud2Line className="mb-4 h-8 w-8 text-sub-600" />
              <p className="mb-2 gap-1 text-base font-semibold text-strong-950">
                <span className="text-primary-base underline">Choose a file</span>
                or drag & drop it here
              </p>
              <p className="text-sm text-sub-600">Max size: 50 MB</p>
            </div>
          </div>
        </FormControl>
        <FormMessage>{errors['documents']?.message}</FormMessage>
      </FormItem>

      <FileUploaderProgress filesToUpload={documents} uploadProgress={uploadProgress} form={form} name="documents" />
    </div>
  );
};
