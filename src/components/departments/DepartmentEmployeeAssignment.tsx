import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import { Input } from '@/hammr-ui/components/input';
import { showErrorToast } from 'utils/errorHandling';
import { useCompany } from '@/hooks/useCompany';
import Search2Line from '@hammr-icons/Search2Line';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef, ValueFormatterParams, ValueGetterParams } from '@ag-grid-community/core';
import { HammrUser } from '@/interfaces/user';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/user';
import { AgGridReact } from '@ag-grid-community/react';
import Button from '@hammr-ui/components/button';
import AddLine from '@hammr-icons/AddLine';
import Link from 'next/link';
import EmptyStateHRNotes from '@hammr-icons/EmptyStateHRNotes';
import { Company } from '@/interfaces/company';
import { ModalV2 } from '@/components/elements/ModalV2';
import { useForm } from 'react-hook-form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { FormV2 } from '@/components/elements/Form';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { departmentsService } from '@/services/departments';
import Building4Line from '@/hammr-icons/Building4Line';
import EmptyStateTrainingAnalyis from '@/hammr-icons/EmptyStateTrainingAnalyis';

const SelectionUI = ({
  gridRef,
  selectOptions,
  isGridReady,
}: {
  gridRef: React.RefObject<AgGridReact>;
  selectOptions: { label: string; value: number }[];
  isGridReady: boolean;
}) => {
  const [showBulkAssignModal, setShowBulkAssignModal] = useState(false);
  const [hasSelectedRows, setHasSelectedRows] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<{ departmentId: number }>({
    defaultValues: {
      departmentId: undefined,
    },
  });

  useEffect(() => {
    if (!isGridReady) return;

    const api = gridRef.current?.api;
    if (!api) return;

    const checkSelection = () => {
      const selectedCount = api.getSelectedRows().length;
      setHasSelectedRows(selectedCount > 0);
    };

    api.addEventListener('selectionChanged', checkSelection);
    checkSelection();

    return () => {
      api.removeEventListener('selectionChanged', checkSelection);
    };
  }, [gridRef, isGridReady]);

  const getSelectedEmployees = () => {
    return gridRef.current?.api?.getSelectedRows() || [];
  };

  const bulkAssignMutation = useMutation({
    mutationFn: (params: { departmentId: number; userIds: number[] }) =>
      departmentsService.bulkAssignDepartmentMembers(params),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      setShowBulkAssignModal(false);
      gridRef.current?.api?.deselectAll();
      form.reset();
    },
    onError: (error) => {
      showErrorToast(error, 'Failed to bulk assign department.');
    },
  });

  const handleBulkAssign = (data: { departmentId: number }) => {
    const selectedEmployees = getSelectedEmployees();

    bulkAssignMutation.mutate({
      departmentId: data.departmentId,
      userIds: selectedEmployees.map((employee) => employee.id),
    });
  };

  return (
    <>
      {hasSelectedRows && <Button onClick={() => setShowBulkAssignModal(true)}>Bulk Assign</Button>}

      <ModalV2
        open={showBulkAssignModal}
        setOpen={setShowBulkAssignModal}
        title="Assign Department"
        icon={<Building4Line />}
      >
        <FormV2
          onSubmit={form.handleSubmit(handleBulkAssign)}
          isLoading={bulkAssignMutation.isPending}
          submitText="Assign"
          onCancel={() => {
            setShowBulkAssignModal(false);
            form.reset();
          }}
        >
          <div className="mb-1.5 text-sm font-medium text-sub-600">
            Employees Selected ({getSelectedEmployees().length})
          </div>
          <div className="mb-5 text-sm">
            {getSelectedEmployees().map((emp) => (
              <div key={emp.id} className="text-strong-950">
                {emp.firstName + ' ' + emp.lastName}
              </div>
            ))}
          </div>
          <ControlledSelect name="departmentId" label="Department" control={form.control} className="w-full">
            {selectOptions.map((item) => (
              <SelectItem key={item.value} value={item.value.toString()}>
                {item.label}
              </SelectItem>
            ))}
          </ControlledSelect>
        </FormV2>
      </ModalV2>
    </>
  );
};

export default function DepartmentEmployeeAssignment({ setShowModal }: { setShowModal: (showModal: boolean) => void }) {
  const { company } = useCompany();
  const [searchTerm, setSearchTerm] = useState('');
  const queryClient = useQueryClient();
  const gridRef = useRef<AgGridReact>(null);
  const [isGridReady, setIsGridReady] = useState(false);

  const departments = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const result = await departmentsService.list();
      return {
        departments: result.departments.sort((a, b) => a.name.localeCompare(b.name)),
      };
    },
  });

  const employees = useQuery({
    queryFn: () =>
      userService.list({
        organizationId: (company as Company).id,
        simple: true,
      }),
    queryKey: ['employees'],
    enabled: Boolean(company),
  });

  // based on the departements and users, build a list where key is the user id and value is the department id
  const departmentMemberships = useMemo(() => {
    if (!departments.data?.departments) return [];
    return departments.data.departments.flatMap((department) =>
      department.users.map((user) => ({
        userId: user.id,
        departmentId: department.id,
      }))
    );
  }, [departments.data?.departments]);

  const assignWorkersMutation = useMutation({
    mutationFn: (params: { userId: number; departmentId: number }) => {
      return departmentsService.updateDepartmentMember({
        userId: params.userId,
        departmentId: params.departmentId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.invalidateQueries({ queryKey: ['departments'] });
    },
    onError: (error) => {
      showErrorToast(error, 'Failed to assign department.');
    },
  });

  const filteredEmployees = useMemo(() => {
    if (!employees.data) return [];
    return employees.data.filter((employee) =>
      (employee.firstName + employee.lastName).toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [employees, searchTerm]);

  const handleDepartmentChange = (userId: number, departmentId: number) => {
    assignWorkersMutation.mutate({ userId, departmentId });
  };

  const selectOptions = useMemo(() => {
    return (
      departments.data?.departments.map((department) => ({
        label: `${department.name}`,
        value: department.id,
      })) ?? []
    );
  }, [departments]);

  const colDefs: ColDef<HammrUser>[] = [
    {
      headerName: '',
      headerCheckboxSelection: true,
      checkboxSelection: true,
      maxWidth: 50,
      pinned: 'left',
    },
    {
      headerName: 'Employee',
      valueGetter: (params: ValueGetterParams<HammrUser>) => `${params.data?.firstName} ${params.data?.lastName}`,
      flex: 3,
    },
    {
      headerName: 'Department',
      flex: 4,
      cellRenderer: (params: ValueFormatterParams<HammrUser>) => (
        <div>
          {assignWorkersMutation.isPending && assignWorkersMutation.variables?.userId === params.data?.id ? (
            <div className="absolute left-0 top-0 z-10 flex h-full w-full bg-white-0/60">
              <div className="m-auto">
                <LoadingIndicator showText={false} />
              </div>
            </div>
          ) : undefined}
          <Select
            value={departmentMemberships
              ?.find((membership) => membership.userId === params.data?.id)
              ?.departmentId.toString()}
            onChange={(value) => handleDepartmentChange(params.data?.id as number, value as number)}
            className="w-full"
          >
            {departments.data?.departments.map((item) => (
              <SelectItem key={item.id} value={item.id.toString()}>
                {item.name}
              </SelectItem>
            ))}
          </Select>
        </div>
      ),
    },
  ];

  if (departments.isFetched && !departments.data?.departments.length) {
    return (
      <div className="flex flex-col items-center justify-center py-28">
        <EmptyStateTrainingAnalyis />
        <p className="mt-5 text-center text-sm text-soft-400">
          There is no department yet.
          <br />
          Click the button below to add one.
        </p>
        <Button className="mt-5" beforeContent={<AddLine />} onClick={() => setShowModal(true)}>
          Create Department
        </Button>
      </div>
    );
  }

  if (employees.isFetched && !employees.data?.length) {
    return (
      <>
        {
          <div className="mt-32 flex flex-col items-center justify-center pb-8">
            <EmptyStateHRNotes />
            <div className="mt-5 text-center text-sm">
              <div className="text-soft-400">There is no employee yet.</div>
              <div className="text-soft-400">Click the button below to add one.</div>
            </div>
            <Link href="/people">
              <Button beforeContent={<AddLine />} className="mt-5">
                Add Employee
              </Button>
            </Link>
          </div>
        }
      </>
    );
  }

  return (
    <>
      <div className="max-w-[540px] p-4">
        <div className="mb-4 flex items-center gap-2">
          <Input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            beforeContent={<Search2Line />}
            boxSize="medium"
            className="max-w-[300px] flex-1"
          />
          <SelectionUI gridRef={gridRef} selectOptions={selectOptions} isGridReady={isGridReady} />
        </div>

        {departments.isLoading || employees.isLoading ? (
          <div className="mt-8 flex justify-center">
            <LoadingIndicator />
          </div>
        ) : (
          <UpdatedTable<HammrUser>
            colDefs={colDefs}
            rowData={filteredEmployees}
            parentRef={gridRef}
            defaultColDef={{
              sortable: true,
              resizable: true,
            }}
            gridOptions={{
              rowSelection: 'multiple',
              onGridReady: () => {
                setIsGridReady(true);
              },
            }}
          />
        )}
      </div>
    </>
  );
}
