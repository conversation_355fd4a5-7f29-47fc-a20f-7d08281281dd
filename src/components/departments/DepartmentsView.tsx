import CompactButton from '@/hammr-ui/components/CompactButton';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { Crew } from 'interfaces/crew';
import PencilLine from '@/hammr-icons/PencilLine';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { ColDef } from '@ag-grid-community/core';
import { ValueGetterParams } from '@ag-grid-community/core/dist/types/src/entities/colDef';
import { Department } from '@/interfaces/department';

interface DepartmentViewProps {
  departments: Department[];
  checkMembersCallback: (index: number) => void;
  rowActionCallback: (index: number) => void;
  deleteActionCallback: (index: number) => void;
}

export default function DepartmentsView({
  departments,
  checkMembersCallback,
  rowActionCallback,
  deleteActionCallback,
}: DepartmentViewProps) {
  const colDefs: ColDef[] = [
    {
      field: 'name',
      headerName: 'Department Name',
    },

    {
      field: 'employees',
      headerName: 'Employees',
      cellRenderer: (params: ValueGetterParams<Department>) => {
        const department = params.data;
        const index = departments.findIndex((d) => d.id === department.id);

        return (
          <LinkButton
            className="text-left"
            key={`department-${department.id}`}
            style="primary"
            size="medium"
            onClick={() => checkMembersCallback(index)}
          >
            {department.users?.length} Employees
          </LinkButton>
        );
      },
    },
    {
      field: 'actions',
      headerName: '',
      cellRenderer: (params: ValueGetterParams<Crew>) => {
        const department = params.data;
        const index = departments.findIndex((d) => d.id === department.id);

        return (
          <div key={`department-${department.id}`} className="flex justify-end gap-2">
            <CompactButton size="large" variant="ghost" onClick={() => rowActionCallback(index)}>
              <PencilLine />
            </CompactButton>
            <CompactButton size="large" variant="ghost" onClick={() => deleteActionCallback(index)}>
              <DeleteBinLine />
            </CompactButton>
          </div>
        );
      },
    },
  ];

  return (
    <div className="max-w-[728px]">
      <UpdatedTable colDefs={colDefs} rowData={departments} />
    </div>
  );
}
