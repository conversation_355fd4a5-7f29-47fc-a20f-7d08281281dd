import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { Control, Controller, FieldErrors, UseFormRegister } from 'react-hook-form';

type T = {
  departmentName: string;
  employeeIds: number[];
};

interface Props {
  control: Control<T>;
  register: UseFormRegister<T>;
  errors: FieldErrors<T>;
  employeesAlreadyInDepartment?: number[];
}

export default function DepartmentForm({ control, register, errors, employeesAlreadyInDepartment }: Props) {
  return (
    <>
      <FormItem required error={!!errors['departmentName']}>
        <FormLabel>Department Name</FormLabel>
        <FormControl>
          <Input
            name="departmentName"
            placeholder="Enter department name"
            {...register('departmentName', {
              required: 'Please enter a department name',
            })}
          />
        </FormControl>
        <FormMessage>{errors['departmentName']?.message}</FormMessage>
      </FormItem>

      <Controller
        control={control}
        name="employeeIds"
        render={({ field }) => (
          <FormItem className="mt-5">
            <FormLabel>
              Employees{' '}
              {field.value.length ? (
                <span className="font-normal text-sub-600">({field.value.length} selected)</span>
              ) : undefined}
            </FormLabel>
            <FormControl>
              <WorkerCrewSelect
                selectedWorkerIds={field.value}
                onChange={field.onChange}
                disabledWorkers={employeesAlreadyInDepartment?.map((id) => ({
                  workerId: id,
                  selected: true,
                  reason: '',
                }))}
              />
            </FormControl>
          </FormItem>
        )}
      />
    </>
  );
}
