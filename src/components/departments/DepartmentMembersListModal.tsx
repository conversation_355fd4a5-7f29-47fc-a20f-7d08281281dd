import Building4Line from '@/hammr-icons/Building4Line';
import TeamLine from '@/hammr-icons/TeamLine';
import { Dialog, DialogBody, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { Dispatch, SetStateAction } from 'react';

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentDepartmentData: any;
}

export default function DepartmentMembersListModal({ open, setOpen, currentDepartmentData }: Props) {
  if (!currentDepartmentData) {
    return null;
  }

  const { users } = currentDepartmentData;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface className="w-[25rem]">
        <DialogHeader title={`${currentDepartmentData.name}`} icon={<Building4Line className="text-sub-600" />} />
        <DialogBody>
          <h2 className="text-xs text-sub-600">Employee List</h2>
          <div className="mt-1.5 flex flex-col gap-1.5">
            {users.length !== 0 ? (
              users.map(({ id, firstName, lastName }) => (
                <span key={id} className="block text-sm text-strong-950">
                  {firstName + ' ' + lastName}
                </span>
              ))
            ) : (
              <span className="block text-sm text-strong-950">-</span>
            )}
          </div>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
}
