import { Dispatch, SetStateAction } from 'react';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { departmentsService } from '@/services/departments';

interface ConfirmDeleteDepartmentModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentDepartmentData: any;
  callback: () => void;
}

export default function ConfirmDeleteDepartmentModal({
  open,
  setOpen,
  currentDepartmentData,
  callback,
}: ConfirmDeleteDepartmentModalProps) {
  const { addToast } = useToast();

  const confirmDeleteEventHandler = async () => {
    try {
      await departmentsService.delete(currentDepartmentData?.id);
      addToast({
        title: 'Deleted Department',
        description: (
          <>
            Successfully deleted the department <strong className="font-medium">{currentDepartmentData.name}</strong>.
          </>
        ),
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to delete department');
    } finally {
      setOpen(false);
    }
  };

  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      onConfirm={confirmDeleteEventHandler}
      data={[]}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      title="Delete Department"
      subtitle={
        <>
          You’re about to delete the department <span className="font-medium">{currentDepartmentData?.name}</span>. Do
          you want to proceed?
        </>
      }
      confirmButton={{
        color: 'error',
      }}
      confirmButtonText="Delete"
    />
  );
}
