import { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import TeamLine from '@/hammr-icons/TeamLine';
import { FormV2 } from '@/components/elements/Form';
import DepartmentForm from './DepartmentForm';
import { Department } from '@/interfaces/department';
import { departmentsService } from '@/services/departments';

interface EditDepartmentModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentDepartmentData: Department;
  departments: Department[];
  callback?: () => void;
}

interface FormData {
  departmentName: string;
  employeeIds: number[];
}

export default function EditDepartmentModal({
  open,
  setOpen,
  currentDepartmentData,
  callback,
  departments,
}: EditDepartmentModalProps) {
  const { addToast } = useToast();
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      departmentName: '',
      employeeIds: [] as number[],
    },
  });

  // get all employee ids from all departments except the current department
  const employeesAlreadyInDepartment = departments
    ?.filter((department) => department.id !== currentDepartmentData.id)
    .map((department) => department.users?.map((user) => user.id))
    .flat();

  useEffect(() => {
    if (!open || !currentDepartmentData) {
      return;
    }

    reset({
      departmentName: currentDepartmentData.name,
      employeeIds: currentDepartmentData.users?.map((user) => user.id),
    });
  }, [open, currentDepartmentData, reset]);

  const onSubmit = async (data: FormData) => {
    try {
      await departmentsService.update(currentDepartmentData.id, {
        name: data.departmentName,
        departmentMembers: data.employeeIds,
      });

      addToast({
        title: 'Edited Department',
        description: (
          <>
            Successfully edited the department <strong className="font-medium">{data.departmentName}</strong>.
          </>
        ),
        type: 'success',
      });

      if (callback) {
        callback();
      }

      setOpen(false);
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Unable to update department');
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<TeamLine className="text-sub-600" />} title="Edit Department" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isSubmitting}
          submitText="Save"
        >
          <DepartmentForm
            control={control}
            register={register}
            errors={errors}
            employeesAlreadyInDepartment={employeesAlreadyInDepartment}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
