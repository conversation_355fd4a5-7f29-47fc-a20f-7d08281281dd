import { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { Control, useForm, UseFormRegister } from 'react-hook-form';

import { useToast } from 'hooks/useToast';

import { logError, showErrorToast } from 'utils/errorHandling';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import TeamLine from '@/hammr-icons/TeamLine';

import DepartmentForm from '@/components/departments/DepartmentForm';
import { FormV2 } from '@/components/elements/Form';
import { departmentsService } from '@/services/departments';
import { Department } from '@/interfaces/department';
interface DepartmentModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  departments: Department[];
}

interface FormData {
  departmentName: string;
  employeeIds: number[];
}

export default function CreateDepartmentModal({ open, setOpen, callback, departments }: DepartmentModalProps) {
  const { addToast } = useToast();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    defaultValues: {
      departmentName: '',
      employeeIds: [] as number[],
    },
  });

  const employeesAlreadyInDepartment = departments?.flatMap((department) => department.users?.map((user) => user.id));

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  const onSubmit = async (data: FormData) => {
    try {
      const res = await departmentsService.create({
        name: data.departmentName,
        departmentMembers: data.employeeIds,
      });

      if (res?.error) {
        addToast({
          type: 'error',
          title: res.error.message,
        });
        return;
      } else {
        addToast({
          type: 'success',
          title: 'Created Department',
          description: (
            <>
              Successfully created the department <strong className="font-medium">{data.departmentName}</strong>.
            </>
          ),
        });
      }

      // should call refresh (callback) and then close modal
      if (callback) {
        callback();
      }

      setOpen(false);
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Unable to create department');
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<TeamLine className="text-sub-600" />} title="Create Department" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isSubmitting}
          submitText="Create"
        >
          <DepartmentForm
            register={register}
            control={control}
            errors={errors}
            employeesAlreadyInDepartment={employeesAlreadyInDepartment}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
