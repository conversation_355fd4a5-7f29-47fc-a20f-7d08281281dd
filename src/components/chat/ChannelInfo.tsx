import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { RiChat3Line } from '@remixicon/react';
import ChannelInfoAdminUI from './ChannelInfoAdminUI';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import { Role } from '@sendbird/chat';
import ChannelInfoNonAdminUI from './ChannelInfoNonAdminUI';

interface ChannelInfoProps {
  open: boolean;
  onRequestClose: () => void;
}

export default function GroupInfo({ open, onRequestClose }: ChannelInfoProps) {
  const { currentChannel } = useGroupChannelContext();

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onRequestClose()}>
      <DialogSurface>
        <DialogHeader icon={<RiChat3Line className="text-sub-600" />} title="Chat Info" />
        {currentChannel?.myRole === Role.OPERATOR ? (
          <ChannelInfoAdminUI onRequestClose={onRequestClose} />
        ) : (
          <ChannelInfoNonAdminUI onRequestClose={onRequestClose} />
        )}
      </DialogSurface>
    </Dialog>
  );
}
