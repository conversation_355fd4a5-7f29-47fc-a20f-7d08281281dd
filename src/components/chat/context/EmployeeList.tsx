import { useEmployees } from '@/hooks/data-fetching/useEmployees';
import { useAuth } from '@/hooks/useAuth';
import { HammrUser } from '@/interfaces/user';
import { createContext, FC, PropsWithChildren, useContext } from 'react';

export const EmployeeListContext = createContext<HammrUser[]>([]);

export const EmployeeListProvider: FC<PropsWithChildren> = ({ children }) => {
  const { user } = useAuth();
  const allEmployees = useEmployees(Number(user.companyId));

  return <EmployeeListContext.Provider value={allEmployees}>{children}</EmployeeListContext.Provider>;
};

export function useEmployeeList() {
  return useContext(EmployeeListContext);
}

export function useEmployeeDict() {
  const list = useContext(EmployeeListContext);

  const dict = list.reduce(
    (acc, employee) => {
      acc[String(employee.id)] = employee;
      return acc;
    },
    {} as Record<string, HammrUser>
  );

  return dict;
}
