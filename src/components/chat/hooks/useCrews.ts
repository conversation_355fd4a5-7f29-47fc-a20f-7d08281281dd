import { useAuth } from '@/hooks/useAuth';
import { getCrews } from '@/services/crew';
import { enhanceCrewsData } from '@/utils/collectionHelpers';
import { useQuery } from '@tanstack/react-query';

export function useCrews() {
  const { user } = useAuth();

  const { data } = useQuery({
    queryKey: ['crews', user.companyId],
    queryFn: async () => {
      if (!user.companyId) return;

      const res = await getCrews({ organizationId: user.companyId });
      return enhanceCrewsData(res.crews, [Number(user.uid)]);
    },
    initialData: [],
  });

  return data;
}
