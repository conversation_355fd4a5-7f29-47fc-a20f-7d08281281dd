import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { HiddenChannelFilter, GroupChannel } from '@sendbird/chat/groupChannel';
import { useEffect, useState } from 'react';

export function useArchiveChannelList() {
  const [channels, setChannels] = useState<GroupChannel[]>([]);
  const [loading, setLoading] = useState(true);

  const state = useSendbirdStateContext();

  const sdk = state.stores.sdkStore.sdk;

  useEffect(() => {
    sdk.groupChannel
      ?.createMyGroupChannelListQuery({
        hiddenChannelFilter: HiddenChannelFilter.HIDDEN,
      })
      .next()
      .then((groupChannels) => {
        setChannels(groupChannels);
        setLoading(false);
      });
  }, [sdk]);

  return { channels, loading };
}
