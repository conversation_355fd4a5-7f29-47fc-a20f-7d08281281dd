import { FC } from 'react';
import { BaseMessage, FileMessage } from '@sendbird/chat/message';
import FileFormatIcon from '@/hammr-icons/FileFormatIcon';
import { cn } from '@/hammr-ui/lib/utils';
import { useMessageMediaViewerStore } from '../components/MessageMediaViewer/store';
import Spinner from '@/hammr-ui/components/spinner';
import { getExtension, isUnsupportedImage, isUnsupportedVideo } from '../types';
import { MessageContentProps as RenderMessageContentProps } from './types';
import { RiPlayCircleLine } from '@remixicon/react';
import { formatBytes } from '../utils';

export function RenderFileMessageContent<T extends BaseMessage>({ message, isOutgoing }: RenderMessageContentProps<T>) {
  if (!message.isFileMessage()) {
    return null;
  }

  if (message.sendingStatus === 'pending') {
    return <SendingMessageContent message={message} isOutgoing={isOutgoing} />;
  }

  const ext = getExtension(message.name);

  const isImage = message.type.includes('image') && !isUnsupportedImage(message.type, ext);
  const isVideo = message.type.includes('video') && !isUnsupportedVideo(message.type, ext);
  const isAudio = message.type.includes('audio');

  if (isImage) {
    return <ImageMessageContent message={message as FileMessage} isOutgoing={isOutgoing} />;
  }

  if (isVideo) {
    return <VideoMessageContent message={message as FileMessage} isOutgoing={isOutgoing} />;
  }

  if (isAudio) {
    return <VoiceMessageContent message={message as FileMessage} isOutgoing={isOutgoing} />;
  }

  return <FileMessageContent message={message as FileMessage} isOutgoing={isOutgoing} />;
}

interface MessageContentProps {
  message: FileMessage;
  isOutgoing: boolean;
}

const SendingMessageContent: FC<MessageContentProps> = ({ isOutgoing }) => {
  return (
    <div
      className={cn(
        'flex h-20 items-center justify-center overflow-hidden rounded-10 border border-soft-200 text-strong-950 shadow-sm',
        isOutgoing ? 'rounded-tr-4' : 'rounded-tl-4'
      )}
    >
      <Spinner />
    </div>
  );
};

const ImageMessageContent: FC<MessageContentProps> = ({ message, isOutgoing }) => {
  return (
    <div
      className={cn(
        'relative cursor-pointer overflow-hidden rounded-10 border border-soft-200 shadow-sm',
        isOutgoing ? 'rounded-tr-4' : 'rounded-tl-4'
      )}
      onClick={() => {
        useMessageMediaViewerStore.setState({
          isOpen: true,
          message,
          messages: null,
        });
      }}
    >
      <img src={message.url} alt={message.name} className="h-auto max-h-60 w-full" />
    </div>
  );
};

const VideoMessageContent: FC<MessageContentProps> = ({ message, isOutgoing }) => {
  return (
    <div
      className={cn(
        'relative cursor-pointer overflow-hidden rounded-10 border border-soft-200 shadow-sm',
        isOutgoing ? 'rounded-tr-4' : 'rounded-tl-4'
      )}
      onClick={() => {
        useMessageMediaViewerStore.setState({
          isOpen: true,
          message,
          messages: null,
        });
      }}
    >
      <video src={message.url} className="h-auto max-h-60 w-full" />
      <div className="text-sub-400 absolute inset-0 flex items-center justify-center bg-white-0/40">
        <RiPlayCircleLine className="h-12 w-12 rounded-full bg-white-0/50" />
      </div>
    </div>
  );
};

const VoiceMessageContent: FC<MessageContentProps> = ({ message }) => {
  return (
    <div className="overflow-hidden">
      <audio src={message.url} controls className="w-full" />
    </div>
  );
};

const FileMessageContent: FC<MessageContentProps> = ({ message, isOutgoing }) => {
  const ext = getExtension(message.name);

  return (
    <div
      className={cn(
        'flex cursor-pointer gap-3 rounded-10 px-3 py-2 text-base text-strong-950 shadow-xs',
        isOutgoing ? 'rounded-tr-4' : 'rounded-tl-4',
        !isOutgoing && 'border border-soft-200'
      )}
      onClick={() => {
        window.open(message.url, '_blank');
      }}
    >
      <div className="relative">
        <FileFormatIcon type={ext} />
      </div>
      <div className="flex flex-col gap-1">
        <div className="text-sm font-medium">{message.name}</div>
        <div className="text-xs text-sub-600">{formatBytes(message.size)}</div>
      </div>
    </div>
  );
};
