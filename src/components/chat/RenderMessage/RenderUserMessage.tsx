import { BaseMessage } from '@sendbird/chat/message';
import { cn } from '@/hammr-ui/lib/utils';
import { MessageContentProps } from './types';
import { getTranslatedMessage } from '../components/utils';

export function RenderUserMessageContent<T extends BaseMessage>({ message, isOutgoing }: MessageContentProps<T>) {
  if (!message.isUserMessage()) {
    return null;
  }

  const text = isOutgoing ? message.message : getTranslatedMessage(message) ?? message.message;

  return (
    <div
      className={cn(
        'rounded-10 border px-3 py-2 text-base text-strong-950 shadow-xs',
        isOutgoing
          ? 'rounded-tr-4 border-transparent bg-primary-base/25'
          : 'rounded-tl-4 border border-soft-200 bg-weak-50'
      )}
    >
      {text}
    </div>
  );
}
