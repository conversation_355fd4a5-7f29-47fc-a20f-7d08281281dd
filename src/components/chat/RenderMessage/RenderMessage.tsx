import { RenderMessageParamsType } from '@sendbird/uikit-react/types/types';
import { FC } from 'react';
import { RenderUserMessageContent } from './RenderUserMessage';
import { RenderFileMessageContent } from './RenderFileMessage';
import { MessageFrame } from './MessageFrame';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { useEmployeeDict } from '../context/EmployeeList';
import { MessageContentFn } from './types';
import { Avatar } from '@/hammr-ui/components/Avatar';
import format from 'date-fns/format';
import { getEmployeeFullName } from '../utils';

export const RenderMessage: FC<RenderMessageParamsType> = ({ message, hasSeparator }) => {
  const globalStore = useSendbirdStateContext();
  const { userId } = globalStore.config;
  const users = useEmployeeDict();

  if (!message) {
    console.error('No message provided to RenderMessage');
    return null;
  }

  if (!('sender' in message)) {
    console.error('No sender in message', message);
    return null;
  }

  const isOutgoing = message.sender.userId === userId;

  const MessageContent: MessageContentFn | null = message.isUserMessage()
    ? RenderUserMessageContent
    : message.isFileMessage()
      ? RenderFileMessageContent
      : null;

  const fullName = getEmployeeFullName(users?.[message.sender.userId]) || message.sender.nickname;

  const seperator = hasSeparator ? <Seperator message={message} /> : null;

  return (
    <>
      {seperator}
      <MessageFrame
        senderName={
          isOutgoing
            ? 'You'
            : message.sender.nickname || users?.[message.sender.userId]?.firstName || message.sender.userId
        }
        timestamp={message.createdAt}
        isOutgoing={isOutgoing}
        avatar={<Avatar imageUrl={message.sender.profileUrl} name={fullName} />}
      >
        {MessageContent ? (
          <MessageContent message={message} isOutgoing={isOutgoing} />
        ) : (
          <RenderUserMessageContent message={message} isOutgoing={isOutgoing} />
        )}
      </MessageFrame>
    </>
  );
};

const Seperator: FC<{ message: RenderMessageParamsType['message'] }> = ({ message }) => {
  return (
    <div className="relative">
      <div className="my-6 h-[1px] w-full bg-soft-200" />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="bg-background px-2.5 text-2xs font-medium text-soft-400">
          {format(message.createdAt, 'MMMM dd, yyyy')}
        </div>
      </div>
    </div>
  );
};
