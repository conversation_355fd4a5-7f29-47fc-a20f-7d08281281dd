import { FC, PropsWithChildren } from 'react';
import moment from 'moment';
import { cn } from '@/hammr-ui/lib/utils';

interface MessageFrameProps {
  isOutgoing?: boolean;
  avatar?: React.ReactNode;
  senderName: string;
  timestamp?: number;
}

export const MessageFrame: FC<PropsWithChildren<MessageFrameProps>> = ({
  senderName,
  timestamp,
  isOutgoing,
  avatar,
  children,
}) => {
  return (
    <div className={cn('mb-4 flex gap-3', isOutgoing ? 'justify-end' : 'justify-start')}>
      {!isOutgoing && avatar}
      <div className="flex min-w-56 flex-col gap-1.5" style={{ maxWidth: 'calc(min(80%, 600px))' }}>
        <div className="flex items-center text-sub-600">
          <div className="flex-1 text-sm font-medium">{senderName}</div>
          {!!timestamp && <div className="text-xs font-normal">{moment(timestamp).format('h:mm A')}</div>}
        </div>
        {children}
      </div>
    </div>
  );
};
