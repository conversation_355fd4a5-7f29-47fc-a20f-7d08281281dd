import { cn } from '@/hammr-ui/lib/utils';
import type { Member } from '@sendbird/chat/groupChannel';
import { FC } from 'react';
import { useEmployeeDict } from '../context/EmployeeList';
import { getEmployeeFullName } from '../utils';
import { Avatar } from '@/hammr-ui/components/Avatar';

interface RenderTypingBubbleProps {
  typingMembers: Member[];
}

export const RenderTypingBubble: FC<RenderTypingBubbleProps> = ({ typingMembers }) => {
  return (
    <div>
      {typingMembers.map((member) => (
        <RenderTypingBubbleItem key={member.userId} member={member} />
      ))}
    </div>
  );
};

interface RenderTypingBubbleItemProps {
  member: Member;
}

const RenderTypingBubbleItem: FC<RenderTypingBubbleItemProps> = ({ member }) => {
  const users = useEmployeeDict();
  const name = users?.[member.userId]?.firstName || member.nickname || member.userId;
  const fullName = getEmployeeFullName(users?.[member.userId]) || member.nickname || member.userId;

  return (
    <div className={cn('mb-4 flex gap-3', 'justify-start')}>
      <Avatar imageUrl={member.profileUrl} name={fullName} />
      <div className="flex min-w-10 max-w-[50%] flex-col gap-1.5">
        <div className="flex text-sub-600">
          <div className="flex-1 text-sm font-medium">{name}</div>
        </div>
        <div className="rounded-10 rounded-tl-4 border border-soft-200 bg-weak-50 px-3 py-2 text-base text-strong-950">
          <div className="flex h-5 items-center gap-2">
            <div className="h-1.5 w-1.5 rounded-full bg-soft-400" style={{ animation: '900ms pulse 0ms infinite' }} />
            <div className="h-1.5 w-1.5 rounded-full bg-soft-400" style={{ animation: '900ms pulse 300ms infinite' }} />
            <div className="h-1.5 w-1.5 rounded-full bg-soft-400" style={{ animation: '900ms pulse 600ms infinite' }} />
          </div>
        </div>
      </div>
    </div>
  );
};
