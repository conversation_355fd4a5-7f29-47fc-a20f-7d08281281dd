import React, { useEffect, useRef, useState } from 'react';
import { GroupChannelUIBasicProps } from '@sendbird/uikit-react/types/modules/GroupChannel/components/GroupChannelUI/GroupChannelUIView';
import { Message } from '@sendbird/uikit-react/GroupChannel/components/Message';
import { FrozenNotification } from '@sendbird/uikit-react/GroupChannel/components/FrozenNotification';
import { UnreadCount } from '@sendbird/uikit-react/GroupChannel/components/UnreadCount';
import { MessageProvider } from '@sendbird/uikit-react/Message/context';
import type { EveryMessage, RenderMessageParamsType } from '@sendbird/uikit-react/types/types';
import PlaceHolder, { PlaceHolderTypes } from '@sendbird/uikit-react/ui/PlaceHolder';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import Icon, { IconColors, IconTypes } from '@sendbird/uikit-react/ui/Icon';
import type { CoreMessageType, SendableMessageType } from '@sendbird/uikit-react/types/utils';
import { InfiniteList } from './InfiniteList';
import { getMessagePartsInfo } from './getMessagePartsInfo';
import { BaseMessage } from '@sendbird/chat/message';
import { SendableMessage } from '@sendbird/chat/lib/__definition';
import { TypingIndicatorBubbleWrapper } from './TypingIndicatorBubbleWrapper';
import { chatEvents } from '../events';

export const isSendableMessage = (message?: BaseMessage | null): message is SendableMessageType => {
  return Boolean(message) && 'sender' in (message as SendableMessage);
};

export function getComponentKeyFromMessage(message: BaseMessage | SendableMessage): string {
  if ('sendingStatus' in message) {
    if (message.sendingStatus === 'succeeded') return String(message.messageId);
    return message.reqId;
  }

  return String(message.messageId);
}

function deleteNullish<T>(obj: T): T {
  const cleaned = {} as T;
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      cleaned[key as keyof T] = value as T[keyof T];
    }
  });
  return cleaned;
}

export interface GroupChannelMessageListProps {
  className?: string;
  /**
   * A function that customizes the rendering of each message component in the message list component.
   */
  renderMessage?: GroupChannelUIBasicProps['renderMessage'];
  /**
   * A function that customizes the rendering of the content portion of each message component.
   */
  renderMessageContent?: GroupChannelUIBasicProps['renderMessageContent'];
  /**
   * A function that customizes the rendering of a separator component between messages.
   */
  renderCustomSeparator?: GroupChannelUIBasicProps['renderCustomSeparator'];
  /**
   * A function that customizes the rendering of a loading placeholder component.
   */
  renderPlaceholderLoader?: GroupChannelUIBasicProps['renderPlaceholderLoader'];
  /**
   * A function that customizes the rendering of an empty placeholder component when there are no messages in the channel.
   */
  renderPlaceholderEmpty?: GroupChannelUIBasicProps['renderPlaceholderEmpty'];
  /**
   * A function that customizes the rendering of a frozen notification component when the channel is frozen.
   */
  renderFrozenNotification?: GroupChannelUIBasicProps['renderFrozenNotification'];
  /**
   * A function that customizes the rendering of a suggested replies component.
   */
  renderSuggestedReplies?: GroupChannelUIBasicProps['renderSuggestedReplies'];
}

export const SCROLL_BUFFER = 10;

export const MessageList = (props: GroupChannelMessageListProps) => {
  const { className = '' } = props;
  const {
    renderMessage = (props: RenderMessageParamsType) => <Message {...props} />,
    renderMessageContent,
    renderSuggestedReplies,
    renderCustomSeparator,
    renderPlaceholderLoader = () => <PlaceHolder type={PlaceHolderTypes.LOADING} />,
    renderPlaceholderEmpty = () => (
      <PlaceHolder className="sendbird-conversation__no-messages" type={PlaceHolderTypes.NO_MESSAGES} />
    ),
    renderFrozenNotification = () => <FrozenNotification className="sendbird-conversation__messages__notification" />,
  } = deleteNullish(props);

  const {
    channelUrl,
    hasNext,
    loading,
    messages,
    newMessages,
    scrollToBottom,
    isScrollBottomReached,
    isMessageGroupingEnabled,
    scrollRef,
    scrollDistanceFromBottomRef,
    scrollPositionRef,
    currentChannel,
    replyType,
    scrollPubSub,
    loadNext,
    loadPrevious,
    setIsScrollBottomReached,
    resetNewMessages,
    refresh,
  } = useGroupChannelContext();

  const refreshRef = useRef(refresh);
  refreshRef.current = refresh;

  useEffect(() => {
    const handleRefresh = () => {
      refreshRef.current();
    };

    chatEvents.on('refreshCurrentChannel', handleRefresh);

    return () => {
      chatEvents.off('refreshCurrentChannel', handleRefresh);
    };
  }, []);

  const store = useSendbirdStateContext();

  const [unreadSinceDate, setUnreadSinceDate] = useState<Date>();

  useEffect(() => {
    if (isScrollBottomReached) {
      setUnreadSinceDate(undefined);
    } else {
      setUnreadSinceDate(new Date());
    }
  }, [isScrollBottomReached]);

  /**
   * 1. Move the message list scroll
   *    when each message's height is changed by `reactions` OR `showEdit`
   * 2. Keep the scrollBottom value after fetching new message list
   */
  const onMessageContentSizeChanged = (isBottomMessageAffected = false): void => {
    const elem = scrollRef.current;
    if (elem) {
      const latestDistance = scrollDistanceFromBottomRef.current;
      const currentDistance = elem.scrollHeight - elem.scrollTop - elem.offsetHeight;
      if (latestDistance < currentDistance && (!isBottomMessageAffected || latestDistance < SCROLL_BUFFER)) {
        const diff = currentDistance - latestDistance;
        // Move the scroll as much as the height of the message has changed
        scrollPubSub.publish('scroll', { top: elem.scrollTop + diff, lazy: false, animated: false });
      }
    }
  };

  const renderer = {
    frozenNotification() {
      if (!currentChannel || !currentChannel.isFrozen) return null;
      return renderFrozenNotification();
    },
    unreadMessagesNotification() {
      if (isScrollBottomReached || !unreadSinceDate) return null;
      return (
        <UnreadCount
          className="sendbird-conversation__messages__notification"
          count={newMessages.length}
          lastReadAt={unreadSinceDate}
          onClick={() => scrollToBottom()}
        />
      );
    },
    scrollToBottomButton() {
      if (!hasNext() && isScrollBottomReached) return null;

      return (
        <div
          className="sendbird-conversation__scroll-bottom-button"
          onClick={() => scrollToBottom()}
          onKeyDown={() => scrollToBottom()}
          tabIndex={0}
          role="button"
        >
          <Icon width="24px" height="24px" type={IconTypes.CHEVRON_DOWN} fillColor={IconColors.PRIMARY} />
        </div>
      );
    },
  };

  if (loading) {
    return renderPlaceholderLoader();
  }

  if (messages.length === 0) {
    return renderPlaceholderEmpty();
  }

  return (
    <>
      <div className={`sendbird-conversation__messages ${className}`}>
        <InfiniteList
          ref={scrollRef}
          initDeps={[channelUrl]}
          scrollPositionRef={scrollPositionRef}
          scrollDistanceFromBottomRef={scrollDistanceFromBottomRef}
          onLoadNext={loadNext}
          onLoadPrevious={loadPrevious}
          onScrollPosition={(it) => {
            const isScrollBottomReached = it === 'bottom';
            if (newMessages.length > 0 && isScrollBottomReached) {
              resetNewMessages();
            }
            setIsScrollBottomReached(isScrollBottomReached);
          }}
          messages={messages}
          renderMessage={({ message, index }) => {
            const { chainTop, chainBottom, hasSeparator } = getMessagePartsInfo({
              allMessages: messages as CoreMessageType[],
              replyType: replyType ?? 'NONE',
              isMessageGroupingEnabled: isMessageGroupingEnabled ?? false,
              currentIndex: index,
              currentMessage: message as CoreMessageType,
              currentChannel: currentChannel!,
            });
            const isOutgoingMessage = isSendableMessage(message) && message.sender.userId === store.config.userId;
            return (
              <MessageProvider message={message} key={getComponentKeyFromMessage(message)} isByMe={isOutgoingMessage}>
                {renderMessage({
                  handleScroll: onMessageContentSizeChanged,
                  message: message as EveryMessage,
                  hasSeparator,
                  chainTop,
                  chainBottom,
                  renderMessageContent,
                  renderSuggestedReplies,
                  renderCustomSeparator,
                })}
              </MessageProvider>
            );
          }}
          typingIndicator={
            !hasNext() && (
              <TypingIndicatorBubbleWrapper channelUrl={channelUrl} handleScroll={onMessageContentSizeChanged} />
            )
          }
        />
        <>{renderer.frozenNotification()}</>
        <>{renderer.unreadMessagesNotification()}</>
        <>{renderer.scrollToBottomButton()}</>
      </div>
    </>
  );
};

export default MessageList;
