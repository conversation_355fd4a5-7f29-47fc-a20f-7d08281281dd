import { type BaseMessage, type UserMessage } from '@sendbird/chat/message';
import type { GroupChannel } from '@sendbird/chat/groupChannel';
import format from 'date-fns/format';
import type { CoreMessageType, SendableMessageType } from '@sendbird/uikit-react/types/utils';
import { ReplyType } from '@sendbird/uikit-react/types/types';
import { OpenChannel } from '@sendbird/chat/openChannel';
import { Nullable } from '../types';

/**
 * exported, should be backward compatible
 * @returns [chainTop: `boolean`, chainBottom: `boolean`]
 */
export const compareMessagesForGrouping = (
  prevMessage: CoreMessageType,
  currMessage: CoreMessageType,
  nextMessage: CoreMessageType,
  currentChannel?: GroupChannel | null,
  replyType?: ReplyType
) => {
  if (!currentChannel || (currentChannel as GroupChannel).channelType !== 'group') {
    return [isSameGroup(prevMessage, currMessage), isSameGroup(currMessage, nextMessage)];
  }

  if (replyType === 'THREAD' && currMessage?.threadInfo) {
    return [false, false];
  }
  const sendingStatus = (currMessage as UserMessage)?.sendingStatus || '';
  const isAcceptable = sendingStatus !== 'pending' && sendingStatus !== 'failed';
  return [
    isSameGroup(prevMessage, currMessage, currentChannel) && isAcceptable,
    isSameGroup(currMessage, nextMessage, currentChannel) && isAcceptable,
  ];
};

export const getMessageCreatedAt = (message: BaseMessage) => {
  // const { stringSet } = useLocalization();
  // return format(message.createdAt, stringSet.DATE_FORMAT__MESSAGE_CREATED_AT);
  return format(message.createdAt, 'yyyy-MM-dd HH:mm:ss');
};

export const isSameGroup = (
  message: CoreMessageType,
  comparingMessage: CoreMessageType,
  currentChannel?: GroupChannel
) => {
  if (
    !(
      message &&
      comparingMessage &&
      message.messageType &&
      message.messageType !== 'admin' &&
      comparingMessage.messageType &&
      comparingMessage?.messageType !== 'admin' &&
      'sender' in message &&
      'sender' in comparingMessage &&
      message.createdAt &&
      comparingMessage.createdAt &&
      message.sender.userId &&
      comparingMessage.sender.userId
    )
  ) {
    return false;
  }
  return (
    message?.sendingStatus === comparingMessage?.sendingStatus &&
    message?.sender?.userId === comparingMessage?.sender?.userId &&
    getMessageCreatedAt(message) === getMessageCreatedAt(comparingMessage) &&
    (currentChannel ? isReadMessage(currentChannel, message) === isReadMessage(currentChannel, comparingMessage) : true)
  );
};

export enum OutgoingMessageStates {
  NONE = 'NONE',
  PENDING = 'PENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
}

export const getOutgoingMessageState = (
  channel: Nullable<GroupChannel | OpenChannel>,
  message: CoreMessageType | undefined | null
) => {
  if (!message || !('sendingStatus' in message)) return OutgoingMessageStates.NONE;

  if (message.sendingStatus === 'pending') {
    return OutgoingMessageStates.PENDING;
  }
  if (message.sendingStatus === 'failed') {
    return OutgoingMessageStates.FAILED;
  }
  if (channel?.isGroupChannel?.()) {
    /* GroupChannel only */
    if ((channel as GroupChannel).getUnreadMemberCount?.(message) === 0) {
      return OutgoingMessageStates.READ;
    } else if ((channel as GroupChannel).getUndeliveredMemberCount?.(message) === 0) {
      return OutgoingMessageStates.DELIVERED;
    }
  }
  if (message.sendingStatus === 'succeeded') {
    return OutgoingMessageStates.SENT;
  }
  return OutgoingMessageStates.NONE;
};

export const isReadMessage = (channel: GroupChannel, message: SendableMessageType): boolean =>
  getOutgoingMessageState(channel, message) === OutgoingMessageStates.READ;

export default {
  compareMessagesForGrouping,
};
