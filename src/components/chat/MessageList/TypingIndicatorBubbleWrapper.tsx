import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import type { Member } from '@sendbird/chat/groupChannel';
import { useGroupChannelHandler } from '@sendbird/uikit-tools';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { RenderTypingBubble } from '../RenderMessage/RenderTypingBubble';

export const TypingIndicatorBubbleWrapper = (props: {
  handleScroll: (isBottomMessageAffected?: boolean) => void;
  channelUrl: string;
}) => {
  const { stores } = useSendbirdStateContext();
  const [typingMembers, setTypingMembers] = useState<Member[]>([]);

  useEffect(() => {
    if (!props.channelUrl) return;

    stores.sdkStore.sdk.groupChannel.getChannel(props.channelUrl).then((channel) => {
      setTypingMembers(channel.getTypingUsers());
    });
  }, [props.channelUrl, stores.sdkStore.sdk.groupChannel]);

  useGroupChannelHandler(stores.sdkStore.sdk, {
    onTypingStatusUpdated(channel) {
      if (channel.url === props.channelUrl) {
        setTypingMembers(channel.getTypingUsers());
      } else {
        setTypingMembers([]);
      }
    },
  });

  const handleScrollRef = useRef(props.handleScroll);
  handleScrollRef.current = props.handleScroll;

  useLayoutEffect(() => {
    // Keep the scrollBottom value after fetching new message list
    handleScrollRef.current?.(true);
  }, []);

  return <RenderTypingBubble typingMembers={typingMembers} />;
};
