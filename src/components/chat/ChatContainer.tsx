import { FC } from 'react';
import { SendBirdProvider } from '@sendbird/uikit-react';
import { GroupChannel } from '@sendbird/uikit-react/GroupChannel';
import '@sendbird/uikit-react/dist/index.css';
import { ChannelHeader } from './components/ChannelHeader';
import {
  handleOnBeforeSendFileMessage,
  handleOnBeforeSendUserMessage,
  handleOnBeforeSendVoiceMessage,
} from './onBeforeMessage';
import { useHammrChatStore } from './store';
import { ChannelListSection } from './components/ChannelListSection';
import { RenderMessage } from './RenderMessage/RenderMessage';
import { MessageMediaViewer } from './components/MessageMediaViewer';
import { MessageInput } from './components/MessageInput';
import MessageList from './MessageList';
import { EmployeeListProvider } from './context/EmployeeList';
import { EmptyPlaceholder } from './EmptyPlaceholder';

interface SendBirdChatContainerProps {
  userId: string;
  accessToken: string;
}

export const ChatContainer: FC<SendBirdChatContainerProps> = ({ userId, accessToken }) => {
  const currentChannelUrl = useHammrChatStore((state) => state.currentChannelUrl);

  return (
    <EmployeeListProvider>
      <SendBirdProvider
        appId={process.env.NEXT_PUBLIC_SENDBIRD_APP_ID}
        accessToken={accessToken}
        userId={userId}
        uikitOptions={{
          groupChannel: {
            enableTypingIndicator: true,
            enableReactions: false,
          },
          groupChannelList: {
            enableMessageReceiptStatus: false,
          },
        }}
      >
        <div style={{ height: '100vh', display: 'flex' }} className="sendbird-hammr">
          <ChannelListSection />
          <GroupChannel
            channelUrl={currentChannelUrl}
            renderChannelHeader={() => <ChannelHeader />}
            onBeforeSendFileMessage={handleOnBeforeSendFileMessage}
            onBeforeSendUserMessage={handleOnBeforeSendUserMessage}
            onBeforeSendVoiceMessage={handleOnBeforeSendVoiceMessage}
            renderMessage={(props) => <RenderMessage {...props} />}
            renderTypingIndicator={() => <></>}
            renderMessageInput={() => <MessageInput />}
            renderMessageList={(props) => <MessageList {...props} />}
            renderPlaceholderEmpty={() => <EmptyPlaceholder />}
          ></GroupChannel>
          <MessageMediaViewer />
        </div>
      </SendBirdProvider>
    </EmployeeListProvider>
  );
};
