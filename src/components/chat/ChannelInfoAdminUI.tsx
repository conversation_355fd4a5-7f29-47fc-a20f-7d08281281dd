import { useEffect, useMemo, useState } from 'react';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { getChannelTitle } from './components/utils';
import { DialogBody, DialogFooter } from '@/hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { useEmployees } from '@/hooks/data-fetching/useEmployees';
import { useAuth } from '@/hooks/useAuth';
import { useGroupMembers } from './components/GroupInfo/useGroupMembers';
import { RestrictedUser, Role } from '@sendbird/chat';
import { logError, showErrorToast } from '@/utils/errorHandling';
import { useMutation } from '@tanstack/react-query';
import { ArchiveSection } from './components/GroupInfo/ArchiveSection';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';

const MAX_PARTICIPANTS = 50;

interface GroupInfoUIProps {
  onRequestClose: () => void;
}

export default function GroupInfoAdminUI({ onRequestClose }: GroupInfoUIProps) {
  const channelStore = useGroupChannelContext();
  const globalStore = useSendbirdStateContext();
  const userId = globalStore?.config?.userId;

  const { currentChannel } = channelStore;
  const [chatName, setChatName] = useState(getChannelTitle(currentChannel, userId));

  const { user } = useAuth();
  const allEmployees = useEmployees(Number(user.companyId));

  const employees = useMemo(() => {
    return allEmployees.filter((employee) => employee.id !== Number(user.uid));
  }, [allEmployees, user.uid]);

  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const members = useGroupMembers(currentChannel);

  const selectedEmployees = useMemo(() => {
    return employees.filter((employee) => selectedUserIds.includes(employee.id));
  }, [employees, selectedUserIds]);

  useEffect(() => {
    setSelectedUserIds(members?.map((member) => Number(member.userId)));
  }, [members]);

  const [bannedUsers, setBannedUsers] = useState<RestrictedUser[]>([]);

  useEffect(() => {
    if (currentChannel?.myRole !== Role.OPERATOR) return;

    const query = currentChannel?.createBannedUserListQuery();
    query?.next().then((bannedUsers) => setBannedUsers(bannedUsers));
  }, [currentChannel]);

  const { isPending: isSaving, mutate: saveChannel } = useMutation({
    mutationFn: async () => {
      try {
        if (chatName !== getChannelTitle(currentChannel, userId)) {
          await currentChannel.updateChannel({
            name: chatName,
          });
        }

        const channelMembers = currentChannel?.members;

        const removedMembers = channelMembers?.filter(
          (member) => !selectedUserIds.includes(Number(member.userId)) && member.userId !== userId
        );

        const newMembers = selectedUserIds.filter(
          (id) => !channelMembers?.map((member) => Number(member.userId)).includes(id)
        );

        for (let i = 0; i < removedMembers?.length; i++) {
          if (removedMembers[i].userId === userId) continue; // don't ban yourself
          await currentChannel?.banUserWithUserId(removedMembers[i].userId);
        }

        for (let i = 0; i < newMembers?.length; i++) {
          if (bannedUsers.map((user) => Number(user.userId)).includes(newMembers[i])) {
            await currentChannel?.unbanUserWithUserId(newMembers[i].toString());
          }

          await currentChannel?.inviteWithUserIds([newMembers[i].toString()]);
        }

        onRequestClose?.();
      } catch (error) {
        logError(error);
        showErrorToast(error);
      }
    },
  });

  const isValid = chatName.length > 0 && selectedUserIds.length > 0;

  return (
    <>
      <DialogBody className="flex flex-col gap-5">
        <FormItem required>
          <FormLabel>Chat Name</FormLabel>
          <FormControl>
            <Input
              placeholder="Enter chat name"
              value={chatName}
              onChange={(event) => setChatName(event.target.value)}
            />
          </FormControl>
        </FormItem>
        <FormItem>
          <FormLabel>Chat Members</FormLabel>
          <FormControl>
            <div className="text-sm font-normal text-foreground">
              {selectedEmployees
                .map((employee) => employee?.fullName || `${employee.firstName} ${employee.lastName}`)
                .join(', ')}
            </div>
          </FormControl>
        </FormItem>
        <FormItem>
          <FormLabel>Manage Members</FormLabel>
          <FormControl>
            <WorkerCrewSelect
              selectedWorkerIds={selectedUserIds}
              onChange={(newSelectedWorkerIds) => {
                if (newSelectedWorkerIds.length <= MAX_PARTICIPANTS) {
                  setSelectedUserIds(newSelectedWorkerIds);
                }
              }}
              showCrews
            />
          </FormControl>
        </FormItem>
      </DialogBody>
      <ArchiveSection onRequestClose={onRequestClose} />
      <DialogFooter>
        <Button type="button" fullWidth variant="outline" color="neutral" onClick={onRequestClose}>
          Cancel
        </Button>
        <Button type="submit" loading={isSaving} fullWidth disabled={!isValid} onClick={() => saveChannel()}>
          Save
        </Button>
      </DialogFooter>
    </>
  );
}
