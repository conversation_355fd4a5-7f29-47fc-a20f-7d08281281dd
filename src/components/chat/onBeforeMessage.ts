import type { BaseMessage, FileMessageCreateParams, UserMessageCreateParams } from '@sendbird/chat/message';
import { getCustomTypeForFileMessage, getTranslatedMessage } from './components/utils';

export function handleOnBeforeSendFileMessage({
  file,
  isReplyToChannel,
  parentMessageId,
}: FileMessageCreateParams): FileMessageCreateParams {
  const customType = getCustomTypeForFileMessage(file);

  const params: FileMessageCreateParams = {
    customType,
    file: file,
  };
  if (isReplyToChannel) {
    params.isReplyToChannel = true;
    params.parentMessageId = parentMessageId;
  }
  return params;
}

export function handleOnBeforeSendUserMessage({
  message,
  isReplyToChannel,
  parentMessageId,
}: UserMessageCreateParams): UserMessageCreateParams {
  const params: UserMessageCreateParams = {
    message: message,
    customType: 'text',
  };

  if (isReplyToChannel) {
    params.isReplyToChannel = true;
    params.parentMessageId = parentMessageId;
  }

  return params;
}

export function handleOnBeforeSendVoiceMessage({
  file,
  isReplyToChannel,
  parentMessageId,
}: FileMessageCreateParams): FileMessageCreateParams {
  const params: FileMessageCreateParams = {
    customType: 'audio',
    file,
    fileName: 'Voice_message.mp3',
    mimeType: 'audio/mp3;sbu_type=voice',
    metaArrays: [],
  };

  if (isReplyToChannel) {
    params.isReplyToChannel = true;
    params.parentMessageId = parentMessageId;
  }

  return params;
}

export function filterMessageList(message: BaseMessage, userId: string) {
  if (message.isUserMessage() && message.sender.userId !== userId) {
    const translatedMessage = getTranslatedMessage(message, userId);

    if (translatedMessage) {
      message.message = translatedMessage;
    }
  }

  return true;
}
