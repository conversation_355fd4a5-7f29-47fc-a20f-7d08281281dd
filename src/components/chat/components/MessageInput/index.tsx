import { RiAttachment2, RiSendPlaneFill } from '@remixicon/react';
import Button from '@/hammr-ui/components/button';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import { useRef, useState } from 'react';
import { isAudio } from '../../types';

export const MessageInput = () => {
  const [text, setText] = useState('');
  const context = useGroupChannelContext();
  const inputFileRef = useRef<HTMLInputElement>(null);
  const { sendUserMessage, sendMultipleFilesMessage, sendVoiceMessage, sendFileMessage, currentChannel } = context;

  const handleAttachment = () => {
    inputFileRef.current?.click();
  };

  const handleUpload = (files: FileList) => {
    if (files.length > 1) {
      sendMultipleFilesMessage({
        fileInfoList: Array.from(files).map((file) => ({
          file,
        })),
      });
    } else {
      const file = files[0];
      if (isAudio(file.type)) {
        sendVoiceMessage(
          {
            file,
          },
          5
        );
      } else {
        sendFileMessage({
          file,
        });
      }
    }
  };

  return (
    <div className="relative px-6">
      <Textarea
        placeholder="Send a message"
        hideCounter
        autoSize
        value={text}
        className="min-h-0"
        onChange={(e) => {
          currentChannel.startTyping();
          setText(e.target.value);
        }}
        rows={1}
        onKeyDown={(e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            if (text) {
              sendUserMessage({
                message: text,
              });
              setText('');
            }
          }
        }}
      />
      <input
        type="file"
        className="absolute hidden"
        ref={inputFileRef}
        onChange={(e) => {
          if (e.target.files.length) {
            handleUpload(e.target.files);
          }
        }}
      />
      <div className="absolute bottom-0.5 right-[1.62rem] flex gap-2">
        {text ? (
          <Button
            beforeContent={<RiSendPlaneFill />}
            size="small"
            variant="ghost"
            color="primary"
            onClick={() => {
              if (!text) return;

              sendUserMessage({
                message: text,
              });
              setText('');
            }}
          />
        ) : (
          <Button
            beforeContent={<RiAttachment2 />}
            size="small"
            variant="ghost"
            color="neutral"
            onClick={() => handleAttachment()}
          />
        )}
      </div>
    </div>
  );
};
