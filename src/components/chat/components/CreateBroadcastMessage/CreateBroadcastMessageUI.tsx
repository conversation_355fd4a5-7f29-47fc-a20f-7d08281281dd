import { FC, useMemo, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { useEmployees } from 'hooks/data-fetching/useEmployees';

import { DialogBody, DialogFooter } from '@hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import Alert from '@/hammr-ui/components/Alert';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { createAnnouncement } from '@/services/sendbird';
import { useMutation } from '@tanstack/react-query';
import { useHammrChatStore } from '../../store';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';

const MAX_BROADCAST_PARTICIPANTS = 20000;

interface CreateBroadcastMessageUIProps {
  onCancel?: () => void;
}

export const CreateBroadcastMessageUI: FC<CreateBroadcastMessageUIProps> = ({ onCancel }) => {
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const state = useSendbirdStateContext();
  const isOnline = state?.config?.isOnline;

  const { user } = useAuth();
  const allEmployees = useEmployees(Number(user.companyId));

  const employees = useMemo(() => {
    return allEmployees.filter((employee) => employee.id !== Number(user.uid));
  }, [allEmployees, user.uid]);

  const selectedEmployees = useMemo(() => {
    return employees.filter((employee) => selectedUserIds.includes(employee.id));
  }, [employees, selectedUserIds]);

  const [message, setMessage] = useState('');

  const { mutate: sendMessage, isPending: sending } = useMutation({
    mutationFn: async () => {
      const filteredUserIds = selectedUserIds.filter((id) => id !== Number(user.uid));
      const result = await createAnnouncement(message, filteredUserIds);
      useHammrChatStore.setState((state) => ({ announcementIds: [...state.announcementIds, result.unique_id] }));
      onCancel?.();
    },
  });

  const isValid = useMemo(() => {
    if (!selectedUserIds.length) return false;
    if (!message) return false;
    return true;
  }, [selectedUserIds, message]);

  const isAllSelected = useMemo(() => {
    return selectedUserIds.length === employees.length;
  }, [selectedUserIds, employees]);

  return (
    <>
      <ScrollArea>
        <DialogBody className="flex flex-col gap-5">
          {!isOnline && (
            <Alert status="warning">You are currently offline. Please check your internet connection.</Alert>
          )}
          <Alert>Broadcast sends a 1:1 message to each employee you select. Replies come directly to you.</Alert>
          <FormItem>
            <FormLabel>
              Employees{' '}
              {selectedUserIds.length > 0 && (
                <span className="text-sm font-normal text-sub-600">({selectedUserIds.length} selected)</span>
              )}
            </FormLabel>
            <FormControl>
              <WorkerCrewSelect
                selectedWorkerIds={selectedUserIds}
                onChange={(newSelectedWorkerIds) => {
                  if (newSelectedWorkerIds.length <= MAX_BROADCAST_PARTICIPANTS) {
                    setSelectedUserIds(newSelectedWorkerIds);
                  }
                }}
                showCrews
                showSelectAll
              />
            </FormControl>
          </FormItem>
          {selectedUserIds.length > 0 && (
            <FormItem>
              <FormLabel>Send To</FormLabel>
              <FormControl>
                <div className="text-sm font-normal text-foreground">
                  {isAllSelected
                    ? 'All employees'
                    : selectedEmployees
                        .map((employee) => employee?.fullName || `${employee.firstName} ${employee.lastName}`)
                        .join(', ')}
                </div>
              </FormControl>
            </FormItem>
          )}
          <FormItem>
            <FormLabel>Message</FormLabel>
            <FormControl>
              <Textarea value={message} onChange={(event) => setMessage(event.target.value)} hideCounter />
            </FormControl>
          </FormItem>
        </DialogBody>
      </ScrollArea>
      <DialogFooter>
        <Button type="button" fullWidth variant="outline" color="neutral" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="submit"
          loading={sending}
          fullWidth
          disabled={!isValid || !isOnline}
          onClick={() => sendMessage()}
        >
          Send Message
        </Button>
      </DialogFooter>
    </>
  );
};
