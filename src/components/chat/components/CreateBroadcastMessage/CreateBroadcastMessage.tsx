import { FC } from 'react';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { RiChat3Line } from '@remixicon/react';
import { CreateBroadcastMessageUI } from './CreateBroadcastMessageUI';

interface CreateBroadcastMessageProps {
  open: boolean;
  onCancel: () => void;
}

export const CreateBroadcastMessage: FC<CreateBroadcastMessageProps> = ({ onCancel, open }) => {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogSurface>
        <DialogHeader icon={<RiChat3Line className="text-sub-600" />} title="Create Broadcast Message" />
        <CreateBroadcastMessageUI onCancel={onCancel} />
      </DialogSurface>
    </Dialog>
  );
};
