import { FC, useEffect, useState } from 'react';
import { CreateChannel } from './CreateChannel';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import Button from '@/hammr-ui/components/button';
import { RiChatNewLine, RiMegaphoneLine } from '@remixicon/react';
import { CreateBroadcastMessage } from './CreateBroadcastMessage';
import { AnnouncementTracker } from './AnnouncementTracker';
import { useRouter } from 'next/router';
import { useAuth } from '@/hooks/useAuth';

export const ChannelListHeader: FC = () => {
  const router = useRouter();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showBroadcastModal, setShowBroadcastModal] = useState(false);
  const state = useSendbirdStateContext();
  const isOnline = state?.config?.isOnline;

  useEffect(() => {
    if (router.query.modal === 'broadcast') {
      setShowBroadcastModal(true);
    }
  }, [router.query.modal]);

  const handleCloseModal = () => {
    setShowBroadcastModal(false);
    router.replace('/chat', undefined, { shallow: true });
  };

  const { user } = useAuth();

  return (
    <div className="flex h-20 items-center p-6 text-2xl font-medium text-strong-950">
      <div className="flex-1">Chat</div>
      <div className="flex gap-3">
        {user.role === 'ADMIN' && (
          <Button
            variant="outline"
            color="neutral"
            disabled={!isOnline}
            beforeContent={<RiMegaphoneLine />}
            onClick={() => setShowBroadcastModal(true)}
          />
        )}
        <Button beforeContent={<RiChatNewLine />} disabled={!isOnline} onClick={() => setShowCreateModal(true)} />
      </div>
      <AnnouncementTracker />
      <CreateChannel open={showCreateModal} onCancel={() => setShowCreateModal(false)} />
      <CreateBroadcastMessage open={showBroadcastModal} onCancel={handleCloseModal} />
    </div>
  );
};
