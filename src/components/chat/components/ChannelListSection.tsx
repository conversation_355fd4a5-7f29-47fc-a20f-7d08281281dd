import { FC, useEffect, useState } from 'react';
import { useHammrChatStore } from '../store';
import { ChannelListLoading } from './ChannelListLoading';
import { ChannelListHeader } from './ChannelListHeader';
import { ChannelPreview } from './ChannelPreview';
import { GroupChannelListProvider, useGroupChannelListContext } from '@sendbird/uikit-react/GroupChannelList/context';
import { ChannelListUI } from './ChannelListUI';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { getUserLanguage } from './utils';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { Input } from '@/hammr-ui/components/input';
import { RiSearch2Line } from '@remixicon/react';
import PUBSUB_TOPICS from '@sendbird/uikit-react/pubSub/topics';
import { GroupChannelListOrder, HiddenChannelFilter } from '@sendbird/chat/groupChannel';

export const ChannelListSection: FC = () => {
  const globalStore = useSendbirdStateContext();
  const { config, stores } = globalStore;
  const [searchText, setSearchText] = useState<string>('');
  const currentChannelUrl = useHammrChatStore((state) => state.currentChannelUrl);

  const { pubSub } = config;

  // update sendbird user's preferred language
  useEffect(() => {
    const userLanguage = getUserLanguage();
    if (userLanguage !== null && globalStore.stores.sdkStore.initialized) {
      globalStore.stores.sdkStore.sdk.updateCurrentUserInfoWithPreferredLanguages([userLanguage]);
    }
  }, [globalStore.stores.sdkStore.sdk, globalStore.stores.sdkStore.initialized]);

  const [selectedTab, setSelectedTab] = useState<string>('active');

  useEffect(() => {
    const handler = () => {
      setSelectedTab('active');
    };

    const { remove } = pubSub.subscribe(PUBSUB_TOPICS.SEND_MESSAGE_START, handler);

    return () => {
      remove();
    };
  }, [pubSub, stores.sdkStore?.initialized]);

  const renderHeader = () => {
    return (
      <div className="flex flex-col">
        <ChannelListHeader />
        <div className="flex flex-col gap-4 p-4 pt-0">
          <TabList className="">
            <TabItem value="active" className="w-full">
              Active
            </TabItem>
            <TabItem value="archived" className="w-full">
              Archived
            </TabItem>
          </TabList>
          <Input
            beforeContent={<RiSearch2Line className="text-soft-400" />}
            placeholder="Search..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
        </div>
      </div>
    );
  };

  const renderHeaderInScroll = () => {
    return null;
  };

  return (
    <GroupChannelListProvider
      className="w-80 flex-shrink-0"
      selectedChannelUrl={currentChannelUrl}
      onChannelSelect={(channel) => {
        if (channel?.url) {
          useHammrChatStore.setState({ currentChannelUrl: channel.url });
        }
      }}
      onChannelCreated={(channel) => {
        useHammrChatStore.setState({ currentChannelUrl: channel.url });
      }}
      disableAutoSelect
      channelListQueryParams={{
        includeEmpty: true,
        hiddenChannelFilter: selectedTab === 'archived' ? HiddenChannelFilter.HIDDEN : HiddenChannelFilter.UNHIDDEN,
        order: GroupChannelListOrder.LATEST_LAST_MESSAGE,
        limit: 100,
      }}
    >
      <Tabs value={selectedTab ?? 'active'} onValueChange={(value) => setSelectedTab(value)}>
        <AutoSelectChannel />
        <ChannelListUI
          renderPlaceHolderLoading={() => <ChannelListLoading />}
          renderHeader={() => renderHeader()}
          renderChannelPreview={(props) => <ChannelPreview channel={props.channel} />}
          renderHeaderInScroll={() => renderHeaderInScroll()}
          searchText={searchText}
        />
      </Tabs>
    </GroupChannelListProvider>
  );
};

const AutoSelectChannel: FC = () => {
  const { currentChannelUrl } = useHammrChatStore();
  const { groupChannels, selectedChannelUrl, onChannelSelect } = useGroupChannelListContext();

  useEffect(() => {
    if (!groupChannels?.length) {
      return;
    }

    if (selectedChannelUrl && currentChannelUrl) {
      return;
    }

    if (!currentChannelUrl) {
      onChannelSelect(groupChannels[0]);
    }
  }, [currentChannelUrl, groupChannels, onChannelSelect, selectedChannelUrl]);

  return null;
};
