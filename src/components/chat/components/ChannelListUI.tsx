import React, { useMemo } from 'react';
import type { GroupChannel, Member } from '@sendbird/chat/groupChannel';
import type { User } from '@sendbird/chat';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { getChannelTitle, isAboutSame } from './utils';
import PlaceHolder, { PlaceHolderTypes } from '@sendbird/uikit-react/ui/PlaceHolder';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import { useEmployeeDict } from '../context/EmployeeList';
import { useGroupChannelListContext } from '@sendbird/uikit-react/GroupChannelList/context';

interface RenderChannelPreviewProps {
  channel: GroupChannel;
}

interface RenderUserProfileProps {
  user: Member | User;
  currentUserId: string;
  close(): void;
}

export interface ChannelListUIProps {
  renderChannelPreview?: (props: RenderChannelPreviewProps) => React.ReactElement;
  renderUserProfile?: (props: RenderUserProfileProps) => React.ReactElement;
  renderHeader?: (props: void) => React.ReactElement;
  renderPlaceHolderError?: (props: void) => React.ReactElement;
  renderPlaceHolderLoading?: (props: void) => React.ReactElement;
  renderPlaceHolderEmptyList?: (props: void) => React.ReactElement;
  renderHeaderInScroll?: (props: void) => React.ReactElement;
  searchText?: string;
}

export const ChannelListUI: React.FC<ChannelListUIProps> = (props: ChannelListUIProps) => {
  const {
    renderHeader,
    renderChannelPreview,
    renderPlaceHolderError,
    renderPlaceHolderLoading,
    renderPlaceHolderEmptyList,
    renderHeaderInScroll,
  } = props;

  const {
    // allChannels, loading, channelListDispatcher, initialized, fetchChannelList
    groupChannels,
    initialized,
    loadMore,
    onChannelSelect,
  } = useGroupChannelListContext();

  const state = useSendbirdStateContext();
  const sdkStore = state?.stores?.sdkStore;
  const config = state?.config;
  const { logger } = config;
  const sdkError = sdkStore?.error;

  const users = useEmployeeDict();

  const sortedChannels = useMemo(() => {
    // Announce message sent at the same time in the same order
    // Sort by channel URL if last message is sent at the same time
    return groupChannels?.sort((a, b) => {
      if (a.lastMessage?.createdAt !== b.lastMessage?.createdAt) {
        // Use original order if last message is on different time
        return 0;
      }

      if (a.url < b.url) {
        return -1;
      } else if (a.url > b.url) {
        return 1;
      }

      return 0;
    });
  }, [groupChannels]);

  const filterChannels = useMemo(() => {
    const channels =
      sortedChannels?.filter((channel) => !(channel.customType !== 'group' && !channel.lastMessage)) ?? [];

    if (!props.searchText) {
      return channels;
    }

    return channels.filter((channel) => {
      const title = getChannelTitle(channel, config.userId, users);
      return title.toLowerCase().includes(props.searchText.toLowerCase());
    });
  }, [sortedChannels, config.userId, props.searchText, users]);

  return (
    <div className="flex h-screen flex-col overflow-hidden">
      <div>{renderHeader?.()}</div>
      <div
        className="flex flex-1 flex-col overflow-hidden"
        onScroll={(e) => {
          const target = e?.target as HTMLDivElement;
          if (isAboutSame(target.clientHeight + target.scrollTop, target.scrollHeight, 10)) {
            loadMore();
          }
        }}
      >
        {sdkError &&
          (renderPlaceHolderError && typeof renderPlaceHolderError === 'function' ? (
            renderPlaceHolderError?.()
          ) : (
            <PlaceHolder type={PlaceHolderTypes.WRONG} />
          ))}
        {initialized && (
          <ScrollArea>
            {renderHeaderInScroll?.()}
            {filterChannels &&
              filterChannels.map((channel) => {
                const onClick = () => {
                  logger.info('ChannelList: Clicked on channel:', channel);
                  onChannelSelect(channel);
                };

                return (
                  <div key={channel?.url} onClick={onClick}>
                    {renderChannelPreview({ channel })}
                  </div>
                );
              })}
          </ScrollArea>
        )}
        {!initialized && !sdkError && renderPlaceHolderLoading?.()}
        {initialized &&
          filterChannels?.length === 0 &&
          (renderPlaceHolderEmptyList && typeof renderPlaceHolderEmptyList === 'function' ? (
            renderPlaceHolderEmptyList?.()
          ) : (
            <PlaceHolder type={PlaceHolderTypes.NO_CHANNELS} />
          ))}
      </div>
    </div>
  );
};
