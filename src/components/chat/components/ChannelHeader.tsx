import { FC, useState } from 'react';
import { getChannelTitle } from './utils';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import ChannelInfo from '../ChannelInfo';
import Button from '@/hammr-ui/components/button';
import { RiImage2Line, RiInformationLine } from '@remixicon/react';
import { GroupMedia } from './GroupInfo/GroupMedia';
import { useEmployeeDict } from '../context/EmployeeList';
import { Badge } from '@/hammr-ui/components/badge';

export const ChannelHeader: FC = () => {
  const [showGroupInfo, setShowGroupInfo] = useState(false);
  const [showGroupMedia, setShowGroupMedia] = useState(false);

  const channelStore = useGroupChannelContext();
  const globalStore = useSendbirdStateContext();
  const userId = globalStore?.config?.userId;

  const { currentChannel } = channelStore;
  const isGroupChannel = currentChannel?.customType === 'group';

  const users = useEmployeeDict();
  const channelName = getChannelTitle(currentChannel, userId, users);

  return (
    <div className="flex h-20 min-h-20 items-center border-b border-b-soft-200 px-6 py-5">
      <div className="flex flex-1 items-center gap-3 text-foreground">
        <div className="text-2xl font-medium">{channelName}</div>
        {isGroupChannel && (
          <Badge color="gray" variant="lighter">
            {currentChannel.memberCount} employees
          </Badge>
        )}
      </div>
      <div className="flex items-center gap-3">
        <Button
          variant="outline"
          color="neutral"
          beforeContent={<RiImage2Line />}
          onClick={() => setShowGroupMedia(true)}
        >
          View Files
        </Button>
        <Button
          variant="ghost"
          color="neutral"
          beforeContent={<RiInformationLine />}
          onClick={() => setShowGroupInfo(true)}
        />
      </div>
      <ChannelInfo open={showGroupInfo} onRequestClose={() => setShowGroupInfo(false)} />
      <GroupMedia open={showGroupMedia} onRequestClose={() => setShowGroupMedia(false)} />
    </div>
  );
};
