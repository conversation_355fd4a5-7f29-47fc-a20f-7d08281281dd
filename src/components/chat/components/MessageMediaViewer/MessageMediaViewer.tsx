import React, { FC, useMemo, useState } from 'react';
import { useMessageMediaViewerStore } from './store';
import { useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FileMessage, MessageTypeFilter } from '@sendbird/chat/message';
import { getExtension, isImage, isVideo } from '../../types';
import { useHammrChatStore } from '../../store';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { Dialog, DialogSurface } from '@/hammr-ui/components/dialog';
import { useEmployeeDict } from '../../context/EmployeeList';
import { MediaViewerBodyUI } from '@/components/MediaViewer';
import { HammrUser } from '@/interfaces/user';

export const MessageMediaViewer: React.FC = () => {
  const isOpen = useMessageMediaViewerStore((state) => state.isOpen);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(value) => {
        useMessageMediaViewerStore.setState({ isOpen: value });
      }}
    >
      <DialogSurface size="4xl" className="h-[700px]" onOpenAutoFocus={(e) => e.preventDefault()}>
        <MessageMediaViewerBody />
      </DialogSurface>
    </Dialog>
  );
};

function transformToMediaViewerItem(message: FileMessage, userId: string, users: Record<string, HammrUser>) {
  const url = message.url;
  const type: 'image' | 'video' = isImage(message.type, getExtension(message.name))
    ? 'image'
    : isVideo(message.type, getExtension(message.name))
      ? 'video'
      : 'image';
  return {
    id: message.messageId,
    type,
    url,
    name: message.name,
    deletable: message.sender.userId === userId,
    createdAt: message.createdAt,
    createdBy: message.sender.nickname || users[message.sender.userId]?.firstName || message.sender.userId,
  };
}

const MessageMediaViewerBody: FC = () => {
  const { message: currentMessage, messages: initialMessages } = useMessageMediaViewerStore();
  const currentChannelUrl = useHammrChatStore((state) => state.currentChannelUrl);

  const globalStore = useSendbirdStateContext();
  const userId = globalStore.config.userId;
  const [initialPageParam] = useState(Date.now());

  const { data, fetchNextPage, isFetching, hasNextPage, refetch } = useInfiniteQuery({
    queryKey: ['message-media-viewer', currentChannelUrl, initialPageParam, initialMessages?.length],
    queryFn: async ({ pageParam = 0 }) => {
      if (initialMessages?.length) {
        return [];
      }

      const FETCH_LIMIT = 20;
      const channel = await globalStore.stores.sdkStore.sdk.groupChannel.getChannel(currentChannelUrl);
      const messages = await channel?.getMessagesByTimestamp(pageParam, {
        reverse: true,
        prevResultSize: FETCH_LIMIT,
        nextResultSize: 0,
        messageTypeFilter: MessageTypeFilter.FILE,
        customTypesFilter: ['photo', 'video'],
      });

      return messages as FileMessage[];
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.length === 0) {
        return null;
      }

      return lastPage[lastPage.length - 1].createdAt;
    },
    refetchOnMount: 'always',
    initialPageParam,
  });

  const mergedMessages = useMemo(() => {
    if (initialMessages?.length) {
      return initialMessages;
    }

    return data?.pages.reduce((acc, val) => acc.concat(val), []) ?? [];
  }, [data, initialMessages]);

  const queryClient = useQueryClient();

  const { mutate: deleteMessage, isPending: isDeleting } = useMutation({
    mutationFn: async (message: FileMessage) => {
      const channel = await globalStore.stores.sdkStore.sdk.groupChannel.getChannel(currentChannelUrl);
      await channel.deleteMessage(message);
    },
    onSuccess: (_, deletedMessage) => {
      if (mergedMessages.length < 2) {
        useMessageMediaViewerStore.setState({ isOpen: false });

        queryClient.invalidateQueries({
          queryKey: ['group-media'],
        });
      } else {
        if (initialMessages?.length) {
          const fileterdMessages = initialMessages.filter((message) => message.messageId !== deletedMessage.messageId);
          useMessageMediaViewerStore.setState({
            messages: fileterdMessages,
            message: fileterdMessages[0],
          });

          queryClient.invalidateQueries({
            queryKey: ['group-media'],
          });
        } else {
          const fileterdMessages = mergedMessages.filter((message) => message.messageId !== deletedMessage.messageId);
          useMessageMediaViewerStore.setState({
            message: fileterdMessages[0],
          });
          refetch();
        }
      }
    },
  });

  const users = useEmployeeDict();

  const tranformedItems = useMemo(() => {
    return mergedMessages.map((message) => transformToMediaViewerItem(message, userId, users));
  }, [mergedMessages, userId, users]);

  const currentItem = useMemo(() => {
    return transformToMediaViewerItem(currentMessage, userId, users);
  }, [currentMessage, userId, users]);

  return (
    <MediaViewerBodyUI
      items={tranformedItems}
      currentItem={currentItem}
      onChange={(item) => {
        const message = mergedMessages.find((message) => message.messageId === item.id);
        useMessageMediaViewerStore.setState({ message });
      }}
      onClose={() => {
        useMessageMediaViewerStore.setState({ isOpen: false });
      }}
      isDeleting={isDeleting}
      onDelete={(item) => {
        const message = mergedMessages.find((message) => message.messageId === item.id);
        deleteMessage(message);
      }}
      isFetching={isFetching}
      hasNextPage={hasNextPage}
      fetchNextPage={fetchNextPage}
    />
  );
};
