import { FC } from 'react';

const ChannelListLoadingItem: FC = () => {
  return (
    <div className="flex animate-pulse flex-col gap-4 border-b border-b-soft-200 px-5 py-4">
      <div className="flex flex-row justify-between">
        <div className="h-4 w-3/5 rounded bg-weak-100"></div>
        <div className="h-4 w-10 rounded bg-weak-100"></div>
      </div>
      <div className="flex flex-col gap-2">
        <div className="h-3 w-full rounded bg-weak-100"></div>
        <div className="h-3 w-2/5 rounded bg-weak-100"></div>
      </div>
    </div>
  );
};

export const ChannelListLoading: FC = () => {
  return (
    <div className="flex flex-col">
      <ChannelListLoadingItem />
      <ChannelListLoadingItem />
      <ChannelListLoadingItem />
      <ChannelListLoadingItem />
      <ChannelListLoadingItem />
      <ChannelListLoadingItem />
    </div>
  );
};
