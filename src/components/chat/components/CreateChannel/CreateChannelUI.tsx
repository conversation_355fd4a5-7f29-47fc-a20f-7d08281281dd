import { FC, useMemo, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { useCreateChannelContext } from '@sendbird/uikit-react/CreateChannel/context';
import { useEmployees } from 'hooks/data-fetching/useEmployees';

import { <PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';

const MAX_PARTICIPANTS = 50;

interface CreateChannelUIProps {
  onCancel?: () => void;
}

export const CreateChannelUI: FC<CreateChannelUIProps> = ({ onCancel }) => {
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);

  const { user } = useAuth();
  const allEmployees = useEmployees(Number(user.companyId));

  const employees = useMemo(() => {
    return allEmployees.filter((employee) => employee.id !== Number(user.uid));
  }, [allEmployees, user.uid]);

  const { createChannel, onChannelCreated } = useCreateChannelContext();

  const selectedEmployees = useMemo(() => {
    return employees.filter((employee) => selectedUserIds.includes(employee.id));
  }, [employees, selectedUserIds]);

  const [chatName, setChatName] = useState('');
  const [creating, setCreating] = useState(false);

  const handleCreate = async () => {
    setCreating(true);
    if (selectedUserIds.length === 1) {
      const channel = await createChannel({
        isDistinct: true,
        operatorUserIds: [String(user.id), String(selectedUserIds[0])],
        invitedUserIds: [String(user.id), String(selectedUserIds[0])],
        customType: 'personal',
      });

      onChannelCreated(channel);
    } else {
      const channel = await createChannel({
        isDistinct: false,
        operatorUserIds: [user.uid],
        invitedUserIds: [...selectedUserIds.map(String)],
        name: chatName,
        customType: 'group',
      });

      onChannelCreated(channel);
    }
  };

  const isValid = useMemo(() => {
    if (!selectedUserIds.length) return false;

    if (selectedUserIds.length > 1 && !chatName) return false;
    if (selectedUserIds.length > MAX_PARTICIPANTS) return false;

    return true;
  }, [selectedUserIds, chatName]);

  return (
    <>
      <DialogBody className="flex flex-col gap-5">
        <FormItem>
          <FormLabel>
            Employees{' '}
            {selectedUserIds.length > 0 && (
              <span className="text-sm font-normal text-sub-600">({selectedUserIds.length} selected)</span>
            )}
          </FormLabel>
          <FormControl>
            <WorkerCrewSelect
              selectedWorkerIds={selectedUserIds}
              onChange={(newSelectedWorkerIds) => {
                if (newSelectedWorkerIds.length <= MAX_PARTICIPANTS) {
                  setSelectedUserIds(newSelectedWorkerIds);
                }
              }}
              showCrews
            />
          </FormControl>
        </FormItem>
        {selectedUserIds.length > 1 && (
          <>
            <FormItem>
              <FormLabel>Chat Members</FormLabel>
              <FormControl>
                <div className="text-sm font-normal text-foreground">
                  {selectedEmployees
                    .map((employee) => employee?.fullName || `${employee.firstName} ${employee.lastName}`)
                    .join(', ')}
                </div>
              </FormControl>
            </FormItem>
            <FormItem required>
              <FormLabel>Chat Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter chat name"
                  value={chatName}
                  onChange={(event) => setChatName(event.target.value)}
                />
              </FormControl>
            </FormItem>
          </>
        )}
      </DialogBody>
      <DialogFooter>
        <Button type="button" fullWidth variant="outline" color="neutral" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" loading={creating} fullWidth disabled={!isValid} onClick={handleCreate}>
          {selectedUserIds.length > 1 ? 'Create Group Chat' : 'Create Chat'}
        </Button>
      </DialogFooter>
    </>
  );
};
