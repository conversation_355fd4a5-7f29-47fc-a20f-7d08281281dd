import { FC } from 'react';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { RiChat3Line } from '@remixicon/react';
import { CreateChannelProvider } from '@sendbird/uikit-react/CreateChannel/context';
import { CreateChannelUI } from './CreateChannelUI';
import { useHammrChatStore } from '../../store';

interface CreateChannelProps {
  open: boolean;
  onCancel: () => void;
}

export const CreateChannel: FC<CreateChannelProps> = ({ onCancel, open }) => {
  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogSurface>
        <DialogHeader icon={<RiChat3Line className="text-sub-600" />} title="Create Chat" />
        <CreateChannelProvider
          onChannelCreated={(channel) => {
            useHammrChatStore.setState({ currentChannelUrl: channel.url });
            onCancel?.();
          }}
        >
          <CreateChannelUI onCancel={onCancel} />
        </CreateChannelProvider>
      </DialogSurface>
    </Dialog>
  );
};
