import { FC, useEffect, useRef } from 'react';
import { useHammrChatStore } from '../store';
import { useGroupChannelListContext } from '@sendbird/uikit-react/GroupChannelList/context';
import { getAnnouncement } from '@/services/sendbird';
import { chatEvents } from '../events';
import { toast } from '@/hammr-ui/hooks/use-toast';

export const AnnouncementTracker: FC = () => {
  const announcementIds = useHammrChatStore((state) => state.announcementIds);

  return (
    <>
      {announcementIds.map((announcementId) => (
        <AnnouncementTrackerItem key={announcementId} announcementId={announcementId} />
      ))}
    </>
  );
};

interface AnnouncementTrackerItemProps {
  announcementId: string;
}

const AnnouncementTrackerItem: FC<AnnouncementTrackerItemProps> = ({ announcementId }) => {
  const channelList = useGroupChannelListContext();

  const toastResult = useRef<ReturnType<typeof toast>>(null);

  const announcementIdRef = useRef(announcementId);
  announcementIdRef.current = announcementId;

  const channelListRef = useRef(channelList);
  channelListRef.current = channelList;

  useEffect(() => {
    toastResult.current = toast({
      toastVariant: 'success',
      title: 'Sending Broadcast Message',
      description: 'Currently sending broadcast message to selected employees.',
      duration: 120000, // 2 minutes
      progress: 10,
    });

    let checkTimeout: NodeJS.Timeout;

    const check = async () => {
      const announcementId = announcementIdRef.current;
      const channelList = channelListRef.current;

      const announcement = await getAnnouncement(announcementId);

      if (!announcement) {
        // Remove from the list
        useHammrChatStore.setState((state) => ({
          announcementIds: state.announcementIds.filter((id) => id !== announcementId),
        }));
        return;
      } else if (announcement.status === 'scheduled') {
        toastResult.current.update({
          progress: 20,
        });
      } else if (announcement.status === 'ready') {
        toastResult.current.update({
          progress: 30,
        });
      } else if (announcement.status === 'running') {
        const target_user_count = announcement.target_user_count;
        const sent_user_count = announcement.sent_user_count;

        const progress = 30 + Math.floor((sent_user_count / target_user_count) * 70);
        toastResult.current.update({
          progress,
        });
      } else if (announcement.status === 'completed') {
        // Refresh channel list and current channel
        channelList.refresh();
        chatEvents.emit('refreshCurrentChannel');

        // Remove from the list
        useHammrChatStore.setState((state) => ({
          announcementIds: state.announcementIds.filter((id) => id !== announcementId),
        }));

        // no need to check anymore
        return;
      }

      checkTimeout = setTimeout(check, 2000); // Check every 2 seconds
    };

    check();

    return () => {
      if (checkTimeout) {
        clearTimeout(checkTimeout);
      }

      toastResult.current.dismiss();
    };
  }, []);

  return null;
};
