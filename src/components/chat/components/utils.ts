import type { BaseMessage, UserMessage } from '@sendbird/chat/message';
import type { GroupChannel } from '@sendbird/chat/groupChannel';
import { getStringSet } from '@sendbird/uikit-react';
import {
  getExtension,
  isAudio,
  isDOC,
  isEXCEL,
  isGif,
  isImage,
  isPDF,
  isVideo,
  isVoiceMessageMimeType,
} from '../types';
import isToday from 'date-fns/isToday';
import format from 'date-fns/format';
import isThisYear from 'date-fns/isThisYear';
import isYesterday from 'date-fns/isYesterday';
import { FileCompat } from '@sendbird/chat';
import { HammrUser } from '@/interfaces/user';
import moment from 'moment';

export const stringSet = getStringSet('en');

export const getChannelTitle = (channel?: GroupChannel, currentUserId?: string, users?: Record<string, HammrUser>) => {
  if (!channel?.name && !channel?.members) {
    return stringSet.NO_TITLE;
  }
  if (channel?.name && channel.name !== 'Group Channel' && channel.customType !== 'personal') {
    return channel.name;
  }
  if (channel?.members?.length === 1) {
    return stringSet.NO_MEMBERS;
  }
  return (channel?.members || [])
    .filter(({ userId }) => userId !== currentUserId)
    .map(({ userId, nickname }) => nickname || users?.[userId]?.firstName || stringSet.NO_NAME)
    .join(', ');
};

const getChannelPreviewFileDisplayString = (mimeType: string) => {
  if (isGif(mimeType)) {
    return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_GIF ?? '';
  }
  if (isImage(mimeType)) {
    return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_PHOTO ?? '';
  }
  if (isVideo(mimeType)) {
    return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_VIDEO ?? '';
  }
  if (isAudio(mimeType)) {
    return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_AUDIO ?? '';
  }
  if (isVoiceMessageMimeType(mimeType)) {
    return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_VOICE_MESSAGE ?? '';
  }
  return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_GENERAL ?? '';
};
const getPrettyLastMessage = (message: BaseMessage, userId: string) => {
  if (!message) return '';
  if (message.isFileMessage()) {
    return getChannelPreviewFileDisplayString(message.type);
  }
  if (message.isMultipleFilesMessage()) {
    const mimeType = message.fileInfoList?.[0]?.mimeType;
    if (isImage(mimeType) || isGif(mimeType)) {
      return stringSet?.CHANNEL_PREVIEW_LAST_MESSAGE_FILE_TYPE_PHOTO ?? '';
    }
    return getChannelPreviewFileDisplayString(mimeType);
  }

  if (message.isUserMessage()) {
    const translatedMessage = getTranslatedMessage(message, userId);

    if (translatedMessage) {
      return translatedMessage;
    }

    return message.message ?? '';
  }

  return (message as UserMessage).message ?? '';
};
export const getLastMessage = (channel: GroupChannel, userId: string) =>
  channel?.lastMessage ? getPrettyLastMessage(channel.lastMessage, userId) : '';

export function getCustomTypeForFileMessage(file: FileCompat) {
  if (!('name' in file)) {
    return 'others';
  }

  const extension = getExtension(file.name);
  if (isImage(file.type, extension) || isGif(file.type)) {
    return 'photo';
  }

  if (isVideo(file.type, extension)) {
    return 'video';
  }

  if (isAudio(file.type, extension)) {
    return 'audio';
  }

  if (isPDF(file.type, extension)) {
    return 'pdf';
  }

  if (isDOC(file.type, extension)) {
    return 'doc';
  }

  if (isEXCEL(file.type, extension)) {
    return 'excel';
  }

  return 'others';
}

export const getLastMessageCreatedAt = ({ channel }) => {
  const createdAt = channel?.lastMessage?.createdAt;
  if (!createdAt) {
    return '';
  }
  if (isToday(createdAt)) {
    return moment(createdAt).fromNow();
  }
  if (isYesterday(createdAt)) {
    return stringSet.MESSAGE_STATUS__YESTERDAY || 'Yesterday';
  }
  if (isThisYear(createdAt)) {
    return format(createdAt, 'MMM d');
  }
  return format(createdAt, 'MM/dd/yyyy');
};

export const isAboutSame = (a: number, b: number, px: number) => Math.abs(a - b) <= px;

export function getTranslatedMessage(message: UserMessage, userId?: string) {
  if (message.sender.userId === userId) {
    // Do not translate the message sent by the current user
    return;
  }

  const language = getUserLanguage();
  if (language === null) {
    return;
  }

  if (language.startsWith('es')) {
    return message.translations['es'];
  } else if (language.startsWith('en')) {
    return message.translations['en'];
  }
}

export function getUserLanguage() {
  const language = navigator.language;

  if (language.startsWith('es')) {
    return 'es';
  } else if (language.startsWith('en')) {
    return 'en';
  }

  return null;
}
