import { FC, useMemo } from 'react';
import type { GroupChannel } from '@sendbird/chat/groupChannel';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import type { FileMessage } from '@sendbird/chat/message';
import classNames from 'classnames';

import * as utils from './utils';
import { isVoiceMessage } from '../types';
import { cn } from '@/hammr-ui/lib/utils';
import { useEmployeeDict } from '../context/EmployeeList';
import type { EveryMessage } from '@sendbird/uikit-react/types/types';
import { useGroupChannelListContext } from '@sendbird/uikit-react/GroupChannelList/context';

interface ChannelPreviewProps {
  channel: GroupChannel;
}

export const ChannelPreview: FC<ChannelPreviewProps> = ({ channel }) => {
  const sbState = useSendbirdStateContext();
  const { selectedChannelUrl } = useGroupChannelListContext();

  const isActive = selectedChannelUrl === channel.url;
  const userId = sbState?.stores?.userStore?.user?.userId;

  const users = useEmployeeDict();
  const channelName = useMemo(() => utils.getChannelTitle(channel, userId, users), [channel, userId, users]);

  const isGroupChannel = channel.customType === 'group';

  const lastMessageSenderName = useMemo(() => {
    const message = channel.lastMessage as EveryMessage;
    if (!message) {
      return null;
    }

    if (!('sender' in message)) {
      return null;
    }

    if (message.sender.userId === userId) {
      return 'You';
    }

    return message.sender.nickname || users?.[message.sender.userId]?.firstName || message.sender.userId;
  }, [users, channel.lastMessage, userId]);

  return (
    <div
      className={classNames(
        'flex cursor-pointer flex-col gap-3 border-b border-b-soft-200 p-4 pl-3 text-foreground hover:bg-weak-50',
        isActive && 'bg-weak-50'
      )}
    >
      <div className="flex items-center gap-3">
        <div className={cn('h-2 w-2 rounded-full', channel.unreadMessageCount > 0 && 'bg-primary-base')} />
        <div className="flex-1 font-medium">{channelName}</div>
        <div className="text-sm text-sub-600">
          {utils.getLastMessageCreatedAt({
            channel,
          })}
        </div>
      </div>
      {!!channel.lastMessage && (
        <div className="ml-5 flex justify-between gap-2">
          <div
            className="overflow-hidden text-sm text-sub-600"
            style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' }}
          >
            {isGroupChannel && lastMessageSenderName && `${lastMessageSenderName}: `}
            {!isVoiceMessage(channel.lastMessage as FileMessage) && utils.getLastMessage(channel, userId)}
            {isVoiceMessage(channel.lastMessage as FileMessage) && utils.stringSet.VOICE_MESSAGE}
          </div>
        </div>
      )}
    </div>
  );
};
