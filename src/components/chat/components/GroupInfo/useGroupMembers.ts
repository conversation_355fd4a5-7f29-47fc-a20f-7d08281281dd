import { useEffect, useRef, useState } from 'react';
import { GroupChannel, Member } from '@sendbird/chat/groupChannel';

function getMembersKey(members: Member[]) {
  return members.map((member) => member.userId).join('-');
}

// sendbird update group member mutation is not reactive
// so we need to use this hook to make it reactive
export function useGroupMembers(channel: GroupChannel) {
  const [members, setMembers] = useState(channel?.members ?? []);
  const [key, setKey] = useState(getMembersKey(members));
  const keyRef = useRef(key);
  keyRef.current = key;

  useEffect(() => {
    setKey(getMembersKey(channel.members));

    const interval = setInterval(
      () => {
        const key = getMembersKey(channel.members);
        if (keyRef.current !== key) {
          setKey(getMembersKey(channel.members));
        }
      },

      500
    );

    return () => clearInterval(interval);
  }, [channel, channel.members]);

  useEffect(() => {
    setMembers([...channel.members]);
  }, [channel.members, key]);

  return members;
}
