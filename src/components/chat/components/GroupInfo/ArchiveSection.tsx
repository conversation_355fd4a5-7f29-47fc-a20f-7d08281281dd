import { FC } from 'react';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import { DialogFooter } from '@/hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import { openConfirmDialog } from '@/hammr-ui/components/ConfirmDialog';
import { hideChannel, unhideChannel } from 'services/sendbird';
import { addToast } from '@/hooks/useToast';
import { showErrorToast } from '@/utils/errorHandling';
import { useMutation } from '@tanstack/react-query';
import { useHammrChatStore } from '../../store';
import { Role } from '@sendbird/chat';

interface ArchiveSectionProps {
  onRequestClose: () => void;
}

export const ArchiveSection: FC<ArchiveSectionProps> = ({ onRequestClose }) => {
  const channelStore = useGroupChannelContext();

  const { currentChannel } = channelStore;

  // Determine if current user is the channel creator/operator
  const isChannelCreator = currentChannel?.myRole === Role.OPERATOR;

  // Determine if it's a group chat
  const isGroupChat = currentChannel?.customType === 'group';

  const { isPending: isArchiving, mutate: archiveChat } = useMutation({
    mutationFn: async () => {
      try {
        await hideChannel(currentChannel.url, {
          isChannelCreator,
          isGroupChat,
        });
        await currentChannel.refresh();

        onRequestClose?.();

        addToast({
          type: 'success',
          title: 'Chat archived',
          description: `${isGroupChat ? 'Group chat' : 'Chat'} archived successfully`,
        });

        useHammrChatStore.setState({ currentChannelUrl: '' });
      } catch (error) {
        showErrorToast(error);
      }
    },
  });

  const { isPending: isUnarchiving, mutate: unarchiveChat } = useMutation({
    mutationFn: async () => {
      try {
        await unhideChannel(currentChannel.url, {
          isChannelCreator,
          isGroupChat,
        });
        await currentChannel.refresh();
        useHammrChatStore.setState({ currentChannelUrl: '' });

        onRequestClose?.();

        addToast({
          type: 'success',
          title: 'Chat unarchived',
          description: `${isGroupChat ? 'Group chat' : 'Chat'} unarchived successfully`,
        });
      } catch (error) {
        showErrorToast(error);
      }
    },
  });

  return (
    <DialogFooter>
      {!currentChannel.isHidden && (
        <Button
          fullWidth
          loading={isArchiving}
          variant="outline"
          color="error"
          onClick={async () => {
            const confirmResult = await openConfirmDialog({
              title: 'Archive Chat',
              subtitle: 'Are you sure you want to archive this chat?',
            });

            if (!confirmResult.confirmed) {
              return;
            }

            archiveChat();
          }}
        >
          Archive Chat
        </Button>
      )}
      {currentChannel.isHidden && (
        <Button
          fullWidth
          loading={isUnarchiving}
          variant="outline"
          color="error"
          onClick={async () => {
            const confirmResult = await openConfirmDialog({
              title: 'Unarchive Chat',
              subtitle: 'Are you sure you want to unarchive this chat?',
            });

            if (!confirmResult.confirmed) {
              return;
            }

            unarchiveChat();
          }}
        >
          Unarchive Chat
        </Button>
      )}
    </DialogFooter>
  );
};
