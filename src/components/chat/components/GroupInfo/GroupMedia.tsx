import { FC } from 'react';
import { MediaContainer } from './Media/MediaContainer';
import { Dialog, DialogBody, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { RiImage2Line } from '@remixicon/react';

interface GroupInfoProps {
  open: boolean;
  onRequestClose: () => void;
}

export const GroupMedia: FC<GroupInfoProps> = ({ open, onRequestClose }) => {
  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          onRequestClose();
        }
      }}
    >
      <DialogSurface className="h-[700px] max-w-[814px]">
        <DialogHeader icon={<RiImage2Line className="text-sub-600" />} title="Chat Files" showCloseButton />
        <DialogBody className="flex flex-1 overflow-hidden p-0">
          <MediaContainer />
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
};
