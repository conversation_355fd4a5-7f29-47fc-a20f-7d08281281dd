import { FC, useMemo } from 'react';
import { FileMessage } from '@sendbird/chat/message';
import { getExtension, isUnsupportedImage } from 'components/chat/types';
import { RiImage2Line } from '@remixicon/react';

interface ImageItemProps {
  message: FileMessage;
  onClick?: () => void;
}

export const ImageItem: FC<ImageItemProps> = ({ message, onClick }) => {
  const isUnsupported = useMemo(() => isUnsupportedImage(message.type, getExtension(message.name)), [message]);

  if (isUnsupported) {
    return (
      <div
        className="relative h-[100px] w-[100px] cursor-pointer overflow-hidden rounded-8 bg-weak-50"
        onClick={() => window.open(message.url, '_blank')}
        role="button"
      >
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <button
            className="flex cursor-pointer items-center justify-center rounded-full text-static-white"
            onClick={onClick}
          >
            <RiImage2Line className="h-10 w-10" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <img
      src={message.url}
      onClick={onClick}
      alt=""
      className="h-[100px] w-[100px] cursor-pointer rounded-8 bg-weak-50 object-cover"
    />
  );
};
