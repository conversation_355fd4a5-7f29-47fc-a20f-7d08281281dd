import { FileMessage } from '@sendbird/chat/message';
import {
  isAudio,
  isDOC,
  isEXCEL,
  isGif,
  isImage,
  isPDF,
  isVideo,
} from 'components/chat/types';

export function getDocumentThumbnail(message: FileMessage) {
  const ext = message.name ? message.name.split('.').pop() : '';

  if (
    isVideo(message.type, ext) ||
    isImage(message.type, ext) ||
    isGif(message.type)
  ) {
    // video and image show in seperate tab
    return '';
  }

  if (isAudio(message.type, ext)) {
    return '/img/file/audio.svg';
  }

  if (isPDF(message.type, ext)) {
    return '/img/file/pdf.svg';
  }

  if (isDOC(message.type, ext)) {
    return '/img/file/doc.svg';
  }

  if (isEXCEL(message.type, ext)) {
    return '/img/file/excel.svg';
  }

  return '/img/file/other.jpg';
}
