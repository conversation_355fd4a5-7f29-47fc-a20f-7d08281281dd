import { FC, useMemo } from 'react';
import { FileMessage } from '@sendbird/chat/message';
import { getExtension, isUnsupportedVideo } from 'components/chat/types';
import { RiPlayCircleLine } from '@remixicon/react';

interface VideoItemProps {
  message: FileMessage;
  onClick?: () => void;
}

export const VideoItem: FC<VideoItemProps> = ({ message, onClick }) => {
  const isUnsupported = useMemo(() => isUnsupportedVideo(message.type, getExtension(message.name)), [message]);

  if (isUnsupported) {
    return (
      <div
        className="relative h-[100px] w-[100px] cursor-pointer overflow-hidden rounded-8 bg-weak-50"
        onClick={() => window.open(message.url, '_blank')}
        role="button"
      >
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <button
            className="flex cursor-pointer items-center justify-center rounded-full text-static-white"
            onClick={onClick}
          >
            <RiPlayCircleLine className="h-10 w-10" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-[100px] w-[100px] cursor-pointer overflow-hidden rounded-8 bg-weak-50">
      <video src={message.url} autoPlay={false} className="h-full w-full" />
      <div className="absolute inset-0 flex items-center justify-center bg-black/20">
        <button
          className="flex cursor-pointer items-center justify-center rounded-full text-static-white"
          onClick={onClick}
        >
          <RiPlayCircleLine className="h-10 w-10" />
        </button>
      </div>
    </div>
  );
};
