import { FC, useState } from 'react';
import { FileMessage } from '@sendbird/chat/message';
import { isAudio, isPDF } from 'components/chat/types';
import { logError } from 'utils/errorHandling';
import FileFormatIcon from '@/hammr-icons/FileFormatIcon';
import Spinner from '@/hammr-ui/components/spinner';
import { useEmployeeDict } from '@/components/chat/context/EmployeeList';
import { formatBytes } from '@/components/chat/utils';
import format from 'date-fns/format';

export const DocumentItem: FC<{ message: FileMessage }> = ({ message }) => {
  const [loading, setLoading] = useState(false);

  const ext = message.name ? message.name.split('.').pop() : '';

  const handleOpenDocument = async (message: FileMessage) => {
    setLoading(true);
    try {
      const ext = message.name ? message.name.split('.').pop() : '';

      // check for browser supported file types
      if (isPDF(message.type, ext) || isAudio(message.type, ext)) {
        const response = await fetch(message.url);
        const blob = await response.blob();

        const file = new File([blob], message.name, {
          type: message.type,
        });

        const url = URL.createObjectURL(file);

        window.open(url, '_blank');
        return;
      }

      window.open(message.url, '_blank');
    } catch (err) {
      logError(err);

      // fallback to opening in new tab
      window.open(message.url, '_blank');
    } finally {
      setLoading(false);
    }
  };

  const users = useEmployeeDict();

  const fullName = users[message.sender.userId]?.fullName || message.sender.nickname || message.sender.userId;

  return (
    <div
      className="flex gap-3 rounded-12 border border-soft-200 p-4"
      role="button"
      onClick={() => handleOpenDocument(message)}
    >
      <div className="flex w-8 items-center justify-center">
        {loading ? <Spinner /> : <FileFormatIcon type={ext} />}
      </div>
      <div className="flex flex-1 flex-col gap-1">
        <div className="text-sm font-medium text-strong-950">{message.name}</div>
        <div className="text-xs font-normal text-sub-600">{formatBytes(message.size)}</div>
      </div>
      <div className="flex flex-col items-end gap-1">
        <div className="text-sm font-normal text-strong-950">{fullName}</div>
        <div className="text-xs font-normal text-sub-600">{format(message.createdAt, "MMM dd, yyyy 'at' hh:mm a")}</div>
      </div>
    </div>
  );
};
