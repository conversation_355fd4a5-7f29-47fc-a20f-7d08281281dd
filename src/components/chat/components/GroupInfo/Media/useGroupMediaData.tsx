import { GroupChannel } from '@sendbird/chat/groupChannel';
import { MessageTypeFilter } from '@sendbird/chat/message';
import { useMemo } from 'react';
import { BaseMessage, FileMessage } from '@sendbird/chat/message';
import { getExtension, isFileMessage, isImage, isVideo } from 'components/chat/types';
import { useQuery } from '@tanstack/react-query';

const FETCH_LIMIT = 100;

export async function fetchAllMediaMessages<T extends BaseMessage = BaseMessage>(channel: GroupChannel): Promise<T[]> {
  const allMessages: T[] = [];

  let ts = Date.now();

  while (true) {
    const messages = await channel?.getMessagesByTimestamp(ts, {
      reverse: true,
      prevResultSize: FETCH_LIMIT,
      nextResultSize: 0,
      messageTypeFilter: MessageTypeFilter.FILE,
    });

    if (!messages?.length) {
      break;
    }

    allMessages.push(...(messages as T[]));

    if (messages.length < FETCH_LIMIT) {
      break;
    }

    const lastMessage = messages[messages.length - 1];

    if (lastMessage.createdAt === ts) {
      break;
    }

    ts = lastMessage.createdAt;
  }

  return allMessages;
}

export const useGroupMediaData = (channel: GroupChannel) => {
  const { data, isPending } = useQuery({
    queryKey: ['group-media', channel.url],
    queryFn: () => fetchAllMediaMessages(channel),
  });

  const photos = useMemo(
    () =>
      data?.filter((message: FileMessage) => isImage(message.type, getExtension(message.name))).filter(isFileMessage) ??
      [],
    [data]
  );

  const videos = useMemo(
    () =>
      data?.filter((message: FileMessage) => isVideo(message.type, getExtension(message.name))).filter(isFileMessage) ??
      [],
    [data]
  );

  const documents = useMemo(
    () =>
      data
        ?.filter(
          (message: FileMessage) =>
            !isImage(message.type, getExtension(message.name)) && !isVideo(message.type, getExtension(message.name))
        )
        .filter(isFileMessage) ?? [],
    [data]
  );

  return {
    loading: isPending,
    photos,
    videos,
    documents,
  };
};
