import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import { FC, useState } from 'react';
import { useMessageMediaViewerStore } from '../../MessageMediaViewer/store';
import { useGroupMediaData } from './useGroupMediaData';
import { DocumentItem } from './DocumentItem';
import { ImageItem } from './ImageItem';
import { VideoItem } from './VideoItem';
import { TabContent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import { EmptytStateChatPhoto } from '@/hammr-ui/components/empty-states/ChatPhoto';
import { EmptytStateChatVideo } from '@/hammr-ui/components/empty-states/ChatVideo';
import { EmptytStateChatDocument } from '@/hammr-ui/components/empty-states/ChatDocument';

export const MediaContainer: FC = () => {
  const [selectedTab, setSelectedTab] = useState('photos');

  const channelStore = useGroupChannelContext();
  const { currentChannel } = channelStore;

  const { photos, videos, documents, loading } = useGroupMediaData(currentChannel);

  return (
    <Tabs
      value={selectedTab}
      onValueChange={(value) => setSelectedTab(value)}
      className="flex flex-1 flex-col gap-4 overflow-hidden py-5"
    >
      <TabList className="mx-5">
        <TabItem value="photos" className="w-full capitalize">
          Photos{photos.length > 0 && ` (${photos.length})`}
        </TabItem>
        <TabItem value="videos" className="w-full capitalize">
          Videos{videos.length > 0 && ` (${videos.length})`}
        </TabItem>
        <TabItem value="documents" className="w-full capitalize">
          Documents{documents.length > 0 && ` (${documents.length})`}
        </TabItem>
      </TabList>
      <TabContent value="photos" className="flex-1 flex-col gap-4 overflow-hidden data-[state=active]:flex">
        {loading ? (
          <div className="px-5">
            <Loading />
          </div>
        ) : photos.length === 0 ? (
          <Placeholder ImageComponent={EmptytStateChatPhoto} title="There are no chat photos yet." />
        ) : (
          <ScrollArea>
            <div className="flex flex-wrap items-start gap-3 px-5">
              {photos.map((message, index) => (
                <ImageItem
                  key={index}
                  message={message}
                  onClick={() =>
                    useMessageMediaViewerStore.setState({
                      isOpen: true,
                      message,
                      messages: photos,
                    })
                  }
                />
              ))}
            </div>
          </ScrollArea>
        )}
      </TabContent>
      <TabContent value="videos" className="flex-1 flex-col gap-4 overflow-hidden data-[state=active]:flex">
        {loading ? (
          <div className="px-5">
            <Loading />
          </div>
        ) : videos.length === 0 ? (
          <Placeholder ImageComponent={EmptytStateChatVideo} title="There are no chat videos yet." />
        ) : (
          <ScrollArea>
            <div className="flex flex-wrap items-start gap-3 px-5">
              {videos.map((message, index) => (
                <VideoItem
                  key={index}
                  message={message}
                  onClick={() =>
                    useMessageMediaViewerStore.setState({
                      isOpen: true,
                      message,
                      messages: videos,
                    })
                  }
                />
              ))}
            </div>
          </ScrollArea>
        )}
      </TabContent>
      <TabContent value="documents" className="flex-1 flex-col gap-4 overflow-hidden data-[state=active]:flex">
        {loading ? (
          <Loading />
        ) : documents.length === 0 ? (
          <Placeholder ImageComponent={EmptytStateChatDocument} title="There are no chat documents yet." />
        ) : (
          <ScrollArea>
            <div className="flex flex-col gap-3 px-5">
              {documents.map((message, index) => (
                <DocumentItem key={index} message={message} />
              ))}
            </div>
          </ScrollArea>
        )}
      </TabContent>
    </Tabs>
  );
};

const Loading: FC = () => {
  // no loading UI for now
  return null;
  // return (
  //   <div className="flex flex-wrap items-start gap-3">
  //     {Array.from({ length: 10 }).map((_, index) => (
  //       <div key={index} className="flex h-[100px] w-[100px] animate-pulse rounded-md bg-weak-100" />
  //     ))}
  //   </div>
  // );
};

const Placeholder: FC<{ ImageComponent: FC<React.SVGProps<SVGSVGElement>>; title: string }> = ({
  ImageComponent,
  title,
}) => {
  return (
    <div className="flex flex-1 flex-col items-center justify-center gap-5 p-5">
      <ImageComponent className="h-[100px] w-[100px]" />
      <p className="text-sm font-normal text-soft-400">{title}</p>
    </div>
  );
};
