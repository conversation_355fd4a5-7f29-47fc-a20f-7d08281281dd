export function getEmployeeFullName(employee: { fullName?: string; firstName?: string; lastName?: string }) {
  if (!employee) {
    return null;
  }

  return employee.fullName || `${employee.firstName} ${employee.lastName}`;
}

export function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}
