import type { FileMessage, UserMessage, MultipleFilesMessage, AdminMessage } from '@sendbird/chat/message';

export type CoreMessageType = AdminMessage | UserMessage | FileMessage | MultipleFilesMessage;
export type SendableMessageType = UserMessage | FileMessage | MultipleFilesMessage;
export type Nullable<T> = T | null;

export const isFileMessage = (message: CoreMessageType): message is FileMessage =>
  message &&
  (message['isFileMessage'] && typeof message.isFileMessage === 'function'
    ? message.isFileMessage()
    : message?.messageType === 'file');

export const isAudioMessageMimeType = (type: string): boolean => /^audio\//.test(type);

export const isVoiceMessageMimeType = (type: string): boolean => /^voice\//.test(type);

export const isVoiceMessage = (message: Nullable<SendableMessageType>): message is FileMessage => {
  // ex) audio/m4a OR audio/m4a;sbu_type=voice
  if (!(message && isFileMessage(message)) || !(message as FileMessage).type) {
    return false;
  }
  const [mimeType, typeParameter] = (message as FileMessage).type.split(';');

  if (!isAudioMessageMimeType(mimeType)) {
    return false;
  }

  if (typeParameter) {
    const [key, value] = typeParameter.split('=');
    return key === 'sbu_type' && value === 'voice';
  }
  // ex) message.metaArrays = [{ key: 'KEY_INTERNAL_MESSAGE_TYPE', value: ['voice/m4a'] }]
  return isVoiceMessageMimeType(
    message?.metaArrays?.find((metaArray) => metaArray.key === 'KEY_INTERNAL_MESSAGE_TYPE')?.value?.[0] ?? ''
  );
};

// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Complete_list_of_MIME_types
export const SUPPORTED_MIMES = {
  IMAGE: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/svg+xml',
    'image/avif',
    'image/webp', // not supported in IE
  ],
  VIDEO: [
    'video/mpeg', // .mpeg
    'video/ogg', // .ogv
    'video/webm', // .webm
    'video/mp4', // .mp4
    'video/quicktime', // .mov
    'video/x-msvideo', // .avi
    'video/3gpp', // .3gp
    'video/x-matroska', // .mkv
  ],
  AUDIO: [
    'audio/aac', // .aac
    'audio/midi', // .mid
    'audio/x-midi', // .mid
    'audio/mpeg', // .mp3
    'audio/ogg', // .oga
    'audio/opus', // .opus
    'audio/wav', // .wav
    'audio/webm', // .weba
    'audio/3gpp', // .3gp
    'audio/3gpp2', // .3g2
    'audio/mp3', // .mp3
  ],
  DOCUMENT: [
    'text/plain', // .txt
    'text/css', // .css
    'text/csv', // .csv
    'text/html', // .html
    'text/calendar', // .ics
    'text/javascript', // .js
    'text/xml', // .xml
  ],
  APPLICATION: [
    'application/x-abiword',
    'application/x-freearc',
    'application/vnd.amazon.ebook',
    'application/octet-stream',
    'application/x-bzip',
    'application/x-bzip2',
    'application/x-cdf',
    'application/x-csh',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-fontobject',
    'application/epub+zip',
    'application/gzip',
    'application/java-archive',
    'application/json',
    'application/ld+json',
    'application/vnd.apple.installer+xml',
    'application/vnd.oasis.opendocument.presentation',
    'application/vnd.oasis.opendocument.spreadsheet',
    'application/vnd.oasis.opendocument.text',
    'application/ogg',
    'application/pdf',
    'application/x-httpd-php',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.rar',
    'application/rtf',
    'application/x-sh',
    'application/x-tar',
    'application/vnd.visio',
    'application/xhtml+xml',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/xml',
    'application/vnd.mozilla.xul+xml',
    'application/zip',
    'application/x-7z-compressed',
  ],
};

const IMAGE_EXT = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'tiff', 'bmp', 'heic'];
const VIDEO_EXT = ['mpeg', 'ogv', 'webm', 'mp4', 'mov', 'avi', '3gp', 'mkv', 'wmv'];
const AUDIO_EXT = ['aac', 'mid', 'midi', 'mp3', 'oga', 'opus', 'wav', 'weba', 'm4a'];

const UNSUPPORTED_IMAGE_EXT = ['tiff', 'heic'];
const UNSUPPORTED_VIDEO_EXT = ['wmv'];

const PDF_MIME = ['application/pdf'];
const PDF_EXT = ['pdf'];

const DOC_MIME = [
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.oasis.opendocument.text',
  'text/plain',
  'application/rtf',
];
const DOC_EXT = ['doc', 'docx', 'odt', 'txt', 'rtf'];

const EXCEL_MIME = [
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.oasis.opendocument.spreadsheet',
  'text/csv',
];
const EXCEL_EXT = ['xls', 'xlsx', 'ods', 'csv'];

export const isImage = (type: string, ext?: string): boolean =>
  SUPPORTED_MIMES.IMAGE.indexOf(type) >= 0 || IMAGE_EXT.indexOf(ext) >= 0;
export const isVideo = (type: string, ext?: string): boolean =>
  SUPPORTED_MIMES.VIDEO.indexOf(type) >= 0 || VIDEO_EXT.indexOf(ext) >= 0;
export const isGif = (type: string): boolean => type === 'image/gif';
export const isSupportedFileView = (type: string): boolean => isImage(type) || isVideo(type);
export const isAudio = (type: string, ext?: string): boolean =>
  SUPPORTED_MIMES.AUDIO.indexOf(type) >= 0 || AUDIO_EXT.indexOf(ext) >= 0;

export const isPDF = (type: string, ext?: string): boolean => PDF_MIME.indexOf(type) >= 0 || PDF_EXT.indexOf(ext) >= 0;

export const isDOC = (type: string, ext?: string): boolean => DOC_MIME.indexOf(type) >= 0 || DOC_EXT.indexOf(ext) >= 0;

export const isEXCEL = (type: string, ext?: string): boolean =>
  EXCEL_MIME.indexOf(type) >= 0 || EXCEL_EXT.indexOf(ext) >= 0;

export const getExtension = (name: string): string => {
  const ext = name.indexOf('.') >= 0 ? name.split('.').pop() : '';
  return ext ? ext.toLowerCase() : '';
};

export const isUnsupportedImage = (type: string, ext?: string): boolean =>
  isImage(type, ext) && UNSUPPORTED_IMAGE_EXT.indexOf(ext) >= 0;

export const isUnsupportedVideo = (type: string, ext?: string): boolean =>
  isVideo(type, ext) && UNSUPPORTED_VIDEO_EXT.indexOf(ext) >= 0;
