import { useMemo } from 'react';
import { useGroupChannelContext } from '@sendbird/uikit-react/GroupChannel/context';
import useSendbirdStateContext from '@sendbird/uikit-react/useSendbirdStateContext';
import { getChannelTitle } from './components/utils';
import { DialogBody } from '@/hammr-ui/components/dialog';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { useEmployees } from '@/hooks/data-fetching/useEmployees';
import { useAuth } from '@/hooks/useAuth';
import { useGroupMembers } from './components/GroupInfo/useGroupMembers';
import { ArchiveSection } from './components/GroupInfo/ArchiveSection';

interface GroupInfoNonAdminUIProps {
  onRequestClose: () => void;
}

export default function GroupInfoNonAdminUI({ onRequestClose }: GroupInfoNonAdminUIProps) {
  const channelStore = useGroupChannelContext();
  const globalStore = useSendbirdStateContext();
  const userId = globalStore?.config?.userId;

  const { currentChannel } = channelStore;
  const isGroupChat = currentChannel?.customType === 'group';

  const { user } = useAuth();
  const allEmployees = useEmployees(Number(user.companyId));

  const employees = useMemo(() => {
    return allEmployees.filter((employee) => employee.id !== Number(userId));
  }, [allEmployees, userId]);

  const members = useGroupMembers(currentChannel);
  const selectedUserIds = useMemo(() => members?.map((member) => Number(member.userId)), [members]);

  const chatName = getChannelTitle(currentChannel, userId);
  const selectedEmployees = useMemo(() => {
    return employees.filter((employee) => selectedUserIds?.includes(employee.id));
  }, [employees, selectedUserIds]);

  return (
    <>
      <DialogBody className="flex flex-col gap-5">
        <FormItem>
          <FormLabel>Chat Name</FormLabel>
          <FormControl>
            <div className="text-sm font-normal text-foreground">{chatName}</div>
          </FormControl>
        </FormItem>
        {/* Only show members list for group chats */}
        {isGroupChat && (
          <FormItem>
            <FormLabel>Chat Members</FormLabel>
            <FormControl>
              <div className="text-sm font-normal text-foreground">
                {selectedEmployees
                  .map((employee) => employee?.fullName || `${employee.firstName} ${employee.lastName}`)
                  .join(', ')}
              </div>
            </FormControl>
          </FormItem>
        )}
      </DialogBody>
      <ArchiveSection onRequestClose={onRequestClose} />
    </>
  );
}
