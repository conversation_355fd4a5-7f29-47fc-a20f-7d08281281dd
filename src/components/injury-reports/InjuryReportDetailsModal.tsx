import { ModalV2 } from '@/components/elements/ModalV2';
import { InjuryReportRow, mapInjuryReportsForTable } from './utils';
import dayjs from 'dayjs';
import { RiCheckLine, RiCloseLine, RiFirstAidKitLine } from '@remixicon/react';
import { AWS_CUSTOMER_BUCKET } from '@/utils/constants';
import { useAuth } from '@/hooks/useAuth';
import { useEffect, useState } from 'react';
import { useAwsS3 } from '@/hooks/useAwsS3';
import { FormV2 } from '../elements/Form';
import { FormControl, FormLabel, FormItem } from '@/hammr-ui/components/form';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { logError, showErrorToast } from '@/utils/errorHandling';
import { apiRequest } from '@/utils/requestHelpers';
import { addToast } from '@/hooks/useToast';
import { useQueryClient } from '@tanstack/react-query';
import { cn } from '@/hammr-ui/lib/cn';

interface InjuryReportDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  report: InjuryReportRow | null;
}

const ResolutionForm = ({
  report,
  showResolutionNoteEditor,
  setShowResolutionNoteEditor,
  onResolve,
  resolutionNote,
  setResolutionNote,
}: {
  report: InjuryReportRow;
  showResolutionNoteEditor: boolean;
  setShowResolutionNoteEditor: (show: boolean) => void;
  onResolve: (resolutionNote: string) => void;
  resolutionNote: string;
  setResolutionNote: (resolutionNote: string) => void;
}) => {
  if (!showResolutionNoteEditor) return null;

  return (
    <FormItem className="relative mt-5">
      <FormLabel className="text-sm text-sub-600">Resolution Notes</FormLabel>
      <FormControl>
        <Textarea
          value={resolutionNote}
          onChange={(event) => {
            setResolutionNote(event.target.value);
          }}
        />
      </FormControl>
      <div className="absolute right-0 flex gap-2">
        <LinkButton
          size="small"
          className="flex gap-1"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setShowResolutionNoteEditor(false);
          }}
        >
          <RiCloseLine className="h-4 w-4" /> Cancel
        </LinkButton>
        <LinkButton
          size="small"
          className="flex gap-1 text-primary-base"
          disabled={!resolutionNote}
          onClick={async (e) => {
            if (!resolutionNote) {
              return;
            }

            e.preventDefault();
            e.stopPropagation();

            onResolve(resolutionNote);
          }}
        >
          <RiCheckLine className="h-4 w-4" /> Resolve
        </LinkButton>
      </div>
    </FormItem>
  );
};

const ResolvedReportExtraDetails = ({ report }: { report: InjuryReportRow }) => {
  if (report.status === 'OPEN') return null;

  return (
    <div>
      <hr className="col-span-2 my-4 border-sub-300" />
      <div className="grid grid-cols-2 gap-5">
        <div>
          <div className="text-xs text-sub-600">Resolved By</div>
          <div className="mt-1.5 text-sm text-strong-950">{report.resolvedBy}</div>
        </div>
        <div>
          <div className="text-xs text-sub-600">Resolved On</div>
          <div className="mt-1.5 text-sm text-strong-950">
            {dayjs(report.resolutionDate).format('MM/DD/YYYY [at] h:mm A')}
          </div>
        </div>

        <div className="col-span-2">
          <div className="text-xs text-sub-600">Resolution Notes</div>
          <div className="mt-1.5 text-sm text-strong-950">{report.resolutionNote}</div>
        </div>
      </div>
    </div>
  );
};

const BasicDetails = ({ signedUrls, report }: { signedUrls: string[]; report: InjuryReportRow }) => {
  const shouldShowMinHeightPhotos = report.photos.length > 0;
  return (
    <div className="grid grid-cols-2 gap-5">
      <div>
        <div className="text-xs text-sub-600">Employee</div>
        <div className="mt-1.5 text-sm text-strong-950">{report.employee}</div>
      </div>
      <div>
        <div className="text-xs text-sub-600">Project</div>
        <div className="mt-1.5 text-sm text-strong-950">{report.project}</div>
      </div>

      <div className="col-span-2">
        <div className="text-xs text-sub-600">Reported On</div>
        <div className="mt-1.5 text-sm text-strong-950">{dayjs(report.createdAt).format('MM/DD/YYYY, h:mm A')}</div>
      </div>

      <div className="col-span-2">
        <div className="text-xs text-sub-600">Injury Notes</div>
        <div className="mt-1.5 text-sm text-strong-950">{report.injuryNotes}</div>
        <div className={cn('flex flex-wrap gap-2', shouldShowMinHeightPhotos && 'mt-5 min-h-[120px]')}>
          {signedUrls.map((url, index) => (
            <div key={`${url}-${index}`} className="size-[120px] rounded-md">
              <img src={url} alt="Injury Report Photo" className="h-full w-full rounded-md object-cover" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export const InjuryReportDetailsModal = ({ isOpen, onClose, report }: InjuryReportDetailsModalProps) => {
  const { user } = useAuth();
  const { getSignedUrlAsync } = useAwsS3();
  const [signedUrls, setSignedUrls] = useState<string[]>([]);
  const [showResolutionNoteEditor, setShowResolutionNoteEditor] = useState(false);
  const [resolvedReport, setResolvedReport] = useState<InjuryReportRow | null>(null);
  const queryClient = useQueryClient();
  const [resolutionNote, setResolutionNote] = useState('');

  const fetchSignedUrls = async () => {
    try {
      const signedUrlPromises = report?.photos?.map((photo) => {
        return getSignedUrlAsync({
          Bucket: `${AWS_CUSTOMER_BUCKET}/${user?.companyId}/injury-report-photos`,
          Key: photo.objectId,
        });
      });

      const signedUrls = await Promise.all(signedUrlPromises ?? []);

      setSignedUrls(signedUrls as string[]);
    } catch (error) {
      console.error(error);
    }
  };

  const resolveReport = async (resolutionNote: string) => {
    try {
      const updatedReport = await apiRequest(`injury-reports/${report?.id}`, {
        method: 'PATCH',
        body: {
          resolutionNote: resolutionNote,
          resolvedBy: user?.uid,
          resolutionDate: new Date().valueOf(),
        },
      });

      addToast({
        type: 'success',
        title: 'Injury Report Resolved',
      });

      setShowResolutionNoteEditor(false);

      queryClient.invalidateQueries({ queryKey: ['injury-reports'] });
      setResolvedReport(mapInjuryReportsForTable([updatedReport.injuryReport])[0]);
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Failed to resolve injury report');
    }
  };

  useEffect(() => {
    fetchSignedUrls();
  }, [report]);

  if (!report) return null;

  if (report.status === 'OPEN' && !resolvedReport) {
    return (
      <ModalV2
        open={isOpen}
        setOpen={onClose}
        title="Injury Report Details"
        icon={<RiFirstAidKitLine className="h-5 w-5" />}
      >
        <FormV2
          onSubmit={() =>
            showResolutionNoteEditor ? resolveReport(resolutionNote) : setShowResolutionNoteEditor(true)
          }
          submitText="Resolve"
          submitProps={{ className: 'max-w-20 align-right', color: 'primary' }}
          isDisabled={showResolutionNoteEditor && !resolutionNote}
        >
          <BasicDetails signedUrls={signedUrls} report={report} />
          <ResolutionForm
            report={report}
            showResolutionNoteEditor={showResolutionNoteEditor}
            setShowResolutionNoteEditor={setShowResolutionNoteEditor}
            resolutionNote={resolutionNote}
            setResolutionNote={setResolutionNote}
            onResolve={resolveReport}
          />
        </FormV2>
      </ModalV2>
    );
  }

  return (
    <ModalV2
      open={isOpen}
      setOpen={onClose}
      title="Injury Report Details"
      icon={<RiFirstAidKitLine className="h-5 w-5" />}
    >
      <div className="p-6">
        <BasicDetails signedUrls={signedUrls} report={resolvedReport || report} />
        <ResolvedReportExtraDetails report={resolvedReport || report} />
      </div>
    </ModalV2>
  );
};
