import { InjuryReport } from '@/interfaces/injury-report';
import dayjs from 'dayjs';

interface InjuryPhoto {
  id: number;
  objectId: string;
}

interface User {
  id: number;
  firstName: string;
  lastName: string;
}

interface Timesheet {
  id: number;
  clockIn: string;
  clockOut: string;
  project: {
    id: number;
    name: string;
  };
}

export interface InjuryReportRow {
  id: number;
  employee: string;
  reportedBy: string;
  createdAt: string;
  injuryNotes: string;
  status: 'OPEN' | 'RESOLVED';
  resolvedBy?: string;
  resolutionNote?: string;
  timesheetDate: string;
  photos: InjuryPhoto[];
  project: string;
  resolutionDate: number;
  isResolved: boolean;
  projectId: number;
  userId: number;
}

export const mapInjuryReportsForTable = (reports: InjuryReport[]): InjuryReportRow[] => {
  return reports.map((report) => ({
    id: report.id,
    employee: `${report.user.firstName} ${report.user.lastName}`,
    reportedBy: `${report.creator?.firstName} ${report.creator?.lastName}`,
    createdAt: report.createdAt,
    injuryNotes: report.note,
    status: report.isResolved ? 'RESOLVED' : 'OPEN',
    project: report.timesheet.project.name,
    resolvedBy: report.resolvedByUser
      ? `${report.resolvedByUser.firstName} ${report.resolvedByUser.lastName}`
      : undefined,
    resolutionNote: report.resolutionNote,
    timesheetDate: dayjs(report.timesheet.clockIn).format('MMM D, YYYY'),
    photos: report.injuryPhotos,
    resolutionDate: report.resolutionDate,
    isResolved: report.isResolved,
    projectId: report.timesheet.project.id,
    userId: report.user.id,
  }));
};
