import { useState } from 'react';
import { useCallback, useMemo } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { mapInjuryReportsForTable, InjuryReportRow } from './utils';
import dayjs from 'dayjs';
import { InjuryReportDetailsModal } from './InjuryReportDetailsModal';
import EmptyStateHRNotes from '@/hammr-icons/EmptyStateHRNotes';
import { InjuryReport } from '@/interfaces/injury-report';
import { MutableRefObject } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { LinkButton } from '@/hammr-ui/components/LinkButton';

interface InjuryReportsTableProps {
  selectedStatus: 'OPEN' | 'RESOLVED';
  selectedProjectIds: number[];
  selectedEmployeeIds: number[];
  isLoading: boolean;
  reports: InjuryReport[];
  gridRef: MutableRefObject<AgGridReact>;
  pagination: any;
  setCurrentPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  pageSize: number;
}

export const InjuryReportsTable = ({
  selectedStatus,
  selectedProjectIds,
  selectedEmployeeIds,
  isLoading,
  reports,
  gridRef,
  pagination,
  setCurrentPage,
  setPageSize,
  pageSize,
}: InjuryReportsTableProps) => {
  const [selectedReport, setSelectedReport] = useState<InjuryReportRow | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const handleViewDetails = (report: InjuryReportRow) => {
    setSelectedReport(report);
    setIsDetailsModalOpen(true);
  };

  const colDefs: ColDef[] = [
    {
      headerName: 'Employee',
      field: 'employee',
      minWidth: 150,
      cellClass: 'font-medium',
    },
    {
      headerName: 'Project',
      field: 'project',
      minWidth: 150,
    },
    {
      headerName: 'Reported On',
      field: 'createdAt',
      initialSort: 'desc',
      minWidth: 120,
      comparator: (valueA, valueB) => {
        return dayjs(valueA).isBefore(dayjs(valueB)) ? -1 : dayjs(valueA).isAfter(dayjs(valueB)) ? 1 : 0;
      },
      cellRenderer: (params) => {
        return dayjs(params.value).format('MM/DD/YYYY, h:mm A');
      },
    },
    {
      headerName: 'Resolved By',
      field: 'resolvedBy',
      minWidth: 120,
      hide: selectedStatus === 'OPEN',
    },
    {
      headerName: 'Resolved On',
      field: 'resolutionDate',
      minWidth: 120,
      hide: selectedStatus === 'OPEN',
      valueFormatter: (params) => {
        return dayjs(params.value).format('MM/DD/YYYY, h:mm A');
      },
    },
    {
      headerName: 'Injury notes',
      field: 'injuryNotes',
      minWidth: 200,
      hide: selectedStatus === 'RESOLVED',
      sortable: false,
    },
    {
      headerName: '',
      sortable: false,
      cellRenderer: (params: ICellRendererParams) => (
        <LinkButton style="primary" onClick={() => handleViewDetails(params.data)}>
          View Details
        </LinkButton>
      ),
      maxWidth: 130,
    },
  ];

  const mappedRowData = useMemo(() => (reports ? mapInjuryReportsForTable(reports) : []), [reports, pageSize]);

  const isExternalFilterPresent = useCallback(() => true, []);

  const doesExternalFilterPass = useCallback(
    (node) => {
      const { data } = node as { data: InjuryReportRow };

      if (selectedEmployeeIds.length > 0 && !selectedEmployeeIds.includes(data.userId)) {
        return false;
      }

      if (selectedProjectIds.length > 0 && !selectedProjectIds.includes(data.projectId)) {
        return false;
      }

      return true;
    },
    [selectedEmployeeIds, selectedProjectIds]
  );

  if (mappedRowData.length === 0) {
    return (
      <div className="flex flex-col items-center gap-2 pt-[120px] text-center text-sm text-soft-400">
        <EmptyStateHRNotes />

        <div>There are no {selectedStatus === 'OPEN' ? 'open' : 'resolved'} injury reports yet.</div>
      </div>
    );
  }

  return (
    <div className="flex flex-grow">
      <UpdatedTable
        colDefs={colDefs}
        rowData={mappedRowData}
        isLoading={isLoading}
        emptyRowsText="No injury reports"
        fitToWindow
        tableProps={{
          isExternalFilterPresent,
          doesExternalFilterPass,
        }}
        parentRef={gridRef}
        onRowClicked={(event) => {
          handleViewDetails(event.data);
        }}
        pagination={{
          total: pagination.total,
          page: pagination.page,
          pageSize: pageSize,
          onPageChanged: (page) => {
            setCurrentPage(page);
          },
          onPageSizeChanged: (pageSize) => {
            setPageSize(pageSize);
          },
        }}
      />

      <InjuryReportDetailsModal
        key={selectedReport?.id}
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedReport(null);
        }}
        report={selectedReport}
      />
    </div>
  );
};
