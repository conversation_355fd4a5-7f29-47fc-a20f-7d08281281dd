import InfoCustomFill from '@/hammr-icons/InfoCustomFill';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { FringeBenefitStatement } from '@/interfaces/fringes';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import { useQuery } from '@tanstack/react-query';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { getFringeBenefitsStatement } from 'services/fringes';

interface Props {
  wageTableId: number;
  tableRef: React.MutableRefObject<AgGridReact<any>>;
}

export default function FringeBenefitsStatementTable({ wageTableId, tableRef }: Props) {
  const fringeBenefitsQuery = useQuery({
    queryKey: ['fringeBenefits', wageTableId],
    queryFn() {
      return getFringeBenefitsStatement({ wageTableId });
    },
    enabled: !!wageTableId,
  });

  const calculateColDefs = () => {
    const cols: ColDef<FringeBenefitStatement>[] = [
      {
        headerName: 'Employee',
        suppressSpanHeaderHeight: true,
        minWidth: 200,
        field: 'user', // only to enable sorting, but not used for sorting
        cellRenderer: (params: ValueFormatterParams<FringeBenefitStatement>) => {
          return `${params.data?.user.firstName} ${params.data?.user.lastName}`;
        },
        comparator: (a, b, nodeA, nodeB) => {
          const nameA = `${nodeA.data.user.firstName} ${nodeA.data.user.lastName}`;
          const nameB = `${nodeB.data.user.firstName} ${nodeB.data.user.lastName}`;
          return nameB.localeCompare(nameA);
        },
      },
      {
        field: 'classification.name',
        headerName: 'Classification',
        suppressSpanHeaderHeight: true,
        minWidth: 200,
      },
      {
        field: 'basePay',
        headerName: 'Base Pay',
        minWidth: 150,
        cellRenderer: (params: ValueFormatterParams) => {
          return `$${Number(params.value).toFixed(2)}`;
        },
      },
      {
        field: 'fringePay',
        headerName: 'Fringe Pay',
        minWidth: 150,
        cellRenderer: (params: ValueFormatterParams) => {
          return `$${Number(params.value).toFixed(2)}`;
        },
      },
      {
        headerName: 'Fringe Offset',
        minWidth: 150,
        cellRenderer: (params: ValueFormatterParams<FringeBenefitStatement>) => {
          if (!params.data) return;
          const employeeBenefits = params.data.employeeBenefits.map((benefit) => (
            <p key={benefit.id} className="mt-1 h-fit text-xs text-soft-400">
              {benefit.companyBenefitName}: ${benefit.fringeHourlyContribution ? benefit.fringeHourlyContribution : 0}
              /hour
            </p>
          ));
          const projectFringes = params.data.fringeBenefits.map((fringe) => (
            <p key={fringe.id} className="mt-1 h-fit text-xs text-soft-400">
              {fringe.name}: ${fringe.amount ? fringe.amount : 0}/hour
            </p>
          ));

          return (
            <span className="flex items-center gap-0.5">
              ${(Number(params.data.fringePay) - Number(params.data.cashFringe)).toFixed(2)}
              <Tooltip
                content={
                  <>
                    {employeeBenefits.length > 0 && (
                      <>
                        <h4 className="font-medium text-white-0">Employee Benefits</h4>
                        {employeeBenefits}
                      </>
                    )}
                    {projectFringes.length > 0 && (
                      <>
                        <h4 className="mt-1 font-medium text-white-0">Project Fringes</h4>
                        {projectFringes}
                      </>
                    )}
                  </>
                }
                contentClassName="!rounded-12"
              >
                <span>
                  <InfoCustomFill className="size-5 text-disabled-300" />
                </span>
              </Tooltip>
            </span>
          );
        },
        field: 'user', // only to enable sorting, but not used for sorting
        comparator: (a, b, nodeA, nodeB) => {
          const fringeOffsetA = Number(nodeA.data.fringePay) - Number(nodeA.data.cashFringe);
          const fringeOffsetB = Number(nodeB.data.fringePay) - Number(nodeB.data.cashFringe);
          return fringeOffsetB - fringeOffsetA;
        },
      },
      {
        field: 'cashFringe',
        headerComponent() {
          return (
            <span className="flex items-center gap-0.5 font-normal">
              Cash Fringe
              <Tooltip content="Cash Fringe = Fringe Pay - Fringe Offset" contentClassName="!rounded-12">
                <span>
                  <InfoCustomFill className="size-5 text-disabled-300" />
                </span>
              </Tooltip>
            </span>
          );
        },
        minWidth: 150,
        cellRenderer: (params: ValueFormatterParams) => {
          return '$' + params.value;
        },
      },
      {
        headerComponent() {
          return (
            <span className="flex items-center gap-0.5 font-normal">
              Hourly Rate
              <Tooltip content="Hourly Rate = Base Pay + Cash Fringe" contentClassName="!rounded-12">
                <span>
                  <InfoCustomFill className="size-5 text-disabled-300" />
                </span>
              </Tooltip>
            </span>
          );
        },
        minWidth: 150,
        cellRenderer: (params: ValueFormatterParams) => {
          return '$' + (Number(params.data.basePay) + Number(params.data.cashFringe)).toFixed(2);
        },
      },
    ];

    return cols;
  };

  return (
    <>
      {fringeBenefitsQuery.isPending ? (
        <div className="flex h-full items-center justify-center">
          <LoadingIndicator />
        </div>
      ) : fringeBenefitsQuery.isError ? (
        <div className="flex h-full items-center justify-center">
          <p className="text-sm text-error-base">An error occurred when fetching the fringe benefits.</p>
        </div>
      ) : (
        <UpdatedTable
          rowData={fringeBenefitsQuery.data.wageTableData}
          colDefs={calculateColDefs()}
          parentRef={tableRef}
        />
      )}
    </>
  );
}
