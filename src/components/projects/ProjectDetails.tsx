import { Project } from 'interfaces/project';
import Item from 'components/shared/DetailsItem';
import { formatLocaleUsa } from 'utils/dateHelper';
import { getWageTable } from 'services/wage-tables';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Badge } from '@/hammr-ui/components/badge';
import { CardContent, Card, CardTitle, CardHeader } from '@/hammr-ui/components/card';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ProjectChart from './ProjectChart';
import CalendarLine from '@/hammr-icons/CalendarLine';
import MapComponent from '@/hammr-ui/components/Map';
import { ONE_FOOT_TO_METERS } from '@/utils/constants';
import overtimeSettingsService from '@/services/overtime-settings';
import { useQuery } from '@tanstack/react-query';

const PrevailingWageComponent = (projectObject: Project) => {
  const [wageTable, setWageTable] = useState({ name: '', id: -1 });

  const setWageTableRequest = async () => {
    if (!projectObject.wageTableId) return;

    const _wageTable = await getWageTable(projectObject.wageTableId);

    setWageTable(_wageTable.wageTable);
  };

  useEffect(() => {
    setWageTableRequest();
  }, [projectObject.wageTableId]);

  if (!projectObject.isPrevailingWage) {
    return false;
  }

  return (
    <>
      <Item title="Wage table">
        <Link href={`/wage-tables/${wageTable.id}`}>
          <span className="cursor-pointer text-primary-base hover:underline">{wageTable?.name}</span>
        </Link>
      </Item>
      <Item title="Prevailing wage category">{projectObject.prevailingWageCategory}</Item>
      {projectObject.prevailingWageDirProjectId && (
        <Item title="DIR Project ID">{projectObject.prevailingWageDirProjectId}</Item>
      )}
      <Item title="Awarding body">{projectObject.prevailingWageAwardingBody}</Item>

      <Item title="Contractor type">
        {projectObject.prevailingWageIsSubcontractor ? 'Subcontractor' : 'Contractor'}
      </Item>

      {projectObject.prevailingWageIsSubcontractor && (
        <Item title="Prime contractor">{projectObject.prevailingWagePrimeContractor}</Item>
      )}

      <Item title="Bid Awarded Date">
        {projectObject.prevailingWageBidAwardDate ? formatLocaleUsa(projectObject.prevailingWageBidAwardDate) : null}
      </Item>
    </>
  );
};

const ProjectDetails = ({
  totalHours,
  totalCost,
  setShowEditProjectModal,
  setSelectedTab,
  ...projectObject
}: Project & {
  totalHours: number;
  totalCost: number;
  setShowEditProjectModal: (value: boolean) => void;
  setSelectedTab: (value: any) => void;
}) => {
  const GeneralDetails = () => {
    const overtimeSettingsQuery = useQuery({
      queryKey: ['overtimeSettings'],
      queryFn() {
        return overtimeSettingsService.getAll();
      },
    });

    return (
      <div className="flex flex-col gap-5 rounded-16 border border-soft-200 p-5 shadow-xs">
        <CardTitle>
          <div className="flex justify-between capitalize">
            <div>General Information</div>
            <LinkButton size="medium" style="primary" onClick={() => setShowEditProjectModal(true)}>
              Edit
            </LinkButton>
          </div>
        </CardTitle>

        <Item title="Project Name">{projectObject.name}</Item>
        <Item title="Project Number">{projectObject.projectNumber}</Item>
        <Item title="Customer Name">{projectObject.customerName}</Item>
        <Item icon={<CalendarLine height={20} width={20} />} title="Start Date">
          {projectObject.startDate ? formatLocaleUsa(projectObject.startDate) : null}
        </Item>
        <Item title="Address">{projectObject.address}</Item>
        <Item title="Notes">{projectObject.notes}</Item>
        {/* if there is no custom overtime policy, then there's no point showing this field as user can't change it anyway */}
        {overtimeSettingsQuery.data?.filter(
          (overtimeSettings) => !overtimeSettings.isDefault && overtimeSettings.isActive
        ).length > 0 && (
          <Item title="Custom Overtime Policy">
            {overtimeSettingsQuery.data?.find((s) => s.id === projectObject.overtimeSettingsId)?.name}
          </Item>
        )}
      </div>
    );
  };

  const GeofenceDetails = () => {
    return (
      <Card className="h-fit">
        <CardHeader>
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div>Geofence</div>
                <Badge color={projectObject.isGeofenced ? 'green' : 'red'} variant="lighter">
                  {projectObject.isGeofenced ? 'ON' : 'OFF'}
                </Badge>
              </div>
              <LinkButton size="medium" style="primary" onClick={() => setShowEditProjectModal(true)}>
                Edit
              </LinkButton>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="px-5 py-0">
          {projectObject.isGeofenced && (
            <div className="pb-5">
              <div className="mb-1.5 text-xs font-medium text-sub-600">Geofence location</div>
              <MapComponent
                locations={
                  projectObject.location
                    ? [
                        {
                          lng: projectObject.location[0],
                          lat: projectObject.location[1],
                          radius: projectObject.geofenceRadius
                            ? projectObject.geofenceRadius * ONE_FOOT_TO_METERS
                            : undefined,
                        },
                      ]
                    : []
                }
                className="h-80"
                zoom={14}
              />

              <div className="mt-5">
                <Item title="Radius">
                  <>{projectObject.geofenceRadius?.toLocaleString()} ft</>
                </Item>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const PrevailingWageDetails = () => {
    return (
      <Card className="h-fit">
        <CardHeader>
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div>Prevailing Wage</div>
                <Badge color={projectObject.isPrevailingWage ? 'green' : 'red'} variant="lighter">
                  {projectObject.isPrevailingWage ? 'ON' : 'OFF'}
                </Badge>
              </div>
              {projectObject.isPrevailingWage && (
                <LinkButton size="medium" style="primary" onClick={() => setShowEditProjectModal(true)}>
                  Edit
                </LinkButton>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="px-5 py-0">
          {projectObject.isPrevailingWage && (
            <div className="flex flex-col gap-5 pb-5">
              <PrevailingWageComponent {...projectObject} />
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="pb-12">
      <div className="cards-grid full-width-card mb-5">
        <ProjectChart
          setSelectedTab={setSelectedTab}
          budget={projectObject.hoursBudget}
          actual={totalHours}
          indicatorName="hours"
        />

        <ProjectChart
          setSelectedTab={setSelectedTab}
          budget={projectObject.costBudget}
          actual={totalCost}
          indicatorName="cost"
        />
      </div>
      <div className="cards-grid">
        <GeneralDetails />
        <GeofenceDetails />
        <PrevailingWageDetails />
      </div>
    </div>
  );
};

export default ProjectDetails;
