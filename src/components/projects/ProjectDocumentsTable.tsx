import { useRef, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { logError, showErrorToast } from 'utils/errorHandling';
import ProjectDocumentsView from 'components/project-documents/ProjectDocumentsView';
import { deleteProjectDocument } from 'services/project-documents';
import UploadDocumentsModal from 'components/project-documents/UploadDocumentsModal';
import { ProjectDocuments } from 'interfaces/project-documents';
import { useProjects } from 'hooks/data-fetching/useProjects';
import { AgGridReact } from '@ag-grid-community/react';
import DeleteBinLine from '@hammr-icons/DeleteBinLine';
import ConfirmDialog from '@hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@hammr-ui/components/KeyIcon';

const ProjectDocumentsTable: React.FC<{
  projectId: number;
  showUploadModal: boolean;
  setShowUploadModal: (show: boolean) => void;
}> = ({ projectId, showUploadModal, setShowUploadModal }) => {
  const { user } = useAuth();

  const projects = useProjects(user?.companyId, {
    includePlaceHolder: true,
    placeholderText: 'All projects',
    placeHolderValue: 0,
  });

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [defaultFiles, setDefaultFiles] = useState<File[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<ProjectDocuments | null>(null);

  const handleOpenDeleteModal = (document: ProjectDocuments) => {
    setSelectedDocument(document);
    setShowDeleteModal(true);
  };

  const gridRef = useRef<AgGridReact>(null);

  const refreshProjectDocuments = () => {
    gridRef.current?.api.refreshServerSide();
  };

  const handleDelete = async (document: ProjectDocuments) => {
    if (!user?.companyId) return;

    try {
      await deleteProjectDocument(document.id);
      refreshProjectDocuments();
    } catch (err) {
      logError(err);
      showErrorToast(err);
    }
  };

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <>
      <div className="relative flex flex-grow flex-col">
        <div className="relative flex flex-grow flex-col">
          <ProjectDocumentsView
            innerRef={gridRef}
            handleDelete={handleOpenDeleteModal}
            onUploadClick={(files) => {
              setShowUploadModal(true);
              setDefaultFiles(files);
            }}
            selectedProjectId={projectId}
          />
        </div>
      </div>
      <UploadDocumentsModal
        projects={projects.slice(1) || []}
        projectId={projectId}
        open={showUploadModal}
        setOpen={setShowUploadModal}
        defaultFiles={defaultFiles}
        onSuccess={() => refreshProjectDocuments()}
      />
      <ConfirmDialog
        open={showDeleteModal}
        setOpen={setShowDeleteModal}
        onConfirm={handleDelete}
        data={selectedDocument}
        icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
        confirmButtonText="Delete"
        confirmButton={{
          color: 'error',
        }}
        title="Delete document"
        subtitle={
          selectedDocument && (
            <div>
              You’re about to delete the document <span className="font-medium">{selectedDocument.name}</span>. Do you
              want to proceed?
            </div>
          )
        }
      />
    </>
  );
};

export default ProjectDocumentsTable;
