import { Dispatch, SetStateAction, useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { useToast } from 'hooks/useToast';
import { useAuth } from 'hooks/useAuth';

import { EditProject, Project } from 'interfaces/project';
import { updateProject } from 'services/projects';
import { FormV2 } from 'components/elements/Form';
import ProjectForm from './ProjectForm';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import PencilLine from '@/hammr-icons/PencilLine';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FormValues } from './CreateProjectModal';
import dayjs from 'dayjs';

interface EditProjectModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentProject: Project;
  callback?: () => void;
}

export default function EditProjectModal({ open, setOpen, currentProject, callback }: EditProjectModalProps) {
  const { user } = useAuth();
  const { addToast } = useToast();
  const form = useForm<FormValues>();

  const queryClient = useQueryClient();

  const updateProjectMutation = useMutation({
    mutationFn(formData: FormValues) {
      if (formData.isGeofenced && !formData.geofenceCoordinates) {
        throw { title: 'Geofence Address Required', message: 'Please select a geofence address.' };
      }

      const project: EditProject = {
        name: formData.name,
        projectNumber: formData.projectNumber ? formData.projectNumber : null,
        customerName: formData.customerName ? formData.customerName : undefined,
        startDate: formData.startDate ? dayjs(formData.startDate).valueOf() : undefined,
        address: formData.address ? formData.address : undefined,
        hoursBudget: formData.hoursBudget ? Number(formData.hoursBudget) : undefined,
        costBudget: formData.costBudget ? Number(formData.costBudget) : undefined,
        notes: formData.notes ? formData.notes : undefined,
        overtimeSettingsId: formData.overtimeSettingsId ? Number(formData.overtimeSettingsId) : undefined,
        foremanId: user?.uid ? Number(user.uid) : undefined,
        isGeofenced: formData.isGeofenced,
        isPrevailingWage: formData.isPrevailingWage,
      };

      if (formData.isGeofenced) {
        project.lat = formData.geofenceCoordinates?.lat;
        project.long = formData.geofenceCoordinates?.lng;
        project.geofenceRadius = formData.geofenceRadius;
      }

      if (formData.isPrevailingWage) {
        project.prevailingWageCategory = formData.prevailingWageCategory;
        project.prevailingWageState = formData.prevailingWageState;
        project.prevailingWageDirProjectId = formData.prevailingWageDirProjectId
          ? formData.prevailingWageDirProjectId
          : undefined;
        project.wageTableId = formData.wageTableId ? Number(formData.wageTableId) : undefined;
        project.prevailingWageAwardingBody = formData.prevailingWageAwardingBody;
        project.prevailingWageIsSubcontractor = formData.contractorType === 'subcontractor';
        project.prevailingWagePrimeContractor = formData.prevailingWagePrimeContractor;
        project.prevailingWagePrimeContractorAddress = formData.prevailingWagePrimeContractorAddress;
        project.prevailingWageBidAwardDate = formData.prevailingWageBidAwardDate
          ? dayjs(formData.prevailingWageBidAwardDate).valueOf()
          : undefined;
      }

      return updateProject(currentProject.id, project);
    },
    onSuccess(res) {
      addToast({
        title: 'Updated Project',
        description: (
          <>
            Successfully edited the project <strong className="font-medium">{res.project.name}</strong>.
          </>
        ),
        type: 'success',
      });

      if (callback) {
        callback();
      }
      setOpen(false);

      // Invalidate and refetch projects
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: currentProject.name,
        projectNumber: currentProject.projectNumber ?? '',
        customerName: currentProject.customerName,
        startDate: currentProject.startDate ? dayjs(currentProject.startDate).toDate() : null,
        address: currentProject.address,
        hoursBudget: currentProject.hoursBudget ? String(currentProject.hoursBudget) : undefined,
        costBudget: currentProject.costBudget ? String(currentProject.costBudget) : undefined,
        notes: currentProject.notes,
        isGeofenced: currentProject.isGeofenced,
        geofenceCoordinates: currentProject.location
          ? { lng: currentProject.location[0], lat: currentProject.location[1] }
          : null,
        geofenceRadius: currentProject.geofenceRadius ?? 1000,
        isPrevailingWage: currentProject.isPrevailingWage,
        prevailingWageCategory: currentProject.prevailingWageCategory ?? undefined,
        prevailingWageState: currentProject.prevailingWageState,
        prevailingWageDirProjectId: currentProject.prevailingWageDirProjectId,
        wageTableId: currentProject.wageTableId ? String(currentProject.wageTableId) : undefined,
        prevailingWageAwardingBody: currentProject.prevailingWageAwardingBody,
        contractorType: currentProject.prevailingWageIsSubcontractor ? 'subcontractor' : 'prime',
        prevailingWagePrimeContractor: currentProject.prevailingWagePrimeContractor,
        prevailingWagePrimeContractorAddress: currentProject.prevailingWagePrimeContractorAddress,
        prevailingWageBidAwardDate: currentProject.prevailingWageBidAwardDate
          ? dayjs(currentProject.prevailingWageBidAwardDate).toDate()
          : null,
      });
    }
  }, [open, form, currentProject]);

  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogSurface>
        <DialogHeader icon={<PencilLine />} title="Edit Project" showCloseButton />
        <FormV2
          onCancel={() => setOpen(false)}
          onSubmit={form.handleSubmit((formData) => updateProjectMutation.mutate(formData))}
          submitText="Save"
          isLoading={updateProjectMutation.isPending}
        >
          <ProjectForm form={form} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
