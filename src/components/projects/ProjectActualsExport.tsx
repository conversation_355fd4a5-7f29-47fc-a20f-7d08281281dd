import FilePdf2Line from '@/hammr-icons/FilePdf2Line';
import FileExcelLine from '@/hammr-icons/FileExcelLine';
import { MenuItem } from '@/hammr-ui/components/Menu';
import { exportTableToPDF } from '@/utils/table';
import { useCompany } from '@/hooks/useCompany';
import { Company } from '@/interfaces/company';
import { AgGridReact } from '@ag-grid-community/react';
import dayjs from 'dayjs';
import { formatMinutesToHoursWorked } from '@/utils/format';
import type { GridApi } from '@ag-grid-community/core';

interface ProjectActualsExportProps {
  gridRef: React.MutableRefObject<AgGridReact>;
  isExportingRef: React.MutableRefObject<boolean>;
  startDate?: Date;
  endDate?: Date;
  projectName: string;
}

function getTotalValue(colId, gridAPI: GridApi<unknown>) {
  if (colId === 'employee' || colId === 'crew' || colId === 'costCode') {
    return 'Total';
  }

  // For numeric columns, calculate the sum of all values
  if (
    ['cost', 'totalMinutes', 'regularMinutes', 'overtimeMinutes', 'doubleOvertimeMinutes', 'driveTimeMinutes'].includes(
      colId
    )
  ) {
    // Get all leaf nodes (non-group rows)
    const allLeafNodes = [];
    gridAPI.forEachLeafNode((node) => {
      allLeafNodes.push(node);
    });

    // Calculate sum of values for the column
    const sum = allLeafNodes.reduce((total, node) => total + (Number(node.data[colId]) || 0), 0);

    // Format the value based on column type
    if (colId === 'cost') {
      return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(sum);
    } else {
      return formatMinutesToHoursWorked(sum);
    }
  }

  return '';
}

const ProjectActualsExport = ({
  gridRef,
  isExportingRef,
  startDate,
  endDate,
  projectName,
}: ProjectActualsExportProps) => {
  const { company }: { company: Company } = useCompany();

  const handleProjectActualsPDFExport = () => {
    const title = getReportTitle();
    const fileName = getFileName('pdf');

    // Use the exportToPDF utility with only visible columns
    exportTableToPDF(gridRef.current.api, fileName, title, {
      onlyAggregatedData: true,
      footerRenderer: (gridAPI) => {
        const columnDefs = gridAPI.getColumnDefs() as any[];
        const fields = columnDefs.filter((col) => !col.hide).map((col) => col.field);

        return fields.map((column) => ({ text: getTotalValue(column, gridAPI), fontSize: 9 }));
      },
    });
  };

  const handleProjectActualsExport = (format: 'csv' | 'pdf') => {
    isExportingRef.current = true;

    if (format === 'pdf') {
      handleProjectActualsPDFExport();
    } else {
      handleProjectActualsCSVExport();
    }

    // Use setTimeout to ensure endExport runs after the export is complete
    setTimeout(() => {
      isExportingRef.current = false;
    }, 0);
  };

  const getFileName = (format: 'csv' | 'pdf') => {
    const dateRange = getDateRangeString();
    return `${projectName.replace(/\s+/g, '_')}_actuals_${dateRange}.${format}`;
  };

  const getDateRangeString = () => {
    if (startDate && endDate) {
      return `${dayjs(startDate).format('MM_DD_YYYY')}-${dayjs(endDate).format('MM_DD_YYYY')}`;
    }
    return 'all_time';
  };

  const getReportTitle = () => {
    const dateRange =
      startDate && endDate ? `${dayjs(startDate).format('MM/DD/YYYY')} - ${dayjs(endDate).format('MM/DD/YYYY')}` : '';

    return `${company.name}\n${projectName} Project Actuals\n${dateRange}`;
  };

  const handleProjectActualsCSVExport = async () => {
    const fileName = getFileName('csv');
    const title = getReportTitle();

    // Get only the visible columns from the grid
    const visibleColumns = gridRef.current.api.getAllDisplayedColumns();
    const columnKeys = visibleColumns.map((col) => col.getColId());

    const exportParams = {
      prependContent: title,
      fileName,
      columnKeys, // Only export visible columns
      allColumns: false, // Don't include hidden columns
      skipHeader: false,
      skipFooters: false,
      skipGroups: false, // Include group rows
      skipPinnedTop: false,
      skipPinnedBottom: false,
      // Only export rows that are currently rendered in the grid
      shouldRowBeSkipped: (params) => {
        // Skip rows that aren't rendered (not visible due to grouping/collapsing)
        return !params.node.displayed;
      },
      // Process cells to ensure we get the displayed value including aggregations
      processCellCallback: (params) => {
        const colId = params.column.getColId();

        // Handle footer/totals row
        if (params.node.footer) {
          // For the employee column, return 'Total'
          return getTotalValue(colId, gridRef.current.api);
        }

        // For grouped rows in the grouping column, use the group key
        if (params.node.group && colId === params.node.rowGroupColumn.getColId()) {
          // Special handling for costCode column
          if (colId === 'costCode') {
            const [costCode, number] = params.node.key.split('---');
            if (!costCode && !number) {
              return 'Unassigned';
            }
            return `${costCode} (${number})`;
          }

          // Special handling for crew column
          if (colId === 'crew') {
            const [crewName, crewLead, crewMembers] = params.node.key.split('---');
            if (!crewName && !crewLead && !crewMembers) {
              return 'Unassigned';
            }
            return `${crewName}`;
          }

          return params.node.key;
        }

        // For group rows, get the aggregated value for numeric columns
        if (
          params.node.group &&
          [
            'cost',
            'totalMinutes',
            'regularMinutes',
            'overtimeMinutes',
            'doubleOvertimeMinutes',
            'driveTimeMinutes',
          ].includes(colId)
        ) {
          // Get the children nodes of this group to sum their values
          const childrenNodes = [];
          params.node.childrenAfterFilter.forEach((node) => {
            if (node.group) {
              node.childrenAfterFilter.forEach((childNode) => {
                childrenNodes.push(childNode);
              });
            } else {
              childrenNodes.push(node);
            }
          });

          // Calculate sum of values for the column
          const sum = childrenNodes.reduce((total, node) => total + (Number(node.data[colId]) || 0), 0);

          // Format the value based on column type
          if (colId === 'cost') {
            return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(sum);
          } else {
            return formatMinutesToHoursWorked(sum);
          }
        }

        // Return the regular value for non-grouped cells
        return params.value;
      },
    };

    gridRef.current!.api.exportDataAsCsv(exportParams);
  };

  const dropdownItems = [
    {
      name: 'Export as CSV',
      icon: <FileExcelLine className="text-sub-600" />,
      callback: () => handleProjectActualsExport('csv'),
    },
    {
      name: 'Export as PDF',
      icon: <FilePdf2Line className="text-sub-600" />,
      callback: () => handleProjectActualsExport('pdf'),
    },
  ];

  return dropdownItems.map((item, index) => (
    <MenuItem key={index} title={item.name} beforeContent={item.icon} onClick={item.callback} />
  ));
};

export default ProjectActualsExport;
