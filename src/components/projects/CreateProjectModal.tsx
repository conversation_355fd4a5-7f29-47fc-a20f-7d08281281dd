import { Dispatch, SetStateAction, useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { addToast } from 'hooks/useToast';
import { useAuth } from 'hooks/useAuth';

import { CreateProject } from 'interfaces/project';
import { createProject } from 'services/projects';
import { FormV2 } from 'components/elements/Form';
import ProjectForm from './ProjectForm';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import FolderOpenLine from '@hammr-icons/FolderOpenLine';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';

export interface FormValues {
  name: string;
  projectNumber: string;
  customerName: string;
  startDate: Date | null;
  address: string;
  hoursBudget: string;
  costBudget: string;
  notes: string;
  overtimeSettingsId: string | undefined;
  isGeofenced: boolean;
  geofenceCoordinates: google.maps.LatLngLiteral | null;
  geofenceRadius: number;
  isPrevailingWage: boolean;
  prevailingWageCategory: 'FEDERAL' | 'STATE' | 'LOCAL' | undefined;
  prevailingWageState: string | null;
  prevailingWageDirProjectId: string;
  wageTableId: string;
  prevailingWageAwardingBody: string;
  contractorType: string;
  prevailingWagePrimeContractor: string;
  prevailingWagePrimeContractorAddress: string;
  prevailingWageBidAwardDate: Date | null;
}

interface CreateProjectModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

export default function CreateProjectModal({ open, setOpen }: CreateProjectModalProps) {
  const { user } = useAuth();
  const form = useForm<FormValues>({
    defaultValues: {
      isGeofenced: false,
      geofenceCoordinates: null,
      geofenceRadius: 1000, // default radius
      isPrevailingWage: false,
      prevailingWageCategory: undefined,
      prevailingWageState: null,
    },
  });

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const queryClient = useQueryClient();

  const createProjectMutation = useMutation({
    mutationFn(formData: FormValues) {
      if (formData.isGeofenced && !formData.geofenceCoordinates) {
        throw { title: 'Geofence Address Required', message: 'Please select a geofence address.' };
      }

      const project: CreateProject = {
        name: formData.name,
        projectNumber: formData.projectNumber ? formData.projectNumber : null,
        customerName: formData.customerName ? formData.customerName : undefined,
        startDate: formData.startDate ? dayjs(formData.startDate).valueOf() : undefined,
        address: formData.address ? formData.address : undefined,
        hoursBudget: formData.hoursBudget ? Number(formData.hoursBudget) : undefined,
        costBudget: formData.costBudget ? Number(formData.costBudget) : undefined,
        notes: formData.notes ? formData.notes : undefined,
        foremanId: Number(user?.uid),
        isGeofenced: formData.isGeofenced,
        isPrevailingWage: formData.isPrevailingWage,
        overtimeSettingsId: formData.overtimeSettingsId ? Number(formData.overtimeSettingsId) : undefined,
      };

      if (formData.isGeofenced) {
        project.lat = formData.geofenceCoordinates?.lat;
        project.long = formData.geofenceCoordinates?.lng;
        project.geofenceRadius = formData.geofenceRadius;
      }

      if (formData.isPrevailingWage) {
        project.prevailingWageCategory = formData.prevailingWageCategory;
        project.prevailingWageState = formData.prevailingWageState;
        project.prevailingWageDirProjectId = formData.prevailingWageDirProjectId
          ? formData.prevailingWageDirProjectId
          : undefined;
        project.wageTableId = formData.wageTableId ? Number(formData.wageTableId) : undefined;
        project.prevailingWageAwardingBody = formData.prevailingWageAwardingBody;
        project.prevailingWageIsSubcontractor = formData.contractorType === 'subcontractor';
        project.prevailingWagePrimeContractor = formData.prevailingWagePrimeContractor;
        project.prevailingWagePrimeContractorAddress = formData.prevailingWagePrimeContractorAddress;
        project.prevailingWageBidAwardDate = formData.prevailingWageBidAwardDate
          ? dayjs(formData.prevailingWageBidAwardDate).valueOf()
          : undefined;
      }

      return createProject(project);
    },
    onSuccess(res) {
      addToast({
        title: 'Created Project',
        description: (
          <>
            Successfully created the project <strong className="font-medium">{res.project.name}</strong>.
          </>
        ),
        type: 'success',
      });

      setOpen(false);

      // Invalidate and refetch projects
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });

  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogSurface className="!rounded-20">
        <DialogHeader icon={<FolderOpenLine className="text-sub-600" />} title="Create Project" showCloseButton />
        <FormV2
          onCancel={() => setOpen(false)}
          onSubmit={form.handleSubmit((formData) => createProjectMutation.mutate(formData))}
          submitText="Create"
          isLoading={createProjectMutation.isPending}
        >
          <ProjectForm form={form} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
