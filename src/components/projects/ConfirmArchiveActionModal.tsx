import { Dispatch, SetStateAction } from 'react';

import { useToast } from 'hooks/useToast';
import { Project } from 'interfaces/project';
import { updateProject } from 'services/projects';
import { logError, showErrorToast } from 'utils/errorHandling';
import { <PERSON><PERSON>, DialogFooter, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import { KeyIcon } from '@hammr-ui/components/KeyIcon';

interface ConfirmArchiveActionModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentRowData: Project;
  callback?: () => void;
}

export default function ConfirmArchiveActionModal({
  open,
  setOpen,
  currentRowData,
  callback,
}: ConfirmArchiveActionModalProps) {
  const { addToast } = useToast();

  const isArchived = currentRowData?.isArchived;

  const confirmArchiveActionHandler = async () => {
    try {
      await updateProject(currentRowData.id, {
        isArchived: !isArchived,
      });
      addToast({
        title: `Project ${!isArchived ? 'archived' : 'unarchived'}`,
        description: `We have successfully ${!isArchived ? 'archived' : 'unarchived'} the project`,
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
    } catch (err) {
      logError(err);
      showErrorToast(err, `Unable to ${!isArchived ? 'archived' : 'unarchived'} timesheet`);
    } finally {
      setOpen(false);
    }
  };

  const projectName = currentRowData?.name;
  const title = !isArchived ? 'Archive Project' : 'Unarchive Project';
  const subtitle = (
    <>
      You’re about to {!isArchived ? 'archive' : 'unarchive'} the project{' '}
      <span className="font-medium">{projectName}</span>.
      <br />
      Do you want to proceed?
    </>
  );

  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogSurface>
        <DialogHeader
          icon={<KeyIcon icon={<ArchiveLine />} />}
          title={title}
          subtitle={subtitle}
          showCloseButton
          noBorder
        />
        <DialogFooter>
          <Button type="button" fullWidth variant="outline" color="neutral" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button type="submit" fullWidth onClick={confirmArchiveActionHandler}>
            {!isArchived ? 'Archive' : 'Unarchive'}
          </Button>
        </DialogFooter>
      </DialogSurface>
    </Dialog>
  );
}
