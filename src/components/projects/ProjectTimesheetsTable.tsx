import { AgGridReact } from '@ag-grid-community/react';
import { useTimesheetListStore } from '@/components/timesheets/store';
import Timesheets from '../timesheets/Timesheets';
import { useEffect } from 'react';

interface ProjectTimesheetsTableProps {
  projectId: number;
  isExportingRef: React.RefObject<boolean>;
  showTimesheetModal: boolean;
  setShowTimesheetModal: (open: boolean) => void;
  gridRef: React.RefObject<AgGridReact>;
}

export const ProjectTimesheetsTable = ({
  projectId,
  isExportingRef,
  showTimesheetModal,
  setShowTimesheetModal,
  gridRef,
}: ProjectTimesheetsTableProps) => {
  // Set project filter on mount

  useEffect(() => {
    useTimesheetListStore.setState((state) => ({
      ...state,
      selectedProjectIds: [projectId],
      selectedEmployeeIds: [],
    }));
  }, [projectId]);

  return (
    <div className="relative flex flex-grow">
      <Timesheets
        isExportingRef={isExportingRef}
        showTimesheetModal={showTimesheetModal}
        setShowTimesheetModal={setShowTimesheetModal}
        gridRef={gridRef}
        selectedProjectId={projectId}
      />
    </div>
  );
};
