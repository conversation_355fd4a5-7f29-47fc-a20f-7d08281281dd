import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import { ChartContainer } from '@/hammr-ui/components/chart';
import { Label, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { formatNumberWithoutDecimals, formatUSDWithoutDecimals } from '@/utils/format';
import { cn } from '@/hammr-ui/lib/cn';

const ProjectChart = ({
  budget,
  actual,
  indicatorName,
  setSelectedTab,
  legendTextAlign,
}: {
  budget?: number;
  actual: number;
  indicatorName: string;
  setSelectedTab?: (value: string) => void;
  legendTextAlign?: 'left' | 'right';
}) => {
  const percentage = budget ? Math.round((actual / budget) * 100) : 0;

  const chartColor =
    percentage <= 50
      ? 'rgb(var(--raw-success-base))' // green
      : percentage < 100
        ? 'rgb(var(--raw-warning-base))' // orange
        : 'rgb(var(--raw-error-base))'; // red

  const invChart = percentage < 100 ? 100 - percentage : 0;
  const chartData = [{ chart: percentage, invChart }];
  const chartConfig = {
    chart: {
      label: '',
      color: chartColor,
    },
    invChart: {
      label: '',
      color: 'rgb(var(--raw-weak-50))',
    },
  };

  if (!budget) {
    return (
      <Card className="h-48">
        <CardHeader className="p-4">
          <CardTitle>
            <div className="flex justify-between capitalize">
              <div>{indicatorName} Actual</div>
              {setSelectedTab && (
                <LinkButton size="medium" style="primary" onClick={() => setSelectedTab('project-actuals')}>
                  See Details
                </LinkButton>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-col p-4 pt-1 text-center">
          <div className="mt-6 text-3xl font-medium">
            {indicatorName === 'cost'
              ? formatUSDWithoutDecimals.format(actual)
              : formatNumberWithoutDecimals.format(actual)}
          </div>
          <div className="mt-1.5 pb-7 text-xs font-medium text-sub-600">TOTAL {indicatorName.toUpperCase()}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-48">
      <CardHeader>
        <CardTitle>
          <div className="relative flex justify-between">
            <div className="capitalize">Total Project {indicatorName}</div>
            <div className="absolute right-0 z-10 grid grid-cols-2 grid-rows-3 items-center gap-2">
              <span className={cn(`text-sm font-medium`, { 'text-right': legendTextAlign === 'right' })}>
                {indicatorName === 'cost' ? formatUSDWithoutDecimals.format(actual) : actual}
              </span>{' '}
              <span className="text-xs font-normal capitalize text-sub-600">Actual</span>
              <span className={cn(`text-sm font-medium`, { 'text-right': legendTextAlign === 'right' })}>
                {indicatorName === 'cost' ? formatUSDWithoutDecimals.format(budget) : budget}
              </span>{' '}
              <span className="text-xs font-normal capitalize text-sub-600">Budget</span>
              {setSelectedTab && (
                <LinkButton
                  className="col-span-2 text-right"
                  size="medium"
                  style="primary"
                  onClick={() => setSelectedTab('project-actuals')}
                >
                  See Details
                </LinkButton>
              )}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="max-h-[110px] flex-col overflow-hidden pt-1 text-center">
        <ChartContainer config={chartConfig} className="aspect-square w-full max-w-[208px]">
          <RadialBarChart data={chartData} endAngle={0} startAngle={180} innerRadius={90} outerRadius={140}>
            <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle">
                        <tspan x={viewBox.cx} y={(viewBox.cy || 0) - 16} className="fill-foreground text-2xl font-bold">
                          {percentage}%
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </PolarRadiusAxis>
            <RadialBar
              dataKey="chart"
              stackId="a"
              cornerRadius={5}
              fill="var(--color-chart)"
              className="stroke-transparent stroke-2"
            />
            <RadialBar
              dataKey="invChart"
              fill="var(--color-invChart)"
              stackId="a"
              cornerRadius={5}
              className="stroke-transparent stroke-2"
            />
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

export default ProjectChart;
