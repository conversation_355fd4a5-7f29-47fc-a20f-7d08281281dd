import { UserTimesheet } from '@/interfaces/timesheet';
import { UpdatedTable } from '../shared/UpdatedTable';
import { ColDef } from '@ag-grid-community/core';
import { MutableRefObject } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { HammrUserCrew } from '@/interfaces/user';
import { customCellValueFormatter } from '@/utils/table';
import UserLine from '@/hammr-icons/UserLine';
import { formatMinutesToHoursWorked } from '@/utils/format';
import { useCompany } from '@/hooks/useCompany';

interface RowData {
  employee: string;
  cost: number;
  totalMinutes: number;
  regularMinutes: number;
  overtimeMinutes: number;
  doubleOvertimeMinutes: number;
  driveTimeMinutes: number;
  costCode: string;
  crew: string;
}

const ProjectActualsTable = ({
  timesheets,
  gridRef,
  groupBy,
  isLoading,
  onGridReady,
}: {
  timesheets: (UserTimesheet & { user: Partial<HammrUserCrew> })[];
  gridRef: MutableRefObject<AgGridReact<RowData>>;
  groupBy: string;
  isLoading: boolean;
  onGridReady: (params: any) => void;
}) => {
  const { company } = useCompany();

  const colDefs: ColDef<RowData>[] = [
    {
      field: 'employee',
      headerName: 'Employee',
      width: 250,
      hide: groupBy !== 'employee',
      cellRenderer: (params) => {
        if (params.node.footer) {
          return <span className="font-medium">Total</span>;
        } else if (params.node.group) {
          return params.node.key;
        } else {
          return params.value;
        }
      },
    },
    {
      field: 'costCode',
      headerName: 'Cost Code',
      width: 250,
      hide: groupBy !== 'costCode',
      cellRenderer: (params) => {
        if (params.node.footer) {
          return <span className="font-medium">Total</span>;
        }

        if (params.node.group) {
          const [costCode, number] = params.node.key.split('---');
          if (!costCode && !number) {
            return 'Unassigned';
          }
          return (
            <div className="flex h-full flex-col justify-center">
              <div className="h-5 text-sm font-medium">{costCode}</div>
              <div className="h-5 text-xs text-sub-600">{number}</div>
            </div>
          );
        } else {
          return params.value;
        }
      },
      valueFormatter: (params) => {
        if (params.node.group && params.node.rowGroupColumn?.getColId() === 'costCode') {
          const [costCode, number] = params.node.key.split('---');
          if (!costCode && !number) {
            return 'Unassigned';
          }
          return `${costCode} (${number})`;
        }
        return params.value;
      },
    },
    {
      field: 'crew',
      headerName: 'Crews',
      minWidth: 250,
      width: 250,
      hide: groupBy !== 'crew',
      cellRenderer: (params) => {
        if (params.node.footer) {
          return <span className="font-medium">Total</span>;
        }

        if (params.node.group) {
          const [crewName, crewLead, crewMembers] = params.node.key.split('---');
          if (!crewName && !crewLead && !crewMembers) {
            return 'Unassigned';
          }

          return (
            <div className="flex h-full flex-col justify-center">
              <div className="h-5 text-sm font-medium">{crewName}</div>
              <div className="-ml-1 mt-1 flex h-5 text-xs text-sub-600">
                <span>
                  <UserLine height={14} width={14} />
                </span>
                <span className="ml-0.5">{crewMembers}</span>
                <span className="ml-0.5"> ∙ Crew Lead: {crewLead}</span>
              </div>
            </div>
          );
        } else {
          return params.value;
        }
      },
      valueFormatter: (params) => {
        if (params.node.group && params.node.rowGroupColumn?.getColId() === 'crew') {
          const [crewName, crewLead, crewMembers] = params.node.key.split('---');
          if (!crewName && !crewLead && !crewMembers) {
            return 'Unassigned';
          }
          return `${crewName}`;
        }
        return params.value;
      },
    },
    {
      field: 'cost',
      headerName: 'Cost',
      width: 150,
      valueFormatter: (params) => customCellValueFormatter({ params, type: 'currency' }),
      cellRenderer: (params) => {
        // Check if this is a grand total row
        if (params.node.footer) {
          return <span className="font-medium">{params.valueFormatted}</span>;
        }
        return params.valueFormatted;
      },
    },
    {
      field: 'totalMinutes',
      headerName: 'Total Hours',
      width: 150,
      valueFormatter: (params) => {
        return formatMinutesToHoursWorked(
          customCellValueFormatter({
            params,
            type: 'billableTime',
            skipFormatting: true,
          })
        );
      },
      cellRenderer: (params) => {
        // Check if this is a grand total row
        if (params.node.footer) {
          return <span className="font-medium">{params.valueFormatted}</span>;
        }
        return params.valueFormatted;
      },
    },
    {
      field: 'regularMinutes',
      headerName: 'Regular Hours',
      width: 150,
      valueFormatter: (params) =>
        formatMinutesToHoursWorked(
          customCellValueFormatter({
            params,
            type: 'billableTime',
            skipFormatting: true,
          })
        ),
      cellRenderer: (params) => {
        // Check if this is a grand total row
        if (params.node.footer) {
          return <span className="font-medium">{params.valueFormatted}</span>;
        }
        return params.valueFormatted;
      },
    },
    {
      field: 'overtimeMinutes',
      headerName: 'Overtime Hours',
      width: 150,
      valueFormatter: (params) =>
        formatMinutesToHoursWorked(
          customCellValueFormatter({
            params,
            type: 'billableTime',
            skipFormatting: true,
          })
        ),
      cellRenderer: (params) => {
        // Check if this is a grand total row
        if (params.node.footer) {
          return <span className="font-medium">{params.valueFormatted}</span>;
        }
        return params.valueFormatted;
      },
    },
    {
      field: 'doubleOvertimeMinutes',
      headerName: 'Double Overtime Hours',
      width: 150,
      valueFormatter: (params) =>
        formatMinutesToHoursWorked(
          customCellValueFormatter({
            params,
            type: 'billableTime',
            skipFormatting: true,
          })
        ),
      cellRenderer: (params) => {
        // Check if this is a grand total row
        if (params.node.footer) {
          return <span className="font-medium">{params.valueFormatted}</span>;
        }
        return params.valueFormatted;
      },
    },
    {
      field: 'driveTimeMinutes',
      hide: !company?.timeTrackingSettings.isDriveTimeEnabled,
      headerName: 'Drive Time Hours',
      width: 150,
      valueFormatter: (params) =>
        formatMinutesToHoursWorked(
          customCellValueFormatter({
            params,
            type: 'billableTime',
            skipFormatting: true,
          })
        ),
      cellRenderer: (params) => {
        // Check if this is a grand total row
        if (params.node.footer) {
          return <span className="font-medium">{params.valueFormatted}</span>;
        }
        return params.valueFormatted;
      },
    },
  ];

  const rowData: RowData[] = [];

  timesheets?.forEach((timesheet) => {
    const rawCrews = timesheet.user.crewMemberUser;
    const crews = rawCrews.map((crew) => {
      return [
        crew.crew.name,
        crew.crew.crewLeadUser.firstName + ' ' + crew.crew.crewLeadUser.lastName,
        crew.crew.crewMembers.length,
      ];
    });

    rowData.push({
      employee: timesheet.user.firstName + ' ' + timesheet.user.lastName,
      cost: timesheet.totalWages,
      totalMinutes: timesheet.totalMinutes,
      regularMinutes: timesheet.regularMinutes,
      overtimeMinutes: timesheet.overtimeMinutes,
      doubleOvertimeMinutes: timesheet.doubleOvertimeMinutes,
      driveTimeMinutes: timesheet.driveTimeMinutes,
      costCode: timesheet.costCode ? timesheet.costCode?.name + '---' + timesheet.costCode?.number : '',
      crew: crews.length > 0 ? crews[0].join('---') : '',
    });

    if (groupBy === 'crew' && timesheet.user.crewMemberUser.length > 1) {
      crews.slice(1).forEach((crew, index) => {
        rowData.push({
          employee: timesheet.user.firstName + ' ' + timesheet.user.lastName,
          cost: timesheet.totalWages,
          totalMinutes: timesheet.totalMinutes,
          regularMinutes: timesheet.regularMinutes,
          overtimeMinutes: timesheet.overtimeMinutes,
          doubleOvertimeMinutes: timesheet.doubleOvertimeMinutes,
          driveTimeMinutes: timesheet.driveTimeMinutes,
          costCode: timesheet.costCode ? timesheet.costCode?.name + '---' + timesheet.costCode?.number : '',
          crew: crews[index + 1].join('---'),
        });
      });
    }
  });

  return (
    <UpdatedTable
      colDefs={colDefs}
      rowData={rowData}
      parentRef={gridRef}
      isLoading={isLoading}
      defaultColDef={{
        sortable: false,
        rowDrag: false,
      }}
      gridOptions={{
        groupDisplayType: 'custom',
        groupDefaultExpanded: 0,
        grandTotalRow: 'bottom',
      }}
      getRowHeight={() => (groupBy === 'employee' ? 52 : 64)}
      onGridReady={onGridReady}
      disableCustomRendering
    />
  );
};

export default ProjectActualsTable;
