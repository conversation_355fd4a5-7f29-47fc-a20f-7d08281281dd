import classNames from 'classnames';
import GoogleMapsPlacesAutocomplete, { PlaceType } from 'components/shared/GoogleMapsPlacesAutocomplete';

import { range } from 'utils/collectionHelpers';
import { DEFAULT_MAP_ZOOM } from './constants';
import { RadiusOption } from 'interfaces/project';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { Label } from '@/hammr-ui/components/label';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { Switch } from '@/hammr-ui/components/Switch';
import MapComponent from '@/hammr-ui/components/Map';
import { ONE_FOOT_TO_METERS } from '@/utils/constants';
import { FormValues } from './CreateProjectModal';
import { Controller, UseFormReturn } from 'react-hook-form';

const radiusRange = range(200, 2000, 200); // collection of integers (unit: ft)
const radiusRangeMapped: RadiusOption[] = radiusRange
  .map((radius, idx) => {
    return {
      id: idx,
      value: radius,
      label: `${radius} ft`,
    };
  })
  .concat([{ id: radiusRange.length, value: 5000, label: '5000 ft' }]);

interface Props {
  form: UseFormReturn<FormValues>;
}

export default function GeofenceComponent({ form }: Props) {
  async function setGeofenceCoordinate(geofencePlace: PlaceType | null) {
    if (geofencePlace && geofencePlace.place_id) {
      const place = new google.maps.places.Place({ id: geofencePlace.place_id });

      const fields = await place.fetchFields({ fields: ['location'] });

      if (fields.place.location) {
        form.setValue('geofenceCoordinates', { lat: fields.place.location?.lat(), lng: fields.place.location?.lng() });
      }
    }
  }

  const isGeofenced = form.watch('isGeofenced');

  return (
    <>
      <FormItem className="flex-row items-center justify-between">
        <FormLabel
          tooltip="When geofence is enabled, employees can only clock in to the project when they are physically within the designated area."
          htmlFor="isGeofenced"
        >
          Geofence
        </FormLabel>
        <FormControl>
          <Controller
            control={form.control}
            name="isGeofenced"
            render={({ field }) => <Switch checked={field.value} onCheckedChange={field.onChange} id="isGeofenced" />}
          />
        </FormControl>
      </FormItem>

      {isGeofenced && (
        <FormItem>
          <FormLabel>Geofence Location</FormLabel>
          <FormControl>
            <GoogleMapsPlacesAutocomplete callback={setGeofenceCoordinate} />
          </FormControl>
        </FormItem>
      )}

      <div
        className={classNames('flex flex-col items-center justify-center', {
          hidden: !isGeofenced,
        })}
      >
        <Controller
          control={form.control}
          name="geofenceCoordinates"
          render={({ field }) => (
            <MapComponent
              locations={
                field.value
                  ? [
                      {
                        lat: field.value.lat,
                        lng: field.value.lng,
                        radius: form.watch('geofenceRadius') * ONE_FOOT_TO_METERS,
                      },
                    ]
                  : []
              }
              zoom={field.value === null ? DEFAULT_MAP_ZOOM : 14} // create modal zoom = default, edit modal zoom = 14
              onClick={(event) => {
                if (!event.latLng) return;
                field.onChange({
                  lat: event.latLng.lat(),
                  lng: event.latLng.lng(),
                });
              }}
            />
          )}
        />
        <p className="mt-3 text-xs text-sub-600">Click on the map to adjust the exact pin location</p>
      </div>

      {isGeofenced ? (
        <FormItem>
          <Label>Radius</Label>
          <FormControl>
            <Controller
              control={form.control}
              name="geofenceRadius"
              render={({ field }) => (
                <DropdownPicker
                  value={field.value}
                  onChange={(value) => field.onChange(value)}
                  items={radiusRangeMapped}
                />
              )}
            />
          </FormControl>
        </FormItem>
      ) : null}
    </>
  );
}
