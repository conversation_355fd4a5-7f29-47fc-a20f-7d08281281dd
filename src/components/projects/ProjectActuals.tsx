import { Project } from 'interfaces/project';
import ProjectChart from './ProjectChart';
import ProjectActualsTable from './ProjectActualsTable';
import { getOneProjectWithActuals } from '@/services/projects';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { UserTimesheet } from '@/interfaces/timesheet';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import * as React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { AgGridReact } from '@ag-grid-community/react';
import { HammrUserCrew } from '@/interfaces/user';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
  <PERSON>Axi<PERSON>,
  YAxis,
} from 'recharts';
import { DatePeriodSelectorImproved } from '../timesheets/DatePeriodSelectorImproved';
import Button from '@/hammr-ui/components/button';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { Menu, MenuContent, MenuTrigger } from '@/hammr-ui/components/Menu';
import { cn } from '@/hammr-ui/lib/cn';
import ProjectActualsExport from './ProjectActualsExport';

const TotalHoursBreakdown = ({ timesheets }: { timesheets?: (UserTimesheet & { user: Partial<HammrUserCrew> })[] }) => {
  // Calculate total hours
  const hoursData = timesheets?.reduce(
    (acc, timesheet) => {
      acc.regularMinutes += timesheet.regularMinutes || 0;
      acc.overtimeMinutes += timesheet.overtimeMinutes || 0;
      acc.doubleOvertimeMinutes += timesheet.doubleOvertimeMinutes || 0;
      acc.driveTimeMinutes += timesheet.driveTimeMinutes || 0;
      return acc;
    },
    { regularMinutes: 0, overtimeMinutes: 0, doubleOvertimeMinutes: 0, driveTimeMinutes: 0 }
  ) || { regularMinutes: 0, overtimeMinutes: 0, doubleOvertimeMinutes: 0, driveTimeMinutes: 0 };

  // Convert minutes to hours
  const regularHours = Math.round(hoursData.regularMinutes / 60);
  const overtimeHours = Math.round(hoursData.overtimeMinutes / 60);
  const doubleOvertimeHours = Math.round(hoursData.doubleOvertimeMinutes / 60);

  // Prepare chart data - only include categories that have hours
  const chartData = [];

  if (regularHours > 0) {
    chartData.push({ name: 'Regular', hours: regularHours });
  }

  if (overtimeHours > 0) {
    chartData.push({ name: 'Overtime', hours: overtimeHours });
  }

  if (doubleOvertimeHours > 0) {
    chartData.push({ name: 'Double OT', hours: doubleOvertimeHours });
  }

  return (
    <Card className="h-48">
      <CardHeader className="p-4">
        <CardTitle>Total Hours Breakdown</CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <ResponsiveContainer width="100%" height={150}>
          <BarChart data={chartData} layout="vertical">
            <CartesianGrid strokeDasharray="3 3" horizontal={false} className="stroke-neutral-alpha-24" />
            <XAxis
              type="number"
              axisLine={false}
              tickLine={false}
              tick={{ fill: 'rgb(var(--raw-sub-600))' }}
              style={{ fontSize: '0.75rem' }}
            />
            <YAxis
              type="category"
              dataKey="name"
              width={80}
              axisLine={false}
              tickLine={false}
              tick={{ fill: 'rgb(var(--raw-sub-600))' }}
              style={{ fontSize: '0.75rem' }}
            />
            <RechartsTooltip
              cursor={false}
              formatter={(value: number, name: string) => {
                if (name === 'spacer') return [''];
                return [`${value} hours`];
              }}
              content={() => (
                <div className="min-w-44 rounded-12 bg-strong-950 p-3 shadow-sm">
                  <h4 className="text-sm font-medium text-white-0">Total Hours Breakdown</h4>
                  {regularHours > 0 && (
                    <p className="mt-1 flex h-fit gap-1 text-xs text-soft-400">
                      <span className="flex size-4 items-center justify-center">
                        <span className="size-3 rounded-full border-2 border-white-0 bg-blue-300"></span>
                      </span>
                      Regular: {regularHours} hours
                    </p>
                  )}
                  {overtimeHours > 0 && (
                    <p className="mt-1 flex h-fit gap-1 text-xs text-soft-400">
                      <span className="flex size-4 items-center justify-center">
                        <span className="size-3 rounded-full border-2 border-white-0 bg-blue-500"></span>
                      </span>
                      Overtime: {overtimeHours} hours
                    </p>
                  )}
                  {doubleOvertimeHours > 0 && (
                    <p className="mt-1 flex h-fit gap-1 text-xs text-soft-400">
                      <span className="flex size-4 items-center justify-center">
                        <span className="size-3 rounded-full border-2 border-white-0 bg-blue-800"></span>
                      </span>
                      Double Overtime: {doubleOvertimeHours} hours
                    </p>
                  )}
                </div>
              )}
            />
            <Bar dataKey="hours" radius={[0, 4, 4, 0]} maxBarSize={18}>
              {chartData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    entry.name === 'Regular'
                      ? 'rgb(var(--raw-color-blue-300))'
                      : entry.name === 'Overtime'
                        ? 'rgb(var(--raw-color-blue-500))'
                        : 'rgb(var(--raw-color-blue-800))'
                  }
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

const ProjectActuals = ({
  totalHours,
  totalCost,
  ...projectObject
}: Project & {
  totalHours: number;
  totalCost: number;
}) => {
  const { user } = useAuth();
  const project_id = projectObject.id;
  const [groupBy, setGroupBy] = useState(undefined);
  const gridRef = useRef<AgGridReact>(null);
  const isExportingRef = useRef(false);
  const [isGridReady, setIsGridReady] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>();
  const [endDate, setEndDate] = useState<Date | null>();

  const totalProjectActuals = useQuery<{
    project: Project;
    timesheets: (UserTimesheet & { user: Partial<HammrUserCrew> })[];
  }>({
    queryKey: ['project-actuals', project_id],
    queryFn: async () =>
      getOneProjectWithActuals(project_id, {
        includeIsArchived: 'true',
      }),
    enabled: !!user?.companyId && !!project_id,
  });

  const tableProjectActuals = useQuery<{
    project: Project;
    timesheets: (UserTimesheet & { user: Partial<HammrUserCrew> })[];
  }>({
    queryKey: ['project-actuals', project_id, startDate?.valueOf(), endDate?.valueOf()],
    queryFn: async () =>
      getOneProjectWithActuals(project_id, {
        includeIsArchived: 'true',
        from: startDate?.valueOf(),
        to: endDate?.valueOf(),
      }),
    enabled: !!user?.companyId && !!project_id,
  });

  useEffect(() => {
    // initial sort by setting - this is to make sure that the side-effects trigger
    if (!groupBy && isGridReady) {
      setGroupBy('employee');
    }
  }, [groupBy, isGridReady]);

  useEffect(() => {
    if (!groupBy || !isGridReady) {
      return;
    }

    gridRef.current.api?.setRowGroupColumns([groupBy]);
    const columnsThatShouldBeHidden = ['employee', 'crew', 'costCode'].filter((column) => column !== groupBy);
    gridRef.current.api?.setColumnsVisible(columnsThatShouldBeHidden, false);
    gridRef.current.api?.setColumnsVisible([groupBy], true);
  }, [groupBy, isGridReady]);

  const allTimeStartDate = useMemo(() => {
    return tableProjectActuals.data?.timesheets?.length
      ? new Date(Math.min(...tableProjectActuals.data.timesheets.map((ts) => new Date(ts.clockIn).getTime())))
      : undefined;
  }, [tableProjectActuals.data?.timesheets]);

  const allTimeEndDate = useMemo(
    () => (allTimeStartDate > new Date() ? allTimeStartDate : new Date()),
    [allTimeStartDate]
  );

  return (
    <div className="pb-12">
      {totalProjectActuals.isPending && (
        <div className="flex h-full items-center justify-center">
          <LoadingIndicator />
        </div>
      )}

      {!totalProjectActuals.isPending && (
        <>
          <div className="cards-grid col-span-3">
            <ProjectChart
              budget={projectObject.hoursBudget}
              actual={totalHours}
              indicatorName="hours"
              legendTextAlign="right"
            />
            <ProjectChart budget={projectObject.costBudget} actual={totalCost} indicatorName="cost" />
            <TotalHoursBreakdown timesheets={totalProjectActuals.data?.timesheets} />
          </div>
          <div className="mt-6 w-full">
            <div className="mb-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <DatePeriodSelectorImproved
                  onDateChange={(startDate, endDate) => {
                    setStartDate(startDate);
                    setEndDate(endDate);
                  }}
                  showAllTimeOption
                  allTimeStartDate={allTimeStartDate}
                  allTimeEndDate={allTimeEndDate}
                />
                <DropdownPicker
                  items={[
                    { value: 'employee', label: 'Employees' },
                    { value: 'crew', label: 'Crews' },
                    { value: 'costCode', label: 'Cost Codes' },
                  ]}
                  value={groupBy}
                  onChange={(value: string) => setGroupBy(value)}
                  className="w-36"
                  size="small"
                />
              </div>
              <Menu>
                <MenuTrigger asChild>
                  <Button variant="outline" color="neutral" afterContent={<ArrowDownSLine />} size="small">
                    Export
                  </Button>
                </MenuTrigger>
                <MenuContent className={cn('min-w-48')} align="end">
                  <ProjectActualsExport
                    gridRef={gridRef}
                    isExportingRef={isExportingRef}
                    startDate={startDate}
                    endDate={endDate}
                    projectName={projectObject.name}
                  />
                </MenuContent>
              </Menu>
            </div>

            <ProjectActualsTable
              onGridReady={() => setIsGridReady(true)}
              groupBy={groupBy}
              isLoading={tableProjectActuals.isLoading || startDate === null}
              timesheets={tableProjectActuals.data?.timesheets}
              gridRef={gridRef}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ProjectActuals;
