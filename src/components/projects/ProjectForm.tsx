import STATE_LIST from 'data/statesList.json';
import GeofenceComponent from './Geofence';
import { FormControl, FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Switch } from '@/hammr-ui/components/Switch';
import AddLine from '@hammr-icons/AddLine';
import { Combobox } from '@/hammr-ui/components/combobox';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { WageTable } from '@/interfaces/wage-table';
import { TextField } from '../elements/form/TextField';
import { ControlledTextarea } from '../elements/form/ControlledTextarea';
import { FormValues } from './CreateProjectModal';
import { NumberFieldV2 } from '../elements/form/NumberFieldV2';
import ControlledDateInput from '../elements/form/ControlledDateInput';
import ControlledSelect from '../elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';
import ControlledCombobox from '../elements/form/ControlledCombobox';
import overtimeSettingsService from '@/services/overtime-settings';

interface Props {
  form: UseFormReturn<FormValues>;
}

export default function ProjectForm({ form }: Props) {
  return (
    <div className="flex flex-col gap-5">
      <FormBody form={form} />

      <hr className="border-soft-200" />

      <GeofenceComponent form={form} />

      <hr className="border-soft-200" />

      <PrevailingWageComponent form={form} />
    </div>
  );
}

function FormBody({ form }: Props) {
  const overtimeSettingsQuery = useQuery({
    queryKey: ['overtimeSettings'],
    queryFn() {
      return overtimeSettingsService.getAll();
    },
  });

  return (
    <div className="flex flex-col gap-5">
      <TextField
        required
        label="Project Name"
        placeholder="Enter project name"
        control={form.control}
        name="name"
        rules={{ required: 'Please provide a Project Name' }}
      />

      <TextField
        label="Project Number"
        placeholder="Enter project number"
        control={form.control}
        name="projectNumber"
      />

      <TextField label="Customer Name" placeholder="Enter customer name" control={form.control} name="customerName" />

      <ControlledDateInput label="Project Start Date" control={form.control} name="startDate" />

      <TextField label="Address" placeholder="Enter address" control={form.control} name="address" />

      <NumberFieldV2
        label="Hours Budget"
        placeholder="Enter number"
        control={form.control}
        name="hoursBudget"
        afterContent="hours"
      />

      <TextField
        name="costBudget"
        label="Cost Budget"
        control={form.control}
        placeholder="Enter amount"
        beforeContent="$"
      />

      <ControlledTextarea
        label="Notes"
        name="notes"
        control={form.control}
        placeholder="Enter notes"
        maxLength={2000}
      />

      {overtimeSettingsQuery.data?.filter(
        (overtimeSettings) => !overtimeSettings.isDefault && overtimeSettings.isActive
      ).length > 0 && (
        <ControlledSelect label="Custom Overtime Policy" name="overtimeSettingsId" control={form.control}>
          {overtimeSettingsQuery.data
            ?.filter((overtimeSettings) => !overtimeSettings.isDefault && overtimeSettings.isActive)
            .sort((a, b) => a.name.localeCompare(b.name))
            .map((overtimeSettings) => (
              <SelectItem key={overtimeSettings.id} value={String(overtimeSettings.id)}>
                {overtimeSettings.name}
              </SelectItem>
            ))}
        </ControlledSelect>
      )}
    </div>
  );
}

function PrevailingWageComponent({ form }: Props) {
  const CATEGORY_OPTIONS = [
    { label: 'Federal Davis Bacon', value: 'FEDERAL' },
    { label: 'State Prevailing Wage', value: 'STATE' },
    { label: 'Local', value: 'LOCAL' },
  ];

  const isPrevailingWage = form.watch('isPrevailingWage');
  const selectedCategory = form.watch('prevailingWageCategory');
  const selectedState = form.watch('prevailingWageState');
  const contractorType = form.watch('contractorType');

  const hidePrimeContractor = contractorType === 'prime';

  return (
    <div className="flex flex-col gap-5">
      <FormItem className="flex-row items-center justify-between">
        <FormLabel
          tooltip="If enabled, you will be able to set up project-specific pay rates and fringes, and also generate certified payroll report for this project."
          htmlFor="isPrevailingWage"
        >
          Prevailing wage/Certified payroll
        </FormLabel>
        <FormControl>
          <Controller
            control={form.control}
            name="isPrevailingWage"
            render={({ field }) => (
              <Switch
                checked={field.value}
                id="isPrevailingWage"
                onCheckedChange={(checked) => field.onChange(checked)}
              />
            )}
          />
        </FormControl>
      </FormItem>

      {isPrevailingWage ? (
        <div className="flex flex-col gap-5">
          <ControlledSelect
            required
            label="Category"
            name="prevailingWageCategory"
            control={form.control}
            rules={{ required: 'Please select a category' }}
          >
            {CATEGORY_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </ControlledSelect>

          {selectedCategory === 'STATE' && (
            <ControlledCombobox
              label="State"
              name="prevailingWageState"
              control={form.control}
              rules={{ required: 'Please select a state' }}
              error={form.formState.errors['prevailingWageState']?.message}
              items={STATE_LIST.map((option) => ({
                label: option.name,
                value: option.abbreviation,
              }))}
            />
          )}

          {selectedState === 'CA' && (
            <TextField
              label="DIR Project ID"
              placeholder="DIR Project ID (optional)"
              control={form.control}
              name="prevailingWageDirProjectId"
            />
          )}

          <WageComponent form={form} />

          <TextField
            label="Awarding Body"
            placeholder="Awarding Body (optional)"
            control={form.control}
            name="prevailingWageAwardingBody"
          />

          <ControlledSelect
            required
            label="Contractor Type"
            name="contractorType"
            control={form.control}
            rules={{ required: 'Please select a contractor type' }}
            tooltip="Specify whether your business is a contractor or subcontractor for this project. This selection ensures accurate categorization in the certified payroll report."
          >
            <SelectItem value="prime">Prime Contractor</SelectItem>
            <SelectItem value="subcontractor">Subcontractor</SelectItem>
          </ControlledSelect>

          {!hidePrimeContractor && (
            <>
              <TextField
                label="Prime Contractor Name"
                placeholder="Prime Contractor Name (optional)"
                control={form.control}
                name="prevailingWagePrimeContractor"
              />
              <TextField
                label="Prime Contractor Address"
                placeholder="Prime Contractor Address (optional)"
                control={form.control}
                name="prevailingWagePrimeContractorAddress"
              />
            </>
          )}

          <ControlledDateInput label="Bid Awarded Date" control={form.control} name="prevailingWageBidAwardDate" />
        </div>
      ) : null}
    </div>
  );
}

function WageComponent({ form }: Props) {
  const currentWageTableId = form.watch('wageTableId');

  const wageTables = useQuery({
    queryKey: ['wageTables'],
    queryFn: () =>
      apiRequest<{ wageTables: WageTable[] }>('wage-tables', { urlParams: { includeArchived: true } }).then(
        (response) => response.wageTables
      ),
  });

  return (
    <div className="">
      <Controller
        name="wageTableId"
        control={form.control}
        rules={{ required: 'Please select a wage table' }}
        render={({ field }) => (
          <FormItem required error={!!form.formState.errors['wageTableId']}>
            <FormLabel
              tooltip={
                !currentWageTableId
                  ? 'Wage Tables are where you can setup classifications, wage rates, fringes etc. for prevailing wage projects'
                  : null
              }
            >
              Wage Table
            </FormLabel>
            <FormControl>
              <Combobox
                value={field.value?.toString()}
                onChange={(value) => field.onChange(value)}
                searchable={false}
                itemClassName="[&>span]:w-full"
                items={[
                  ...(wageTables.data ?? [])
                    .filter((item) => !item.isArchived || item.id === Number(currentWageTableId))
                    .map((option) => ({
                      label: (
                        <>
                          {option.name}
                          {option.isArchived ? <span className="opacity-60"> (archived)</span> : undefined}
                        </>
                      ),
                      value: option.id.toString(),
                      disabled: option.isArchived,
                    })),
                  {
                    selectedLabel: 'Create wage table',
                    value: '-1',
                    label: (
                      <a
                        href="/wage-tables?create=true"
                        target="_blank"
                        className="flex items-center gap-2 text-primary-base hover:underline"
                        onClick={(event) => event.stopPropagation()}
                      >
                        <AddLine />
                        Create Wage Table
                      </a>
                    ),
                  },
                ]}
              />
            </FormControl>
            <FormMessage>{form.formState.errors['wageTableId']?.message}</FormMessage>
          </FormItem>
        )}
      />
    </div>
  );
}
