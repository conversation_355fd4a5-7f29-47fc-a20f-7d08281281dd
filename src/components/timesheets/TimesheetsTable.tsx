/* eslint-disable react/display-name */
import moment from 'moment';
import React, { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';

import { formatMinutesToHoursWorked } from 'utils/format';

import { UserTimesheet } from 'interfaces/timesheet';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import {
  ColDef,
  ColumnRowGroupChangedEvent,
  GridReadyEvent,
  ICellRendererParams,
  SortDirection,
  ValueFormatterParams,
} from '@ag-grid-community/core';
import { AgGridReact, CustomCellRendererProps } from '@ag-grid-community/react';
import { customCellValueFormatter } from 'utils/table';
import { customGroupValueFormatter, mapTimesheetsForTable } from './utils';
import CustomGroupCellRenderer from './grid-components/CustomGroupCellRenderer';
import AlertsCell from './grid-components/AlertsCell';
import BreaksCell from './grid-components/BreaksCell';
import TimeCell from './grid-components/TimeCell';
import StatusCell from './grid-components/StatusCell';
import { useTimesheetListStore } from './store';
import { NoTimesheetResultOverlay } from './NoTimesheetResultOverlay';
import { cn } from '@/utils/cn';
import { Badge } from '@/hammr-ui/components/badge';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { useCompany } from '@/hooks/useCompany';

interface TimesheetTableProps {
  userTimesheets: UserTimesheet[];
  isLoading: boolean;
  onFilterChange?: (params: Record<string, number[]>) => void;
  useDecimalHours?: boolean;
  parentRef?: React.MutableRefObject<AgGridReact>;
  setSelectedRowTimesheetId?: Dispatch<SetStateAction<number | null>>;
  setShowDetailsModal?: (shouldShow: boolean) => void;
  isExportingRef?: React.MutableRefObject<boolean>;
  onGridReady?: (event: GridReadyEvent<UserTimesheet>) => void;
  onAddTimesheet?: () => void;
  rowGroupBy?: 'employee' | 'project'; // add other columns to group by if needed
  showAddTimesheetButton?: boolean;
  showCheckboxes?: boolean;
}

export default function TimesheetsTable({
  userTimesheets,
  parentRef,
  isLoading,
  onFilterChange,
  useDecimalHours,
  setSelectedRowTimesheetId,
  setShowDetailsModal,
  isExportingRef,
  onGridReady,
  onAddTimesheet,
  rowGroupBy = 'employee',
  showAddTimesheetButton = true,
  showCheckboxes = true,
}: TimesheetTableProps) {
  const [isGroupingEnabled, setIsGroupingEnabled] = useState(false);

  const onRowGroupChange = (params: ColumnRowGroupChangedEvent) => {
    parentRef.current?.api?.setColumnsVisible(['statusText'], true);
    if (params.api.getState().rowGroup) {
      setIsGroupingEnabled(true);
    } else {
      setIsGroupingEnabled(false);
    }
  };

  const { company } = useCompany();

  const defaultColDef = {
    enableValue: true,
    minWidth: 40,
    onCellClicked({ colDef, data }) {
      if (colDef.field === 'actions' || colDef.field === 'id') return;

      setSelectedRowTimesheetId?.(data.id);
      setShowDetailsModal?.(true);
    },
    cellClass: 'cursor-pointer',
  };

  const colState = parentRef.current?.api?.getColumnState();
  const sortState = (colState ?? [])
    .filter(function (s) {
      return s.sort !== null;
    })
    .map(function (s) {
      return { colId: s.colId, sort: s.sort, sortIndex: s.sortIndex };
    });

  const isDriveTimeEnabled = company?.timeTrackingSettings.isDriveTimeEnabled;

  // columns used for export only
  const invisibleColumns: ColDef[] = useMemo(
    () =>
      [
        {
          headerName: 'Description',
          field: 'description',
        },
        {
          headerName: 'Hourly wage',
          field: 'hourlyWage',
          valueFormatter: (params: ValueFormatterParams) =>
            customCellValueFormatter({
              params,
              type: 'currency',
              isExporting: isExportingRef.current,
            }),
        },
        {
          headerName: 'Total hours',
          // TODO -> totalHours is actually in raw form totalMinutes and then the formatter will convert to hours
          field: 'totalHours',
          valueFormatter: (params: ValueFormatterParams) =>
            customCellValueFormatter({
              params,
              type: 'billableTime',
              useDecimalHours,
              isExporting: isExportingRef.current,
            }),
        },
        {
          headerName: 'Project number',
          field: 'projectNumber',
        },
        {
          headerName: 'Employee ID',
          field: 'employeeId',
        },
        {
          headerName: 'Position',
          field: 'position',
        },
        {
          headerName: 'Clock in',
          field: 'clockIn',
          valueFormatter: (params: ValueFormatterParams) =>
            customCellValueFormatter({
              params,
              type: 'time',
              isExporting: isExportingRef.current,
            }),
        },
        {
          headerName: 'Clock out',
          field: 'clockOut',
          valueFormatter: (params: ValueFormatterParams) =>
            customCellValueFormatter({
              params,
              type: 'time',
              isExporting: isExportingRef.current,
            }),
        },
        {
          headerName: 'Earnings',
          field: 'totalWages',
          valueFormatter: (params: ValueFormatterParams) =>
            customCellValueFormatter({
              params,
              type: 'currency',
              isExporting: isExportingRef.current,
            }),
        },
      ].map((col) => ({
        ...col,
        hide: true,
        suppressColumnsToolPanel: true,
      })),
    [useDecimalHours, isExportingRef.current]
  );

  const visibleColumns: ColDef[] = useMemo(
    () => [
      ...(showCheckboxes
        ? [
            {
              field: 'id',
              checkboxSelection: true,
              headerCheckboxSelection: true,
              headerName: '',
              maxWidth: 34,
              pinned: 'left' as const,
              cellClass: 'cursor-default',
              cellRenderer: () => {
                return null;
              },
            },
          ]
        : []),
        {
          field: 'statusText',
          headerName: 'Status',
          minWidth: 100,
          enableRowGroup: true,
          valueFormatter: (params: ValueFormatterParams) =>
            customGroupValueFormatter(params, undefined, isExportingRef.current),
          cellRenderer: (params: CustomCellRendererProps) => {
            if (params.node.allLeafChildren?.length > 0) {
              return params.valueFormatted;
            }
            return <StatusCell {...params} />;
          },
        },
      {
        maxWidth: 178, // 162 (from figma) + 16 (cell padding)
        minWidth: 178, // to make the Date not resizable, so that earnings and project name stay aligned
        headerName: 'Date',
        field: 'date',
        suppressSizeToFit: true,
        enableRowGroup: true,
        // only sort by date if no sort is applied
        initialSort: sortState.length === 0 ? ('asc' as SortDirection) : undefined,
        cellClass: isGroupingEnabled ? 'ml-4' : undefined,
        comparator: (date1, date2) => {
          const parsedDate1 = moment(date1, 'ddd, MMM D');
          const parsedDate2 = moment(date2, 'ddd, MMM D');
          return parsedDate1.isBefore(parsedDate2) ? -1 : parsedDate1.isAfter(parsedDate2) ? 1 : 0;
        },
        valueFormatter: (params: ValueFormatterParams) =>
          customGroupValueFormatter(params, undefined, isExportingRef.current),
      },
      {
        minWidth: 220,
        initialWidth: 220,
        initialRowGroup: rowGroupBy === 'project',
        headerName: 'Project',
        field: 'projectName',
        initialHide: rowGroupBy === 'project',
        enableRowGroup: true,
        filterParams: {
          applyMiniFilterWhileTyping: true,
        },
        valueFormatter: (params: ValueFormatterParams) =>
          customGroupValueFormatter(params, undefined, isExportingRef.current),
        cellRenderer(params: ValueFormatterParams<UserTimesheet>) {
          if (params.data?.project?.isPrevailingWage) {
            return (
              <span className="flex items-center gap-1.5">
                {params.value}
                <Tooltip content="Prevailing Wage">
                  <Badge variant="outline" color="gray">
                    PW
                  </Badge>
                </Tooltip>
              </span>
            );
          } else {
            // for non-PW projects only return the text name of project, so
            // that it can be truncated when it's used as a group header
            return params.value;
          }
        },
      },
      {
        minWidth: 140,
        field: 'time',
        headerName: 'Time',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'time',
            isExporting: isExportingRef.current,
          }),
        cellRenderer: (params: ValueFormatterParams) => <TimeCell params={params} />,
      },
      {
        minWidth: 150,
        initialWidth: 150,
        initialRowGroup: rowGroupBy === 'employee',
        headerName: 'Employee',
        field: 'employeeName',
        // this is true because when we group by project it's only in the employee tab and we want to hide it there
        // in case we add the timesheets table in another part of the app then we'd need to revisit this logic
        initialHide: true,
        enableRowGroup: true,
        filterParams: {
          applyMiniFilterWhileTyping: true,
        },
        valueFormatter: (params: ValueFormatterParams) =>
          customGroupValueFormatter(params, undefined, isExportingRef.current),
      },
      {
        initialWidth: 150,
        initialHide: true,
        headerName: 'Cost code',
        field: 'costCodeName',
        enableRowGroup: true,
        filterParams: {
          applyMiniFilterWhileTyping: true,
        },
        valueFormatter: (params: ValueFormatterParams) =>
          customGroupValueFormatter(params, 'Unassigned', isExportingRef.current),
      },
      {
        minWidth: 80,
        headerName: 'REG',
        field: 'regularMinutes',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours,
            isExporting: isExportingRef.current,
          }),
        cellRenderer: (params) => formatMinutesToHoursWorked(params.value),
      },
      {
        minWidth: 80,
        headerName: 'OT',
        field: 'overtimeMinutes',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours,
            isExporting: isExportingRef.current,
          }),
        cellRenderer: (params) => formatMinutesToHoursWorked(params.value),
      },
      {
        minWidth: 80,
        headerName: 'DOT',
        field: 'doubleOvertimeMinutes',
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours,
            isExporting: isExportingRef.current,
          }),
        cellRenderer: (params) => formatMinutesToHoursWorked(params.value),
      },
      {
        minWidth: 90,
        headerName: 'Drive Time',
        field: 'driveTimeMinutes',
        initialHide: !isDriveTimeEnabled,
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours,
            isExporting: isExportingRef.current,
          }),
        cellRenderer: (params) => formatMinutesToHoursWorked(params.value),
      },
      {
        headerName: 'Breaks',
        field: 'breakMinutes',
        minWidth: 170,
        initialHide: true,
        valueFormatter: (params: ValueFormatterParams) =>
          customCellValueFormatter({
            params,
            type: 'billableTime',
            useDecimalHours,
            isExporting: isExportingRef.current,
          }),
        cellRenderer: (params) => <BreaksCell params={params} />,
      },
      {
        headerName: 'Alerts',
        initialWidth: 100,
        sortable: false,
        field: 'alerts',
        cellStyle: { overflow: 'visible' },
        cellRenderer: (params) => {
          return <AlertsCell params={params} />;
        },
      },
    ],
    [formatMinutesToHoursWorked, setSelectedRowTimesheetId, useDecimalHours, isExportingRef.current, isDriveTimeEnabled]
  );

  const columnValueMappings = [...visibleColumns, ...invisibleColumns];

  const mappedRowData = useMemo(() => (userTimesheets ? mapTimesheetsForTable(userTimesheets) : []), [userTimesheets]);

  const selectedStatusIds = useTimesheetListStore((state) => state.selectedStatusKeys);
  const selectedEmployeeIds = useTimesheetListStore((state) => state.selectedEmployeeIds);
  const selectedProjectIds = useTimesheetListStore((state) => state.selectedProjectIds);
  const selectedCostCodeIds = useTimesheetListStore((state) => state.selectedCostCodeIds);
  const selectedEquipmentIds = useTimesheetListStore((state) => state.selectedEquipmentIds);

  const isExternalFilterPresent = useCallback(() => true, []);

  const doesExternalFilterPass = useCallback(
    (node) => {
      const { data } = node;

      if (selectedStatusIds.length > 0 && !selectedStatusIds.includes(data.status)) {
        return false;
      }

      if (selectedEmployeeIds.length > 0 && !selectedEmployeeIds.includes(data.user?.id)) {
        return false;
      }

      if (selectedProjectIds.length > 0 && !selectedProjectIds.includes(data.projectId)) {
        return false;
      }

      if (selectedCostCodeIds.length > 0 && !selectedCostCodeIds.includes(data.costCodeId)) {
        return false;
      }

      if (selectedEquipmentIds.length > 0 && !selectedEquipmentIds.includes(data.equipmentId)) {
        return false;
      }

      return true;
    },
    [selectedStatusIds, selectedProjectIds, selectedCostCodeIds, selectedEmployeeIds, selectedEquipmentIds]
  );

  return (
    <div className="relative flex flex-grow">
      <div className={cn('flex flex-grow', mappedRowData.length === 0 && !isLoading && 'invisible')}>
        {company && (
          <UpdatedTable
            enableGroupCheckbox={showCheckboxes}
            onRowGroupChange={onRowGroupChange}
            isLoading={isLoading}
            onGridReady={onGridReady}
            parentRef={parentRef}
            groupRenderer={(params: ICellRendererParams) => (
              <CustomGroupCellRenderer params={params} columnValueMappings={columnValueMappings} />
            )}
            defaultColDef={defaultColDef}
            rowData={mappedRowData}
            colDefs={columnValueMappings}
            emptyRowsText="No timesheets"
            noRowsOverlayComponent={() => (
              <NoTimesheetResultOverlay
                onAddTimesheet={onAddTimesheet}
                showAddTimesheetButton={showAddTimesheetButton}
              />
            )}
            onFilterChange={onFilterChange}
            tableProps={{
              isExternalFilterPresent,
              doesExternalFilterPass,
            }}
            showRowGroupPanel
            hideSidebar
            fitToWindow
            hideChevron={true}
          />
        )}
      </div>
      {!isLoading && mappedRowData.map((row) => doesExternalFilterPass({ data: row })).indexOf(true) === -1 && (
        <div className="absolute inset-0 pt-32">
          <NoTimesheetResultOverlay onAddTimesheet={onAddTimesheet} showAddTimesheetButton={showAddTimesheetButton} />
        </div>
      )}
    </div>
  );
}
