import { Dispatch, SetStateAction } from 'react';

import { useToast } from 'hooks/useToast';
import { UserTimesheet } from 'interfaces/timesheet';
import { deleteTimesheet } from 'services/timesheet';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { TimesheetObjectTable } from './utils';

interface ConfirmDeleteTimesheetModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentRowData: UserTimesheet;
  callback: () => void;
}

export default function ConfirmDeleteTimesheetModal({
  open,
  setOpen,
  currentRowData,
  callback,
}: ConfirmDeleteTimesheetModalProps) {
  const { addToast } = useToast();

  const confirmDeleteHandler = async () => {
    try {
      await deleteTimesheet(`${currentRowData.id}`);
      addToast({
        title: 'Deleted Timesheet Entry',
        description: (
          <>
            Successfully deleted the timesheet entry for{' '}
            <span className="font-medium">{(currentRowData as TimesheetObjectTable)?.employeeName}</span>.
          </>
        ),
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to delete timesheet');
    } finally {
      setOpen(false);
    }
  };

  return (
    <ConfirmDialog
      data={[]}
      onConfirm={confirmDeleteHandler}
      open={open}
      setOpen={setOpen}
      title="Delete Timesheet Entry"
      subtitle={
        <>
          You’re about to delete the timesheet entry for{' '}
          <span className="font-medium">{(currentRowData as TimesheetObjectTable)?.projectName}</span>. Do you want to
          proceed?
        </>
      }
      confirmButton={{ color: 'error' }}
      confirmButtonText="Delete"
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
    />
  );
}
