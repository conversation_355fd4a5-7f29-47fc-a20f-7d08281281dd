import { DatePeriodSelector } from './DatePeriodSelector';
import Button from '@/hammr-ui/components/button';
import dayjs, { Dayjs } from 'dayjs';
import React, { useMemo, useState } from 'react';
import { filteredDateChangeMappings, useDateSelection } from '@/hooks/useDateSelection';
import { getPayDays } from '@/services/pay-schedule';
import { useCompany } from '@/hooks/useCompany';
import { PayDay } from '@/interfaces/check/pay-schedule';
import { Company } from '@/interfaces/company';
import { showErrorToast } from '@/utils/errorHandling';
import { addToast } from '@/hooks/useToast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { approveTimesheets } from '@/services/timesheet';
import Spinner from '@/hammr-ui/components/spinner';
import { UserTimesheet } from '@/interfaces/timesheet';
import { RiCheckLine, Ri<PERSON>loseLine, RiDeleteBinLine } from '@remixicon/react';
import TotalEarningsAndHours from './TotalEarningsAndHours';
import { apiRequest } from '@/utils/requestHelpers';

interface TimesheetActionBarProps {
  isLoadingPayDays: boolean;
  payDays: PayDay[];
  setPayDays: (payDays: PayDay[]) => void;
  selectedPayDay: PayDay;
  setSelectedPayDay: (payDay: PayDay) => void;
  selectedTimesheets: UserTimesheet[];
  timesheets?: UserTimesheet[];
}

export const TimesheetActionBar = ({
  isLoadingPayDays,
  payDays,
  setPayDays,
  selectedPayDay,
  setSelectedPayDay,
  selectedTimesheets,
  timesheets,
}: TimesheetActionBarProps) => {
  const [disabledPreviousPayDaysButton, setDisabledPreviousPayDaysButton] = useState(false);
  const [disabledNextPayDaysButton, setDisabledNextPayDaysButton] = useState(false);
  const { company }: { company: Company } = useCompany();

  const {
    currentStartDateSelected,
    setCurrentStartDateSelected,
    currentEndDateSelected,
    setCurrentEndDateSelected,
    currentSelectedDateFilter,
    setCurrentSelectedDateFilter,
    registerDatesByFilter,
    initialized,
  } = useDateSelection();

  const handleDateFilterSelection = (dateFilter: string, newStartDate?: Dayjs, newEndDate?: Dayjs) => {
    setCurrentSelectedDateFilter(dateFilter);
    // record any date changes - if dateFilter === 'PAY_PERIOD' or 'CUSTOM'
    if (dateFilter === 'PAY_PERIOD') {
      registerDatesByFilter(dateFilter, {
        startDate: dayjs(selectedPayDay.period_start),
        endDate: dayjs(selectedPayDay.period_end),
      });
    } else if (dateFilter === 'CUSTOM') {
      if (newStartDate && newEndDate) {
        registerDatesByFilter(dateFilter, {
          startDate: newStartDate,
          endDate: newEndDate,
        });
      }
    } else {
      registerDatesByFilter(dateFilter, { weekStartDay: company?.overtimeSettings.weekStartDay });
    }
  };

  const fetchMorePayDays = async (direction: 'prev' | 'next') => {
    if (!payDays.length) {
      return [];
    }

    try {
      let startDate: string;
      if (direction === 'prev') {
        startDate = dayjs(payDays[0].period_start).add(2, 'day').subtract(12, 'months').format('YYYY-MM-DD');
      } else {
        startDate = dayjs(payDays[payDays.length - 1].period_end)
          .add(2, 'day')
          .format('YYYY-MM-DD');
      }

      setDisabledPreviousPayDaysButton(true);
      setDisabledNextPayDaysButton(true);
      const payDaysData = await getPayDays(company.paySchedule.checkPayScheduleId, startDate).finally(() => {
        setDisabledPreviousPayDaysButton(false);
        setDisabledNextPayDaysButton(false);
      });

      let newPayDays: PayDay[] = [];

      if (payDaysData.length) {
        if (direction === 'prev') {
          newPayDays = [...payDaysData, ...payDays];
          setPayDays(newPayDays);
        } else {
          newPayDays = [...payDays, ...payDaysData];
          setPayDays(newPayDays);
        }
      } else {
        if (direction === 'prev') {
          setDisabledPreviousPayDaysButton(true);
        } else {
          setDisabledNextPayDaysButton(true);
        }
      }

      return newPayDays;
    } catch (error) {
      console.error('Error fetching more paydays:', error);
      showErrorToast(error, 'Failed to fetch more pay days');
    }
  };

  const handlePayPeriodNavigation = async (direction: 'prev' | 'next') => {
    setDisabledPreviousPayDaysButton(false);
    setDisabledNextPayDaysButton(false);

    const currentIndex = payDays.findIndex((payDay) => payDay.period_start === selectedPayDay.period_start);

    if (direction === 'prev' && currentIndex === 0) {
      const newPayDays = await fetchMorePayDays('prev');
      const newIndex = newPayDays.findIndex((payDay) => payDay.period_start === selectedPayDay.period_start);
      if (newIndex > 0) {
        const newPayDay = newPayDays[newIndex - 1];
        setSelectedPayDay(newPayDay);
        registerDatesByFilter('PAY_PERIOD', {
          startDate: dayjs(newPayDay.period_start),
          endDate: dayjs(newPayDay.period_end),
        });
      }
      return;
    }

    if (direction === 'next' && currentIndex === payDays.length - 1) {
      const newPayDays = await fetchMorePayDays('next');
      const nextPayDay = newPayDays[currentIndex + 1];
      if (nextPayDay) {
        setSelectedPayDay(nextPayDay);
        registerDatesByFilter('PAY_PERIOD', {
          startDate: dayjs(nextPayDay.period_start),
          endDate: dayjs(nextPayDay.period_end),
        });
      }
      return;
    }

    const canNavigate = direction === 'prev' ? currentIndex > 0 : currentIndex < payDays.length - 1;
    if (canNavigate) {
      const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1;
      const newPayDay = payDays[newIndex];
      setSelectedPayDay(newPayDay);
      registerDatesByFilter('PAY_PERIOD', {
        startDate: dayjs(newPayDay.period_start),
        endDate: dayjs(newPayDay.period_end),
      });
    }
  };

  // handlers for left and right arrow clicks
  const handlePrevDateSelector = async () => {
    // should take current dateFilter state and decrement accordingly
    if (currentSelectedDateFilter === 'PAY_PERIOD') {
      await handlePayPeriodNavigation('prev');
    } else {
      // Dayjs objects are immutable, so we don't need to clone
      let updatedStartDate = currentStartDateSelected;
      let updatedEndDate = currentEndDateSelected;
      const prevStepChange = filteredDateChangeMappings(`PREV_${currentSelectedDateFilter}`);

      if (prevStepChange) {
        prevStepChange.forEach((stepChange) => {
          updatedStartDate = updatedStartDate[stepChange.operation](stepChange.amount, stepChange.unit);
          updatedEndDate = updatedEndDate[stepChange.operation](stepChange.amount, stepChange.unit);
        });

        if (currentSelectedDateFilter === 'MONTHLY') {
          updatedStartDate = updatedStartDate.startOf('month');
          updatedEndDate = updatedEndDate.endOf('month');
        }

        setCurrentStartDateSelected(updatedStartDate);
        setCurrentEndDateSelected(updatedEndDate);
      }
    }
  };

  const handleNextDateSelector = async () => {
    // should take current dateFilter state and increment accordingly
    if (currentSelectedDateFilter === 'PAY_PERIOD') {
      await handlePayPeriodNavigation('next');
    } else {
      // Dayjs objects are immutable, so we don't need to clone
      let updatedStartDate = currentStartDateSelected;
      let updatedEndDate = currentEndDateSelected;
      const nextStepChange = filteredDateChangeMappings(`NEXT_${currentSelectedDateFilter}`);

      if (nextStepChange) {
        nextStepChange.forEach((stepChange) => {
          updatedStartDate = updatedStartDate[stepChange.operation](stepChange.amount, stepChange.unit);
          updatedEndDate = updatedEndDate[stepChange.operation](stepChange.amount, stepChange.unit);
        });

        if (currentSelectedDateFilter === 'MONTHLY') {
          updatedStartDate = updatedStartDate.startOf('month');
          updatedEndDate = updatedEndDate.endOf('month');
        }

        setCurrentStartDateSelected(updatedStartDate);
        setCurrentEndDateSelected(updatedEndDate);
      }
    }
  };

  const queryClient = useQueryClient();

  const approveTimesheetsMutation = useMutation({
    mutationFn: (timesheetIds: number[]) => approveTimesheets(timesheetIds),
    onSuccess() {
      addToast({
        title: 'Approved Timesheets',
        description: 'Successfully approved timesheets.',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const unapproveTimesheetsMutation = useMutation({
    mutationFn(timesheetIds: number[]) {
      return apiRequest('timesheets/unapprove', { method: 'POST', body: { timesheetIds } });
    },
    onSuccess() {
      addToast({
        title: 'Unapproved Timesheets',
        description: 'Successfully unapproved timesheets.',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const deleteTimesheetsMutation = useMutation({
    mutationFn(timesheetIds: number[]) {
      return apiRequest('timesheets/bulk-delete', { method: 'POST', body: { timesheetIds } });
    },
    onSuccess() {
      addToast({
        title: 'Deleted Timesheets',
        description: 'Successfully deleted timesheets.',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const isAllSelectedTimesheetsApproved = useMemo(() => {
    let isAllApproved = true;
    selectedTimesheets.forEach((timesheet) => {
      if (timesheet.status !== 'APPROVED') {
        isAllApproved = false;
      }
    });
    return isAllApproved;
  }, [selectedTimesheets]);

  return (
    <>
      <div className="flex w-full justify-between whitespace-nowrap">
        <div className="flex items-center">
          {/* Show skeleton loader while dates are being initialized */}
          {!initialized ? (
            <DatePeriodSkeleton />
          ) : (
            <DatePeriodSelector
              showPayPeriod={company?.isPayrollEnabled}
              period={currentSelectedDateFilter}
              startDate={currentStartDateSelected.toDate()}
              endDate={currentEndDateSelected.toDate()}
              onDateChange={(startDate, endDate) => {
                if (!startDate || !endDate) return;

                if (currentSelectedDateFilter === 'DAILY') {
                  handleDateFilterSelection(currentSelectedDateFilter, dayjs(startDate), dayjs(endDate));
                } else {
                  handleDateFilterSelection('CUSTOM', dayjs(startDate), dayjs(endDate));
                }
              }}
              onPeriodChange={(period) => {
                handleDateFilterSelection(period);
              }}
              onPreviousClick={handlePrevDateSelector}
              onNextClick={handleNextDateSelector}
              previousButtonDisabled={
                currentSelectedDateFilter === 'PAY_PERIOD' && (disabledPreviousPayDaysButton || isLoadingPayDays)
              }
              nextButtonDisabled={
                currentSelectedDateFilter === 'PAY_PERIOD' && (disabledNextPayDaysButton || isLoadingPayDays)
              }
            />
          )}

          {timesheets && (
            <TotalEarningsAndHours
              timesheets={timesheets}
              className="mx-6 hidden flex-shrink-0 rounded-8 bg-weak-50 p-2 lg:flex"
              showDriveTime={false}
            />
          )}
        </div>

        <div className="flex items-center">
          {selectedTimesheets.length !== 0 && isAllSelectedTimesheetsApproved ? (
            <Button
              className="flex-shrink-0"
              color="primary"
              size="small"
              beforeContent={unapproveTimesheetsMutation.isPending ? <Spinner /> : <RiCloseLine />}
              disabled={selectedTimesheets.length === 0 || unapproveTimesheetsMutation.isPending}
              onClick={() => {
                const approvedTimesheetIds = selectedTimesheets.map((row) => row.id);
                unapproveTimesheetsMutation.mutate(approvedTimesheetIds);
              }}
            >
              Unapprove
            </Button>
          ) : (
            <Button
              className="flex-shrink-0"
              color="primary"
              size="small"
              beforeContent={approveTimesheetsMutation.isPending ? <Spinner /> : <RiCheckLine />}
              disabled={selectedTimesheets.length === 0 || approveTimesheetsMutation.isPending}
              onClick={() => {
                const submittedTimesheetIds = selectedTimesheets
                  .filter((row) => row.status === 'SUBMITTED')
                  .map((row) => row.id);

                if (submittedTimesheetIds.length === 0) {
                  addToast({
                    title: 'No timesheets approved',
                    description: 'The selected timesheets are not ready to be approved',
                    type: 'warning',
                  });
                  return;
                }

                approveTimesheetsMutation.mutate(submittedTimesheetIds);
              }}
            >
              Approve
            </Button>
          )}
          <Button
            className="ml-2 flex-shrink-0"
            color="neutral"
            variant="outline"
            size="small"
            beforeContent={deleteTimesheetsMutation.isPending ? <Spinner /> : <RiDeleteBinLine />}
            disabled={selectedTimesheets.length === 0 || deleteTimesheetsMutation.isPending}
            onClick={() => {
              const timesheetIds = selectedTimesheets.map((row) => row.id);

              deleteTimesheetsMutation.mutate(timesheetIds);
            }}
          >
            Delete
          </Button>
        </div>
      </div>
      {timesheets && (
        <div className="flex lg:hidden">
          <TotalEarningsAndHours
            timesheets={timesheets}
            className="flex-shrink-0 rounded-8 bg-weak-50 p-2"
            showDriveTime={false}
          />
        </div>
      )}
    </>
  );
};

// Skeleton loader that matches the exact dimensions of DatePeriodSelector
function DatePeriodSkeleton() {
  return (
    <div className="flex h-9 overflow-hidden rounded-8 border border-soft-200">
      {/* Period dropdown skeleton */}
      <div className="flex min-w-[124px] items-center justify-between gap-2 px-2 pl-4">
        <div className="h-4 w-16 animate-pulse rounded-full bg-weak-100"></div>
        <div className="h-5 w-5 animate-pulse rounded bg-weak-100"></div>
      </div>
      {/* Date range skeleton */}
      <div className="flex min-w-[226px] flex-shrink-0 border-l border-soft-200">
        <div className="flex items-center justify-center px-2">
          <div className="h-5 w-5 animate-pulse rounded bg-weak-100"></div>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="h-4 w-32 animate-pulse rounded-full bg-weak-100"></div>
        </div>
        <div className="flex items-center justify-center px-2">
          <div className="h-5 w-5 animate-pulse rounded bg-weak-100"></div>
        </div>
      </div>
    </div>
  );
}
