import { cn } from '@/utils/cn';
import { Badge } from '@/hammr-ui/components/badge';
import AlertLine from '@/hammr-icons/AlertLine';
import CheckLine from '@/hammr-icons/CheckLine';

export const AlertBadge = ({ type = 'CLOCK_OUT_OUTSIDE_GEOFENCE', fit = 'fit' }: { type: string; fit?: string }) => {
  const typeMap = {
    CLOCK_OUT_LOCATION_MISSING: 'Location Missing',
    CLOCK_OUT_OUTSIDE_GEOFENCE: 'Outside Geofence',
    CLOCK_OUT_LOCATION_OUTSIDE_GEOFENCE: 'Outside Geofence',
  };

  const fitClass = `w-${fit}`;

  return (
    <div
      className={` mt-2 flex items-center justify-center gap-[6px] rounded-full bg-[#F2AE03] px-3 py-1.5 ${fitClass}`}
    >
      <AlertLine className="size-4 text-white-0" />
      <div className="font-medium text-white">{typeMap[type]}</div>
    </div>
  );
};

export const AlertBadge2 = ({ type = 'CLOCK_OUT_OUTSIDE_GEOFENCE' }: { type: string; fit?: string }) => {
  const typeMap = {
    CLOCK_OUT_LOCATION_MISSING: 'Location Missing',
    CLOCK_OUT_OUTSIDE_GEOFENCE: 'Outside Geofence',
    CLOCK_OUT_LOCATION_OUTSIDE_GEOFENCE: 'Outside Geofence',
  };

  return (
    <Badge color="yellow" variant="light">
      {typeMap[type]}
    </Badge>
  );
};

export const ResolvedAlertButton = ({ onClick, disabled = false }) => {
  const classesToUse = cn(
    'mt-1 flex w-fit items-center justify-items-center gap-[6px] rounded-lg py-1.5 px-3 ring-1 ring-steel-blue-500',
    {
      'bg-white text-steel-blue-500 hover:bg-steel-blue-500 hover:text-white focus:border-steel-blue-700 focus:ring-steel-blue-600  active:bg-steel-blue-700':
        !disabled,
      'bg-steel-blue-500 text-white': disabled,
    }
  );

  return (
    <div className={classesToUse} onClick={onClick}>
      <CheckLine className="size-4 text-white-0" />
      <div className="font-medium">Alert Resolved</div>
    </div>
  );
};
