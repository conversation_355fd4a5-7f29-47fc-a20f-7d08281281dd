import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import ArrowUpSLine from '@/hammr-icons/ArrowUpSLine';
import Draggable from '@/hammr-icons/Draggable';
import Layout5Line from '@/hammr-icons/Layout5Line';
import Button from '@/hammr-ui/components/button';
import { Checkbox } from '@/hammr-ui/components/checkbox';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { ColDef } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import { FC, MutableRefObject, useCallback, useEffect, useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import type { Identifier, XYCoord } from 'dnd-core';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';

interface CustomizeColumnsProps {
  gridRef: MutableRefObject<AgGridReact>;
}

function useGridColumns(gridRef: MutableRefObject<AgGridReact>, watch: boolean = false) {
  const [items, setItems] = useState<ColDef<unknown, unknown>[]>([]);

  const itemsRef = useRef(items);
  itemsRef.current = items;

  useEffect(() => {
    if (!watch) return;

    const interval = setInterval(() => {
      const columns = gridRef.current?.api?.getColumnDefs() ?? [];

      if (JSON.stringify(columns) === JSON.stringify(itemsRef.current)) {
        return;
      }

      setItems(columns);
    }, 100);

    return () => clearInterval(interval);
  }, [watch]);

  return items;
}

export const CustomizeColumns: FC<CustomizeColumnsProps> = ({ gridRef }) => {
  const [open, setOpen] = useState(false);

  const gridColumns = useGridColumns(gridRef, open);

  const [items, setItems] = useState<ColDef<unknown, unknown>[]>([]);

  useEffect(() => {
    setItems(gridColumns.filter((col) => !col.pinned && !col.rowGroup && !col.suppressColumnsToolPanel));
  }, [gridColumns]);

  const moveItem = useCallback((dragIndex: number, hoverIndex: number) => {
    setItems((prev) =>
      update(prev, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prev[dragIndex]],
        ],
      })
    );
  }, []);

  const handleRearrange = (colId: string, index: number) => {
    const gridOptions = gridRef.current;

    gridOptions.api.moveColumns([colId], index + 1);
  };

  const handleToggleCheck = (colId: string) => {
    const columnStates = gridRef.current.api.getColumnState();

    const col = columnStates.find((col) => col.colId === colId);

    if (!col) return;

    col.hide = !col.hide;

    gridRef.current.api.applyColumnState({
      state: columnStates,
      applyOrder: true,
    });
  };

  const handleSelectAll = () => {
    const columnStates = gridRef.current.api.getColumnState();

    columnStates.forEach((col) => {
      if (!items.find((item) => item.colId === col.colId)) {
        return;
      }

      if (col.pinned || col.rowGroup) return;
      col.hide = false;
    });

    gridRef.current.api.applyColumnState({
      state: columnStates,
      applyOrder: true,
    });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          color="neutral"
          beforeContent={<Layout5Line />}
          afterContent={open ? <ArrowUpSLine /> : <ArrowDownSLine />}
        >
          Columns
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="flex w-80 flex-col border border-soft-200">
        <DndProvider backend={HTML5Backend}>
          <div className="flex flex-col overflow-hidden">
            <div className="flex justify-between border-b border-soft-200 px-5 py-3">
              <div className="text-base font-medium text-strong-950">Table Layout</div>
              <LinkButton className="text-primary-base" onClick={handleSelectAll}>
                Select All
              </LinkButton>
            </div>
            <ScrollArea className="h-full">
              <div className="gap-8 px-5 py-3">
                {items.map((column, index) => (
                  <Item
                    key={column.colId}
                    id={column.colId}
                    index={index}
                    title={column.headerName}
                    selected={!column.hide}
                    onSelect={() => handleToggleCheck(column.colId)}
                    moveItem={moveItem}
                    onDragEnd={handleRearrange}
                  />
                ))}
              </div>
            </ScrollArea>
          </div>
        </DndProvider>
      </PopoverContent>
    </Popover>
  );
};

interface ItemProps {
  id: string;
  title: string;
  selected: boolean;
  onSelect: () => void;
  index: number;
  moveItem: (dragIndex: number, hoverIndex: number) => void;
  onDragEnd?: (colId: string, index: number) => void;
}

const ItemType = 'COLUMN';

interface DragItem {
  index: number;
  id: string;
  type: string;
}

const Item: FC<ItemProps> = ({ title, selected, onSelect, index, moveItem, id, onDragEnd }) => {
  const labelRef = useRef<HTMLLabelElement>(null);
  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: Identifier | null }>({
    accept: ItemType,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!labelRef.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = labelRef.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      // Time to actually perform the action
      moveItem(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [, drag, preview] = useDrag({
    type: ItemType,
    item: () => {
      return { id, index };
    },
    end: (item, monitor) => {
      const dropResult: { id: string; index: number } = monitor.getDropResult();
      if (dropResult) {
        onDragEnd?.(item.id, index);
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return (
    <label
      className="flex items-center rounded-6 p-2 text-sm font-medium text-strong-950 hover:bg-weak-50"
      ref={(ref) => {
        labelRef.current = ref;
        preview(drop(ref));
      }}
    >
      <div className="cursor-grab" data-handler-id={handlerId} ref={drag}>
        <Draggable />
      </div>
      <div className="flex-1">{title}</div>
      <div>
        <Checkbox checked={selected} onCheckedChange={() => onSelect()} />
      </div>
    </label>
  );
};
