import { cn } from '@/hammr-ui/lib/cn';
import { useAuth } from '@/hooks/useAuth';
import { useCompany } from '@/hooks/useCompany';
import { UserTimesheet } from '@/interfaces/timesheet';
import { formatMinutesToHoursWorked } from '@/utils/format';
import React from 'react';

function getTotalEarningsAndHoursFromTimesheets(timesheets: UserTimesheet[]) {
  let totalEarnings = 0;
  let totalRegularMinutes = 0;
  let totalOTMinutes = 0;
  let totalDOTMinutes = 0;
  let totalDriveTimeMinutes = 0;

  timesheets.forEach((timesheet) => {
    totalEarnings += timesheet.totalWages ?? 0;
    totalRegularMinutes += timesheet.regularMinutes ?? 0;
    totalOTMinutes += timesheet.overtimeMinutes ?? 0;
    totalDOTMinutes += timesheet.doubleOvertimeMinutes ?? 0;
    totalDriveTimeMinutes += timesheet.driveTimeMinutes ?? 0;
  });

  const totalMinutes = totalRegularMinutes + totalOTMinutes + totalDOTMinutes + totalDriveTimeMinutes;

  return {
    totalEarnings: Math.round(totalEarnings).toLocaleString(),
    totalHours: totalMinutes === 0 ? '0h 0m' : formatMinutesToHoursWorked(totalMinutes),
    totalRegularHours: totalRegularMinutes === 0 ? '0h 0m' : formatMinutesToHoursWorked(totalRegularMinutes),
    totalOTHours: totalOTMinutes === 0 ? '0h 0m' : formatMinutesToHoursWorked(totalOTMinutes),
    totalDOTHours: totalDOTMinutes === 0 ? '0h 0m' : formatMinutesToHoursWorked(totalDOTMinutes),
    totalDriveTimeHours: totalDriveTimeMinutes === 0 ? '0h 0m' : formatMinutesToHoursWorked(totalDriveTimeMinutes),
  };
}

interface Props {
  timesheets: UserTimesheet[];
  className?: string;
  showDriveTime?: boolean;
}

export default function TotalEarningsAndHours({ timesheets, className, showDriveTime = true }: Props) {
  const { user } = useAuth();
  const { company } = useCompany();

  return (
    <article className={cn('flex flex-shrink-0 flex-nowrap gap-2 text-sm font-normal', className)}>
      {[getTotalEarningsAndHoursFromTimesheets(timesheets)].map((data, i) => (
        <React.Fragment key={i}>
          {user.role === 'ADMIN' && (
            <>
              <strong className="font-medium">${data.totalEarnings}</strong>
              <div className="flex w-0 items-center justify-center text-sub-600">|</div>
            </>
          )}
          <strong className="font-medium">{data.totalHours}</strong>
          <div className="flex w-0 items-center justify-center text-sub-600">∙</div>
          <section className="flex">
            <h2 className="text-sub-600">REG:&nbsp;</h2>
            <strong className="h-fit font-medium text-strong-950">{data.totalRegularHours}</strong>
          </section>
          <div className="flex w-0 items-center justify-center text-sub-600">∙</div>
          <section className="flex">
            <h2 className="text-sub-600">OT:&nbsp;</h2>
            <strong className="h-fit font-medium text-strong-950">{data.totalOTHours}</strong>
          </section>
          <div className="flex w-0 items-center justify-center text-sub-600">∙</div>
          <section className="flex">
            <h2 className="text-sub-600">DOT:&nbsp;</h2>
            <strong className="h-fit font-medium text-strong-950">{data.totalDOTHours}</strong>
          </section>
          {company.timeTrackingSettings.isDriveTimeEnabled && showDriveTime && (
            <>
              <div className="flex w-0 items-center justify-center text-sub-600">∙</div>
              <section className="hide-drive-time flex">
                <h2 className="text-sub-600">Drive Time:&nbsp;</h2>
                <strong className="h-fit font-medium text-strong-950">{data.totalDriveTimeHours}</strong>
              </section>
            </>
          )}
        </React.Fragment>
      ))}
    </article>
  );
}
