import { generateCardData } from './utils';

export const propToHeaderMap = {
  'timesheet-clockIn': 'Clock-in',
  'timesheet-clockOut': 'Clock-out',
  'timesheet-breakDuration': 'Break duration',
  'timesheet-description': 'Description',
  'timesheet-projectId': 'Project',
  'timesheet-costCodeId': 'Cost code',
  'timesheet-workersCompCodeId': 'Workers Comp Code',
  'break-start': 'Break start time',
  'break-end': 'Break end time',
  'break-isDeleted': 'Break',
  'break-breakCreated': 'Break created',
  'timesheet-userClassificationId': 'Classification',
  'timesheet-otDuration': 'Overtime',
  'timesheet-dotDuration': 'Double Overtime',
  'timesheet-driveTimeDuration': 'Drive time',
};

export const isTimestampValue = {
  'timesheet-clockIn': true,
  'timesheet-clockOut': true,
  'break-start': true,
  'break-end': true,
  'break-isDeleted': true,
};

function TimesheetHistoryCard({ historyData }) {
  const { cardHeader, cardMainData, cardFooter } = generateCardData(historyData);
  return (
    <div className="flex flex-col gap-2 rounded-10 border border-soft-200 p-4">
      {!!cardHeader && <div className="text-sm font-medium text-strong-950">{cardHeader}</div>}
      <div className="text-sm font-medium text-strong-950">{cardMainData}</div>
      <div className="text-xs font-normal text-sub-600">{cardFooter}</div>
    </div>
  );
}

export default TimesheetHistoryCard;
