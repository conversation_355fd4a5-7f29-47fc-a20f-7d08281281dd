import AddLine from '@/hammr-icons/AddLine';
import Button from '@/hammr-ui/components/button';
import { FC } from 'react';
import EmptyStateTimeTracker from '@/hammr-icons/EmptyStateTimeTracker';

interface NoTimesheetResultOverlayProps {
  onAddTimesheet: () => void;
  showAddTimesheetButton?: boolean;
}

export const NoTimesheetResultOverlay: FC<NoTimesheetResultOverlayProps> = ({
  onAddTimesheet,
  showAddTimesheetButton = true,
}) => {
  return (
    <div className="pointer-events-auto flex flex-col items-center gap-5">
      <EmptyStateTimeTracker />
      <div className="text-center text-sm font-normal text-soft-400">
        There are no timesheets for these dates.
        <br />
        {showAddTimesheetButton ? 'Click the button below to add one.' : ''}
      </div>
      {showAddTimesheetButton && (
        <Button beforeContent={<AddLine />} onClick={onAddTimesheet}>
          Add Timesheet
        </Button>
      )}
    </div>
  );
};
