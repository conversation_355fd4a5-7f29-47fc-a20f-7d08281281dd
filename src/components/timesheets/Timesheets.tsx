/* eslint-disable react/display-name */
import React, { useEffect, useRef, useState } from 'react';

import { AgGridReact } from '@ag-grid-community/react';
import TimesheetsTable from './TimesheetsTable';
import { TimesheetActionBar } from './TimesheetActionBar';
import { useAuth } from '@/hooks/useAuth';
import { Company } from '@/interfaces/company';
import { useDate } from '@/hooks/useDate';
import { useDateSelection } from '@/hooks/useDateSelection';
import { useCompany } from '@/hooks/useCompany';
import { useQuery } from '@tanstack/react-query';
import { getPayDays } from '@/services/pay-schedule';
import { PayDay } from '@/interfaces/check/pay-schedule';
import moment from 'moment';
import { clearAllTimesheetFilter, TimesheetFilterBar } from '@/components/timesheets/TimesheetFilterBar';
import { useTimesheetListStore } from '@/components/timesheets/store';
import { apiRequest } from '@/utils/requestHelpers';
import TimesheetDetailsModal from './TimesheetDetailsModal';
import { UserTimesheet } from '@/interfaces/timesheet';
import CreateBulkTimesheetsModal from './CreateBulkTimesheetsModal';
import { useRouter } from 'next/router';

interface TimesheetProps {
  isExportingRef?: React.MutableRefObject<boolean>;
  showTimesheetModal?: boolean;
  setShowTimesheetModal?: (show: boolean) => void;
  gridRef?: React.MutableRefObject<AgGridReact>;
  selectedProjectId?: number;
  selectedWorkerId?: number;
  selectedEquipmentId?: number;
  from?: number;
  to?: number;
}

const Timesheets = ({
  isExportingRef,
  showTimesheetModal,
  setShowTimesheetModal,
  gridRef: gridRefParent,
  selectedEquipmentId,
  selectedProjectId,
  selectedWorkerId,
  from,
  to,
}: TimesheetProps) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedRowTimesheetId, setSelectedRowTimesheetId] = useState<number | null>(null);
  const router = useRouter();
  useEffect(() => {
    if (Number(router.query.timesheetId)) {
      setSelectedRowTimesheetId(Number(router.query.timesheetId));
      setShowDetailsModal(true);
    }
  }, [router.query.timesheetId]);

  const { user } = useAuth();
  const { company }: { company: Company } = useCompany();
  const { dayjs } = useDate();

  const {
    currentStartDateSelected,
    currentEndDateSelected,
    currentSelectedDateFilter,
    setCurrentSelectedDateFilter,
    registerDatesByFilter,
    initialized,
    setInitialized,
  } = useDateSelection();

  const ref = useRef<AgGridReact>(null);
  const gridRef = gridRefParent || ref;
  const [selectedTimesheets, setSelectedTimesheets] = useState<UserTimesheet[]>([]);
  const [isLoadingPayDays, setIsLoadingPayDays] = useState(false);
  const [payDays, setPayDays] = useState<PayDay[]>([]);
  const [selectedPayDay, setSelectedPayDay] = useState<PayDay>();

  const selectedHasAlerts = useTimesheetListStore((state) => state.selectedHasAlerts);
  const timesheetsQuery = useQuery({
    queryKey: [
      'timesheets',
      currentStartDateSelected?.format('x'),
      currentEndDateSelected?.endOf('day').format('x'),
      selectedHasAlerts,
    ],
    queryFn: async () => {
      const params = {
        from: currentStartDateSelected?.format('x'),
        to: currentEndDateSelected?.endOf('day').format('x'),
        isResolved: selectedHasAlerts ? false : undefined,
      };

      if (!currentStartDateSelected || !currentEndDateSelected) {
        return { timesheets: [] };
      }

      return apiRequest<{ timesheets: UserTimesheet[] }>('timesheets', { urlParams: params });
    },
    enabled:
      initialized &&
      !!currentStartDateSelected &&
      !!currentEndDateSelected &&
      !!user?.companyId &&
      (currentSelectedDateFilter === 'PAY_PERIOD' ? payDays.length > 0 && !!selectedPayDay : true),
  });

  const userTimesheets = timesheetsQuery.data?.timesheets;
  const timesheetsFilteredByProject = userTimesheets?.filter((timesheet) => timesheet.project.id === selectedProjectId);
  const timesheetsFilteredByWorker = userTimesheets?.filter((timesheet) => timesheet.user.id === selectedWorkerId);
  const timesheetsFilteredByEquipment = userTimesheets?.filter(
    (timesheet) => timesheet.equipmentId === selectedEquipmentId
  );

  const selectedRowTimesheet = userTimesheets?.find((timesheet) => timesheet.id === selectedRowTimesheetId);

  const isLoading = timesheetsQuery.isPending || (currentSelectedDateFilter === 'PAY_PERIOD' && payDays.length === 0);

  useEffect(() => {
    setTotals();
  }, [userTimesheets]);

  const setTotals = () => {
    const children = [];

    gridRef.current?.api?.forEachNodeAfterFilterAndSort((node) => children.push(node));
  };

  useEffect(() => {
    // Skip if already initialized to prevent re-running on every render
    if (initialized && !from && !to && selectedPayDay) {
      return;
    }

    if (from && to) {
      setCurrentSelectedDateFilter('CUSTOM');
      registerDatesByFilter('CUSTOM', {
        startDate: dayjs(from),
        endDate: dayjs(to),
      });
      return;
    }

    if (!company) {
      return;
    }

    if (company?.isPayrollEnabled && company.paySchedule) {
      // Don't update if we're still loading pay days
      if (!selectedPayDay || isLoadingPayDays) {
        return;
      }

      setCurrentSelectedDateFilter('PAY_PERIOD');
      registerDatesByFilter('PAY_PERIOD', {
        startDate: dayjs(selectedPayDay.period_start),
        endDate: dayjs(selectedPayDay.period_end),
        weekStartDay: company?.overtimeSettings?.weekStartDay || 0,
      });
    } else {
      setCurrentSelectedDateFilter('WEEKLY');
      registerDatesByFilter('WEEKLY', {
        weekStartDay: company?.overtimeSettings?.weekStartDay || 0,
      });
    }
  }, [
    company?.isPayrollEnabled,
    company?.paySchedule,
    selectedPayDay,
    from,
    to,
    initialized,
    isLoadingPayDays,
    registerDatesByFilter,
    setCurrentSelectedDateFilter,
  ]);

  useEffect(() => {
    if (!company?.isPayrollEnabled || !company?.paySchedule) {
      return;
    }

    async function fetchPayDays() {
      setIsLoadingPayDays(true);
      const sixMonthsAgo = dayjs().subtract(6, 'months').format('YYYY-MM-DD');

      const payDaysData = await getPayDays(company.paySchedule.checkPayScheduleId, sixMonthsAgo).finally(() => {
        setIsLoadingPayDays(false);
      });
      setPayDays(payDaysData);

      const now = dayjs();
      const currentPeriod = payDaysData.find((payDay) =>
        now.isBetween(dayjs(payDay.period_start), dayjs(payDay.period_end), 'day', '[]')
      );

      if (currentPeriod) {
        setSelectedPayDay(currentPeriod);
      }
    }

    fetchPayDays();

    return () => {
      setInitialized(false);
    };
  }, [company?.isPayrollEnabled, company?.paySchedule, dayjs, setInitialized]);

  const [visibleTimesheetRows, setVisibleTimesheetRows] = useState<number[]>([]);

  if (!user) return null;

  // List employee timesheets view
  return (
    <div className="flex flex-1 flex-col gap-4 overflow-hidden">
      <div className="overflow-x-auto overflow-y-hidden pb-1">
        <div className="flex min-w-full flex-col gap-4">
          <TimesheetActionBar
            isLoadingPayDays={isLoadingPayDays}
            payDays={payDays}
            setPayDays={setPayDays}
            selectedPayDay={selectedPayDay}
            setSelectedPayDay={(payday) => {
              clearAllTimesheetFilter(Boolean(selectedProjectId), Boolean(selectedWorkerId));
              setSelectedPayDay(payday);
            }}
            selectedTimesheets={selectedTimesheets}
            timesheets={userTimesheets?.filter((timesheet) => visibleTimesheetRows.includes(timesheet.id))}
          />
          <TimesheetFilterBar
            userTimesheets={
              selectedProjectId
                ? timesheetsFilteredByProject
                : selectedWorkerId
                  ? timesheetsFilteredByWorker
                  : selectedEquipmentId
                    ? timesheetsFilteredByEquipment
                    : userTimesheets
            }
            gridRef={gridRef}
            hideProjectFilter={Boolean(selectedProjectId)}
            hideWorkerFilter={Boolean(selectedWorkerId)}
          />
        </div>
      </div>

      <div className="flex flex-1 flex-col overflow-auto">
        <TimesheetsTable
          isLoading={isLoading}
          parentRef={gridRef}
          onGridReady={(event) => {
            event.api?.addEventListener('selectionChanged', () => {
              const selectedRows = gridRef?.current?.api.getSelectedRows();
              setSelectedTimesheets(selectedRows);
            });

            // for first time render of rows as filterChanged doesn't apply yet
            event.api?.addEventListener('rowDataUpdated', () => {
              const currentlyDisplayedRows = [];
              gridRef?.current?.api.forEachNode((node) => {
                if (node.displayed) {
                  currentlyDisplayedRows.push(node);
                }
              });
              const nonGroupRows = currentlyDisplayedRows?.filter((row) => row.group === false);
              setVisibleTimesheetRows(nonGroupRows?.map((row) => row.data.id));
            });
            event.api?.addEventListener('filterChanged', () => {
              const currentlyDisplayedRows = [];
              gridRef?.current?.api.forEachNode((node) => {
                if (node.displayed) {
                  currentlyDisplayedRows.push(node);
                }
              });
              const nonGroupRows = currentlyDisplayedRows?.filter((row) => row.group === false);
              setVisibleTimesheetRows(nonGroupRows?.map((row) => row.data.id));
            });
          }}
          userTimesheets={userTimesheets}
          onFilterChange={() => setTotals()}
          useDecimalHours={company?.timeTrackingSettings?.useDecimalHours || false}
          setSelectedRowTimesheetId={setSelectedRowTimesheetId}
          setShowDetailsModal={setShowDetailsModal}
          isExportingRef={isExportingRef}
          onAddTimesheet={() => setShowTimesheetModal(true)}
          rowGroupBy={selectedWorkerId ? 'project' : 'employee'}
        />
        {selectedRowTimesheet && (
          <TimesheetDetailsModal
            open={showDetailsModal}
            setOpen={(isOpen) => {
              if (isOpen === false && router.query.timesheetId) {
                const query = { ...router.query };
                delete query['timesheetId'];
                router.push({ query });
              }
              setShowDetailsModal(isOpen);
            }}
            timesheet={selectedRowTimesheet}
          />
        )}

        <CreateBulkTimesheetsModal
          open={showTimesheetModal}
          setOpen={setShowTimesheetModal}
          onAdd={timesheetsQuery.refetch}
          projectId={selectedProjectId?.toString()}
          workerId={selectedWorkerId}
          equipmentId={selectedEquipmentId?.toString()}
        />
      </div>
    </div>
  );
};

export default Timesheets;
