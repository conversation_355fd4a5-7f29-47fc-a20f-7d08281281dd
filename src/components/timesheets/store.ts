import { create } from 'zustand';

interface TimesheetListStoreState {
  selectedStatusKeys: string[];
  selectedEmployeeIds: number[];
  selectedProjectIds: number[];
  selectedEquipmentIds: number[];
  selectedCostCodeIds: number[];
  selectedHasAlerts?: boolean;
}

export const useTimesheetListStore = create<TimesheetListStoreState>((set) => ({
  selectedStatusKeys: [],
  selectedEmployeeIds: [],
  selectedProjectIds: [],
  selectedEquipmentIds: [],
  selectedCostCodeIds: [],
  selectedHasAlerts: false,
}));
