import { useState } from 'react';
import moment from 'moment';

import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { Menu, MenuContent, MenuItem, MenuTrigger } from '@/hammr-ui/components/Menu';
import { Calendar } from '@/hammr-ui/components/calendar';
import { cn } from '@/utils/cn';
import { RiArrowDownSLine, RiArrowLeftSLine, RiArrowRightSLine } from '@remixicon/react';

export interface PeriodOption {
  label: string;
  value: string;
}

const DEFAULT_PERIOD_OPTIONS: PeriodOption[] = [
  { label: 'Daily', value: 'DAILY' },
  { label: 'Weekly', value: 'WEEKLY' },
  { label: 'Monthly', value: 'MONTHLY' },
  { label: 'Custom Dates', value: 'CUSTOM' },
];

const PAY_PERIOD_OPTION: PeriodOption = { label: 'Pay Period', value: 'PAY_PERIOD' };

interface DatePeriodSelectorProps {
  period: string;
  startDate: Date;
  endDate: Date;
  onPeriodChange: (period: string) => void;
  onDateChange: (startDate: Date, endDate: Date) => void;
  onPreviousClick: () => void;
  onNextClick: () => void;
  previousButtonDisabled?: boolean;
  nextButtonDisabled?: boolean;
  showPayPeriod?: boolean;
}

/**
 * We now have a new date period selector that uses dayjs and is more robust: DatePeriodSelectorImproved
 * @deprecated
 */
export function DatePeriodSelector({
  period,
  startDate,
  endDate,
  onDateChange,
  onPeriodChange,
  onPreviousClick,
  onNextClick,
  previousButtonDisabled,
  nextButtonDisabled,
  showPayPeriod = false,
}: DatePeriodSelectorProps) {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);

  const periodOptions = showPayPeriod ? [PAY_PERIOD_OPTION, ...DEFAULT_PERIOD_OPTIONS] : DEFAULT_PERIOD_OPTIONS;

  const handleSelectPeriod = (period: string) => {
    onPeriodChange(period);
    setDropdownOpen(false);
  };

  const selectedOption = periodOptions.find((option) => option.value === period) ?? periodOptions[0];

  return (
    <div
      className={cn(
        'flex h-9 overflow-hidden rounded-8 border border-soft-200',
        'text-sm font-medium text-sub-600',
        '[&>:not(:first-child)]:border-l [&>:not(:first-child)]:border-soft-200',
        'flex-shrink-0 flex-nowrap'
      )}
    >
      <Menu open={dropdownOpen} onOpenChange={(open) => setDropdownOpen(open)}>
        <MenuTrigger asChild>
          <button
            type="button"
            className='flex min-w-[124px] items-center justify-between gap-2 px-2 pl-4 hover:bg-weak-50 data-[state="open"]:text-strong-950'
          >
            <span>{selectedOption.label}</span>
            <RiArrowDownSLine className="size-5" />
          </button>
        </MenuTrigger>
        <MenuContent className="min-w-[226px]" align="start">
          {periodOptions.map((option) => (
            <MenuItem
              key={option.value}
              title={option.label}
              selected={period === option.value}
              onClick={() => handleSelectPeriod(option.value)}
            />
          ))}
        </MenuContent>
      </Menu>
      <Popover open={calendarOpen} onOpenChange={(open) => setCalendarOpen(open)}>
        <PopoverTrigger asChild>
          <div
            className={`parent group flex min-w-[226px] flex-shrink-0 data-[state="open"]:text-strong-950  [&:has(.child:hover)]:bg-weak-50`}
          >
            {period !== 'CUSTOM' && (
              <button
                className={cn(
                  'flex items-center justify-center px-2',
                  previousButtonDisabled ? 'bg-strong-950/5 opacity-40' : 'hover:bg-weak-50'
                )}
                onClick={(event) => {
                  event.stopPropagation();
                  onPreviousClick();
                }}
                disabled={previousButtonDisabled}
              >
                <RiArrowLeftSLine className="size-5" />
              </button>
            )}
            <button type="button" className={cn('child flex flex-1 items-center justify-center')}>
              {period === 'DAILY' ? (
                <span>{moment(startDate).format('MMM DD, YYYY')}</span>
              ) : (
                <span>
                  {moment(startDate)?.format('MMM DD')} - {moment(endDate)?.format('MMM DD, YYYY')}
                </span>
              )}
            </button>

            {period !== 'CUSTOM' && (
              <button
                className={cn(
                  'flex items-center justify-center px-2',
                  nextButtonDisabled ? 'bg-strong-950/5 opacity-40' : 'hover:bg-weak-50'
                )}
                onClick={(event) => {
                  event.stopPropagation();
                  onNextClick();
                }}
                disabled={nextButtonDisabled}
              >
                <RiArrowRightSLine className="size-5" />
              </button>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className="mt-1 w-auto p-0 !shadow-md" align="start">
          {period === 'DAILY' ? (
            <Calendar
              mode="single"
              onCancel={() => {
                setCalendarOpen(false);
              }}
              value={startDate ?? null}
              onApply={(value) => {
                setCalendarOpen(false);
                onDateChange(value, value);
              }}
            />
          ) : (
            <Calendar
              mode="range"
              onCancel={() => {
                setCalendarOpen(false);
              }}
              value={startDate && endDate ? [startDate, endDate] : null}
              onApply={(value) => {
                setCalendarOpen(false);

                if (!value) return;

                onDateChange(value[0], value[1]);
              }}
            />
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
