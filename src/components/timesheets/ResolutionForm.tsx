import { FormItem, FormLabel, FormControl } from '@/hammr-ui/components/form';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { useToast } from '@/hooks/useToast';
import { useQueryClient } from '@tanstack/react-query';
import { logError, showErrorToast } from '@/utils/errorHandling';
import CheckLine from '@/hammr-icons/CheckLine';
import CloseLine from '@/hammr-icons/CloseLine';
import { useState } from 'react';
import ArrowUpSLine from '@/hammr-icons/ArrowUpSLine';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import Button from '@/hammr-ui/components/button';
import dayjs from 'dayjs';
type ResolutionStyle = 'card' | 'collapsed';

interface ResolutionFormProps {
  resolveAlertCallback: (data: { resolutionDate: number; id: number; resolutionNote: string }) => Promise<void>;
  alertId: number;
  queryKey: string[];
  isItemResolved: boolean;
  resolvedByUser: { id: number; firstName: string; lastName: string };
  itemResolutionNote: string;
  resolvedByDate?: number;
  resolutionStyle?: ResolutionStyle;
}

const ResolvedCollapsedAlert = ({
  resolvedByUser,
  itemResolutionNote,
  resolutionNote,
}: {
  resolvedByUser: { id: number; firstName: string; lastName: string };
  itemResolutionNote: string;
  resolutionNote: string;
}) => {
  const [showResolutionNote, setShowResolutionNote] = useState(false);
  return (
    <div className="flex flex-col gap-1">
      <LinkButton
        className="flex items-center gap-1 text-primary-base"
        size="medium"
        onClick={(e) => {
          e.preventDefault();
          setShowResolutionNote(!showResolutionNote);
        }}
      >
        <CheckLine />
        {resolvedByUser?.firstName} {resolvedByUser?.lastName} resolved this alert
        {showResolutionNote ? <ArrowUpSLine /> : <ArrowDownSLine />}
      </LinkButton>
      {showResolutionNote && (
        <div className="text-sm font-normal text-sub-600">“{resolutionNote || itemResolutionNote}”</div>
      )}
    </div>
  );
};

const ResolvedCardAlert = ({
  resolvedByUser,
  itemResolutionNote,
  resolvedByDate,
}: {
  resolvedByUser: { id: number; firstName: string; lastName: string };
  itemResolutionNote: string;
  resolvedByDate: number;
}) => {
  return (
    <div className="flex flex-col gap-2 rounded-10 border border-soft-200 p-4">
      <div className="text-sm font-medium uppercase text-strong-950">Resolved</div>
      <div className="text-xs font-normal text-sub-600">
        {`By ${resolvedByUser?.firstName} ${resolvedByUser?.lastName} on ${dayjs(resolvedByDate).format('ddd, MMM D, YYYY, h:mm A')}`}
      </div>
      <div className="text-xs font-normal text-strong-950">“{itemResolutionNote}”</div>
    </div>
  );
};

const ResolutionForm = ({
  resolveAlertCallback,
  alertId,
  queryKey,
  isItemResolved,
  resolvedByUser,
  itemResolutionNote,
  resolvedByDate,
  resolutionStyle = 'card',
}: ResolutionFormProps) => {
  const { addToast } = useToast();
  const queryClient = useQueryClient();
  const [showResolutionNoteEditor, setShowResolutionNoteEditor] = useState(false);
  const [resolutionNote, setResolutionNote] = useState('');
  const [isResolved, setIsResolved] = useState(false);

  if (isItemResolved || isResolved) {
    return resolutionStyle === 'collapsed' ? (
      <ResolvedCollapsedAlert
        resolvedByUser={resolvedByUser}
        itemResolutionNote={itemResolutionNote}
        resolutionNote={resolutionNote}
      />
    ) : (
      <ResolvedCardAlert
        resolvedByUser={resolvedByUser}
        itemResolutionNote={itemResolutionNote}
        resolvedByDate={resolvedByDate}
      />
    );
  }

  if (!showResolutionNoteEditor) {
    return (
      <Button
        title="Resolve"
        className="mt-5"
        variant="outline"
        size="x-small"
        fullWidth
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setShowResolutionNoteEditor(!showResolutionNoteEditor);
        }}
      >
        Resolve Alert
      </Button>
    );
  }

  return (
    <FormItem className="relative mt-5">
      <FormLabel>Resolution Notes</FormLabel>
      <FormControl>
        <Textarea
          value={resolutionNote}
          onChange={(event) => {
            setResolutionNote(event.target.value);
          }}
        />
      </FormControl>
      <div className="absolute right-0 flex gap-2">
        <LinkButton
          size="small"
          className="flex gap-1"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setShowResolutionNoteEditor(false);
          }}
        >
          <CloseLine className="h-4 w-4" /> Cancel
        </LinkButton>
        <LinkButton
          size="small"
          className="flex gap-1 text-primary-base"
          disabled={!resolutionNote}
          onClick={async (e) => {
            if (!resolutionNote) {
              return;
            }

            e.preventDefault();
            e.stopPropagation();

            try {
              await resolveAlertCallback?.({
                resolutionDate: new Date().valueOf(),
                id: alertId,
                resolutionNote: resolutionNote,
              });

              addToast({
                type: 'success',
                title: 'Alert Resolved',
              });

              setShowResolutionNoteEditor(false);
              setIsResolved(true);

              queryClient.invalidateQueries({ queryKey: queryKey });
            } catch (error) {
              logError(error);
              showErrorToast(addToast);
            }
          }}
        >
          <CheckLine className="h-4 w-4" /> Resolve
        </LinkButton>
      </div>
    </FormItem>
  );
};

export default ResolutionForm;
