import { Badge } from '@/hammr-ui/components/badge';
import { CustomCellRendererProps } from '@ag-grid-community/react';
import { TimesheetStatus } from 'interfaces/timesheet';
import { useMemo } from 'react';

const StatusCell = (params: CustomCellRendererProps) => {
  // as status is a new value we need to set the default to an empty string as it may not be available in all timesheets
  const status = (params.data?.statusKey ?? '') as TimesheetStatus;

  return (
    <div>
      <StatusBadge statusKey={status} />
    </div>
  );
};

export const StatusBadge = ({ statusKey, className }: { statusKey: TimesheetStatus; className?: string }) => {
  const statusColor = useMemo(() => {
    switch (statusKey) {
      case 'SUBMITTED':
        return 'orange';
      case 'APPROVED':
        return 'blue';
      case 'PAID':
        return 'green';
      case 'CLOCKED_IN':
        return 'sky';
      default:
        return 'orange';
    }
  }, [statusKey]);

  return (
    <Badge color={statusColor} variant="lighter" className={className}>
      {statusKey.replace(/_/g, ' ')}
    </Badge>
  );
};

export default StatusCell;
