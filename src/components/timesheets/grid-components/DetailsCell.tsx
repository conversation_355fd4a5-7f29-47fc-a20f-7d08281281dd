import { Tooltip } from '@hammr-ui/components/tooltip';
import CompactButton from '@/hammr-ui/components/CompactButton';
import HistoryLine from '@/hammr-icons/HistoryLine';
import PencilLine from '@/hammr-icons/PencilLine';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';

export default function DetailsCell({ params, onEdit, onDelete, onHistory }) {
  return (
    <div key={params.data ? params.data.id : 'no-id'} className="relative flex items-center justify-end gap-2">
      {params.data?.hasTimesheetHistory ? (
        <Tooltip content="History">
          <CompactButton variant="ghost" size="large" onClick={onHistory}>
            <HistoryLine />
          </CompactButton>
        </Tooltip>
      ) : null}
      <Tooltip content="Edit">
        <CompactButton variant="ghost" size="large" onClick={onEdit}>
          <PencilLine />
        </CompactButton>
      </Tooltip>
      <Tooltip content="Delete">
        <CompactButton variant="ghost" size="large" onClick={onDelete}>
          <DeleteBinLine />
        </CompactButton>
      </Tooltip>
    </div>
  );
}
