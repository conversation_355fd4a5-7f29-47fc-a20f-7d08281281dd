import moment from 'moment';
import { ValueFormatterParams } from '@ag-grid-community/core';
import React from 'react';
import { useCompany } from 'hooks/useCompany';

const BreaksCell = ({ params }: { params: ValueFormatterParams }) => {
  const { company } = useCompany();

  // Real time breaks should only be shown if they are enabled, otherwise show break duration
  // If real time breaks are enabled but there's not a value for it while there's a break duration then show the break duration
  if (company.timeTrackingSettings.areRealtimeBreaksEnabled && Boolean(params.data.breaks.length)) {
    return params.data.breaks.map((b, index) => {
      return b?.end ? (
        <React.Fragment key={b.id}>
          {index > 0 && ', '}
          {moment(b.start).format('hh:mm a')}
          {' - '}
          {moment(b.end).format('hh:mm a')}
        </React.Fragment>
      ) : (
        <div key={b.id} className="inline-flex items-center gap-2">
          <div className="h-2 w-2 rounded-full bg-[#F2AE03]" />
          <span>Break started at {moment(b.start).format('h:mm a')}</span>
        </div>
      );
    });
  }

  if (params.data.breakDuration > 0) {
    return params.data.breakDuration / 60;
  }

  return '';
};

export default BreaksCell;
