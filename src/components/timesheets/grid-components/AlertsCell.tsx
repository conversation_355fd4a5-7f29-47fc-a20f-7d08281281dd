import { ValueFormatterParams } from '@ag-grid-community/core';
import { Badge } from '@/hammr-ui/components/badge';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import AlertLine from '@/hammr-icons/AlertLine';
import { Tooltip } from '@hammr-ui/components/tooltip';

export default function AlertsCell({ params }: { params: ValueFormatterParams }) {
  const { data } = params;

  return (
    <div className="relative flex h-full items-center gap-2">
      {data.hasUnresolvedAlerts && (
        <Tooltip content={'This timesheet has unresolved location alerts.'}>
          <span>
            <KeyIcon icon={<AlertLine />} color="yellow" />
          </span>
        </Tooltip>
      )}
      {data.isManual && (
        <Tooltip content={'Manual Timesheet'}>
          <Badge color="purple" variant="outline">
            M
          </Badge>
        </Tooltip>
      )}
    </div>
  );
}
