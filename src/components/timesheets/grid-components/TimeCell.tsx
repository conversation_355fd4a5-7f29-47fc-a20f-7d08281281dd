import { ValueFormatterParams } from '@ag-grid-community/core';
import moment from 'moment';

const TimeCell = ({ params }: { params: ValueFormatterParams }) => {
  const { data } = params;
  if (!data || !data.clockIn) {
    return null;
  }

  return data.clockOut ? (
    <div className="flex items-center justify-start">
      <span>{`${moment(data.clockIn).format('h:mm a')} -
            ${moment(data.clockOut).format('h:mm a')}`}</span>
    </div>
  ) : (
    <div className="inline-flex items-center gap-2">
      <div className="h-2 w-2 rounded-full bg-green-500" />
      <span>Clocked in at {moment(data.clockIn).format('h:mm a')}</span>
    </div>
  );
};

export default TimeCell;
