import { ICellRendererParams } from '@ag-grid-community/core';
import { getAllVisibleChildren } from 'utils/table';
import { UserTimesheet } from '@/interfaces/timesheet';
import TotalEarningsAndHours from '../TotalEarningsAndHours';

const CustomGroupCellRenderer = ({
  params,
  columnValueMappings,
}: {
  params: ICellRendererParams;
  columnValueMappings: any;
}) => {
  const colId = params.node.rowGroupColumn.getColId();
  const col = columnValueMappings.find((col) => col.field === colId);

  const render = col.cellRenderer ? col.cellRenderer(params) : params.value;

  const children: any[] = getAllVisibleChildren(params.node);
  const timesheets = children.map((child) => child.data as UserTimesheet);

  return (
    <div className="ml-1.5 flex items-center text-sm font-medium text-strong-950">
      <span className="w-[178px] truncate pr-4">{render}</span>
      <TotalEarningsAndHours timesheets={timesheets} className="[&_strong]:text-sub-600" />
    </div>
  );
};

export default CustomGroupCellRenderer;
