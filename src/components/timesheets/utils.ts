import moment from 'moment';
import { Break, UserTimesheet } from 'interfaces/timesheet';

import { isTimestampValue, propToHeaderMap } from './TimesheetHistoryCard';
import { UserLocationDetails } from 'interfaces/userlocation';
import { ValueFormatterParams } from '@ag-grid-community/core';
import { capitalize } from 'utils/stringHelper';
import dayjs from 'dayjs';
import { UseFormReturn } from 'react-hook-form';
import { formatMinutesToHoursWorked, minutesToFormattedTime } from '@/utils/format';

export const TIMESHEET_DATE_FORMAT = 'MM_DD_YYYY';

const COLUMNS_TO_EXPORT = [
  'workersCompCode',
  'employeeName',
  'position',
  'employeeId',
  'projectName',
  'projectNumber',
  'date',
  'costCodeName',
  'clockIn',
  'clockOut',
  'totalHours',
  'regularMinutes',
  'overtimeMinutes',
  'doubleOvertimeMinutes',
  'breakMinutes',
  'hourlyWage',
  'totalWages',
  'statusText',
  'description',
];

export type TimesheetObjectTable = UserTimesheet & {
  employeeName: string;
  projectName: string;
  date: string;
  costCodeName: string;
  totalHours: number;
  hourlyWage: number;
  projectNumber: string;
  employeeId: string;
  position: string;
};

function overrideDatePart(date: number, time: number) {
  const dateMoment = moment(date);
  const timeMoment = moment(time);

  timeMoment.set({
    year: dateMoment.get('year'),
    month: dateMoment.get('month'),
    date: dateMoment.get('date'),
    millisecond: 0,
  });

  if (pickTimePart(dateMoment.toDate().getTime()) > pickTimePart(timeMoment.toDate().getTime())) {
    timeMoment.add(1, 'day');
  }

  return timeMoment.toDate().getTime();
}

export function pickTimePart(date: number) {
  const d = new Date(date);
  d.setFullYear(1970);
  d.setMonth(0);
  d.setDate(0);
  return d.getTime();
}

export function transformBreaks(breaks: Break[], clockIn: number) {
  if (!breaks) return [];
  if (!breaks.length) return [];
  return breaks.map(({ start, end, ...rest }) => ({
    ...rest,
    start: overrideDatePart(clockIn, start),
    end: overrideDatePart(clockIn, end),
  }));
}

export function generateCardData(historyObj) {
  const { property, createdAt, createdBy } = historyObj;

  const cardHeader = propToHeaderMap[property];
  const cardMainData = generateMainCardData(historyObj);
  const cardFooter = `By ${createdBy.firstName} ${
    createdBy.lastName
  } on ${moment(createdAt).format('ddd, MMM D, YYYY, h:mm A')}`;

  return {
    cardHeader,
    cardMainData,
    cardFooter,
  };
}

export function generateMainCardData(historyObj) {
  const { type, property, previousValue, newValue } = historyObj;

  if (type === 'UPDATE') {
    // check if date, if so, convert
    if (isTimestampValue[property]) {
      const previousValueDate = moment(previousValue);
      const newValueDate = moment(newValue);

      const prevTime = previousValueDate.isValid() ? previousValueDate.format('h:mm A') : 'None';
      const nextTime = newValueDate.isValid() ? newValueDate.format('h:mm A') : 'None';

      // if the values are the same, let's also add the date too
      return previousValueDate.isValid() &&
        newValueDate.isValid() &&
        previousValueDate.format('MMM DD') !== newValueDate.format('MMM DD')
        ? `${previousValueDate.format('MMM DD, h:mm A')} → ${newValueDate.format('MMM DD, h:mm A')}`
        : `${prevTime} → ${nextTime}`;
    } else {
      return `${previousValue} → ${newValue}`;
    }
  }

  if (type === 'DELETE') {
    if (isTimestampValue[property]) {
      // previousValue is typically an object on deletes
      if (property === 'break-isDeleted') {
        const startDate = dayjs(previousValue?.start);
        const endDate = dayjs(previousValue?.end);
        return `Deleted (${
          startDate.isValid() ? startDate.format('h:mm A') : 'None'
        } - ${endDate.isValid() ? endDate.format('h:mm A') : 'None'})`;
      } else {
        const previousValueDate = moment(previousValue?.start);
        const newValueDate = moment(newValue?.end);
        return `Deleted (${
          previousValueDate.isValid() ? previousValueDate.format('h:mm A') : 'None'
        } - ${newValueDate.isValid() ? newValueDate.format('h:mm A') : 'None'})`;
      }
    } else {
      return `Deleted (${previousValue.name})`;
    }
  }

  if (type === 'CREATE') {
    if (isTimestampValue[property]) {
      const transformedPreviousValue = moment(previousValue);
      const transformedNewValue = moment(newValue);
      return `Created (${transformedPreviousValue.isValid() ? transformedPreviousValue.format('h:mm A') : 'None'}  - ${
        transformedNewValue.isValid() ? transformedNewValue.format('h:mm A') : 'None'
      })`;
    } else if (property === 'break-breakCreated') {
      const transformedNewStart = moment(newValue?.start);
      const transformedNewEnd = moment(newValue?.end);

      return `Created (${transformedNewStart.isValid() ? transformedNewStart.format('h:mm A') : 'None'} - ${
        transformedNewEnd.isValid() ? transformedNewEnd.format('h:mm A') : 'None'
      })`;
    }
    {
      return `Created (${previousValue} - ${newValue})`;
    }
  }
}

export const generateMapPathArray = (array: UserLocationDetails[], value: string): UserLocationDetails[][] => {
  const result = [];
  let subarray = [];

  for (const item of array) {
    if (item?.location?.latlng?.lat && item?.location?.latlng?.lng) {
      subarray.push(item);
      if (item.type === value) {
        result.push(subarray);
        subarray = [];
      }
    }
  }
  if (subarray.length > 0) {
    result.push(subarray);
  }

  return result;
};

export const svgParser = (svg: string) => {
  const parser = new DOMParser();
  const svgString = parser.parseFromString(svg, 'image/svg+xml').documentElement;
  return svgString;
};

export const getPinSvg = (color: string) => {
  const pinSvgString = `
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 21 20" fill="none">
      <circle cx="10.5" cy="10" r="10" fill="${color}" fill-opacity="0.5"/>
      <circle cx="10.5" cy="10" r="9.75" stroke="${color}" stroke-opacity="0.7" stroke-width="0.5"/>
      <circle cx="10.5" cy="10" r="3" fill="${color}"/>
    </svg>
  `;
  return pinSvgString;
};

export const getGreyPinSvg = () => {
  const greyPin = `
    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
      <g clip-path="url(#a)">
        <path d="M10.642 1.22a6.41 6.41 0 0 0-6.4 6.4c0 5.645 5.852 12.378 6.1 12.662a.4.4 0 0 0 .********* 0 0 0 .3-.138c.249-.289 6.1-7.137 6.1-12.662 0-3.528-2.872-6.4-6.4-6.4m0 4.4a2.4 2.4 0 1 1-.001 4.801 2.4 2.4 0 0 1 .001-4.801" fill="#3D4043"/>
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M.643.82h20v20h-20z"/>
        </clipPath>
      </defs>
    </svg>
  `;
  return greyPin;
};

export const locationDotSvg = () => {
  const locationDot = `
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#a)">
        <circle cx="8.738" cy="4.746" transform="rotate(-157.346 8.738 4.746)" fill="#fff" r="4"/>
        <path d="M5.508 3.398a3.5 3.5 0 1 1 6.46 2.697 3.5 3.5 0 0 1-6.46-2.697Z" stroke="#4682B4"/>
      </g>
      <defs>
        <filter id="a" x=".737" y=".745" width="16.002" height="16.002" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
          <feFlood flood-opacity="0" result="BackgroundImageFix"/>
          <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
          <feOffset dy="4"/>
          <feGaussianBlur stdDeviation="2"/>
          <feComposite in2="hardAlpha" operator="out"/>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2805_2416"/>
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_2805_2416" result="shape"/>
        </filter>
      </defs>
    </svg>
  `;
  return locationDot;
};

export const mapTimesheetsForTable = (userTimesheets: UserTimesheet[]): TimesheetObjectTable[] => {
  return userTimesheets.map((timesheetObj) => {
    const statusKey = timesheetObj.status;
    return {
      ...timesheetObj,
      employeeName: `${timesheetObj.user.firstName} ${timesheetObj.user.lastName}`,
      projectName: timesheetObj.project.name,
      date: moment(timesheetObj?.clockIn).format('ddd, MMM D'),
      costCodeName: timesheetObj.costCode?.name,
      // totalHours is in minutes, it's a misleading name but changing it involves changing and testing a lot of components
      totalHours: timesheetObj.totalMinutes,
      hourlyWage: timesheetObj.hourlyWage,
      projectNumber: timesheetObj.project.projectNumber,
      employeeId: timesheetObj.user.employeeId,
      position: timesheetObj.user.position,
      statusKey,
      statusText: capitalize(statusKey.split('_').join(' ').toLowerCase()),
      // we add clock in again as time - because there's already a clockIn column - this is done so that sorting works
      time: timesheetObj.clockIn,
    };
  });
};

// used for columns that can be grouped
export const customGroupValueFormatter = (params: ValueFormatterParams, defaultText = '', isExporting = false) => {
  const index = params.column.getColId();
  if (params.node.allChildrenCount > 0) {
    const isGroupedBy = params.node.allLeafChildren.every((row) => row.data[index] === params.node.key);

    if (!isGroupedBy && params.node.key) {
      return '';
    }

    const customHeaderValue = Boolean(params.node.key) ? params.node.key : defaultText;

    // if export append (Total)
    const header = !isExporting ? customHeaderValue : `${customHeaderValue} (Total)`;

    return customHeaderValue ? header : '';
  }
  return params.value ? params.value : defaultText;
};

export const getReportTitle = ({
  companyName,
  startDate,
  endDate,
}: {
  companyName: string;
  startDate: string;
  endDate: string;
}) => {
  return `${companyName} Timesheet Report ${startDate} - ${endDate}`;
};

// this function is used for PDF export
export const getColumnsToExportWidths = (columns: string[]): string[] => {
  const COLUMNS_WIDTHS = {
    employeeName: 6,
    statusText: 6,
    position: 6,
    employeeId: 4,
    projectName: 6,
    projectNumber: 5,
    date: 5,
    costCodeName: 4,
    clockIn: 4,
    clockOut: 4,
    totalHours: 5,
    regularMinutes: 4,
    overtimeMinutes: 4,
    doubleOvertimeMinutes: 4,
    breakMinutes: 5,
    hourlyWage: 5,
    totalWages: 5,
    description: 5,
    driveTimeMinutes: 4,
  };

  const columnWidths = columns.map((col) => COLUMNS_WIDTHS[col]);

  const totalWidthValues = columnWidths.reduce((acc, curr) => acc + curr, 0);
  const multiplier = 100 / totalWidthValues;
  const columnWidthsNormalized = columnWidths.map((width) => (width * multiplier).toFixed(2));

  return columnWidthsNormalized.map((width) => `${width}%`);
};

// @return {string[]} - the column ids of the columns to export
export const getColumnsToExportIds = (gridApi, includeDriveTimeMinutes = false, userRole = 'ADMIN') => {
  const allColumns = gridApi.getColumns();
  const groupedCols = gridApi.getState().rowGroup?.groupColIds;

  let columnsToExport = [...COLUMNS_TO_EXPORT];

  // For foreman users, exclude earnings columns
  if (userRole === 'FOREMAN') {
    const earningsColumns = ['hourlyWage', 'totalWages'];
    columnsToExport = columnsToExport.filter((col) => !earningsColumns.includes(col));
  }

  if (includeDriveTimeMinutes) {
    // Insert after 'doubleOvertimeMinutes'
    const doubleOvertimeMinutesIndex = columnsToExport.findIndex((col) => col === 'doubleOvertimeMinutes');
    if (doubleOvertimeMinutesIndex !== -1) {
      columnsToExport.splice(doubleOvertimeMinutesIndex + 1, 0, 'driveTimeMinutes');
    }
  }

  // filter for columns the user has hidden
  const filteredColumnIds = columnsToExport.filter((col) => {
    let modifiedColId = col;

    if (col === 'clockIn' || col === 'clockOut') {
      modifiedColId = 'time';
    } else if (col === 'projectNumber') {
      modifiedColId = 'projectName';
    } else if (col === 'employeeId' || col === 'position') {
      modifiedColId = 'employeeName';
    }

    // Check in both allColumns and groupedCols
    const column = allColumns.find((column) => column.getColId() === modifiedColId);
    const isInGroupedCols = groupedCols?.includes(modifiedColId);

    if (!column && !isInGroupedCols) {
      return false;
    }

    // If column exists, check visibility, if it's visible then show it
    // If it's not visible but it's in groupedCols then also include it
    // supressColumnsToolPanel is an heuristic to get the "hidden" columns, we only use this on TimesheetsTable
    // we also want to always show breakMinutes and employeeName even if hidden
    const shouldShow =
      isInGroupedCols ||
      (column &&
        (column.isVisible() ||
          column.getUserProvidedColDef().suppressColumnsToolPanel ||
          column.getColId() === 'breakMinutes' ||
          column.getColId() === 'employeeName'));

    return shouldShow;
  });

  if (groupedCols && groupedCols.length) {
    const sortedGroupCols = groupedCols.slice().sort((a, b) => {
      return groupedCols.indexOf(a) - groupedCols.indexOf(b);
    });

    const columnsWithoutGrouping = filteredColumnIds.filter((col) => sortedGroupCols.indexOf(col) === -1);

    return [...sortedGroupCols, ...columnsWithoutGrouping];
  } else {
    return filteredColumnIds;
  }
};

export const validateFormHours = ({
  formAttribute,
  formValues,
  index,
  form,
  breakDuration,
}: {
  formAttribute: 'hours' | 'otHours' | 'dotHours' | 'driveTimeHours';
  formValues: any;
  index: number;
  form: UseFormReturn<any>;
  breakDuration: number;
}) => {
  const workedHours = Number(formValues.timesheets[index].hours) - breakDuration;
  const otHours = Number(formValues.timesheets[index].otHours) || 0;
  const dotHours = Number(formValues.timesheets[index].dotHours) || 0;
  const driveHours = Number(formValues.timesheets[index].driveTimeHours) || 0;

  const humanReadableWorkHours = formatMinutesToHoursWorked(workedHours * 60);
  const humanReadableBreak = breakDuration > 0 ? ` with a ${(breakDuration * 60).toFixed(0)} minutes break` : '';
  const humanReadableWorkHoursWithBreak = `${humanReadableWorkHours}${humanReadableBreak}`;

  if (
    Number(otHours) + Number(dotHours) + Number(driveHours) > workedHours &&
    (formAttribute === 'hours' ||
      formAttribute === 'otHours' ||
      formAttribute === 'dotHours' ||
      formAttribute === 'driveTimeHours') &&
    Number(otHours) > 0 &&
    Number(dotHours) > 0 &&
    Number(driveHours) > 0
  ) {
    const delta = Number(otHours) + Number(dotHours) + Number(driveHours) - workedHours;
    const humanReadableDelta = formatMinutesToHoursWorked(delta * 60);
    const errorMessage = `Total Hours (${humanReadableWorkHoursWithBreak}) is ${humanReadableDelta} under the combined OT (${otHours}h), DOT (${dotHours}h), and Drive Time (${driveHours}h) hours`;
    form.setError(`timesheets.${index}.hours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.otHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.dotHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.driveTimeHours`, {
      type: 'manual',
      message: errorMessage,
    });
    return errorMessage;
  }

  if (
    Number(otHours) + Number(dotHours) > workedHours &&
    (formAttribute === 'hours' || formAttribute === 'otHours' || formAttribute === 'dotHours') &&
    Number(otHours) > 0 &&
    Number(dotHours) > 0
  ) {
    const delta = Number(otHours) + Number(dotHours) - workedHours;
    const humanReadableDelta = formatMinutesToHoursWorked(delta * 60);
    const errorMessage = `Total Hours (${humanReadableWorkHoursWithBreak}) is ${humanReadableDelta} under the combined OT (${otHours}h) and DOT (${dotHours}h) hours`;
    form.setError(`timesheets.${index}.otHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.dotHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.hours`, {
      type: 'manual',
      message: errorMessage,
    });
    return errorMessage;
  }

  if (
    Number(otHours) + Number(driveHours) > workedHours &&
    (formAttribute === 'hours' || formAttribute === 'otHours' || formAttribute === 'driveTimeHours') &&
    Number(otHours) > 0 &&
    Number(driveHours) > 0
  ) {
    const delta = Number(otHours) + Number(driveHours) - workedHours;
    const humanReadableDelta = formatMinutesToHoursWorked(delta * 60);
    const errorMessage = `Total Hours (${humanReadableWorkHoursWithBreak}) is ${humanReadableDelta} under the combined OT (${otHours}h) and Drive Time (${driveHours}h) hours`;
    form.setError(`timesheets.${index}.otHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.driveTimeHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.hours`, {
      type: 'manual',
      message: errorMessage,
    });
    return errorMessage;
  }

  if (
    Number(dotHours) + Number(driveHours) > workedHours &&
    (formAttribute === 'hours' || formAttribute === 'dotHours' || formAttribute === 'driveTimeHours') &&
    Number(dotHours) > 0 &&
    Number(driveHours) > 0
  ) {
    const delta = Number(dotHours) + Number(driveHours) - workedHours;
    const humanReadableDelta = formatMinutesToHoursWorked(delta * 60);
    const errorMessage = `Total Hours (${humanReadableWorkHoursWithBreak}) is ${humanReadableDelta} under the combined DOT (${dotHours}h) and Drive Time (${driveHours}h) hours`;
    form.setError(`timesheets.${index}.hours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.dotHours`, {
      type: 'manual',
      message: errorMessage,
    });
    form.setError(`timesheets.${index}.driveTimeHours`, {
      type: 'manual',
      message: errorMessage,
    });
    return errorMessage;
  }

  return null;
};

export const getBreakDuration = ({
  areRealtimeBreaksEnabled,
  formValues,
  index,
}: {
  areRealtimeBreaksEnabled: boolean;
  formValues: any;
  index: number;
}) => {
  let breakDuration = 0;
  if (areRealtimeBreaksEnabled) {
    formValues.timesheets[index].breaks.forEach((_break) => {
      breakDuration += _break.duration;
    });
  } else {
    breakDuration += (formValues.timesheets[index].breakDuration ?? 0) / 3600;
  }

  return breakDuration;
};
