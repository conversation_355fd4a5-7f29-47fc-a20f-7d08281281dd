import AlertFill from '@/hammr-icons/AlertFill';
import { AlertBadge2 } from './EditTimesheetModal';
import MapPinLine from '@/hammr-icons/MapPinLine';
import { useMemo, useState } from 'react';
import { logError, showErrorToast } from 'utils/errorHandling';
import { updateTimesheetAlert } from 'services/timesheet-alert';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import PencilLine from '@/hammr-icons/PencilLine';
import Button from '@/hammr-ui/components/button';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import CloseLine from '@/hammr-icons/CloseLine';
import CheckLine from '@/hammr-icons/CheckLine';
import { useToast } from '@/hooks/useToast';
import ArrowUpSLine from '@/hammr-icons/ArrowUpSLine';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { useQueryClient } from '@tanstack/react-query';
import MapComponent, { type Location, type Path, pinColors } from '@/hammr-ui/components/Map';
import { UserTimesheet } from '@/interfaces/timesheet';
import dayjs from 'dayjs';
import { has } from 'lodash';
import { EmptyStateLocation } from '@/hammr-icons/EmptyStateLocation';
import { LocationEvent, UserLocationDetails } from '@/interfaces/userlocation';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';

interface Props {
  timesheet: UserTimesheet;
}

export default function LocationForm({ timesheet }: Props) {
  const locations = useMemo(() => {
    const _locations: Location[] = [];
    timesheet.userLocations?.forEach((locationDetails) => {
      const locationEventLabel = locationDetails.locationEvent
        .split('_')
        .map((str) => str.toLowerCase())
        .map((str) => str.charAt(0).toUpperCase() + str.slice(1))
        .join(' '); // e.g. Clock In

      if (Array.isArray(locationDetails.locationCoordinates)) {
        _locations.push({
          lat: locationDetails.locationCoordinates[1],
          lng: locationDetails.locationCoordinates[0],
          locationEvent: locationDetails.locationEvent,
          markerInfo: {
            title: `${timesheet.user?.firstName} ${timesheet.user?.lastName}`,
            description: (
              <>
                {locationDetails.locationAddress}
                <br />
                {locationEventLabel}: {dayjs(locationDetails.loggedAt).format('h:mm A')}
              </>
            ),
          },
        });
      }
    });
    return _locations;
  }, [timesheet]);

  const paths = useMemo(() => {
    if (!timesheet.userLocations?.length) return [];

    // Filter locations that have valid coordinates and sort by timestamp
    const locationsWithCoords = timesheet.userLocations
      .filter((location) => Array.isArray(location.locationCoordinates))
      .sort((a, b) => a.loggedAt - b.loggedAt);

    if (locationsWithCoords.length < 2) return [];

    const pathSegments: Path[] = [];
    let currentPathPoints: google.maps.LatLngLiteral[] = [];

    for (let i = 0; i < locationsWithCoords.length; i++) {
      const location = locationsWithCoords[i];
      const point = {
        lat: location.locationCoordinates![1],
        lng: location.locationCoordinates![0],
      };

      // Add current point to the path
      currentPathPoints.push(point);

      // Check if we should break the path (don't connect CLOCK_OUT or BREAK_START to next point)
      const shouldBreakPath = location.locationEvent === 'CLOCK_OUT' || location.locationEvent === 'BREAK_START';
      const isLastPoint = i === locationsWithCoords.length - 1;

      if (shouldBreakPath || isLastPoint) {
        // End current path segment if we have at least 2 points
        if (currentPathPoints.length >= 2) {
          pathSegments.push({ points: [...currentPathPoints] });
        }
        // Start new path segment from this point (unless it's the last point)
        currentPathPoints = shouldBreakPath && !isLastPoint ? [point] : [];
      }
    }

    return pathSegments;
  }, [timesheet]);

  const [panToLocation, setPanToLocation] = useState<{ lat: number; lng: number; locationEvent: LocationEvent }>();

  return (
    <>
      {!timesheet.userLocations || timesheet.userLocations.length === 0 ? (
        <section className="flex h-full flex-col items-center justify-center">
          <EmptyStateLocation className="size-[120px]" />
          <p className="mt-5 h-fit text-soft-400">There are no locations in this timesheet.</p>
        </section>
      ) : timesheet.userLocations?.length === 1 &&
        timesheet.userLocations[0]?.timesheetAlert?.type === 'CLOCK_OUT_LOCATION_MISSING' ? (
        <section className="space-y-5 p-5">
          <div className="flex">
            <AlertFill className="text-warning-base" />
            <span className="ml-2 text-base text-[#708090]">No location history available for this timesheet</span>
          </div>
          <LocationAlertResolver item={timesheet.userLocations[0]} />
        </section>
      ) : (
        <section className="flex h-full flex-col">
          <div className="p-5 pb-4">
            <MapComponent
              locations={locations}
              paths={paths}
              className="h-[320px] rounded-10"
              panToLocation={panToLocation}
              disableClustering={true}
            />
          </div>
          <ScrollArea className="h-full px-5">
            {timesheet.userLocations
              ?.filter((item) => item?.locationEvent !== 'LOCATION_CHANGE')
              .map((item, i) => (
                <LocationItem
                  key={i}
                  item={item}
                  onClick={() => {
                    const latlng = Array.isArray(item.locationCoordinates)
                      ? getLocationLatLng(item.locationCoordinates)
                      : getLocationLatLng(item.locationCoordinates?.coordinates);
                    if (latlng) {
                      setPanToLocation({ ...latlng, locationEvent: item.locationEvent });
                    }
                  }}
                />
              ))}
          </ScrollArea>
        </section>
      )}
    </>
  );
}

interface LocationItemProps {
  item: UserLocationDetails;
  onClick?: () => void;
}

function LocationItem({ item, onClick }: LocationItemProps) {
  const time = dayjs(item?.loggedAt).format('h:mm A');
  const mapPinsColor = pinColors[item?.locationEvent];
  const horizontalAccuracyFt = item?.horizontalAccuracy ? Math.round(item?.horizontalAccuracy * FOOT_PER_METER) : null;
  const isWarn = has(item, 'timesheetAlert') && item?.timesheetAlert?.isResolved === false;

  return (
    <div className="mb-5 flex flex-col gap-2">
      <div
        className="flex items-start gap-3"
        style={{
          cursor: 'pointer',
          textDecoration: item?.strikeout ? 'line-through' : 'unset',
        }}
        onClick={onClick}
      >
        <div>
          <MapPinLine className="size-5" style={{ color: mapPinsColor }} />
        </div>

        <div className="flex flex-1 flex-col gap-1">
          <div className="flex items-center gap-2 text-sm font-medium text-strong-950">
            <span>
              {item?.locationEvent
                .toLowerCase()
                .split('_')
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ')}
            </span>
            {item?.isEdited && <PencilLine className="h-4 w-4 text-sub-600" />}

            {isWarn && <AlertBadge2 type={item?.timesheetAlert?.type} fit="full" />}
          </div>
          <div className="text-xs font-normal text-sub-600">{item?.geofenceProject?.name || item?.locationAddress}</div>
          <div className="text-xs font-normal text-sub-600">
            {horizontalAccuracyFt && `Accuracy: ${horizontalAccuracyFt} ft`}
          </div>
        </div>

        <div className="flex flex-col">
          <div className="text-sm font-normal text-sub-600">{time}</div>
        </div>
      </div>
      <LocationAlertResolver item={item} />
    </div>
  );
}

// Utility functions
const FOOT_PER_METER = 3.28084;

function getLocationLatLng(coordinates: number[] | undefined): { lat: number; lng: number } | null {
  if (Array.isArray(coordinates) && coordinates.length >= 2) {
    return { lat: coordinates[1], lng: coordinates[0] };
  }
  return null;
}

interface LocationAlertResolverProps {
  item: UserLocationDetails;
}

function LocationAlertResolver({ item }: LocationAlertResolverProps) {
  const queryClient = useQueryClient();

  const { addToast } = useToast();
  const [showResolutionNoteEditor, setShowResolutionNoteEditor] = useState(false);
  const [resolutionNote, setResolutionNote] = useState('');
  const [showResolutionNote, setShowResolutionNote] = useState(false);
  const [isResolved, setIsResolved] = useState(false);

  if (!item.timesheetAlert) {
    return null;
  }

  if (item.timesheetAlert.isResolved || isResolved) {
    const resolvedBy = item.timesheetAlert.resolvedByUser.firstName + ' ' + item.timesheetAlert.resolvedByUser.lastName;
    return (
      <div className="flex flex-col gap-1">
        <LinkButton
          className="flex items-center gap-1 text-primary-base"
          size="medium"
          onClick={(e) => {
            e.preventDefault();
            setShowResolutionNote(!showResolutionNote);
          }}
        >
          <CheckLine />
          {resolvedBy} resolved this alert
          {showResolutionNote ? <ArrowUpSLine /> : <ArrowDownSLine />}
        </LinkButton>
        {showResolutionNote && (
          <div className="text-sm font-normal text-sub-600">
            “{resolutionNote || item.timesheetAlert.resolutionNote}”
          </div>
        )}
      </div>
    );
  }

  if (!showResolutionNoteEditor) {
    return (
      <Button
        title="Resolve"
        variant="outline"
        size="x-small"
        fullWidth
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          setShowResolutionNoteEditor(!showResolutionNoteEditor);
        }}
      >
        Resolve
      </Button>
    );
  }

  // handler for updating alerts
  async function resolveAlert(data: { resolutionDate: number; resolutionNote: string; id: number }) {
    const { id, ...body } = data;

    const updateAlertBody = {
      isResolved: true,
      ...body,
    };

    await updateTimesheetAlert(id, updateAlertBody);
  }

  return (
    <FormItem className="relative">
      <FormLabel>Resolution Notes</FormLabel>
      <FormControl>
        <Textarea
          value={resolutionNote}
          onChange={(event) => {
            setResolutionNote(event.target.value);
          }}
        />
      </FormControl>
      <div className="absolute right-0 flex gap-2">
        <LinkButton
          size="small"
          className="flex gap-1"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setShowResolutionNoteEditor(false);
          }}
        >
          <CloseLine className="h-4 w-4" /> Cancel
        </LinkButton>
        <LinkButton
          size="small"
          className="flex gap-1 text-primary-base"
          onClick={async (e) => {
            if (!resolutionNote) {
              return;
            }

            e.preventDefault();
            e.stopPropagation();

            try {
              await resolveAlert({
                resolutionDate: new Date().valueOf(),
                id: item?.timesheetAlert?.id,
                resolutionNote: resolutionNote,
              });

              addToast({
                type: 'success',
                title: 'Alert Resolved',
              });

              setShowResolutionNoteEditor(false);
              setIsResolved(true);

              queryClient.invalidateQueries({ queryKey: ['timesheets'] });
            } catch (error) {
              logError(error);
              showErrorToast(addToast);
            }
          }}
        >
          <CheckLine className="h-4 w-4" /> Resolve
        </LinkButton>
      </div>
    </FormItem>
  );
}
