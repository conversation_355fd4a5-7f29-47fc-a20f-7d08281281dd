import FilePdf2Line from '@/hammr-icons/FilePdf2Line';
import FileExcelLine from '@/hammr-icons/FileExcelLine';
import { MenuItem } from '@/hammr-ui/components/Menu';
import { getColumnsToExportIds, getColumnsToExportWidths, TIMESHEET_DATE_FORMAT } from './utils';
import { exportToPDF } from '@/utils/table';
import { getReportTitle } from './utils';
import { useAuth } from '@/hooks/useAuth';
import { useCompany } from '@/hooks/useCompany';
import { Company } from '@/interfaces/company';
import { AgGridReact } from '@ag-grid-community/react';

interface TimesheetExportProps {
  gridRef: React.MutableRefObject<AgGridReact>;
  currentStartDateSelected: moment.Moment;
  currentEndDateSelected: moment.Moment;
  isExportingRef: React.MutableRefObject<boolean>;
}

const TimesheetExport = ({
  gridRef,
  currentStartDateSelected,
  currentEndDateSelected,
  isExportingRef,
}: TimesheetExportProps) => {
  const { company }: { company: Company } = useCompany();
  const { user } = useAuth();

  const handleTimesheetReportPDFExportAgGrid = () => {
    const title = getReportTitle({
      companyName: company.name,
      startDate: currentStartDateSelected.format(TIMESHEET_DATE_FORMAT),
      endDate: currentEndDateSelected.format(TIMESHEET_DATE_FORMAT),
    });

    const fileName = getFileName(gridRef.current.api, 'pdf');

    const columnsToExport = getColumnsToExportIds(
      gridRef.current.api,
      company.timeTrackingSettings.isDriveTimeEnabled,
      user?.role
    );
    const columnWidths = getColumnsToExportWidths(columnsToExport);

    exportToPDF(gridRef.current.api, fileName, title, columnsToExport, columnWidths, company, user?.role);
  };

  const handleTimesheetExportAgGrid = (format: 'csv' | 'pdf') => {
    isExportingRef.current = true;

    if (format === 'pdf') {
      handleTimesheetReportPDFExportAgGrid();
    } else {
      handleTimesheetReportCSVExportAgGrid();
    }

    // Use setTimeout to ensure endExport runs after the export is complete
    setTimeout(() => {
      isExportingRef.current = false;
    }, 0);
  };

  const getFileName = (gridApi, format: 'csv' | 'pdf') => {
    const startDate = currentStartDateSelected.format('MM_DD_YYYY');
    const endDate = currentEndDateSelected.format('MM_DD_YYYY');

    const groupedCols = gridApi.getState().rowGroup?.groupColIds;

    const groupedColsString = groupedCols ? `-groupings-${groupedCols.join('_')}` : '';

    return `${startDate}-${endDate}${groupedColsString}-timesheets_report.${format}`;
  };

  const handleTimesheetReportCSVExportAgGrid = async () => {
    const columnsToExport = getColumnsToExportIds(
      gridRef.current.api,
      company.timeTrackingSettings.isDriveTimeEnabled,
      user?.role
    );

    const fileName = getFileName(gridRef.current.api, 'csv');

    const title = getReportTitle({
      companyName: company.name,
      startDate: currentStartDateSelected.format(TIMESHEET_DATE_FORMAT),
      endDate: currentEndDateSelected.format(TIMESHEET_DATE_FORMAT),
    });

    const exportParams = {
      prependContent: title,
      columnKeys: columnsToExport,
      fileName,
    };

    gridRef.current!.api.exportDataAsCsv(exportParams);

    return;
  };

  const dropdownItems = [
    {
      name: 'Export as CSV',
      icon: <FileExcelLine className="text-sub-600" />,
      callback: () => handleTimesheetExportAgGrid('csv'),
    },
    {
      name: 'Export as PDF',
      icon: <FilePdf2Line className="text-sub-600" />,
      callback: () => handleTimesheetExportAgGrid('pdf'),
    },
  ];

  return dropdownItems.map((item, index) => (
    <MenuItem key={index} title={item.name} beforeContent={item.icon} onClick={item.callback} />
  ));
};

export default TimesheetExport;
