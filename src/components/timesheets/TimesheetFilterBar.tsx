import { MultiSelect } from '@/hammr-ui/components/multi-select';
import React, { FC, MutableRefObject, useMemo } from 'react';
import { useTimesheetListStore } from './store';
import { AgGridReact } from '@ag-grid-community/react';
import { uniqBy } from 'lodash';
import Button from '@/hammr-ui/components/button';
import { ScrollArea, ScrollBar } from '@/hammr-ui/components/scroll-area';
import { UserTimesheet } from '@/interfaces/timesheet';
import { CustomizeColumns } from '@/components/timesheets/CustomizeColumns';

interface TimesheetFilterBarProps {
  userTimesheets: UserTimesheet[];
  gridRef: MutableRefObject<AgGridReact>;
  hideProjectFilter?: boolean;
  hideWorkerFilter?: boolean;
}

export const TimesheetFilterBar: FC<TimesheetFilterBarProps> = ({
  gridRef,
  userTimesheets,
  hideProjectFilter = false,
  hideWorkerFilter = false,
}) => {
  const selectedStatusKeys = useTimesheetListStore((state) => state.selectedStatusKeys);
  const selectedEmployeeIds = useTimesheetListStore((state) => state.selectedEmployeeIds);
  const selectedProjectIds = useTimesheetListStore((state) => state.selectedProjectIds);
  const selectedCostCodeIds = useTimesheetListStore((state) => state.selectedCostCodeIds);
  const selectedHasAlerts = useTimesheetListStore((state) => state.selectedHasAlerts);

  const statusItems = useMemo(() => {
    const uniqueStatuses = Array.from(new Set(userTimesheets?.map((item) => item.status as string) ?? []));
    return (
      uniqueStatuses?.map((item) => ({
        label: item.replace(/_/g, ' '),
        value: item,
        isSelected: selectedStatusKeys.includes(item),
      })) ?? []
    );
  }, [userTimesheets, selectedStatusKeys]);

  const employeItems = useMemo(() => {
    return uniqBy(
      userTimesheets
        ?.filter((item) => !!item.user)
        ?.map((item) => ({
          label: item.user?.firstName + ' ' + item.user?.lastName,
          value: item.user.id,
          isSelected: selectedEmployeeIds.includes(item.user?.id),
        })) ?? [],
      (item) => item.value
    );
  }, [userTimesheets, selectedEmployeeIds]);

  const projectItems = useMemo(() => {
    return uniqBy(
      userTimesheets
        ?.filter((item) => !!item.project)
        ?.map((item) => ({
          label: item.project?.name + (item.project.projectNumber ? ` (${item.project.projectNumber})` : ''),
          value: item.project?.id,
          isSelected: selectedProjectIds.includes(item.project?.id),
        })) ?? [],
      (item) => item.value
    );
  }, [userTimesheets, selectedProjectIds]);

  const costCodeItems = useMemo(() => {
    return uniqBy(
      userTimesheets
        ?.filter((item) => !!item.costCode)
        ?.map((item) => ({
          label: item.costCode?.name,
          value: item.costCode?.id,
          isSelected: selectedCostCodeIds.includes(item.costCode?.id),
        })) ?? [],
      (item) => item.value
    );
  }, [userTimesheets, selectedCostCodeIds]);

  const hasAlertsItems = useMemo(() => {
    return [
      {
        label: 'Has Alerts',
        value: true,
        isSelected: selectedHasAlerts,
      },
    ];
  }, [selectedHasAlerts]);

  const hasFilters = useMemo(() => {
    if (hideProjectFilter) {
      // don't take into account the project filter if it's hidden
      return selectedStatusKeys.length + selectedEmployeeIds.length + selectedCostCodeIds.length > 0;
    }

    if (hideWorkerFilter) {
      // don't take into account the worker filter if it's hidden
      return selectedStatusKeys.length + selectedProjectIds.length + selectedCostCodeIds.length > 0;
    }

    return (
      selectedStatusKeys.length +
        selectedEmployeeIds.length +
        selectedProjectIds.length +
        selectedCostCodeIds.length +
        (selectedHasAlerts ? 1 : 0) >
      0
    );
  }, [
    selectedStatusKeys,
    selectedEmployeeIds,
    selectedProjectIds,
    selectedCostCodeIds,
    selectedHasAlerts,
    hideProjectFilter,
    hideWorkerFilter,
  ]);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between gap-3 pb-3 xl:pb-0">
        <div className="flex items-center gap-3 whitespace-nowrap">
          <MultiSelect
            buttonProps={{ className: 'w-[150px]' }}
            popoverProps={{ align: 'start', className: 'w-[200px]' }}
            label="Status"
            items={statusItems}
            onChange={(newItems) => {
              useTimesheetListStore.setState({
                selectedStatusKeys: newItems.filter((item) => item.isSelected).map((item) => item.value as string),
              });
              gridRef.current.api.onFilterChanged();
            }}
          />
          {!hideWorkerFilter && (
            <MultiSelect
              buttonProps={{ className: 'w-[150px]' }}
              popoverProps={{ align: 'start', className: 'w-[200px]' }}
              label="All Employees"
              items={employeItems}
              onChange={(newItems) => {
                useTimesheetListStore.setState({
                  selectedEmployeeIds: newItems.filter((item) => item.isSelected).map((item) => item.value as number),
                });
                gridRef.current.api.onFilterChanged();
              }}
            />
          )}
          {!hideProjectFilter && (
            <MultiSelect
              buttonProps={{ className: 'w-[150px]' }}
              popoverProps={{ align: 'start', className: 'w-[200px]' }}
              label="All Projects"
              items={projectItems}
              onChange={(newItems) => {
                useTimesheetListStore.setState({
                  selectedProjectIds: newItems.filter((item) => item.isSelected).map((item) => item.value as number),
                });
                gridRef.current.api.onFilterChanged();
              }}
            />
          )}
          <MultiSelect
            buttonProps={{ className: 'w-[150px]' }}
            popoverProps={{ align: 'start', className: 'w-[200px]' }}
            label="All Cost Codes"
            items={costCodeItems}
            onChange={(newItems) => {
              useTimesheetListStore.setState({
                selectedCostCodeIds: newItems.filter((item) => item.isSelected).map((item) => item.value as number),
              });
              gridRef.current.api.onFilterChanged();
            }}
          />

          <MultiSelect
            buttonProps={{ className: 'w-[150px]' }}
            popoverProps={{ align: 'start', className: 'w-[200px]' }}
            label="Alerts"
            searchable={false}
            items={hasAlertsItems}
            onChange={(newItems) => {
              useTimesheetListStore.setState({
                selectedHasAlerts: newItems.filter((item) => item.isSelected).length > 0,
              });
              gridRef.current.api.onFilterChanged();
            }}
          />

          {hasFilters && (
            <Button
              size="small"
              variant="outline"
              color="neutral"
              onClick={() => {
                clearAllTimesheetFilter(hideProjectFilter, hideWorkerFilter);
                gridRef.current.api.onFilterChanged();
              }}
            >
              Clear All
            </Button>
          )}
        </div>
        <div className="flex-shrink-0">
          <CustomizeColumns gridRef={gridRef} />
        </div>
      </div>
    </div>
  );
};

export function clearAllTimesheetFilter(hideProjectFilter: boolean, hideWorkerFilter: boolean) {
  if (hideProjectFilter) {
    // don't remove the project filter if it's hidden
    useTimesheetListStore.setState({
      selectedStatusKeys: [],
      selectedEmployeeIds: [],
      selectedCostCodeIds: [],
      selectedHasAlerts: false,
    });
  } else if (hideWorkerFilter) {
    // don't remove the worker filter if it's hidden
    useTimesheetListStore.setState({
      selectedStatusKeys: [],
      selectedProjectIds: [],
      selectedCostCodeIds: [],
      selectedHasAlerts: false,
    });
  } else {
    useTimesheetListStore.setState({
      selectedStatusKeys: [],
      selectedEmployeeIds: [],
      selectedProjectIds: [],
      selectedCostCodeIds: [],
      selectedHasAlerts: false,
    });
  }
}
