import { Dialog, DialogFooter, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { Break, TimesheetHistory, UserTimesheet } from '@/interfaces/timesheet';
import {
  Ri<PERSON>lertFill,
  RiArrowRightSLine,
  RiBillLine,
  RiCheckLine,
  RiCloseLine,
  RiMoneyDollarCircleLine,
  RiTimeLine,
  RiEdit2Line,
  RiArrowGoBackLine,
  RiSparkling2Line,
} from '@remixicon/react';
import { Dispatch, SetStateAction, useCallback, useEffect, useState } from 'react';
import * as TabMenuVertical from '@/components/ui/tab-menu-vertical';
import { Badge } from '@/hammr-ui/components/badge';
import { formatMinutesToHoursWorked } from '@/utils/format';
import dayjs from 'dayjs';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import TimesheetHistoryCard from './TimesheetHistoryCard';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { approveTimesheets, updateTimesheet } from '@/services/timesheet';
import LocationForm from './LocationForm';
import { ProjectPhotosCollection } from '@/interfaces/project-photo';
import Image from 'next/image';
import { diffObjects, rgbDataURL } from '@/utils/utils';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import Button from '@/hammr-ui/components/button';
import { StatusBadge } from './grid-components/StatusCell';
import { HammrUser } from '@/interfaces/user';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import ConfirmDeleteTimesheetModal from '@/components/timesheets/ConfirmDeleteTimesheetModal';
import { addToast } from '@/hooks/useToast';
import Spinner from '@/hammr-ui/components/spinner';
import { EmptytStateChatPhoto } from '@/hammr-ui/components/empty-states/ChatPhoto';
import { Controller, UseFormReturn, useForm } from 'react-hook-form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Combobox } from '@/hammr-ui/components/combobox';
import { RealtimeBreaks } from '@/components/timesheets/TimesheetModalForm/RealtimeBreaks';
import { ClockoutBreak } from '@/components/timesheets/TimesheetModalForm/ClockoutBreak';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { DateTimeInput } from '@/hammr-ui/components/datetime-input';
import { useEmployeesQuery } from '@/hooks/data-fetching/useEmployees';
import { Project } from '@/interfaces/project';
import { getEmployeeClassifications } from '@/services/classifications';
import { useCompany } from '@/hooks/useCompany';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { validateNetworkResponse } from '@/utils/errorHandling';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { sortListAlphabetically } from '@/utils/collectionHelpers';
import { CostCode } from '@/interfaces/cost-code';
import EmptyStateDailyWorkHours from '@/hammr-icons/EmptyStateDailyWorkHours';
import { useAuth } from '@/hooks/useAuth';
import { MediaViewer } from '@/components/project-photos/MediaViewer';
import { MediaViewerItem } from '@/components/MediaViewer';
import { useAwsS3 } from '@/hooks/useAwsS3';
import EmptyStateHRNotes from '@/hammr-icons/EmptyStateHRNotes';
import ResolutionForm from '@/components/timesheets/ResolutionForm';
import { transformBreaks } from '@/components/timesheets/utils';
import { Input } from '@/hammr-ui/components/input';
import { equipmentService } from '@/services/equipment';
import Alert from '@/hammr-ui/components/Alert';
import InfoCustomFill from '@/hammr-icons/InfoCustomFill';

const tabItems = [
  {
    name: 'General',
    value: 'general',
    component: GeneralTabContent,
  },
  {
    name: 'Earnings Breakdown',
    value: 'earning-breakdown',
    component: EarningsBreakDownTabContent,
  },
  {
    name: 'Timesheet History',
    value: 'timesheet-history',
    component: TimesheetHistoryTabContent,
  },
  {
    name: 'Location',
    value: 'location',
    component: LocationTabContent,
  },
  {
    name: 'Photos',
    value: 'photos',
    component: PhotosTabContent,
  },
  {
    name: 'Clock In/Out Photos',
    value: 'clock-in-out-photos',
    component: ClockInOutPhotosTabContent,
  },
  {
    name: 'Injury Report',
    value: 'injury-report',
    component: InjuryReportTabContent,
  },
] as const;

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  timesheet: UserTimesheet;
}

type FormValues = {
  userId: string | null;
  projectId: string | null;
  userClassificationId: string | null;
  costCodeId: string | null;
  workersCompCodeId: string | null;
  equipmentId: string | null;
  description: string;
  clockIn: Date | null;
  clockOut: Date | null;
  breaks: Break[];
  breakDuration: number;
  otHours: number | null;
  driveTimeHours: number;
  dotHours: number | null;
};

export default function TimesheetDetailsModal({ open, setOpen, timesheet }: Props) {
  const form = useForm<FormValues>();

  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (open) {
      const defaultValues: FormValues = {
        userId: timesheet.user?.id ? String(timesheet.user.id) : null,
        projectId: timesheet.project?.id ? String(timesheet.project.id) : null,
        userClassificationId: timesheet.userClassification?.id ? String(timesheet.userClassification.id) : null,
        costCodeId: timesheet.costCode?.id ? String(timesheet.costCode.id) : null,
        workersCompCodeId: timesheet.workersCompCode?.id ? String(timesheet.workersCompCode.id) : null,
        equipmentId: timesheet.equipmentId ? String(timesheet.equipmentId) : null,
        description: timesheet.description || '',
        clockIn: timesheet.clockIn ? dayjs(timesheet.clockIn).toDate() : null,
        clockOut: timesheet.clockOut ? dayjs(timesheet.clockOut).toDate() : null,
        breaks: timesheet.breaks ?? [],
        breakDuration: timesheet.breakDuration ?? null,
        otHours: timesheet.otDuration !== null ? timesheet.otDuration / 3600 : null,
        driveTimeHours: timesheet.driveTimeMinutes / 60,
        dotHours: timesheet.dotDuration !== null ? timesheet.dotDuration / 3600 : null,
      };
      form.reset(defaultValues);
    }
  }, [open, timesheet, form, isEditing]);

  const timesheetMutation = useMutation({
    mutationFn(formData: FormValues) {
      const isManualOvertimeApplied = formData.otHours !== null || formData.dotHours !== null;
      const updatedTimesheet = {
        ...formData,
        clockIn: formData.clockIn ? dayjs(formData.clockIn).valueOf() : null,
        clockOut: formData.clockOut ? dayjs(formData.clockOut).valueOf() : null,
        breaks: transformBreaks(formData.breaks, dayjs(formData.clockIn).valueOf()),
        // Handle null vs 0/number values properly
        otDuration: formData.otHours !== null ? formData.otHours * 3600 : null,
        driveTimeDuration: formData.driveTimeHours * 3600,
        dotDuration: formData.dotHours !== null ? formData.dotHours * 3600 : null,
        isManualOvertimeApplied,
        // the below fields are not needed by the API
        otHours: undefined,
        driveTimeHours: undefined,
        dotHours: undefined,
      };
      const diffedUpdatePayload = diffObjects(timesheet, updatedTimesheet);
      return updateTimesheet(timesheet.id, diffedUpdatePayload);
    },
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });

      const userName = `${timesheet?.user?.firstName} ${timesheet?.user?.lastName}`;
      addToast({
        title: 'Edited Timesheet Entry',
        description: (
          <>
            Successfully edited the timesheet entry for <span className="font-medium">{userName}</span>.
          </>
        ),
        type: 'success',
      });
      setIsEditing(false);
    },
  });

  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const queryClient = useQueryClient();

  const approveTimesheetMutation = useMutation({
    mutationFn: (timesheetIds: number[]) => approveTimesheets(timesheetIds),
    onSuccess() {
      addToast({
        title: 'Timesheets Approved',
        description: 'Successfully approved timesheets',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const unapproveTimesheetMutation = useMutation({
    mutationFn(timesheetIds: string[]) {
      return apiRequest('timesheets/unapprove', {
        method: 'POST',
        body: {
          timesheetIds,
        },
      });
    },
    onSuccess() {
      addToast({
        title: 'Timesheets Unapproved',
        description: 'Successfully unapproved timesheets',
        type: 'success',
      });
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    },
  });

  const [tab, setTab] = useState('general');

  const { user } = useAuth();
  const allowedTabItems = tabItems.filter((tabItem) => {
    if (tabItem.value === 'earning-breakdown' && user.role === 'FOREMAN') {
      return false;
    }
    return true;
  });

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (open === false) {
          setTab('general');
          setIsEditing(false);
        }
      }}
    >
      <DialogSurface className="max-w-[676px] rounded-20">
        <DialogHeader
          icon={<RiBillLine className="text-sub-600" />}
          title={
            <span className="flex items-center gap-3">
              {timesheet.user?.firstName} {timesheet.user?.lastName} ・ {dayjs(timesheet.clockIn).format('ddd, MMM D')}{' '}
              <StatusBadge statusKey={timesheet.status} />
            </span>
          }
          showCloseButton
        />

        <TabMenuVertical.Root className="grid min-h-[368px] grid-cols-[204px_auto]" value={tab} onValueChange={setTab}>
          <TabMenuVertical.List className="border-r border-soft-200 p-3">
            {allowedTabItems.map((item) => (
              <TabMenuVertical.Trigger value={item.value} className="data-[state=active]:bg-weak-50" key={item.value}>
                {item.name}
                {item.value === 'location' && timesheet.hasUnresolvedAlerts && (
                  <RiAlertFill className="size-5 text-away-base" />
                )}
                {item.value === 'injury-report' && timesheet.injuryReport && !timesheet.injuryReport.isResolved && (
                  <RiAlertFill className="size-5 text-away-base" />
                )}
                <TabMenuVertical.ArrowIcon as={RiArrowRightSLine} />
              </TabMenuVertical.Trigger>
            ))}
          </TabMenuVertical.List>

          {allowedTabItems.map((item) => {
            return (
              <TabMenuVertical.Content value={item.value} className="overflow-hidden" key={item.value}>
                {/* if it's location tab, don't show scroll area */}
                {item.value === 'location' ? (
                  <item.component timesheet={timesheet} />
                ) : (
                  <ScrollArea className="h-full">
                    {item.value === 'general' ? (
                      <item.component
                        timesheet={timesheet}
                        isEditing={isEditing}
                        form={form}
                        setIsEditing={setIsEditing}
                      />
                    ) : (
                      <item.component timesheet={timesheet} />
                    )}
                  </ScrollArea>
                )}
              </TabMenuVertical.Content>
            );
          })}
        </TabMenuVertical.Root>

        <DialogFooter>
          {timesheet.status === 'PAID' ? (
            <Tooltip content="Can't delete a timesheet that's already paid.">
              <span className="mr-auto flex">
                <LinkButton style="error" size="medium" disabled>
                  Delete Timesheet
                </LinkButton>
              </span>
            </Tooltip>
          ) : (
            <LinkButton style="error" size="medium" onClick={() => setShowDeleteConfirmModal(true)} className="mr-auto">
              Delete Timesheet
            </LinkButton>
          )}
          {isEditing ? (
            <>
              <Button size="small" onClick={() => setIsEditing(false)} variant="outline" color="neutral">
                Cancel
              </Button>
              <Button
                size="small"
                onClick={form.handleSubmit((formData) => timesheetMutation.mutate(formData))}
                disabled={timesheetMutation.isPending}
              >
                {timesheetMutation.isPending ? <Spinner /> : 'Save'}
              </Button>
            </>
          ) : (
            <>
              {tab === 'general' && (
                <>
                  {timesheet.status === 'PAID' ? (
                    <Tooltip content="Can't edit a timesheet that's already paid.">
                      <span>
                        <Button size="small" variant="outline" disabled>
                          Edit
                        </Button>
                      </span>
                    </Tooltip>
                  ) : (
                    <Button size="small" variant="outline" onClick={() => setIsEditing(true)}>
                      Edit
                    </Button>
                  )}
                </>
              )}

              {timesheet.status === 'CLOCKED_IN' ? (
                <Tooltip content="This timesheet is not ready to be approved.">
                  <span>
                    <Button size="small" beforeContent={<RiCheckLine />} disabled={true}>
                      Approve
                    </Button>
                  </span>
                </Tooltip>
              ) : timesheet.status === 'SUBMITTED' ? (
                <Button
                  size="small"
                  beforeContent={approveTimesheetMutation.isPending ? <Spinner /> : <RiCheckLine />}
                  onClick={() => approveTimesheetMutation.mutate([timesheet.id])}
                  disabled={approveTimesheetMutation.isPending}
                >
                  Approve
                </Button>
              ) : timesheet.status === 'APPROVED' ? (
                <Button
                  size="small"
                  beforeContent={unapproveTimesheetMutation.isPending ? <Spinner /> : <RiCloseLine />}
                  onClick={() => unapproveTimesheetMutation.mutate([timesheet.id.toString()])}
                  disabled={unapproveTimesheetMutation.isPending}
                >
                  Unapprove
                </Button>
              ) : null}
            </>
          )}
        </DialogFooter>
        <ConfirmDeleteTimesheetModal
          open={showDeleteConfirmModal}
          setOpen={setShowDeleteConfirmModal}
          currentRowData={timesheet}
          callback={() => {
            queryClient.invalidateQueries({ queryKey: ['timesheets'] });
            setOpen(false);
          }}
        />
      </DialogSurface>
    </Dialog>
  );
}

interface TabContentProps {
  timesheet: UserTimesheet;
}

interface GeneralTabContentProps {
  isEditing: boolean;
  setIsEditing: (isEditing: boolean) => void;
  form: UseFormReturn<FormValues>;
}

function GeneralTabContent({ timesheet, isEditing, setIsEditing, form }: TabContentProps & GeneralTabContentProps) {
  const workersQuery = useQuery({
    queryKey: ['workers'],
    queryFn: () => apiRequest<{ users: HammrUser[] }>('users'),
  });

  const approvedByUser = workersQuery.data?.users.find((user) => user.id === Number(timesheet.createdBy));

  const { company } = useCompany();

  const queryClient = useQueryClient();

  const resetManualOvertimeMutation = useMutation({
    mutationFn() {
      return updateTimesheet(timesheet.id, {
        otDuration: null,
        dotDuration: null,
        isManualOvertimeApplied: false,
      });
    },
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });
      const userName = `${timesheet?.user?.firstName} ${timesheet?.user?.lastName}`;
      addToast({
        title: 'Reset Overtime',
        description: (
          <>
            Overtime reset for <span className="font-medium">{userName}</span>. The system will now automatically
            calculate overtime.
          </>
        ),
        type: 'success',
      });
    },
  });

  if (!isEditing) {
    return (
      <article className="grid grid-cols-6 gap-4 p-5">
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Employee</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {timesheet.user?.firstName} {timesheet.user?.lastName}
          </p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Project</h3>
          <p className="mt-1.5 flex h-fit gap-1.5 text-sm text-strong-950">
            {timesheet.project?.name || '-'}{' '}
            {timesheet.project?.isPrevailingWage && (
              <Tooltip content="Prevailing Wage">
                <Badge variant="outline">PW</Badge>
              </Tooltip>
            )}
          </p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Cost Code</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{timesheet.costCode?.name || '-'}</p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Workers Comp Code</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {timesheet.workersCompCode?.code
              ? `${timesheet.workersCompCode?.code} ・ ${timesheet.workersCompCode?.name}`
              : '-'}
          </p>
        </section>
        {timesheet.project?.isPrevailingWage && (
          <section className="col-span-6">
            <h3 className="text-xs text-sub-600">Classification</h3>
            <p className="mt-1.5 h-fit text-sm text-strong-950">
              {timesheet.userClassification?.classification?.name || '-'}
            </p>
          </section>
        )}
        <section className="col-span-6">
          <h3 className="text-xs text-sub-600">Equipment</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{timesheet.equipment?.name || '-'}</p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Creation Mode</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {timesheet.isManual ? 'Manual Timesheet' : 'Time-tracking App'}
          </p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Created By</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {approvedByUser?.firstName} {approvedByUser?.lastName}
          </p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Status</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            <StatusBadge statusKey={timesheet.status} />
          </p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Approved At</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {timesheet.approvedAt ? dayjs(timesheet.approvedAt).format('MM/DD/YYYY, h:mm A') : '-'}
          </p>
        </section>
        <section className="col-span-6">
          <h3 className="text-xs text-sub-600">Notes</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{timesheet.description || '-'}</p>
        </section>
        <hr className="col-span-6 border-soft-200" />
        {timesheet.isManualOvertimeApplied ? (
          <Alert
            status="success"
            size="x-small"
            className="col-span-6 [&>span]:flex [&>span]:grow [&>span]:items-center [&>span]:justify-between"
            icon={<RiEdit2Line />}
          >
            <span>Manual Overtime Applied</span>
            <Tooltip
              content={
                <span className="font-medium">
                  Manual Overtime is applied on this timesheet. Click <strong className="font-bold">Reset</strong> to
                  remove manual values and let the system calculate overtime.
                </span>
              }
            >
              <span className="ml-2">
                <InfoCustomFill className="size-4 text-disabled-300" />
              </span>
            </Tooltip>
            <LinkButton
              className="ml-auto flex gap-1 text-strong-950"
              onClick={() => resetManualOvertimeMutation.mutate()}
            >
              Reset <RiArrowGoBackLine className="size-4" />
            </LinkButton>
          </Alert>
        ) : (
          <Alert
            status="information"
            size="x-small"
            className="col-span-6 [&>span]:flex [&>span]:grow [&>span]:items-center [&>span]:justify-between"
            icon={<RiSparkling2Line />}
          >
            <span>Overtime Calculated Automatically</span>
            <Tooltip
              content={
                <span className="font-medium">
                  Overtime is calculated automatically. Click <strong className="font-bold">Override</strong> to set it
                  manually on this timesheet.
                </span>
              }
            >
              <span className="ml-2">
                <InfoCustomFill className="size-4 text-disabled-300" />
              </span>
            </Tooltip>
            <LinkButton className="ml-auto flex gap-1 text-strong-950" onClick={() => setIsEditing(true)}>
              Override <RiArrowRightSLine className="size-4" />
            </LinkButton>
          </Alert>
        )}
        <div className="col-span-6 grid grid-cols-12 justify-between gap-4">
          <section className={company?.timeTrackingSettings.isDriveTimeEnabled ? 'col-span-3' : 'col-span-4'}>
            <h3 className="text-xs text-sub-600">Regular Hours</h3>
            <p className="mt-1.5 h-fit text-sm text-strong-950">
              {formatMinutesToHoursWorked(timesheet.regularMinutes ?? 0)}
            </p>
          </section>
          <section className={company?.timeTrackingSettings.isDriveTimeEnabled ? 'col-span-3' : 'col-span-4'}>
            <h3 className="text-xs text-sub-600">OT Hours</h3>
            <p className="mt-1.5 h-fit text-sm text-strong-950">
              {formatMinutesToHoursWorked(timesheet.overtimeMinutes ?? 0)}
            </p>
          </section>
          <section className={company?.timeTrackingSettings.isDriveTimeEnabled ? 'col-span-3' : 'col-span-4'}>
            <h3 className="text-xs text-sub-600">Double OT</h3>
            <p className="mt-1.5 h-fit text-sm text-strong-950">
              {formatMinutesToHoursWorked(timesheet.doubleOvertimeMinutes ?? 0)}
            </p>
          </section>

          {company?.timeTrackingSettings.isDriveTimeEnabled && (
            <section className="col-span-3">
              <h3 className="text-xs text-sub-600">Drive Time</h3>
              <p className="mt-1.5 h-fit text-sm text-strong-950">
                {formatMinutesToHoursWorked(timesheet.driveTimeMinutes)}
              </p>
            </section>
          )}
        </div>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Clock In</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            <RiTimeLine className="mr-2 size-5 text-sub-600" /> {dayjs(timesheet.clockIn).format('MM/DD/YYYY, h:mm A')}
          </p>
        </section>
        <section className="col-span-3">
          <h3 className="text-xs text-sub-600">Clock Out</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            <RiTimeLine className="mr-2 size-5 text-sub-600" />{' '}
            {timesheet.clockOut ? dayjs(timesheet.clockOut).format('MM/DD/YYYY, h:mm A') : '-'}
          </p>
        </section>
        {timesheet.breaks?.map((_break) => (
          <section className="col-span-6" key={_break.id}>
            <h3 className="text-xs text-sub-600">Break</h3>
            <p className="text-sm text-strong-950">
              <RiTimeLine className="mr-2 size-5 text-sub-600" /> {dayjs(_break.start).format('h:mm A')} -{' '}
              {dayjs(_break.end).format('h:mm A')} ・ {dayjs(_break.end).diff(_break.start, 'minutes')}min
            </p>
          </section>
        ))}
        {timesheet.breakDuration > 0 && (
          <section className="col-span-6">
            <h3 className="text-xs text-sub-600">Break</h3>
            <p className="text-sm text-strong-950">{timesheet.breakDuration / 60} min</p>
          </section>
        )}
      </article>
    );
  }

  return <GeneralForm form={form} />;
}

interface GeneralFormProps {
  form: UseFormReturn<FormValues>;
}

function GeneralForm({ form }: GeneralFormProps) {
  const employeesQuery = useEmployeesQuery();

  const projectsQuery = useQuery({
    queryKey: ['projects'],
    async queryFn() {
      const res = await apiRequest<{ projects: Project[] }>('projects');
      return sortListAlphabetically(res.projects, 'name');
    },
  });

  const selectedEmployeeId = form.watch('userId');
  const selectedProjectId = form.watch('projectId');
  const selectedProject = projectsQuery.data?.find((project) => project.id === Number(selectedProjectId));

  const classificationsQuery = useQuery({
    queryKey: ['classifications', selectedEmployeeId, selectedProject?.wageTableId],
    async queryFn() {
      const userClassifications = await getEmployeeClassifications({
        wageTableId: selectedProject?.wageTableId,
        userIds: [Number(selectedEmployeeId)], // selectedEmployeeId cannot be null
        active: true,
      });

      // select the classification by default if only one is found
      if (userClassifications.length === 1) {
        form.setValue('userClassificationId', String(userClassifications[0].id));
      }

      return userClassifications;
    },
    enabled: !!selectedEmployeeId && !!selectedProject?.wageTableId,
  });

  useEffect(() => {
    if (!classificationsQuery.data) return;

    form.trigger('projectId');
  }, [classificationsQuery.data, form]);

  const costCodesQuery = useQuery({
    queryKey: ['costCodes'],
    async queryFn() {
      const res = await apiRequest<{ costCodes: CostCode[] }>('cost-codes');
      return sortListAlphabetically(res.costCodes, 'name');
    },
  });

  const { company } = useCompany();

  // Show both archived and non-archived WC code in Edit Timesheet modal
  const workersCompCodes = useQuery({
    queryKey: ['workersCompCodes', 'all'],
    queryFn: () => workersCompCodesService.get(true),
  });

  const equipmentQuery = useQuery({
    queryKey: ['equipment'],
    queryFn: () => equipmentService.getAll({ isArchived: false }),
  });

  const workersQuery = useQuery({
    queryKey: ['workers'],
    queryFn: () => apiRequest<{ users: HammrUser[] }>('users'),
  });

  return (
    <section className="flex flex-col gap-5 p-5">
      <Controller
        name="userId"
        control={form.control}
        rules={{ required: 'Please select an employee' }}
        render={({ field }) => (
          <FormItem required error={!!form.formState.errors['userId']} disabled>
            <FormLabel>Employee</FormLabel>
            <FormControl>
              <Combobox
                placeholder={employeesQuery.isPending ? 'Fetching employees...' : 'Select an employee'}
                emptyMessage="No employees found"
                value={field.value}
                items={
                  employeesQuery.data?.map((employee) => ({
                    label: `${employee.firstName} ${employee.lastName}`,
                    value: employee.id.toString(),
                  })) ?? []
                }
                onChange={(value) => {
                  const selectedEmployee = employeesQuery.data?.find((item) => item.id === Number(value));
                  if (!form.watch('workersCompCodeId') && selectedEmployee?.workersCompCode?.id) {
                    form.setValue('workersCompCodeId', String(selectedEmployee.workersCompCode.id));
                  }

                  field.onChange(value);
                }}
              />
            </FormControl>
            <FormMessage>{form.formState.errors['userId']?.message}</FormMessage>
          </FormItem>
        )}
      />

      <Controller
        name="projectId"
        control={form.control}
        rules={{
          required: 'Please select a project',
          validate(_, formValues) {
            // this avoids false positives that an employee doesn't have any classifications, while the data itself has not loaded yet
            if (!classificationsQuery.data) return;

            // Only show classification error for prevailing wage projects
            const selectedProject = projectsQuery.data?.find((project) => project.id === Number(formValues.projectId));
            if (selectedProject?.isPrevailingWage && classificationsQuery.data.length === 0) {
              const user = workersQuery.data?.users.find((user) => user.id === Number(formValues.userId));
              return `${user?.firstName} ${user?.lastName} is not assigned a classification on this project.`;
            }
          },
        }}
        render={({ field }) => (
          <FormItem required error={!!form.formState.errors['projectId']}>
            <FormLabel>Project</FormLabel>
            <FormControl>
              <Combobox
                placeholder={projectsQuery.isPending ? 'Fetching projects...' : 'Select an option'}
                value={field.value}
                items={
                  projectsQuery.data?.map((project) => ({
                    label: (
                      <span className="flex gap-1">
                        <span className="truncate">
                          {project.name + (project.projectNumber ? ` (${project.projectNumber})` : '')}
                        </span>
                        {project.isPrevailingWage && (
                          <Tooltip content="Prevailing Wage">
                            <Badge variant="outline" color="gray">
                              PW
                            </Badge>
                          </Tooltip>
                        )}
                      </span>
                    ),
                    value: project.id.toString(),
                  })) ?? []
                }
                onChange={(value) => {
                  field.onChange(value);

                  // if there was a classification selected previously clear it
                  form.setValue('userClassificationId', null);
                }}
              />
            </FormControl>
            <FormMessage>{form.formState.errors['projectId']?.message}</FormMessage>
          </FormItem>
        )}
      />

      {Boolean(classificationsQuery.data?.length) && (
        <Controller
          name="userClassificationId"
          control={form.control}
          rules={{ required: 'Please select a classification' }}
          render={({ field }) => (
            <FormItem
              required
              error={!!form.formState.errors['userClassificationId']}
              disabled={classificationsQuery.data?.length === 0}
            >
              <FormLabel>Classification</FormLabel>
              <FormControl>
                <Combobox
                  placeholder={!classificationsQuery.data?.length ? 'No classifications found' : 'Select an option'}
                  value={field.value}
                  items={
                    classificationsQuery.data?.map((classification) => ({
                      label: classification.classification.name,
                      value: classification.id.toString(),
                    })) ?? []
                  }
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage>{form.formState.errors['userClassificationId']?.message}</FormMessage>
            </FormItem>
          )}
        />
      )}

      <Controller
        name="costCodeId"
        control={form.control}
        rules={{
          required: company?.timeTrackingSettings.isCostCodeRequired ? 'Please select a cost code' : false,
        }}
        render={({ field }) => (
          <FormItem
            error={!!form.formState.errors['costCodeId']}
            required={company?.timeTrackingSettings.isCostCodeRequired}
          >
            <FormLabel>Cost Code</FormLabel>
            <FormControl>
              <Combobox
                placeholder="Select an option"
                value={field.value}
                items={
                  costCodesQuery.data?.map((item) => ({
                    label: item.name,
                    value: item.id.toString(),
                  })) ?? []
                }
                onChange={(value) => {
                  field.onChange(value || null);

                  const workersCompCodeId = form.getValues('workersCompCodeId');
                  if (!workersCompCodeId) {
                    const selectedCostCode = costCodesQuery.data?.find((code) => code.id === Number(value));
                    if (selectedCostCode?.workersCompCode?.id) {
                      form.setValue('workersCompCodeId', String(selectedCostCode.workersCompCode.id));
                    }
                  }
                }}
              />
            </FormControl>
            <FormMessage>{form.formState.errors['costCodeId']?.message}</FormMessage>
          </FormItem>
        )}
      />

      <Controller
        name="workersCompCodeId"
        control={form.control}
        render={({ field }) => {
          return (
            <FormItem error={!!form.formState.errors['workersCompCodeId']}>
              <FormLabel>Workers Comp Code</FormLabel>
              <FormControl>
                <Combobox
                  placeholder="Select an option"
                  value={field.value}
                  items={
                    workersCompCodes.data?.map((item: WorkersCompCode) => ({
                      label: `${item.name} • ${item.code}`,
                      value: item.id.toString(),
                    })) ?? []
                  }
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage>{form.formState.errors['workersCompCodeId']?.message}</FormMessage>
            </FormItem>
          );
        }}
      />

      <Controller
        name="equipmentId"
        control={form.control}
        render={({ field }) => {
          return (
            <FormItem error={!!form.formState.errors['equipmentId']}>
              <FormLabel>Equipment</FormLabel>
              <FormControl>
                <Combobox
                  placeholder="Select an option"
                  value={field.value}
                  items={
                    equipmentQuery.data?.map((singleEquipment) => ({
                      label: singleEquipment.name,
                      value: singleEquipment.id.toString(),
                    })) ?? []
                  }
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage>{form.formState.errors['equipmentId']?.message}</FormMessage>
            </FormItem>
          );
        }}
      />

      <Controller
        name="description"
        control={form.control}
        render={({ field }) => (
          <FormItem error={!!form.formState.errors['description']}>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea value={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage>{form.formState.errors['description']?.message}</FormMessage>
          </FormItem>
        )}
      />

      <Controller
        name="clockIn"
        control={form.control}
        render={({ field }) => (
          <FormItem error={!!form.formState.errors['clockIn']}>
            <FormLabel>Clock In</FormLabel>
            <FormControl>
              <DateTimeInput
                value={field.value}
                onChange={(_value) => {
                  field.onChange(_value);

                  // set the clock out day to be same as clock in if it was same before
                  const clockOut = form.getValues('clockOut');
                  const isSameDay = dayjs(field.value).isSame(dayjs(clockOut), 'day');
                  if (_value && isSameDay) {
                    const newClockOut = dayjs(_value)
                      .set('hour', dayjs(clockOut).hour())
                      .set('minute', dayjs(clockOut).minute());
                    form.setValue('clockOut', newClockOut.toDate());
                  }
                }}
              />
            </FormControl>
            <FormMessage>{form.formState.errors['clockIn']?.message}</FormMessage>
          </FormItem>
        )}
      />

      <Controller
        name="clockOut"
        control={form.control}
        render={({ field }) => (
          <FormItem error={!!form.formState.errors['clockOut']}>
            <FormLabel>Clock Out</FormLabel>
            <FormControl>
              <DateTimeInput
                value={field.value}
                onChange={(_value) => {
                  field.onChange(_value);
                }}
              />
            </FormControl>
            <FormMessage>{form.formState.errors['clockOut']?.message}</FormMessage>
          </FormItem>
        )}
      />

      {/* Always show OT and DOT fields */}
      <Controller
        name="otHours"
        control={form.control}
        rules={{
          validate(otHours, formValues) {
            // Skip validation if otHours is null (automatic OT)
            if (otHours === null || otHours === undefined) {
              return;
            }

            const timesheetDuration = dayjs(formValues.clockOut).diff(dayjs(formValues.clockIn), 'hour', true);

            let breakDuration = 0;
            if (company?.timeTrackingSettings.areRealtimeBreaksEnabled) {
              formValues.breaks.forEach((_break) => {
                breakDuration += (_break.end - _break.start) / 3600000;
              });
            } else {
              breakDuration += (formValues.breakDuration ?? 0) / 3600;
            }

            const workedHours = timesheetDuration - breakDuration;
            const dotHours = Number(formValues.dotHours) || 0;
            const driveHours = Number(formValues.driveTimeHours) || 0;
            const humanReadableWorkHours = formatMinutesToHoursWorked(workedHours * 60);
            const humanReadableBreak =
              breakDuration > 0 ? ` with a ${(breakDuration * 60).toFixed(0)} minutes break` : '';
            const humanReadableWorkHoursWithBreak = `${humanReadableWorkHours}${humanReadableBreak}`;

            if (otHours > workedHours) {
              return `OT Hours (${Number(otHours).toFixed(2)}h) must be less than total work hours (${humanReadableWorkHoursWithBreak})`;
            }

            if (Number(otHours) + Number(dotHours) + Number(driveHours) > workedHours) {
              const delta = Number(otHours) + Number(dotHours) + Number(driveHours) - workedHours;
              const deltaHours = formatMinutesToHoursWorked(delta * 60);
              return `Total Work hours (${humanReadableWorkHoursWithBreak}) is ${deltaHours} under OT hours (${Number(otHours).toFixed(2)}h) ${Number(dotHours) > 0 ? `plus DOT hours ${Number(dotHours).toFixed(2)}h` : ''} ${Number(driveHours) > 0 ? `plus Drive Time hours ${Number(driveHours).toFixed(2)}h` : ''}`;
            }
          },
        }}
        render={({ field }) => (
          <FormItem error={!!form.formState.errors['otHours']}>
            <FormLabel>OT Hours</FormLabel>
            <FormControl>
              <Input
                value={field.value ?? ''}
                onChange={(e) => {
                  const value = e.target.value;
                  // Set to null if empty, otherwise convert to number
                  field.onChange(value === '' ? null : Number(value));
                  form.clearErrors('dotHours');
                  form.clearErrors('driveTimeHours');
                }}
                type="number"
                placeholder="Auto calculated"
              />
            </FormControl>
            <FormMessage>{form.formState.errors['otHours']?.message}</FormMessage>
          </FormItem>
        )}
      />

      <Controller
        name="dotHours"
        control={form.control}
        rules={{
          validate(dotHours, formValues) {
            // Skip validation if dotHours is null (automatic OT)
            if (dotHours === null || dotHours === undefined) {
              return;
            }

            const timesheetDuration = dayjs(formValues.clockOut).diff(dayjs(formValues.clockIn), 'hour', true);

            let breakDuration = 0;
            if (company?.timeTrackingSettings.areRealtimeBreaksEnabled) {
              formValues.breaks.forEach((_break) => {
                breakDuration += (_break.end - _break.start) / 3600000;
              });
            } else {
              breakDuration += (formValues.breakDuration ?? 0) / 3600;
            }

            const workedHours = timesheetDuration - breakDuration;
            const otHours = Number(formValues.otHours) || 0;
            const driveHours = Number(formValues.driveTimeHours) || 0;
            const humanReadableWorkHours = formatMinutesToHoursWorked(workedHours * 60);
            const humanReadableBreak =
              breakDuration > 0 ? ` with a ${(breakDuration * 60).toFixed(0)} minutes break` : '';
            const humanReadableWorkHoursWithBreak = `${humanReadableWorkHours}${humanReadableBreak}`;

            if (dotHours > workedHours) {
              return `DOT hours (${Number(dotHours).toFixed(2)}h) must be less than work hours (${humanReadableWorkHoursWithBreak})`;
            }

            if (Number(otHours) + Number(dotHours) + Number(driveHours) > workedHours) {
              const delta = Number(otHours) + Number(dotHours) + Number(driveHours) - workedHours;
              const deltaHours = formatMinutesToHoursWorked(delta * 60);
              return `Total Work hours (${humanReadableWorkHoursWithBreak}) is ${deltaHours} under OT hours (${Number(otHours).toFixed(2)}h) ${Number(dotHours) > 0 ? `plus DOT hours ${Number(dotHours).toFixed(2)}h` : ''} ${Number(driveHours) > 0 ? `plus Drive Time hours ${Number(driveHours).toFixed(2)}h` : ''}`;
            }
          },
        }}
        render={({ field }) => (
          <FormItem error={!!form.formState.errors['dotHours']}>
            <FormLabel>DOT Hours</FormLabel>
            <FormControl>
              <Input
                value={field.value ?? ''}
                onChange={(e) => {
                  const value = e.target.value;
                  // Set to null if empty, otherwise convert to number
                  field.onChange(value === '' ? null : Number(value));
                  form.clearErrors('otHours');
                  form.clearErrors('driveTimeHours');
                }}
                type="number"
                placeholder="Auto calculated"
              />
            </FormControl>
            <FormMessage>{form.formState.errors['dotHours']?.message}</FormMessage>
          </FormItem>
        )}
      />

      {company?.timeTrackingSettings.isDriveTimeEnabled && (
        <Controller
          name="driveTimeHours"
          control={form.control}
          rules={{
            validate(driveTimeHours, formValues) {
              const timesheetDuration = dayjs(formValues.clockOut).diff(dayjs(formValues.clockIn), 'hour', true);

              let breakDuration = 0;
              if (company?.timeTrackingSettings.areRealtimeBreaksEnabled) {
                formValues.breaks.forEach((_break) => {
                  breakDuration += (_break.end - _break.start) / 3600000;
                });
              } else {
                breakDuration += (formValues.breakDuration ?? 0) / 3600;
              }

              const workedHours = timesheetDuration - breakDuration;
              const otHours = Number(formValues.otHours) || 0;
              const driveHours = Number(formValues.driveTimeHours) || 0;
              const dotHours = Number(formValues.dotHours) || 0;
              const humanReadableWorkHours = formatMinutesToHoursWorked(workedHours * 60);
              const humanReadableBreak =
                breakDuration > 0 ? ` with a ${(breakDuration * 60).toFixed(0)} minutes break` : '';
              const humanReadableWorkHoursWithBreak = `${humanReadableWorkHours}${humanReadableBreak}`;

              if (driveTimeHours > workedHours) {
                return `Drive Time hours (${Number(driveTimeHours).toFixed(2)}h) must be less than work hours (${humanReadableWorkHoursWithBreak})`;
              }

              if (Number(otHours) + Number(dotHours) + Number(driveHours) > workedHours) {
                const delta = Number(otHours) + Number(dotHours) + Number(driveHours) - workedHours;
                const deltaHours = formatMinutesToHoursWorked(delta * 60);
                return `Total Work hours (${humanReadableWorkHoursWithBreak}) is ${deltaHours} under OT hours (${Number(otHours).toFixed(2)}h) ${Number(dotHours) > 0 ? `plus DOT hours ${Number(dotHours).toFixed(2)}h` : ''} ${Number(driveHours) > 0 ? `plus Drive Time hours ${Number(driveHours).toFixed(2)}h` : ''}`;
              }
            },
          }}
          render={({ field }) => (
            <FormItem error={!!form.formState.errors['driveTimeHours']}>
              <FormLabel>Drive Hours</FormLabel>
              <FormControl>
                <Input
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e);
                    form.clearErrors('otHours');
                    form.clearErrors('dotHours');
                  }}
                  type="number"
                />
              </FormControl>
              <FormMessage>{form.formState.errors['driveTimeHours']?.message}</FormMessage>
            </FormItem>
          )}
        />
      )}

      {company?.timeTrackingSettings.areRealtimeBreaksEnabled && <RealtimeBreaks form={form as any} />}
      {company &&
        !company?.timeTrackingSettings.areRealtimeBreaksEnabled &&
        Boolean(company?.timeTrackingSettings.breakOptions.length) && (
          <ClockoutBreak form={form as any} company={company} />
        )}
    </section>
  );
}

function EarningsBreakDownTabContent({ timesheet }: TabContentProps) {
  const { company } = useCompany();
  const totalMinutes =
    (timesheet.regularMinutes ?? 0) +
    (timesheet.overtimeMinutes ?? 0) +
    (timesheet.doubleOvertimeMinutes ?? 0) +
    (timesheet.driveTimeMinutes ?? 0);

  return (
    <article className="p-5">
      <section className="grid grid-cols-2">
        <div className="flex items-center gap-2">
          <KeyIcon icon={<RiTimeLine />} size="small" />
          <div>
            <h3 className="text-xs font-medium text-soft-400">TOTAL HOURS</h3>
            <p className="text-lg font-medium text-strong-950">{formatMinutesToHoursWorked(totalMinutes)}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <KeyIcon icon={<RiMoneyDollarCircleLine />} size="small" />
          <div>
            <h3 className="text-xs font-medium text-soft-400">TOTAL EARNINGS</h3>
            <p className="text-lg font-medium text-strong-950">${timesheet.totalWages?.toFixed(2)}</p>
          </div>
        </div>
      </section>
      <table className="mt-8 w-full [&_td]:text-sm [&_tr]:mt-3 [&_tr]:grid [&_tr]:grid-cols-4 [&_tr]:gap-x-4">
        <thead>
          <tr className="!mt-0 text-left text-sm text-sub-600 [&>th]:p-0 [&>th]:font-normal">
            <th></th>
            <th>Rate</th>
            <th>Hours</th>
            <th>Earnings</th>
          </tr>
        </thead>
        <tbody className="[&_td:first-child]:font-medium">
          <tr className="border-t border-soft-200" />
          <tr>
            <td>Regular</td>
            <td>${timesheet.hourlyWage?.toFixed(2)}/hr</td>
            <td>{formatMinutesToHoursWorked(timesheet.regularMinutes ?? 0)}</td>
            <td>${timesheet.regularWages?.toFixed(2)}</td>
          </tr>
          <tr className="border-t border-soft-200" />
          <tr>
            <td>OT</td>
            <td>${timesheet.otHourlyWage?.toFixed(2)}/hr</td>
            <td>{formatMinutesToHoursWorked(timesheet.overtimeMinutes ?? 0)}</td>
            <td>${timesheet.overtimeWages?.toFixed(2)}</td>
          </tr>
          <tr className="border-t border-soft-200" />
          <tr>
            <td>DOT</td>
            <td>${timesheet.dotHourlyWage?.toFixed(2)}/hr</td>
            <td>{formatMinutesToHoursWorked(timesheet.doubleOvertimeMinutes ?? 0)}</td>
            <td>${timesheet.doubleOvertimeWages?.toFixed(2)}</td>
          </tr>
          {company?.timeTrackingSettings.isDriveTimeEnabled && (
            <>
              <tr className="border-t border-soft-200" />
              <tr>
                <td>Drive Time</td>
                <td>${company?.timeTrackingSettings.driveTimeRate}/hr</td>
                <td>{formatMinutesToHoursWorked(timesheet.driveTimeMinutes ?? 0)}</td>
                <td>${timesheet.driveTimeWages?.toFixed(2)}</td>
              </tr>
            </>
          )}
        </tbody>
        <tfoot>
          <tr className="border border-soft-200" />
          <tr className="[&>td]:font-medium">
            <td>Total</td>
            <td></td>
            <td>{formatMinutesToHoursWorked(totalMinutes)}</td>
            <td>${timesheet.totalWages?.toFixed(2)}</td>
          </tr>
        </tfoot>
      </table>
    </article>
  );
}

function TimesheetHistoryTabContent({ timesheet }: TabContentProps) {
  const timesheetHistories = useQuery({
    queryKey: ['timesheetHistories', timesheet.id],
    async queryFn() {
      const res = await apiRequest<{ timesheetHistory: TimesheetHistory[] }>(`timesheets/${timesheet.id}/history`);
      return res.timesheetHistory;
    },
    enabled: timesheet.hasTimesheetHistory,
  });

  return (
    <article className="flex h-full flex-col gap-5 p-5">
      <section className="flex justify-between">
        <hgroup>
          <h3 className="text-sm">
            {dayjs(timesheet.clockIn).format('h:mm A')} -{' '}
            {timesheet.clockOut ? (
              dayjs(timesheet.clockOut).format('h:mm A')
            ) : (
              <Badge variant="outline">Still clocked in</Badge>
            )}
          </h3>
          <p className="mt-1 h-fit text-xs text-sub-600">{timesheet.project?.name || '-'}</p>
        </hgroup>
        <p className="h-fit text-strong-950">
          {formatMinutesToHoursWorked((timesheet.regularMinutes ?? 0) + (timesheet.overtimeMinutes ?? 0))}
        </p>
      </section>

      <hr className="border-soft-200" />

      {!timesheet.hasTimesheetHistory ? (
        <section className="flex grow flex-col items-center justify-center text-sm">
          <EmptyStateDailyWorkHours />
          <p className="mt-5 h-fit text-soft-400">This timesheet has no edit history.</p>
        </section>
      ) : timesheetHistories.isPending ? (
        <section className="flex grow items-center justify-center">
          <LoadingIndicator text="Fetching histories..." />
        </section>
      ) : (
        timesheetHistories.data?.map((history, index) => <TimesheetHistoryCard key={index} historyData={history} />)
      )}
    </article>
  );
}

function LocationTabContent({ timesheet }: TabContentProps) {
  const timesheetQuery = useQuery({
    queryKey: ['timesheet', timesheet.id],
    async queryFn() {
      const res = await apiRequest<{ timesheet: UserTimesheet }>(`timesheets/${timesheet.id}`);
      return res.timesheet;
    },
  });

  return (
    <>
      {timesheetQuery.isPending ? (
        <section className="flex h-full items-center justify-center">
          <LoadingIndicator text="Fetching timesheet..." />
        </section>
      ) : timesheetQuery.isError ? (
        <section className="flex h-full items-center justify-center">
          <p className="text-sm text-error-base">An error occurred when fetching timesheets.</p>
        </section>
      ) : (
        <LocationForm timesheet={timesheetQuery.data} />
      )}
    </>
  );
}

async function fetchImageAsBlob(url: string) {
  const response = await fetch(url, {
    headers: {
      'Cache-Control': 'no-cache',
    },
  });
  await validateNetworkResponse(response);
  const blob = await response.blob();
  return blob;
}

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

function InjuryReportTabContent({ timesheet }: TabContentProps) {
  const queryClient = useQueryClient();
  const [signedUrls, setSignedUrls] = useState<string[]>([]);
  const { getSignedUrlAsync } = useAwsS3();
  const { user } = useAuth();

  const injuryReportQuery = useQuery({
    queryKey: ['timesheet', timesheet.id],
    async queryFn() {
      const res = await apiRequest<{ timesheet: UserTimesheet }>(`timesheets/${timesheet.id}`);
      return res.timesheet;
    },
  });

  const injuryReport = injuryReportQuery.data?.injuryReport;

  const fetchSignedUrls = async () => {
    const signedUrlPromises = injuryReportQuery.data.injuryReport?.injuryPhotos?.map((photo) => {
      return getSignedUrlAsync({
        Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/injury-report-photos`,
        Key: photo.objectId,
      });
    });

    const signedUrls = await Promise.all(signedUrlPromises ?? []);
    setSignedUrls(signedUrls as string[]);
  };

  const resolveAlert = async (data: any) => {
    await apiRequest(`injury-reports/${injuryReport?.id}`, {
      method: 'PATCH',
      body: {
        resolutionDate: new Date().valueOf(),
        resolutionNote: data.resolutionNote,
        resolvedBy: user?.uid,
      },
    });
    queryClient.invalidateQueries({ queryKey: ['timesheets'] });
    queryClient.invalidateQueries({ queryKey: ['timesheet', timesheet.id] });
  };

  useEffect(() => {
    if (injuryReportQuery.data && injuryReportQuery.data.injuryReport) {
      fetchSignedUrls();
    }
  }, [injuryReportQuery.data]);

  if (!injuryReportQuery.data || !injuryReportQuery.data.injuryReport) {
    return (
      <div className="flex h-full items-center justify-center p-5 text-center text-sm">
        <div className="flex flex-col items-center gap-2">
          <EmptyStateHRNotes className="size-[120px]" />
          <p className="mt-5 h-fit text-soft-400">There is no injury reported on this timesheet. Pheew!</p>
        </div>
      </div>
    );
  }

  return (
    <article className="p-5">
      {injuryReport?.note && <div className="text-sm text-strong-950">&quot;{injuryReport?.note}&quot;</div>}

      <div className="mt-5 flex flex-wrap gap-2">
        {signedUrls.map((url, index) => (
          <div key={`${url}-${index}`} className="size-[120px] rounded-md">
            <img src={url} alt="Injury Report Photo" className="h-full w-full rounded-md object-cover" />
          </div>
        ))}
      </div>

      {injuryReport.isResolved && <div className="my-5 w-full border-t border-soft-200" />}
      <ResolutionForm
        itemResolutionNote={injuryReport?.resolutionNote}
        resolvedByUser={injuryReport?.resolvedByUser}
        isItemResolved={!!injuryReport.isResolved}
        resolveAlertCallback={resolveAlert}
        alertId={injuryReport?.id}
        queryKey={['timesheets']}
        resolutionStyle="card"
        resolvedByDate={injuryReport?.updatedAt}
      />
    </article>
  );
}

function PhotosTabContent({ timesheet }: TabContentProps) {
  const { s3 } = useAwsS3();
  const { user } = useAuth();

  const collectionsQuery = useQuery({
    queryKey: ['projectPhotosCollection', timesheet.id],
    async queryFn() {
      const res = await apiRequest<{ projectPhotosCollections: ProjectPhotosCollection[] }>(
        `project-photos-collections?timesheetId=${timesheet.id}`
      );

      async function generateSignedUrl(key = '1.png'): Promise<undefined | string> {
        if (!s3) return;

        return new Promise((resolve, reject) => {
          const params = {
            Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/project-photos`,
            Key: key,
            Expires: 21600, // 6 hours - input in seconds
          };

          s3.getSignedUrl('getObject', params, (err, url) => {
            if (err) {
              reject(err);
            }
            resolve(url);
          });
        });
      }

      // Step 1: Loop through each projectPhotosCollection
      const collectionPromises = res.projectPhotosCollections.map(async (collection) => {
        // Step 2: Loop through photos in each collection and generate signed URLs
        const photoPromises = collection.projectPhotos.map(async (photo) => {
          const signedUrl = await generateSignedUrl(photo.objectId);
          return { ...photo, imageUrl: signedUrl };
        });

        // Step 3: Await for all photo promises to resolve
        const updatedPhotos = await Promise.all(photoPromises);

        // Update the current collection's photos
        return { ...collection, projectPhotos: updatedPhotos };
      });

      // Step 4: Await for all collection promises to resolve
      const updatedCollections = await Promise.all(collectionPromises);

      return updatedCollections;
    },
  });

  return (
    <>
      {collectionsQuery.isPending ? (
        <section className="flex h-full items-center justify-center">
          <LoadingIndicator text="Fetching photos..." />
        </section>
      ) : collectionsQuery.isError ? (
        <section className="flex h-full items-center justify-center">
          <p className="text-error-base">An error occured when fetching the photos.</p>
        </section>
      ) : collectionsQuery.data.length === 0 ? (
        <section className="flex h-full flex-col items-center justify-center">
          <EmptytStateChatPhoto className="size-[120px]" />
          <p className="mt-5 h-fit text-soft-400">There are no photos uploaded in this timesheet.</p>
        </section>
      ) : (
        <section className="flex flex-col gap-6 p-5">
          {collectionsQuery.data.map((collection) => {
            return (
              <PhotosCollection
                key={collection.id}
                collection={collection}
                collections={collectionsQuery.data}
                timesheetId={timesheet.id}
              />
            );
          })}
        </section>
      )}
    </>
  );
}

interface PhotosCollectionProps {
  collection: ProjectPhotosCollection;
  collections: ProjectPhotosCollection[];
  timesheetId: number;
}

function PhotosCollection({ collection, collections, timesheetId }: PhotosCollectionProps) {
  const [isMediaViewerOpen, setIsMediaViewerOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<MediaViewerItem | null>(null);

  const zip = new JSZip();

  const generateZipMutation = useMutation({
    async mutationFn(collection: ProjectPhotosCollection) {
      const imageUrls = collection.projectPhotos.map((photos) => photos.imageUrl);

      // add images to zip
      const imagePromises = imageUrls.map(async (url, index) => {
        if (!url) return;
        const blob = await fetchImageAsBlob(url);
        zip.file(`image-${index}.jpg`, blob);
      });
      await Promise.all(imagePromises);

      const content = await zip.generateAsync({ type: 'blob' });
      saveAs(content, 'images.zip');
    },
  });

  const queryClient = useQueryClient();

  return (
    <div className="flex flex-wrap gap-3" id={`collection-${collection.id}`}>
      <p className="h-fit w-full text-strong-950">{collection.note}</p>
      {collection.projectPhotos.map((photo) => {
        return (
          <div
            key={photo.id}
            className="size-[120px]"
            onClick={() => {
              setIsMediaViewerOpen(true);
              setSelectedItem({
                id: photo.id,
                type: 'image',
                url: photo.imageUrl!,
                name: photo.objectId,
                deletable: true,
                createdAt: photo.createdAt!,
                createdBy: collection.user.fullName || `${collection.user.firstName} ${collection.user.lastName}`,
              });
            }}
          >
            <Image
              src={photo.imageUrl!}
              width={288}
              height={288}
              className="h-32 w-32 rounded-10 object-cover"
              placeholder="blur"
              blurDataURL={rgbDataURL(141, 141, 141)}
            />
          </div>
        );
      })}
      <MediaViewer
        isOpen={isMediaViewerOpen}
        onClose={() => setIsMediaViewerOpen(false)}
        onDeleted={(id) => {
          // manually delete photos on the UI side because reloading photos from the API makes the images re-render and blink
          const newCollection = collections.map((collection) => {
            const newProjectPhotos = collection.projectPhotos.filter((photo) => photo.id !== id);
            return {
              ...collection,
              projectPhotos: newProjectPhotos,
            };
          });

          queryClient.setQueryData(['projectPhotosCollection', timesheetId], newCollection);
        }}
        items={collection.projectPhotos.map((photo) => ({
          id: photo.id,
          type: 'image',
          url: photo.imageUrl!,
          name: photo.objectId,
          deletable: true,
          createdAt: photo.createdAt!,
          createdBy: collection.user.fullName || `${collection.user.firstName} ${collection.user.lastName}`,
        }))}
        selectedItem={selectedItem}
        onSelectedItemChange={setSelectedItem}
      />
      <div className="mt-1 w-full">
        <LinkButton
          className="col-span-3 mr-auto flex items-center gap-1"
          style="primary"
          size="medium"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            generateZipMutation.mutate(collection);
          }}
          disabled={generateZipMutation.isPending}
        >
          {generateZipMutation.isPending && <Spinner className="size-4" />} Download
        </LinkButton>
      </div>
    </div>
  );
}

function ClockInOutPhotosTabContent({ timesheet }: TabContentProps) {
  const { s3 } = useAwsS3();
  const { user } = useAuth();

  const generateSignedUrl = useCallback(
    async (key = '1.png') => {
      if (!s3) return;

      return new Promise((resolve, reject) => {
        const params = {
          Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/clock-in-photos`,
          Key: key,
          Expires: 21600, // 6 hours - input in seconds
        };

        s3.getSignedUrl('getObject', params, (err, url) => {
          if (err) {
            reject(err);
          }
          resolve(url);
        });
      });
    },
    [s3, user?.companyId]
  );

  const photosQuery = useQuery({
    queryKey: ['clockInOutPhotos', timesheet.id],
    async queryFn() {
      let clockInPhotoUrl: string | undefined, clockOutPhotoUrl: string | undefined;
      if (timesheet.clockInPhotoObjectId) {
        clockInPhotoUrl = (await generateSignedUrl(timesheet.clockInPhotoObjectId)) as string;
      }
      if (timesheet.clockOutPhotoObjectId) {
        clockOutPhotoUrl = (await generateSignedUrl(timesheet.clockOutPhotoObjectId)) as string;
      }
      return [clockInPhotoUrl, clockOutPhotoUrl].filter(Boolean);
    },
  });

  const [isMediaViewerOpen, setIsMediaViewerOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<MediaViewerItem | null>(null);

  const generateZip = useMutation({
    async mutationFn(imageUrls: string[]) {
      const zip = new JSZip();
      const imagePromises = imageUrls.map(async (url, index) => {
        if (!url) return;
        const blob = await fetchImageAsBlob(url);
        zip.file(`image-${index}.jpg`, blob);
      });
      await Promise.all(imagePromises);
      return zip.generateAsync({ type: 'blob' });
    },
    onSuccess(content) {
      saveAs(content, 'images.zip');
    },
  });

  return (
    <>
      {photosQuery.isPending ? (
        <section className="flex h-full items-center justify-center">
          <LoadingIndicator text="Fetching photos..." />
        </section>
      ) : photosQuery.isError ? (
        <section className="flex h-full items-center justify-center">
          <p className="text-error-base">An error occured when fetching the photos.</p>
        </section>
      ) : photosQuery.data.length === 0 ? (
        <section className="flex h-full flex-col items-center justify-center">
          <EmptytStateChatPhoto className="size-[120px]" />
          <p className="mt-5 h-fit text-soft-400">There are no clock in/out photos uploaded in this timesheet.</p>
        </section>
      ) : (
        <section className="p-5">
          {timesheet.clockInPhotoObjectId && (
            <div className="flex justify-between">
              <div>
                <h3 className="text-sm font-medium">Clock In</h3>
                <p className="mt-1 h-fit text-xs font-medium text-sub-600">
                  {dayjs(timesheet.clockIn).format('h:mm A')}
                </p>
                <Button
                  variant="link"
                  color="primary"
                  size="2x-small"
                  className="mt-1 px-0 [&>span]:px-0"
                  onClick={() => generateZip.mutate(photosQuery.data[0] ? [photosQuery.data[0]] : [])}
                  disabled={generateZip.isPending || photosQuery.data.length === 0}
                  loading={generateZip.isPending}
                >
                  Download
                </Button>
              </div>
              <div className="size-[120px] overflow-hidden rounded-8">
                <Image
                  className="size-full cursor-pointer object-cover"
                  height={200}
                  width={200}
                  placeholder="blur"
                  blurDataURL={rgbDataURL(141, 141, 141)}
                  src={photosQuery.data[0] || ''}
                  onClick={() => {
                    setIsMediaViewerOpen(true);
                    setSelectedItem(
                      transformToMediaViewerItem(
                        timesheet.clockInPhotoObjectId || '',
                        photosQuery.data[0] || '',
                        timesheet.clockIn,
                        timesheet.user?.firstName + ' ' + timesheet.user?.lastName
                      )
                    );
                  }}
                />
              </div>
            </div>
          )}
          {timesheet.clockOutPhotoObjectId && (
            <>
              <hr className="mt-5 border-soft-200" />
              <div className="mt-5 flex justify-between">
                <div>
                  <h3 className="text-sm font-medium">Clock Out</h3>
                  <p className="mt-1 h-fit text-xs font-medium text-sub-600">
                    {dayjs(timesheet.clockOut).format('h:mm A')}
                  </p>
                  <Button
                    variant="link"
                    color="primary"
                    size="2x-small"
                    className="mt-1 px-0 [&>span]:px-0"
                    onClick={() => generateZip.mutate(photosQuery.data[1] ? [photosQuery.data[1]] : [])}
                    disabled={generateZip.isPending || photosQuery.data.length === 0}
                    loading={generateZip.isPending}
                  >
                    Download
                  </Button>
                </div>
                <div className="size-[120px] overflow-hidden rounded-8">
                  <Image
                    className="size-full cursor-pointer object-cover"
                    height={200}
                    width={200}
                    placeholder="blur"
                    blurDataURL={rgbDataURL(141, 141, 141)}
                    src={photosQuery.data[1] || ''}
                    onClick={() => {
                      setIsMediaViewerOpen(true);
                      setSelectedItem(
                        transformToMediaViewerItem(
                          timesheet.clockOutPhotoObjectId || '',
                          photosQuery.data[1] || '',
                          timesheet.clockOut,
                          timesheet.user?.firstName + ' ' + timesheet.user?.lastName
                        )
                      );
                    }}
                  />
                </div>
              </div>
            </>
          )}
        </section>
      )}
      <MediaViewer
        isOpen={isMediaViewerOpen}
        onClose={() => setIsMediaViewerOpen(false)}
        onDeleted={() => setIsMediaViewerOpen(false)}
        items={[
          ...(timesheet.clockInPhotoObjectId
            ? [
                {
                  id: timesheet.clockInPhotoObjectId || '',
                  type: 'image' as const,
                  url: photosQuery.data?.[0] || '',
                  name: timesheet.clockInPhotoObjectId || 'Clock In Photo',
                  deletable: false,
                  createdAt: timesheet.clockIn,
                  createdBy: timesheet.user?.firstName + ' ' + timesheet.user?.lastName,
                },
              ]
            : []),
          ...(timesheet.clockOutPhotoObjectId
            ? [
                {
                  id: timesheet.clockOutPhotoObjectId || '',
                  type: 'image' as const,
                  url: photosQuery.data?.[1] || '',
                  name: timesheet.clockOutPhotoObjectId || 'Clock Out Photo',
                  deletable: false,
                  createdAt: timesheet.clockOut,
                  createdBy: timesheet.user?.firstName + ' ' + timesheet.user?.lastName,
                },
              ]
            : []),
        ]}
        selectedItem={selectedItem}
        onSelectedItemChange={setSelectedItem}
      />
    </>
  );
}

function transformToMediaViewerItem(objectId: string, url: string, createdAt: number, name: string): MediaViewerItem {
  return {
    id: objectId,
    type: 'image',
    url: url,
    name: objectId,
    deletable: false,
    createdAt: createdAt,
    createdBy: name,
  };
}
