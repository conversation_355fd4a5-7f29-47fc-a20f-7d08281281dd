import { useCallback, useEffect, useState } from 'react';

import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { Menu, MenuContent, MenuItem, MenuTrigger } from '@/hammr-ui/components/Menu';
import { Calendar } from '@/hammr-ui/components/calendar';
import { cn } from '@/utils/cn';
import { RiArrowDownSLine, RiArrowLeftSLine, RiArrowRightSLine } from '@remixicon/react';
import dayjs, { Dayjs } from 'dayjs';
import { WEEK_DAYS, WeekDay } from '@/interfaces/payschedule';
import { DateFilter, filteredDateChangeMappings } from '@/hooks/useDateSelection';
import { Company } from '@/interfaces/company';
import { useCompany } from '@/hooks/useCompany';
import { PayDay } from '@/interfaces/check/pay-schedule';
import { getPayDays } from '@/services/pay-schedule';
import isoWeek from 'dayjs/plugin/isoWeek';
import { showErrorToast } from '@/utils/errorHandling';

dayjs.extend(isoWeek);

export interface PeriodOption {
  label: string;
  value: string;
}

const DEFAULT_PERIOD_OPTIONS: PeriodOption[] = [
  { label: 'Daily', value: 'DAILY' },
  { label: 'Weekly', value: 'WEEKLY' },
  { label: 'Monthly', value: 'MONTHLY' },
  { label: 'Custom Dates', value: 'CUSTOM' },
];

const ALL_TIME_OPTION: PeriodOption = { label: 'All Time', value: 'ALL_TIME' };

const PAY_PERIOD_OPTION: PeriodOption = { label: 'Pay Period', value: 'PAY_PERIOD' };

interface DatePeriodSelectorProps {
  onDateChange: (startDate: Date, endDate: Date) => void;
  showAllTimeOption?: boolean;
  allTimeStartDate?: Date;
  allTimeEndDate?: Date;
}

export function DatePeriodSelectorImproved({
  onDateChange,
  showAllTimeOption,
  allTimeStartDate,
  allTimeEndDate,
}: DatePeriodSelectorProps) {
  const [startDate, setStartDate] = useState<Dayjs | null>(null);
  const [endDate, setEndDate] = useState<Dayjs | null>(null);

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);

  const { company }: { company: Company } = useCompany();

  const [disabledPreviousPayDaysButton, setDisabledPreviousPayDaysButton] = useState(false);
  const [disabledNextPayDaysButton, setDisabledNextPayDaysButton] = useState(false);

  const [selectedPayDay, setSelectedPayDay] = useState<PayDay>();
  const [payDays, setPayDays] = useState<PayDay[]>([]);
  const [initialized, setInitialized] = useState(false);
  const [isLoadingPayDays, setIsLoadingPayDays] = useState(false);

  const periodOptions = company?.isPayrollEnabled
    ? [PAY_PERIOD_OPTION, ...DEFAULT_PERIOD_OPTIONS]
    : DEFAULT_PERIOD_OPTIONS;

  if (showAllTimeOption) {
    periodOptions.unshift(ALL_TIME_OPTION);
  }

  const [selectedOption, setSelectedOption] = useState(periodOptions[0]);

  useEffect(() => {
    if (!initialized) {
      return;
    }
    onDateChange(startDate?.toDate(), endDate?.toDate());
    // this gets into an infinite loop if we add "onDateChange" in the deps of this useEffect
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDate, endDate, initialized]);

  const registerDatesByFilter = useCallback(
    (
      filterSelected: DateFilter,
      dateInputs?: {
        startDate?: Dayjs;
        endDate?: Dayjs;
        weekStartDay?: WeekDay;
      }
    ) => {
      const currentDate = dayjs();
      // setInitialized(true);

      switch (filterSelected) {
        case 'PAY_PERIOD':
          setStartDate(dateInputs.startDate);
          setEndDate(dateInputs.endDate);
          return {
            startDate: dateInputs ? dateInputs.startDate : null, // pass in the start date from usePayPeriodSelection
            endDate: dateInputs ? dateInputs.endDate : null, // pass the end date from usePayPeriodSelection
          };
        case 'DAILY':
          const updatedStartDate = currentDate.clone().startOf('day');
          const updatedEndDate = currentDate.clone().endOf('day');
          setStartDate(updatedStartDate);
          setEndDate(updatedEndDate);
          return {
            startDate: updatedStartDate,
            endDate: updatedEndDate,
          };
        case 'WEEKLY':
          const electedWeekStartDate = dateInputs.weekStartDay || 'MONDAY';
          const updatedStartDateWeekly = currentDate
            .clone()
            .isoWeekday(WEEK_DAYS.indexOf(electedWeekStartDate) + 1)
            .startOf('day');
          const updatedEndDateWeekly = updatedStartDateWeekly.clone().add(6, 'days').endOf('day');
          setStartDate(updatedStartDateWeekly);
          setEndDate(updatedEndDateWeekly);
          return {
            startDate: updatedStartDateWeekly,
            endDate: updatedEndDateWeekly,
          };
        case 'MONTHLY':
          const updatedStartDateMonthly = currentDate.clone().startOf('month');
          const updatedEndDateMonthly = currentDate.clone().endOf('month');
          setStartDate(updatedStartDateMonthly);
          setEndDate(updatedEndDateMonthly);
          return {
            startDate: updatedStartDateMonthly,
            endDate: updatedEndDateMonthly,
          };
        case 'CUSTOM':
          setStartDate(dateInputs.startDate);
          setEndDate(dateInputs.endDate);
          return {
            startDate: dateInputs ? dateInputs.startDate.startOf('day') : null,
            endDate: dateInputs ? dateInputs.endDate.endOf('day') : null,
          };
      }
    },
    []
  );

  useEffect(() => {
    if (!company?.isPayrollEnabled || !company?.paySchedule || selectedOption.value !== 'PAY_PERIOD') {
      setInitialized(true);
      return;
    }

    async function fetchPayDays() {
      setIsLoadingPayDays(true);
      const sixMonthsAgo = dayjs().subtract(6, 'months').format('YYYY-MM-DD');

      const payDaysData = await getPayDays(company.paySchedule.checkPayScheduleId, sixMonthsAgo).finally(() => {
        setIsLoadingPayDays(false);
      });
      setPayDays(payDaysData);

      const now = dayjs();
      const currentPeriod = payDaysData.find((payDay) =>
        now.isBetween(dayjs(payDay.period_start), dayjs(payDay.period_end), 'day', '[]')
      );

      if (currentPeriod) {
        setSelectedPayDay(currentPeriod);

        registerDatesByFilter('PAY_PERIOD', {
          startDate: dayjs(currentPeriod.period_start),
          endDate: dayjs(currentPeriod.period_end),
          weekStartDay: company?.overtimeSettings?.weekStartDay,
        });
        setInitialized(true);
      }
    }

    fetchPayDays();

    // return () => {
    //   setInitialized(false);
    // };
  }, [
    company?.isPayrollEnabled,
    company?.overtimeSettings?.weekStartDay,
    company.paySchedule,
    registerDatesByFilter,
    selectedOption.value,
  ]);

  const handleSelectPeriod = (period: DateFilter, newStartDate?: Dayjs, newEndDate?: Dayjs) => {
    setSelectedOption(periodOptions.find((option) => option.value === period) ?? periodOptions[0]);
    // record any date changes - if period === 'PAY_PERIOD' or 'CUSTOM'
    if (period === 'PAY_PERIOD') {
      if (!payDays.length) {
        return;
      }
      registerDatesByFilter('PAY_PERIOD', {
        startDate: dayjs(selectedPayDay.period_start),
        endDate: dayjs(selectedPayDay.period_end),
      });
    } else if (period === 'CUSTOM') {
      if (newStartDate && newEndDate) {
        registerDatesByFilter(period, {
          startDate: newStartDate,
          endDate: newEndDate,
        });
      }
    } else if (period === 'ALL_TIME') {
      setStartDate(null);
      setEndDate(null);
    } else {
      registerDatesByFilter(period, { weekStartDay: company?.overtimeSettings.weekStartDay });
    }

    setDropdownOpen(false);
  };

  const period = selectedOption.value;
  const previousButtonDisabled = period === 'PAY_PERIOD' && (disabledPreviousPayDaysButton || isLoadingPayDays);
  const nextButtonDisabled = period === 'PAY_PERIOD' && (disabledNextPayDaysButton || isLoadingPayDays);

  const fetchMorePayDays = async (direction: 'prev' | 'next') => {
    if (!payDays.length) {
      return [];
    }

    try {
      let startDate: string;
      if (direction === 'prev') {
        startDate = dayjs(payDays[0].period_start).add(1, 'day').subtract(12, 'months').format('YYYY-MM-DD');
      } else {
        startDate = dayjs(payDays[payDays.length - 1].period_end)
          .add(1, 'day')
          .format('YYYY-MM-DD');
      }

      setDisabledPreviousPayDaysButton(true);
      setDisabledNextPayDaysButton(true);
      const payDaysData = await getPayDays(company.paySchedule.checkPayScheduleId, startDate).finally(() => {
        setDisabledPreviousPayDaysButton(false);
        setDisabledNextPayDaysButton(false);
      });

      let newPayDays: PayDay[] = [];

      if (payDaysData.length) {
        if (direction === 'prev') {
          newPayDays = [...payDaysData, ...payDays];
          setPayDays(newPayDays);
        } else {
          payDaysData.shift(); // it seems check returns a pay period we already have so we need to remove it
          newPayDays = [...payDays, ...payDaysData];
          setPayDays(newPayDays);
        }
      } else {
        if (direction === 'prev') {
          setDisabledPreviousPayDaysButton(true);
        } else {
          setDisabledNextPayDaysButton(true);
        }
      }

      return newPayDays;
    } catch (error) {
      console.error('Error fetching more paydays:', error);
      showErrorToast(error, 'Failed to fetch more pay days');
    }
  };

  const handlePayPeriodNavigation = async (direction: 'prev' | 'next') => {
    setDisabledPreviousPayDaysButton(false);
    setDisabledNextPayDaysButton(false);

    const currentIndex = payDays.findIndex((payDay) => payDay.period_start === selectedPayDay.period_start);

    if (direction === 'prev' && currentIndex === 0) {
      const newPayDays = await fetchMorePayDays('prev');
      const newIndex = newPayDays.findIndex((payDay) => payDay.period_start === selectedPayDay.period_start);
      if (newIndex > 0) {
        const newPayDay = newPayDays[newIndex - 1];
        setSelectedPayDay(newPayDay);
        registerDatesByFilter('PAY_PERIOD', {
          startDate: dayjs(newPayDay.period_start),
          endDate: dayjs(newPayDay.period_end),
        });
      }
      return;
    }

    if (direction === 'next' && currentIndex === payDays.length - 1) {
      const newPayDays = await fetchMorePayDays('next');
      const nextPayDay = newPayDays[currentIndex + 1];
      if (nextPayDay) {
        setSelectedPayDay(nextPayDay);
        registerDatesByFilter('PAY_PERIOD', {
          startDate: dayjs(nextPayDay.period_start),
          endDate: dayjs(nextPayDay.period_end),
        });
      }
      return;
    }

    const canNavigate = direction === 'prev' ? currentIndex > 0 : currentIndex < payDays.length - 1;
    if (canNavigate) {
      const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1;
      const newPayDay = payDays[newIndex];
      setSelectedPayDay(newPayDay);
      registerDatesByFilter('PAY_PERIOD', {
        startDate: dayjs(newPayDay.period_start),
        endDate: dayjs(newPayDay.period_end),
      });
    }
  };

  // handlers for left and right arrow clicks
  const handlePrevDateSelector = async () => {
    // should take current dateFilter state and decrement accordingly
    if (period === 'PAY_PERIOD') {
      await handlePayPeriodNavigation('prev');
    } else {
      let clonedSelectedStartDate = startDate.clone();
      let clonedSelectedEndDate = endDate.clone();
      const prevStepChange = filteredDateChangeMappings(`PREV_${period}`);

      if (prevStepChange) {
        prevStepChange.forEach((stepChange) => {
          clonedSelectedStartDate = clonedSelectedStartDate[stepChange.operation](stepChange.amount, stepChange.unit);
        });

        prevStepChange.forEach((stepChange) => {
          clonedSelectedEndDate = clonedSelectedEndDate[stepChange.operation](stepChange.amount, stepChange.unit);
        });

        if (period === 'MONTHLY') {
          clonedSelectedStartDate = clonedSelectedStartDate.startOf('month');
          clonedSelectedEndDate = clonedSelectedEndDate.endOf('month');
        }

        setStartDate(clonedSelectedStartDate);
        setEndDate(clonedSelectedEndDate);
      }
    }
  };

  const handleNextDateSelector = async () => {
    // should take current dateFilter state and increment accordingly
    if (period === 'PAY_PERIOD') {
      await handlePayPeriodNavigation('next');
    } else {
      let clonedSelectedStartDate = startDate.clone();
      let clonedSelectedEndDate = endDate.clone();
      const nextStepChange = filteredDateChangeMappings(`NEXT_${period}`);

      if (nextStepChange) {
        nextStepChange.forEach((stepChange) => {
          clonedSelectedStartDate = clonedSelectedStartDate[stepChange.operation](stepChange.amount, stepChange.unit);
        });

        nextStepChange.forEach((stepChange) => {
          clonedSelectedEndDate = clonedSelectedEndDate[stepChange.operation](stepChange.amount, stepChange.unit);
        });

        if (period === 'MONTHLY') {
          clonedSelectedStartDate = clonedSelectedStartDate.startOf('month');
          clonedSelectedEndDate = clonedSelectedEndDate.endOf('month');
        }

        setStartDate(clonedSelectedStartDate);
        setEndDate(clonedSelectedEndDate);
      }
    }
  };

  const showYearForAllTime =
    allTimeStartDate && allTimeEndDate && allTimeStartDate.getFullYear() !== allTimeEndDate.getFullYear();

  return (
    <div
      className={cn(
        'flex h-9 w-max overflow-hidden rounded-8 border border-soft-200',
        'text-sm font-medium text-sub-600',
        '[&>:not(:first-child)]:border-l [&>:not(:first-child)]:border-soft-200'
      )}
    >
      <Menu open={dropdownOpen} onOpenChange={(open) => setDropdownOpen(open)}>
        <MenuTrigger asChild>
          <button
            type="button"
            className='flex items-center gap-2 px-2 pl-4 hover:bg-weak-50 data-[state="open"]:text-strong-950'
          >
            <span>{selectedOption.label}</span>
            <RiArrowDownSLine className="size-5" />
          </button>
        </MenuTrigger>
        <MenuContent className="min-w-40" align="start">
          {periodOptions.map((option) => (
            <MenuItem
              key={option.value}
              title={option.label}
              selected={period === option.value}
              onClick={() => handleSelectPeriod(option.value as DateFilter)}
            />
          ))}
        </MenuContent>
      </Menu>
      <Popover open={calendarOpen} onOpenChange={(open) => setCalendarOpen(open)}>
        <PopoverTrigger asChild>
          <div
            className={`parent group flex min-w-[225px] data-[state="open"]:text-strong-950  [&:has(.child:hover)]:bg-weak-50`}
          >
            {period !== 'CUSTOM' && period !== 'ALL_TIME' && (
              <button
                className={cn(
                  'flex w-8 items-center justify-center',
                  previousButtonDisabled ? 'bg-strong-950/5 opacity-40' : 'hover:bg-weak-50'
                )}
                onClick={(event) => {
                  event.stopPropagation();
                  handlePrevDateSelector();
                }}
                disabled={previousButtonDisabled}
              >
                <RiArrowLeftSLine className="size-5" />
              </button>
            )}
            <button type="button" className={cn('child flex flex-1 items-center justify-center px-2')}>
              {period === 'DAILY' ? (
                <span>{dayjs(startDate).format('MMM DD, YYYY')}</span>
              ) : (
                <span>
                  {`${dayjs(startDate || allTimeStartDate)?.format(
                    `MMM DD${!startDate && allTimeStartDate && showYearForAllTime ? ', YYYY' : ''}`
                  )}
                   - 
                   ${dayjs(endDate || allTimeEndDate)?.format('MMM DD, YYYY')}`}
                </span>
              )}
            </button>

            {period !== 'CUSTOM' && period !== 'ALL_TIME' && (
              <button
                className={cn(
                  'flex w-8 items-center justify-center',
                  nextButtonDisabled ? 'bg-strong-950/5 opacity-40' : 'hover:bg-weak-50'
                )}
                onClick={(event) => {
                  event.stopPropagation();
                  handleNextDateSelector();
                }}
                disabled={nextButtonDisabled}
              >
                <RiArrowRightSLine className="size-5" />
              </button>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent className="mt-1 w-auto p-0 !shadow-md" align="start">
          {period === 'DAILY' ? (
            <Calendar
              mode="single"
              onCancel={() => {
                setCalendarOpen(false);
              }}
              value={startDate?.toDate() ?? null}
              onApply={(value) => {
                setCalendarOpen(false);
                setStartDate(dayjs(value));
                setEndDate(dayjs(value).endOf('day'));
              }}
            />
          ) : (
            <Calendar
              mode="range"
              onCancel={() => {
                setCalendarOpen(false);
              }}
              value={startDate && endDate ? [startDate.toDate(), endDate.toDate()] : null}
              onApply={(value) => {
                setCalendarOpen(false);

                if (!value) return;

                if (period === 'ALL_TIME') {
                  handleSelectPeriod('CUSTOM', dayjs(value[0]), dayjs(value[1]));
                }
                setStartDate(dayjs(value[0]));
                setEndDate(dayjs(value[1]).endOf('day'));
              }}
            />
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
