import { Dialog, DialogFooter, DialogSurface } from '@/hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/hammr-ui/components/popover';
import { Controller, UseFormReturn, useFieldArray, useForm } from 'react-hook-form';
import UserLine from '@/hammr-icons/UserLine';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';
import { UseQueryResult, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { HammrUser } from '@/interfaces/user';
import {
  RiAddLine,
  RiArrowDownSLine,
  RiCloseFill,
  RiDeleteBinLine,
  RiFileCopyLine,
  RiMore2Line,
  RiScissorsCutLine,
  RiStackLine,
  RiZzzLine,
} from '@remixicon/react';
import { Combobox } from '@/hammr-ui/components/combobox';
import { Project } from '@/interfaces/project';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { Input } from '@/hammr-ui/components/input';
import { addToast } from '@/hooks/useToast';
import { useEffect, useMemo, useState } from 'react';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { TimeInput } from '@/hammr-ui/components/time-input';
import dayjs from 'dayjs';
import { useCompany } from '@/hooks/useCompany';
import { DialogTitle } from '@radix-ui/react-dialog';
import { cn } from '@/utils/cn';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import { ButtonGroup, ButtonGroupItem } from '@/hammr-ui/components/ButtonGroup';
import { getEmployeeClassifications } from '@/services/classifications';
import { CostCode } from '@/interfaces/cost-code';
import { sortListAlphabetically } from '@/utils/collectionHelpers';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';
import { DateInput } from '@/hammr-ui/components/date-input';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { Badge } from '@/hammr-ui/components/badge';
import { minutesAfterMidnight } from '@/utils/dateHelper';
import { getBreakDuration, validateFormHours } from './utils';
import { equipmentService } from '@/services/equipment';

type FormValues = {
  selectedWorkerIds: number[];
  timesheets: {
    date: Date | null;
    projectId: string | null;
    classificationForEachUser: Record<number, string | undefined>; // Record<employeeId, classificationId>
    costCodeId: string | null;
    workersCompCodeId: string | null;
    clockIn: Date;
    clockOut: Date;
    hours: number;
    otHours: number | null;
    driveTimeHours: number;
    description: string;
    breakDuration: number | null;
    breaks: { start: Date; end: Date; duration: number }[];
    dotHours: number | null;
    equipmentId: string | null;
  }[];
};

interface Props {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  onAdd?: () => void;
  projectId?: string;
  workerId?: number;
  equipmentId?: string;
}

export default function CreateBulkTimesheetsModal({ open, setOpen, onAdd, projectId, workerId, equipmentId }: Props) {
  const { company } = useCompany();
  const queryClient = useQueryClient();

  const timesheetDefaultValues: FormValues['timesheets'][number] = useMemo(() => {
    const defaultClockIn = company?.timeTrackingSettings.defaultClockInTime
      ? dayjs(company.timeTrackingSettings.defaultClockInTime, 'HH:mm:ss').toDate()
      : dayjs().startOf('day').set('hour', 8).toDate();
    const defaultClockOut = company?.timeTrackingSettings.defaultClockOutTime
      ? dayjs(company.timeTrackingSettings.defaultClockOutTime, 'HH:mm:ss').toDate()
      : dayjs().startOf('day').set('hour', 16).toDate();

    return {
      date: null,
      projectId: projectId ?? null,
      classificationForEachUser: {},
      costCodeId: null,
      workersCompCodeId: null,
      clockIn: defaultClockIn,
      clockOut: defaultClockOut,
      hours: +dayjs(defaultClockOut).diff(dayjs(defaultClockIn), 'hour', true).toFixed(2),
      otHours: null,
      driveTimeHours: 0,
      description: '',
      breakDuration: null,
      breaks: [],
      dotHours: null,
      equipmentId: equipmentId ?? null,
    };
  }, [
    company?.timeTrackingSettings.defaultClockInTime,
    company?.timeTrackingSettings.defaultClockOutTime,
    equipmentId,
    projectId,
  ]);

  const form = useForm<FormValues>({
    defaultValues: {
      selectedWorkerIds: workerId ? [workerId] : [],
      timesheets: [timesheetDefaultValues],
    },
  });

  useEffect(() => {
    form.reset({ selectedWorkerIds: workerId ? [workerId] : [], timesheets: [timesheetDefaultValues] });
  }, [form, timesheetDefaultValues, workerId]);

  useEffect(() => {
    if (open === false) {
      form.reset();
    }
  }, [form, open]);

  const fieldArray = useFieldArray({ name: 'timesheets', control: form.control });

  const timesheetsMutation = useMutation({
    mutationFn(formData: FormValues) {
      const payload = formData.selectedWorkerIds.flatMap((workerId) => {
        return formData.timesheets.map((timesheet) => {
          const isManualOvertimeApplied = timesheet.otHours !== null || timesheet.dotHours !== null;
          return {
            ...timesheet,
            userId: workerId,
            userClassificationId: timesheet.classificationForEachUser[workerId],
            breakDuration: Number(timesheet.breakDuration),
            clockIn: dayjs(timesheet.date)
              .set('hour', dayjs(timesheet.clockIn).hour())
              .set('minute', dayjs(timesheet.clockIn).minute())
              .valueOf(),
            clockOut:
              minutesAfterMidnight(dayjs(timesheet.clockOut)) < minutesAfterMidnight(dayjs(timesheet.clockIn))
                ? dayjs(timesheet.date)
                    .add(1, 'day')
                    .set('hour', dayjs(timesheet.clockOut).hour())
                    .set('minute', dayjs(timesheet.clockOut).minute())
                    .valueOf()
                : dayjs(timesheet.date)
                    .set('hour', dayjs(timesheet.clockOut).hour())
                    .set('minute', dayjs(timesheet.clockOut).minute())
                    .valueOf(),
            breaks: timesheet.breaks.map((breakItem) => ({
              start: dayjs(timesheet.date)
                .set('hour', dayjs(breakItem.start).hour())
                .set('minute', dayjs(breakItem.start).minute())
                .valueOf(),
              end: dayjs(timesheet.date)
                .set('hour', dayjs(breakItem.end).hour())
                .set('minute', dayjs(breakItem.end).minute())
                .valueOf(),
            })),
            otDuration: timesheet.otHours !== null ? timesheet.otHours * 3600 : null,
            driveTimeDuration: timesheet.driveTimeHours * 3600,
            dotDuration: timesheet.dotHours !== null ? timesheet.dotHours * 3600 : null,
            isManualOvertimeApplied,
            // the below fields are not needed by the API
            date: undefined,
            classificationForEachUser: undefined,
            hours: undefined,
            otHours: undefined,
            driveTimeHours: undefined,
            dotHours: undefined,
          };
        });
      });

      return apiRequest('timesheets/bulk', {
        method: 'POST',
        body: payload,
      });
    },
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['timesheets'] });

      setOpen(false);

      addToast({
        type: 'success',
        title: 'Added Bulk Timesheets',
        description: 'Successfully added timesheet entries.',
      });

      onAdd?.();
    },
  });

  const workersQuery = useQuery({
    queryKey: ['workers'],
    queryFn: () => apiRequest<{ users: HammrUser[] }>('users'),
  });

  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogSurface className="w-[1200px] !rounded-16" size="6xl" aria-describedby={undefined}>
        <DialogTitle className="sr-only">Create Bulk Timesheets</DialogTitle>
        <div className="flex flex-col overflow-hidden">
          <ScrollArea className="p-5">
            <form className="flex flex-col">
              <ControlledWorkerCrewSelectInput form={form} workersQuery={workersQuery} disabled={!!workerId} />

              <hr className="mt-3 border-soft-200" />

              {fieldArray.fields.map((field, index) => (
                <section key={field.id}>
                  <div className="mt-3 flex items-center gap-3">
                    <SingleDayTimesheetForm
                      form={form}
                      index={index}
                      workersQuery={workersQuery}
                      projectId={projectId}
                    />

                    <Button
                      type="button"
                      size="2x-small"
                      color="neutral"
                      variant="outline"
                      beforeContent={<RiFileCopyLine />}
                      onClick={() => {
                        const currentValues = form.getValues(`timesheets.${index}`);
                        fieldArray.insert(index + 1, currentValues);
                      }}
                    />
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          size="2x-small"
                          color="neutral"
                          variant="outline"
                          beforeContent={<RiMore2Line />}
                        />
                      </PopoverTrigger>
                      <PopoverContent className="flex w-[250px] flex-col gap-1 p-2" align="start">
                        <button
                          className="flex items-center gap-2 rounded-8 p-2 text-sm hover:bg-weak-50"
                          onClick={() => {
                            const clockIn = form.getValues(`timesheets.${index}.clockIn`);
                            const clockOut = form.getValues(`timesheets.${index}.clockOut`);
                            const clockInToClockOut = dayjs(clockOut).diff(dayjs(clockIn), 'hours', true);
                            const splitTime = dayjs(clockIn)
                              .add(clockInToClockOut / 2, 'hour')
                              .toDate();

                            // end the first timesheet clockOut at the split time
                            form.setValue(`timesheets.${index}.clockOut`, splitTime);
                            form.setValue(`timesheets.${index}.hours`, clockInToClockOut / 2);

                            // create a new timesheet with the split time as clockIn and the original clockOut
                            const newTimesheet = {
                              ...form.getValues(`timesheets.${index}`),
                              clockIn: splitTime,
                              clockOut,
                            };
                            fieldArray.insert(index + 1, newTimesheet);

                            // place breaks in the correct timesheet
                            const breaks = form.getValues(`timesheets.${index}.breaks`);

                            form.setValue(
                              `timesheets.${index}.breaks`,
                              breaks.filter((breakItem) => breakItem.end <= splitTime)
                            );
                            form.setValue(
                              `timesheets.${index + 1}.breaks`,
                              breaks.filter((breakItem) => breakItem.start >= splitTime)
                            );

                            closePopover();
                          }}
                        >
                          <RiScissorsCutLine className="size-5 text-sub-600" />
                          Split Entry
                        </button>
                        <button
                          className="flex items-center gap-2 rounded-8 p-2 text-sm hover:bg-weak-50"
                          onClick={() => {
                            if (!company?.timeTrackingSettings.areRealtimeBreaksEnabled) {
                              form.setValue(`timesheets.${index}.breakDuration`, 0);
                            } else {
                              const clockIn = form.getValues(`timesheets.${index}.clockIn`);
                              const clockOut = form.getValues(`timesheets.${index}.clockOut`);

                              const clockInToClockOut = dayjs(clockOut).diff(dayjs(clockIn), 'hours', true);

                              const breakStart = dayjs(clockIn)
                                .add(clockInToClockOut / 2, 'hour')
                                .toDate();
                              const breakEnd = dayjs(breakStart).add(30, 'minutes').toDate();

                              form.setValue(`timesheets.${index}.breaks`, [
                                ...form.getValues(`timesheets.${index}.breaks`),
                                {
                                  start: breakStart,
                                  end: breakEnd,
                                  duration: 0.5,
                                },
                              ]);
                            }

                            closePopover();
                          }}
                        >
                          <RiZzzLine className="size-5 text-sub-600" />
                          Add Break
                        </button>
                        <button
                          className="flex items-center gap-2 rounded-8 p-2 text-sm hover:bg-weak-50"
                          onClick={() => {
                            fieldArray.remove(index);
                            form.trigger('selectedWorkerIds');

                            closePopover();
                          }}
                        >
                          <RiDeleteBinLine className="size-5 text-sub-600" />
                          Delete Entry
                        </button>
                        <hr className="my-[1.5px] border-soft-200" />
                        <button
                          className="flex items-center gap-2 rounded-8 p-2 text-sm hover:bg-weak-50"
                          onClick={() => {
                            // take the current timesheet and create 4 more timesheets on subsequent dates with the exact same data as the current timesheet
                            const currentTimesheet = form.getValues(`timesheets.${index}`);

                            if (!currentTimesheet.date) {
                              form.trigger(`timesheets.${index}.date`);
                              return;
                            }

                            for (let i = 1; i <= 4; i++) {
                              const newDate = dayjs(currentTimesheet.date).add(i, 'day').toDate();
                              fieldArray.append({
                                ...currentTimesheet,
                                date: newDate,
                              });
                            }

                            closePopover();
                          }}
                        >
                          <RiStackLine className="size-5 text-sub-600" />
                          Create Weekly Timesheets
                        </button>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {company?.timeTrackingSettings.areRealtimeBreaksEnabled ? (
                    <RealTimeBreaks form={form} index={index} />
                  ) : (
                    Boolean(company?.timeTrackingSettings.breakOptions.length) &&
                    form.watch('timesheets')[index].breakDuration !== null && (
                      <div className="mt-1.5 flex items-center" key={index}>
                        <span className="relative h-14 w-20 overflow-hidden">
                          <span className="absolute bottom-7 left-16 h-40 w-40 rounded-12 border border-r-0 border-t-0 border-sub-300"></span>
                        </span>
                        <fieldset className="ml-1 flex grow items-center justify-between gap-2 rounded-8 bg-weak-50 p-2">
                          <p className="px-1 text-xs text-faded-base">
                            <RiZzzLine className="mr-0.5 size-3" /> BREAK
                          </p>

                          <Controller
                            control={form.control}
                            name={`timesheets.${index}.breakDuration`}
                            render={({ field: { onChange, value } }) => (
                              <ButtonGroup className="w-80">
                                {company?.timeTrackingSettings.breakOptions.map((option: number) => {
                                  return (
                                    <ButtonGroupItem
                                      key={option}
                                      onClick={() => onChange(option * 60)}
                                      active={value === option * 60}
                                    >
                                      {option === 0 ? 'No' : `${option} min`}
                                    </ButtonGroupItem>
                                  );
                                })}
                              </ButtonGroup>
                            )}
                          />
                        </fieldset>
                        <Button
                          className="ml-3 mr-[42px]"
                          type="button"
                          size="2x-small"
                          color="neutral"
                          variant="outline"
                          beforeContent={<RiDeleteBinLine />}
                          onClick={() => form.setValue(`timesheets.${index}.breakDuration`, null)}
                        />
                      </div>
                    )
                  )}
                </section>
              ))}

              <Button
                variant="outline"
                color="neutral"
                className="mx-auto mt-3"
                type="button"
                beforeContent={<RiAddLine />}
                onClick={() => {
                  // when creating a new timesheet if all selected employees have the same workers comp code, use it
                  const selectedWorkerIds = form.getValues('selectedWorkerIds');
                  const commonWorkersCompCodeId = getCommonWorkersCompCodeId(
                    selectedWorkerIds,
                    workersQuery.data?.users
                  );

                  if (commonWorkersCompCodeId) {
                    fieldArray.append({
                      ...timesheetDefaultValues,
                      workersCompCodeId: commonWorkersCompCodeId,
                    });
                  } else {
                    fieldArray.append(timesheetDefaultValues);
                  }
                }}
              >
                Add Entry
              </Button>
            </form>
          </ScrollArea>
        </div>
        <DialogFooter>
          <Button variant="outline" color="neutral" size="small" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            size="small"
            onClick={form.handleSubmit((formData) => timesheetsMutation.mutate(formData))}
            disabled={timesheetsMutation.isPending}
            loading={timesheetsMutation.isPending}
          >
            Add Timesheets
          </Button>
        </DialogFooter>
      </DialogSurface>
    </Dialog>
  );
}

interface ControlledWorkerCrewSelectInputProps {
  form: UseFormReturn<FormValues>;
  workersQuery: UseQueryResult<{ users: HammrUser[] }>;
  disabled?: boolean;
}

function ControlledWorkerCrewSelectInput({ form, workersQuery, disabled }: ControlledWorkerCrewSelectInputProps) {
  return (
    <Controller
      name="selectedWorkerIds"
      control={form.control}
      rules={{ required: 'Please select an employee' }}
      render={({ field }) => (
        <FormItem error={!!form.formState.errors.selectedWorkerIds}>
          <FormLabel>
            Employees{' '}
            <span className="text-sub-600">{field.value.length ? `(${field.value.length} selected)` : ''}</span>
          </FormLabel>
          <FormControl>
            <Popover
              onOpenChange={(isOpen) => {
                if (isOpen === false) {
                  // automatically assign the Workers Comp Code if all selected employees have the same code
                  const selectedWorkerIds = form.getValues('selectedWorkerIds');
                  const commonWorkersCompCodeId = getCommonWorkersCompCodeId(
                    selectedWorkerIds,
                    workersQuery.data?.users
                  );

                  if (commonWorkersCompCodeId) {
                    const timesheets = form.getValues('timesheets');
                    timesheets.forEach((_, index) => {
                      const workersCompCodeId = form.getValues(`timesheets.${index}.workersCompCodeId`);
                      if (!workersCompCodeId) {
                        form.setValue(`timesheets.${index}.workersCompCodeId`, commonWorkersCompCodeId);
                      }
                    });
                  }
                }
              }}
            >
              <PopoverTrigger asChild>
                <button
                  className={cn(
                    'flex cursor-pointer items-start gap-2 rounded-10 border border-soft-200 px-[11px] py-[7px]',
                    !!form.formState.errors.selectedWorkerIds && 'border-error-base',
                    disabled && 'cursor-not-allowed border-weak-50 bg-weak-50'
                  )}
                  disabled={disabled}
                >
                  <UserLine className="size-5 shrink-0 text-sub-600" />
                  {field.value.length === 0 && <span className="text-sm text-soft-400">Select employee</span>}
                  <div className="flex flex-wrap gap-2">
                    {workersQuery.data?.users
                      .filter((user) => field.value.includes(user.id))
                      .map((user) => (
                        <span
                          key={user.id}
                          className={cn(
                            'flex h-6 cursor-default items-center gap-0.5 rounded-6 border border-soft-200 pl-[7px] pr-[3px] text-xs font-medium text-sub-600 hover:bg-weak-50',
                            disabled && 'cursor-not-allowed'
                          )}
                          onClick={(event) => event.stopPropagation()}
                        >
                          {user.firstName} {user.lastName}
                          <span
                            onClick={() => {
                              form.setValue(
                                'selectedWorkerIds',
                                field.value.filter((id) => id !== user.id)
                              );
                              form.trigger('selectedWorkerIds');
                            }}
                          >
                            <RiCloseFill
                              className={cn(
                                'size-4 cursor-pointer text-soft-400 hover:text-sub-600',
                                disabled && 'cursor-not-allowed hover:text-soft-400'
                              )}
                            />
                          </span>
                        </span>
                      ))}
                  </div>
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px]" align="start">
                <WorkerCrewSelect
                  selectedWorkerIds={field.value}
                  onChange={(value) => {
                    field.onChange(value);
                    form.trigger('selectedWorkerIds');
                  }}
                  showCrews
                  showSelectAll
                />
              </PopoverContent>
            </Popover>
          </FormControl>
          <FormMessage>{form.formState.errors.selectedWorkerIds?.message}</FormMessage>
        </FormItem>
      )}
    />
  );
}

interface SingleDayTimesheetFormProps {
  form: UseFormReturn<FormValues>;
  index: number;
  workersQuery: UseQueryResult<{ users: HammrUser[] }>;
  projectId?: string;
}

function SingleDayTimesheetForm({ form, index, workersQuery, projectId }: SingleDayTimesheetFormProps) {
  const { company } = useCompany();

  const projectsQuery = useQuery({
    queryKey: ['projects'],
    async queryFn() {
      const res = await apiRequest<{ projects: Project[] }>('projects');
      return sortListAlphabetically(res.projects, 'name');
    },
  });

  const costCodesQuery = useQuery({
    queryKey: ['costCodes'],
    async queryFn() {
      const res = await apiRequest<{ costCodes: CostCode[] }>('cost-codes');
      return sortListAlphabetically(res.costCodes, 'name');
    },
  });

  // Only show non-archived WC code in Add Timesheet modal
  const workersCompCodesQuery = useQuery({
    queryKey: ['workersCompCodes', 'all'],
    queryFn: () => workersCompCodesService.get(true),
  });

  const equipmentQuery = useQuery({
    queryKey: ['equipment'],
    queryFn: () => equipmentService.getAll({ isArchived: false }),
  });

  const selectedWorkerIds = form.watch('selectedWorkerIds');

  const selectedProjectId = form.getValues(`timesheets.${index}.projectId`);
  const selectedProject = projectsQuery.data?.find((project) => project.id === Number(selectedProjectId));
  const selectedProjectWageTableId = selectedProject?.wageTableId;

  const classificationsQuery = useQuery({
    queryKey: ['classifications', selectedWorkerIds, selectedProjectWageTableId],
    async queryFn() {
      const selecteWorkerIds = form.getValues('selectedWorkerIds');

      const userClassifications = await getEmployeeClassifications({
        wageTableId: selectedProject?.wageTableId,
        userIds: selecteWorkerIds.join(','),
        active: true,
      });

      return userClassifications;
    },
    enabled: selectedWorkerIds.length !== 0 && !!selectedProjectWageTableId,
  });

  const [showDetails, setShowDetails] = useState(false);

  // HACK: This is only because clearing the error on date change of DatePicker(V2) doesn't work
  const selectedDate = form.watch(`timesheets.${index}.date`);
  useEffect(() => {
    if (form.getValues(`timesheets.${index}.date`)) {
      form.trigger(`timesheets.${index}.date`);
    }
  }, [form, index, selectedDate]);

  useEffect(() => {
    if (!classificationsQuery.data) return;

    // select the classification for each employee if they're assinged to only *one* classification
    // or set it to `undefined`, so that we can still access the key (userId) in Classification field validation
    const usersWithSomeClassification = [...new Set(classificationsQuery.data.map((c) => c.userId))];

    // first remove a user if it has been removed from Employees Selection field
    const classificationForEachUser = form.getValues(`timesheets.${index}.classificationForEachUser`);
    for (const userId in classificationForEachUser) {
      if (!usersWithSomeClassification.includes(Number(userId))) {
        delete classificationForEachUser[userId];
      }
    }
    form.setValue(`timesheets.${index}.classificationForEachUser`, classificationForEachUser);

    usersWithSomeClassification.forEach((userId) => {
      const classificationsAssignedToEmployee = classificationsQuery.data.filter(
        (classification) => classification.userId === userId
      );
      if (classificationsAssignedToEmployee.length === 1) {
        const classificationForEachUser = form.getValues(`timesheets.${index}.classificationForEachUser`);
        const newObject = {
          ...classificationForEachUser,
          [userId]: classificationsAssignedToEmployee[0].id.toString(),
        };
        form.setValue(`timesheets.${index}.classificationForEachUser`, newObject);
      } else {
        const classificationForEachUser = form.getValues(`timesheets.${index}.classificationForEachUser`);
        const newObject = {
          ...classificationForEachUser,
          [userId]: classificationForEachUser[userId] ?? undefined,
        };
        form.setValue(`timesheets.${index}.classificationForEachUser`, newObject);
      }
    });

    form.trigger(`timesheets.${index}.projectId`);
    form.trigger(`timesheets.${index}.classificationForEachUser`);
  }, [classificationsQuery.data, form, index]);

  const hoursRelatedErrors = [
    form.formState.errors.timesheets?.[index]?.hours,
    form.formState.errors.timesheets?.[index]?.otHours,
    form.formState.errors.timesheets?.[index]?.dotHours,
    form.formState.errors.timesheets?.[index]?.driveTimeHours,
  ].filter((error) => !!error && error.message);

  const firstHourRelatedError = hoursRelatedErrors[0];

  return (
    <fieldset className="grow rounded-8 bg-weak-50 p-2">
      <section
        className="grid gap-2"
        style={{
          gridTemplateColumns: `repeat(${!classificationsQuery.data?.length ? 2 : 3},1fr) 168px ${!company?.timeTrackingSettings.isDriveTimeEnabled ? '226px' : '304px'}`,
        }}
      >
        <Controller
          name={`timesheets.${index}.date`}
          control={form.control}
          rules={{ required: 'Please select a day' }}
          render={({ field, formState }) => {
            return (
              <FormItem required error={!!form.formState.errors.timesheets?.[index]?.date}>
                <FormLabel className="text-xs text-sub-600">Date</FormLabel>

                <FormControl>
                  <DateInput
                    value={field.value ? dayjs(field.value).toDate() : null}
                    onChange={field.onChange}
                    className="bg-white-0"
                  />
                </FormControl>

                <FormMessage className="mt-1">{formState.errors.timesheets?.[index]?.date?.message}</FormMessage>
              </FormItem>
            );
          }}
        />

        <Controller
          name={`timesheets.${index}.projectId`}
          control={form.control}
          rules={{
            required: 'Please select a project',
            validate(_, formValues) {
              // this avoids false positives that an employee doesn't have any classifications, while the data itself has not loaded yet
              if (!classificationsQuery.data) return;

              // if no user classifications found for one or more users, show an error message
              const workersWithoutClassification: string[] = [];
              formValues.selectedWorkerIds.forEach((workerId) => {
                const classification = classificationsQuery.data.find(
                  (classification) => classification.userId === workerId
                );
                if (!classification) {
                  const user = workersQuery.data?.users.find((user) => user.id === workerId);
                  user && workersWithoutClassification.push(`${user.firstName} ${user.lastName}`);
                }
              });
              if (workersWithoutClassification.length > 0) {
                return `${workersWithoutClassification.join(', ')} is not assigned a classification on this project.`;
              }
            },
          }}
          render={({ field }) => (
            <FormItem
              error={!!form.formState.errors['timesheets']?.[index]?.projectId}
              className="grow basis-0 overflow-hidden"
              required
            >
              <FormLabel className="text-xs text-sub-600">Project</FormLabel>
              <FormControl>
                <Combobox
                  className="bg-white-0 hover:bg-weak-50"
                  popoverContentProps={{ className: 'w-auto' }}
                  placeholder={projectsQuery.isPending ? 'Fetching projects...' : 'Select an option'}
                  disabled={projectsQuery.isPending || !!projectId}
                  value={field.value}
                  items={
                    projectsQuery.data?.map((project) => ({
                      label: (
                        <span className="flex gap-1">
                          <span className="truncate">
                            {project.name + (project.projectNumber ? ` (${project.projectNumber})` : '')}
                          </span>
                          {project.isPrevailingWage && (
                            <Tooltip content="Prevailing Wage">
                              <Badge variant="outline" color="gray">
                                PW
                              </Badge>
                            </Tooltip>
                          )}
                        </span>
                      ),
                      value: project.id.toString(),
                    })) ?? []
                  }
                  onChange={(value) => {
                    field.onChange(value);
                    form.trigger('selectedWorkerIds');

                    // clear the user classification error that's set from classificationsQuery
                    form.clearErrors(`timesheets.${index}.projectId`);
                  }}
                />
              </FormControl>
              <FormMessage>{form.formState.errors['timesheets']?.[index]?.projectId?.message}</FormMessage>
            </FormItem>
          )}
        />

        {Boolean(classificationsQuery.data?.length) && (
          <Controller
            name={`timesheets.${index}.classificationForEachUser`}
            control={form.control}
            rules={{
              required: 'Please select a classification',
              validate(_, formValues) {
                const classificationForEachUser = formValues.timesheets[index].classificationForEachUser;
                const userIds = Object.keys(classificationForEachUser);
                const isClassificationForEachEmployeeSelected = userIds.every(
                  (userId) => classificationForEachUser[Number(userId)]
                );

                if (!isClassificationForEachEmployeeSelected) {
                  return 'Select a classification for each employee.';
                }
              },
            }}
            render={({ field }) => {
              const userIds = Object.keys(field.value);
              const isClassificationForEachUserSelected = userIds.every((userId) => field.value[Number(userId)]);

              return (
                <FormItem
                  error={!!form.formState.errors['timesheets']?.[index]?.classificationForEachUser}
                  disabled={!classificationsQuery.data?.length}
                  className="grow basis-0 overflow-hidden"
                  required
                >
                  <FormLabel className="text-xs text-sub-600">Classification</FormLabel>
                  <FormControl>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          type="button"
                          color="neutral"
                          variant="outline"
                          className={cn('justify-between bg-white-0 hover:border-soft-200', {
                            'border-error-base':
                              !!form.formState.errors['timesheets']?.[index]?.classificationForEachUser,
                          })}
                          afterContent={<RiArrowDownSLine />}
                        >
                          {isClassificationForEachUserSelected
                            ? Object.values(field.value)
                                .filter(Boolean)
                                .map(
                                  (classificationId) =>
                                    classificationsQuery.data?.find(
                                      (classification) => classification.id === Number(classificationId)
                                    )?.classification.name
                                )
                                .join(', ')
                            : 'Select'}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="mt-2 flex w-[250px] flex-col p-2" align="start">
                        {selectedWorkerIds.length === 0 ? (
                          <div className="p-3 text-xs text-error-base">Please select one or more employees first.</div>
                        ) : (
                          <ScrollArea className="max-h-[400px]">
                            <ul className="flex flex-col gap-1 text-sm">
                              {selectedWorkerIds.map((workerId) => {
                                const user = workersQuery.data?.users.find((user) => user.id === workerId);
                                if (!user) return;

                                const userClassifications = classificationsQuery.data?.filter(
                                  (classification) => classification.userId === user.id
                                );

                                if (userClassifications?.length === 0) return;

                                return (
                                  <li key={user.id} className="p-2 font-medium">
                                    {user.firstName} {user.lastName}
                                    <RadioGroup
                                      value={field.value[user.id]}
                                      onValueChange={(value) => {
                                        field.onChange({
                                          ...field.value,
                                          [user.id]: value,
                                        });

                                        form.trigger(`timesheets.${index}.classificationForEachUser`);
                                      }}
                                      className="!mt-3 pl-9"
                                    >
                                      {userClassifications?.map((classification) => {
                                        return (
                                          <div className="flex items-center space-x-2" key={classification.id}>
                                            <RadioGroupItem
                                              value={classification.id.toString()}
                                              id={classification.id.toString()}
                                            />
                                            <Label className="font-normal" htmlFor={classification.id.toString()}>
                                              {classification.classification.name}
                                            </Label>
                                          </div>
                                        );
                                      })}
                                    </RadioGroup>
                                  </li>
                                );
                              })}
                            </ul>
                          </ScrollArea>
                        )}
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  <FormMessage>
                    {form.formState.errors['timesheets']?.[index]?.classificationForEachUser?.message}
                  </FormMessage>
                </FormItem>
              );
            }}
          />
        )}

        <FormItem error={!!form.formState.errors.timesheets?.[index]?.clockIn?.message}>
          <FormLabel className="text-xs text-sub-600">Clock In - Clock Out</FormLabel>
          <FormControl>
            <div className="flex h-10 items-center justify-center gap-px rounded-10 border border-soft-200 bg-white-0 aria-[invalid=true]:border-error-base">
              <Controller
                control={form.control}
                name={`timesheets.${index}.clockIn`}
                rules={{
                  validate() {
                    const clockIn = form.getValues(`timesheets.${index}.clockIn`);
                    const clockOut = form.getValues(`timesheets.${index}.clockOut`);
                    const hoursDiff = dayjs(clockOut).diff(dayjs(clockIn), 'hour', true);

                    if (hoursDiff < 0) {
                      return 'Clock Out must be after Clock In';
                    }
                  },
                }}
                render={({ field }) => (
                  <TimeInput
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(value);

                      if (!value) return;

                      let clockOut = form.getValues(`timesheets.${index}.clockOut`);

                      // if clock out is before clock in then consider the clock out time to be of the next day
                      const dayjsObj = dayjs(clockOut);
                      if (minutesAfterMidnight(dayjs(clockOut)) < minutesAfterMidnight(dayjs(value))) {
                        clockOut = dayjs(value).add(1, 'day').hour(dayjsObj.hour()).minute(dayjsObj.minute()).toDate();
                      } else {
                        clockOut = dayjs(value).hour(dayjsObj.hour()).minute(dayjsObj.minute()).toDate();
                      }
                      form.setValue(`timesheets.${index}.clockOut`, clockOut);

                      const hoursDiff = dayjs(clockOut).diff(dayjs(value), 'hour', true);
                      form.setValue(`timesheets.${index}.hours`, Number(hoursDiff.toFixed(2)));
                    }}
                    className="w-[7ch] border-none p-0 ring-transparent focus-within:ring-0 focus-within:ring-offset-0 hover:bg-transparent hover:text-inherit [&>svg]:hidden"
                  />
                )}
              />
              -
              <Controller
                control={form.control}
                name={`timesheets.${index}.clockOut`}
                render={({ field }) => (
                  <TimeInput
                    value={field.value}
                    onChange={(value) => {
                      if (!value) return;

                      const clockIn = form.getValues(`timesheets.${index}.clockIn`);

                      // if clock out is before clock in then consider the clock out time to be of the next day
                      const dayjsObj = dayjs(value);
                      if (minutesAfterMidnight(dayjs(value)) < minutesAfterMidnight(dayjs(clockIn))) {
                        value = dayjs(clockIn).add(1, 'day').hour(dayjsObj.hour()).minute(dayjsObj.minute()).toDate();
                      } else {
                        value = dayjs(clockIn).hour(dayjsObj.hour()).minute(dayjsObj.minute()).toDate();
                      }
                      field.onChange(value);

                      const hoursDiff = dayjs(value).diff(dayjs(clockIn), 'hour', true);
                      form.setValue(`timesheets.${index}.hours`, Number(hoursDiff.toFixed(2)));
                    }}
                    className="w-[7ch] border-none p-0 ring-transparent focus-within:ring-0 focus-within:ring-offset-0 hover:bg-transparent hover:text-inherit [&>svg]:hidden"
                  />
                )}
              />
            </div>
          </FormControl>
          <FormMessage>{form.formState.errors.timesheets?.[index]?.clockIn?.message}</FormMessage>
        </FormItem>

        <div
          className="grid gap-2"
          style={{
            gridTemplateColumns: `repeat(${!company?.timeTrackingSettings.isDriveTimeEnabled ? 3 : 4},1fr)`,
          }}
        >
          <Controller
            name={`timesheets.${index}.hours`}
            control={form.control}
            rules={{
              required: 'Required',
              validate(workedHoursValue, formValues) {
                const breakDuration = getBreakDuration({
                  areRealtimeBreaksEnabled: company?.timeTrackingSettings.areRealtimeBreaksEnabled,
                  formValues,
                  index,
                });

                return validateFormHours({
                  formAttribute: 'hours',
                  formValues,
                  index,
                  form,
                  breakDuration,
                });
              },
            }}
            render={({ field: { onChange, value } }) => (
              <FormItem error={!!form.formState.errors['timesheets']?.[index]?.hours?.message}>
                <FormLabel className="text-xs text-sub-600">Total Hours</FormLabel>
                <FormControl>
                  <Input
                    value={value}
                    onChange={(event) => {
                      form.clearErrors(`timesheets.${index}.hours`);
                      form.clearErrors(`timesheets.${index}.otHours`);
                      form.clearErrors(`timesheets.${index}.dotHours`);
                      form.clearErrors(`timesheets.${index}.driveTimeHours`);

                      onChange(event.target.value);

                      // update the clockin and clock out time based on hours
                      // clockIn should start with 8:00 AM and clockOut should be 8:00 AM + hours
                      form.setValue(`timesheets.${index}.clockIn`, dayjs().startOf('day').set('hour', 8).toDate());
                      form.setValue(
                        `timesheets.${index}.clockOut`,
                        dayjs()
                          .startOf('day')
                          .set('minutes', 8 * 60 + Number(event.target.value) * 60) // covert hours to minutes because setting fractional hours (e.g. 4.5) only applies 4
                          .toDate()
                      );
                    }}
                    type="number"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <Controller
            name={`timesheets.${index}.otHours`}
            control={form.control}
            rules={{
              validate(otHours, formValues) {
                // Skip validation if otHours is null (automatic OT)
                if (otHours === null || otHours === undefined) {
                  return;
                }

                const breakDuration = getBreakDuration({
                  areRealtimeBreaksEnabled: company?.timeTrackingSettings.areRealtimeBreaksEnabled,
                  formValues,
                  index,
                });

                const workedHours = formValues.timesheets[index].hours - breakDuration;

                if (otHours > workedHours) {
                  return `OT hours (${otHours}h) must be less than worked hours (${workedHours}h)`;
                }

                return validateFormHours({
                  formAttribute: 'otHours',
                  formValues,
                  index,
                  form,
                  breakDuration,
                });
              },
            }}
            render={({ field: { onChange, value } }) => (
              <FormItem error={!!form.formState.errors['timesheets']?.[index]?.otHours?.message}>
                <FormLabel className="text-xs text-sub-600">OT Hours</FormLabel>
                <FormControl>
                  <Input
                    value={value ?? ''}
                    onChange={(event) => {
                      const inputValue = event.target.value;
                      // Set to null if empty, otherwise convert to number
                      onChange(inputValue === '' ? null : Number(inputValue));
                      form.clearErrors(`timesheets.${index}.otHours`);
                      form.clearErrors(`timesheets.${index}.hours`);
                      form.clearErrors(`timesheets.${index}.dotHours`);
                      form.clearErrors(`timesheets.${index}.driveTimeHours`);
                    }}
                    type="number"
                    placeholder="Auto"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <Controller
            name={`timesheets.${index}.dotHours`}
            control={form.control}
            rules={{
              validate(dotHours, formValues) {
                // Skip validation if dotHours is null (automatic OT)
                if (dotHours === null || dotHours === undefined) {
                  return;
                }

                const breakDuration = getBreakDuration({
                  areRealtimeBreaksEnabled: company?.timeTrackingSettings.areRealtimeBreaksEnabled,
                  formValues,
                  index,
                });

                const workedHours = formValues.timesheets[index].hours - breakDuration;

                if (dotHours > workedHours) {
                  return `DOT hours (${dotHours}h) must be less than worked hours (${workedHours}h)`;
                }

                return validateFormHours({
                  formAttribute: 'dotHours',
                  formValues,
                  index,
                  form,
                  breakDuration,
                });
              },
            }}
            render={({ field: { onChange, value } }) => (
              <FormItem error={!!form.formState.errors['timesheets']?.[index]?.dotHours?.message}>
                <FormLabel className="text-xs text-sub-600">DOT Hours</FormLabel>
                <FormControl>
                  <Input
                    value={value ?? ''}
                    onChange={(event) => {
                      const inputValue = event.target.value;
                      // Set to null if empty, otherwise convert to number
                      onChange(inputValue === '' ? null : Number(inputValue));
                      form.clearErrors(`timesheets.${index}.dotHours`);
                      form.clearErrors(`timesheets.${index}.hours`);
                      form.clearErrors(`timesheets.${index}.otHours`);
                      form.clearErrors(`timesheets.${index}.driveTimeHours`);
                    }}
                    type="number"
                    placeholder="Auto"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {company?.timeTrackingSettings.isDriveTimeEnabled && (
            <Controller
              name={`timesheets.${index}.driveTimeHours`}
              control={form.control}
              rules={{
                validate(driveTimeHours, formValues) {
                  const breakDuration = getBreakDuration({
                    areRealtimeBreaksEnabled: company?.timeTrackingSettings.areRealtimeBreaksEnabled,
                    formValues,
                    index,
                  });

                  const workedHours = formValues.timesheets[index].hours - breakDuration;

                  if (driveTimeHours > workedHours) {
                    return `Drive Time hours (${driveTimeHours}h) must be less than worked hours (${workedHours}h)`;
                  }

                  return validateFormHours({
                    formAttribute: 'driveTimeHours',
                    formValues,
                    index,
                    form,
                    breakDuration,
                  });
                },
              }}
              render={({ field }) => (
                <FormItem error={!!form.formState.errors['timesheets']?.[index]?.driveTimeHours}>
                  <FormLabel className="text-xs text-sub-600">Drive Hours</FormLabel>
                  <FormControl>
                    <Input
                      value={field.value}
                      onChange={(event) => {
                        form.clearErrors(`timesheets.${index}.driveTimeHours`);
                        form.clearErrors(`timesheets.${index}.hours`);
                        form.clearErrors(`timesheets.${index}.otHours`);
                        form.clearErrors(`timesheets.${index}.dotHours`);
                        field.onChange(event.target.value);
                      }}
                      type="number"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}
          <FormItem
            error={!!firstHourRelatedError}
            className={!company?.timeTrackingSettings.isDriveTimeEnabled ? 'col-span-3' : 'col-span-4'}
          >
            <FormMessage>{firstHourRelatedError?.message}</FormMessage>
          </FormItem>
        </div>
      </section>

      {showDetails && (
        <>
          <hr className="mt-2 border-soft-200" />

          <section
            className="mt-2 grid gap-2"
            style={{
              gridTemplateColumns: `repeat(3,1fr) 168px ${!company?.timeTrackingSettings.isDriveTimeEnabled ? '226px' : '304px'}`,
            }}
          >
            <Controller
              name={`timesheets.${index}.costCodeId`}
              control={form.control}
              rules={{
                required: company?.timeTrackingSettings.isCostCodeRequired ? 'Please select a cost code' : false,
              }}
              render={({ field: { onChange, value } }) => (
                <FormItem
                  className="grow basis-0 overflow-hidden"
                  error={!!form.formState.errors['timesheets']?.[index]?.costCodeId}
                  required={company?.timeTrackingSettings.isCostCodeRequired}
                >
                  <FormLabel className="text-xs text-sub-600">Cost Code</FormLabel>
                  <FormControl>
                    <Combobox
                      className="bg-white-0 hover:bg-weak-50"
                      popoverContentProps={{ className: 'w-auto' }}
                      placeholder={costCodesQuery.isPending ? 'Fetching cost codes...' : 'Select an option'}
                      value={value}
                      items={
                        costCodesQuery.data?.map((item) => ({
                          label: item.name,
                          value: item.id.toString(),
                        })) ?? []
                      }
                      onChange={(value) => {
                        onChange(value || null);

                        const workersCompCodeId = form.getValues(`timesheets.${index}.workersCompCodeId`);
                        if (!workersCompCodeId) {
                          const selectedCostCode = costCodesQuery.data?.find((code) => code.id === Number(value));
                          if (selectedCostCode?.workersCompCode?.id) {
                            form.setValue(
                              `timesheets.${index}.workersCompCodeId`,
                              String(selectedCostCode.workersCompCode.id)
                            );
                          }
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage>{form.formState.errors['timesheets']?.[index]?.costCodeId?.message}</FormMessage>
                </FormItem>
              )}
            />

            <Controller
              name={`timesheets.${index}.workersCompCodeId`}
              control={form.control}
              render={({ field }) => {
                return (
                  <FormItem error={!!form.formState.errors['timesheets']?.[index]?.workersCompCodeId}>
                    <FormLabel className="text-xs text-sub-600">Workers Comp Code</FormLabel>
                    <FormControl>
                      <Combobox
                        className="bg-white-0 hover:bg-weak-50"
                        popoverContentProps={{ className: 'w-auto' }}
                        placeholder="Select an option"
                        value={field.value}
                        items={
                          workersCompCodesQuery.data
                            ?.filter((item) => !item.isArchived)
                            .map((item: WorkersCompCode) => ({
                              label: `${item.name} • ${item.code}`,
                              value: item.id.toString(),
                            })) ?? []
                        }
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors['timesheets']?.[index]?.workersCompCodeId?.message}
                    </FormMessage>
                  </FormItem>
                );
              }}
            />

            <Controller
              name={`timesheets.${index}.equipmentId`}
              control={form.control}
              render={({ field }) => {
                return (
                  <FormItem error={!!form.formState.errors['timesheets']?.[index]?.equipmentId}>
                    <FormLabel className="text-xs text-sub-600">Equipment</FormLabel>
                    <FormControl>
                      <Combobox
                        className="bg-white-0 hover:bg-weak-50"
                        popoverContentProps={{ className: 'w-auto' }}
                        placeholder="Select an option"
                        value={field.value}
                        items={
                          equipmentQuery.data?.map((singleEquipment) => ({
                            label: singleEquipment.name,
                            value: singleEquipment.id.toString(),
                          })) ?? []
                        }
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage>{form.formState.errors['timesheets']?.[index]?.equipmentId?.message}</FormMessage>
                  </FormItem>
                );
              }}
            />

            <Controller
              name={`timesheets.${index}.description`}
              control={form.control}
              render={({ field }) => (
                <FormItem
                  error={!!form.formState.errors['timesheets']?.[index]?.description?.message}
                  className="col-start-4 -col-end-1"
                >
                  <FormLabel className="text-xs text-sub-600">Notes</FormLabel>
                  <FormControl>
                    <Input value={field.value} onChange={field.onChange} />
                  </FormControl>
                  <FormMessage>{form.formState.errors['timesheets']?.[index]?.description?.message}</FormMessage>
                </FormItem>
              )}
            />
          </section>
        </>
      )}

      <LinkButton className="mt-2" onClick={() => setShowDetails((_showDetails) => !_showDetails)}>
        {showDetails ? 'Hide' : 'Show'} Details{' '}
        <RiArrowDownSLine className={`inline-block size-4 ${showDetails && 'rotate-180'}`} />
      </LinkButton>
    </fieldset>
  );
}

function RealTimeBreaks({ form, index }: { form: UseFormReturn<FormValues>; index: number }) {
  const breakArray = useFieldArray({
    name: `timesheets.${index}.breaks`,
    control: form.control,
  });

  return breakArray.fields.map((field, breakIndex) => (
    <div className="mt-1.5 flex items-center" key={field.id}>
      <span className="relative h-14 w-20 overflow-hidden">
        <span className="absolute bottom-7 left-16 h-40 w-40 rounded-12 border border-r-0 border-t-0 border-sub-300"></span>
      </span>
      <fieldset className="ml-1 flex grow items-start gap-2 rounded-8 bg-weak-50 p-2">
        <p className="my-auto px-1 text-xs text-faded-base">
          <RiZzzLine className="mr-0.5 size-3" /> BREAK
        </p>

        <span className="ml-auto h-10 w-px bg-sub-300" />

        <FormItem
          error={!!form.formState.errors.timesheets?.[index]?.breaks?.[breakIndex]?.start}
          className="w-[168px]"
        >
          <FormControl>
            <div className="flex h-10 items-center justify-center rounded-10 border border-soft-200 bg-white-0 aria-[invalid=true]:border-error-base">
              <Controller
                control={form.control}
                name={`timesheets.${index}.breaks.${breakIndex}.start`}
                rules={{
                  validate() {
                    const breakStart = form.getValues(`timesheets.${index}.breaks.${breakIndex}.start`);
                    const breakEnd = form.getValues(`timesheets.${index}.breaks.${breakIndex}.end`);
                    const hoursDiff = dayjs(breakEnd).diff(dayjs(breakStart), 'hour', true);

                    if (hoursDiff < 0) {
                      return 'Break End must be after Break Start';
                    }
                  },
                }}
                render={({ field }) => (
                  <TimeInput
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(value);

                      // update duration based on break start and end time
                      if (!value) return;
                      const breakEnd = form.getValues(`timesheets.${index}.breaks.${breakIndex}.end`);
                      form.setValue(
                        `timesheets.${index}.breaks.${breakIndex}.duration`,
                        dayjs(breakEnd).diff(dayjs(value), 'hour', true)
                      );
                    }}
                    className="w-[7ch] border-none p-0 ring-transparent focus-within:ring-0 focus-within:ring-offset-0 hover:bg-transparent hover:text-inherit [&>svg]:hidden"
                  />
                )}
              />
              -
              <Controller
                control={form.control}
                name={`timesheets.${index}.breaks.${breakIndex}.end`}
                render={({ field }) => (
                  <TimeInput
                    value={field.value}
                    onChange={(value) => {
                      field.onChange(value);

                      // update duration based on break start and end time
                      if (!value) return;
                      const breakStart = form.getValues(`timesheets.${index}.breaks.${breakIndex}.start`);
                      form.setValue(
                        `timesheets.${index}.breaks.${breakIndex}.duration`,
                        dayjs(value).diff(dayjs(breakStart), 'hour', true)
                      );
                    }}
                    className="w-[7ch] border-none p-0 ring-transparent focus-within:ring-0 focus-within:ring-offset-0 hover:bg-transparent hover:text-inherit [&>svg]:hidden"
                  />
                )}
              />
            </div>
          </FormControl>
          <FormMessage>{form.formState.errors.timesheets?.[index]?.breaks?.[breakIndex]?.start?.message}</FormMessage>
        </FormItem>

        <Controller
          name={`timesheets.${index}.breaks.${breakIndex}.duration`}
          control={form.control}
          rules={{ required: 'Required' }}
          render={({ field: { onChange, value } }) => (
            <FormItem error={!!form.formState.errors['timesheets']?.[index]?.breaks?.[breakIndex]?.duration}>
              <FormControl>
                <Input
                  className="w-[70px]"
                  value={value}
                  onChange={(event) => {
                    onChange(event.target.value);

                    // update the break end time based on break duration
                    const breakStart = form.getValues(`timesheets.${index}.breaks.${breakIndex}.start`);
                    form.setValue(`timesheets.${index}.breaks.${breakIndex}.start`, breakStart);
                    form.setValue(
                      `timesheets.${index}.breaks.${breakIndex}.end`,
                      dayjs(breakStart).add(Number(event.target.value), 'hour').toDate()
                    );
                  }}
                  type="number"
                />
              </FormControl>
              <FormMessage>
                {form.formState.errors['timesheets']?.[index]?.breaks?.[breakIndex]?.duration?.message}
              </FormMessage>
            </FormItem>
          )}
        />
      </fieldset>
      <Button
        className="ml-3 mr-[42px]"
        type="button"
        size="2x-small"
        color="neutral"
        variant="outline"
        beforeContent={<RiDeleteBinLine />}
        onClick={() => breakArray.remove(breakIndex)}
      />
    </div>
  ));
}

function getCommonWorkersCompCodeId(selectedWorkerIds: number[], users: HammrUser[] | undefined): string | null {
  if (selectedWorkerIds.length === 0 || !users) return null;

  const selectedEmployees = users.filter((user) => selectedWorkerIds.includes(user.id));

  if (selectedEmployees.length === 0) return null;

  // Get the first employee's workers comp code ID as reference
  const firstWorkersCompCodeId = selectedEmployees[0]?.workersCompCode?.id;

  // If the first employee doesn't have a workers comp code, return null
  if (!firstWorkersCompCodeId) return null;

  // Check if all selected employees have the same workers comp code ID
  const allHaveSameCode = selectedEmployees.every(
    (employee) => employee.workersCompCode?.id === firstWorkersCompCodeId
  );

  return allHaveSameCode ? String(firstWorkersCompCodeId) : null;
}

function closePopover() {
  // close the popover by triggering a Esc key press event
  const event = new KeyboardEvent('keydown', { key: 'Escape' });
  document.dispatchEvent(event);
}
