import { Controller, useFieldArray, UseFormReturn } from 'react-hook-form';
import moment from 'moment';
import { Break } from 'interfaces/timesheet';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import Button from '@/hammr-ui/components/button';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { TimeInput } from '@/hammr-ui/components/time-input';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { useState } from 'react';

interface BreakFormData {
  breaks: Break[];
}

interface RealtimeBreaksProps<T extends BreakFormData> {
  form: UseFormReturn<any>;
}

export function RealtimeBreaks<T extends BreakFormData>({ form }: RealtimeBreaksProps<T>) {
  // HACK: after setting isDeleted field, the component doesn't update
  // so forcefully update it
  const [, setState] = useState(0);
  function forceUpdate() {
    setState((prev) => prev + 1);
  }

  const { control, watch } = form;

  const items = useFieldArray({ control, name: 'breaks' });

  const watchBreaks = watch('breaks');

  return (
    <div className="flex flex-col gap-5">
      {items.fields.map((field, index) => {
        if (watchBreaks[index]?.isDeleted) return null;

        return (
          <div key={field.id} className="flex gap-3">
            <div className="flex-1">
              <Controller
                control={control}
                name={`breaks[${index}].start`}
                render={({ field, fieldState }) => (
                  <FormItem required error={!!fieldState.error}>
                    <FormLabel>Break Start</FormLabel>
                    <TimeInput
                      value={field.value ? moment(field.value).toDate() : null}
                      onChange={(value) => {
                        field.onChange(value ? value.getTime() : null);
                      }}
                    />
                    <FormMessage>{fieldState.error?.message}</FormMessage>
                  </FormItem>
                )}
              />
            </div>
            <div className="flex-1">
              <Controller
                control={control}
                name={`breaks[${index}].end`}
                render={({ field, fieldState }) => (
                  <FormItem required error={!!fieldState.error}>
                    <FormLabel>Break End</FormLabel>
                    <TimeInput
                      value={field.value ? moment(field.value).toDate() : null}
                      onChange={(value) => {
                        field.onChange(value ? value.getTime() : null);
                      }}
                    />
                    <FormMessage>{fieldState.error?.message}</FormMessage>
                  </FormItem>
                )}
              />
            </div>
            <div>
              <Controller
                control={control}
                name={`breaks[${index}].isDeleted`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>&nbsp;</FormLabel>
                    <FormControl>
                      <Button
                        // size="large"
                        variant="ghost"
                        onClick={() => {
                          if (watchBreaks[index]?.id) {
                            field.onChange(true);
                            forceUpdate();
                          } else {
                            items.remove(index);
                          }
                        }}
                        color="neutral"
                        beforeContent={<DeleteBinLine />}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
        );
      })}
      <div>
        <LinkButton
          size="medium"
          className="text-primary-base"
          onClick={() => {
            items.append({ start: null, end: null });
          }}
        >
          Add break
        </LinkButton>
      </div>
    </div>
  );
}
