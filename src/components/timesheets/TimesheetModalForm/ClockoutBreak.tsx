import { Controller, Path, UseFormReturn } from 'react-hook-form';
import { Company } from 'interfaces/company';
import { ButtonGroup, ButtonGroupItem } from '@/hammr-ui/components/ButtonGroup';

interface BreakFormData {
  breakDuration: number | null;
}

interface RealtimeBreaksProps<T extends BreakFormData> {
  form: UseFormReturn<T>;
  company: Company;
}

export function ClockoutBreak<T extends BreakFormData>({ form, company }: RealtimeBreaksProps<T>) {
  const { control } = form;

  return (
    <div className="flex items-center justify-between text-sm font-medium text-strong-950">
      <label>Breaks</label>
      <Controller
        control={control}
        name={'breakDuration' as Path<T>}
        render={({ field: { onChange, value } }) => (
          <ButtonGroup className="w-80">
            {company.timeTrackingSettings.breakOptions.map((option: number) => {
              return (
                <ButtonGroupItem key={option} onClick={() => onChange(option * 60)} active={value === option * 60}>
                  {option === 0 ? 'No' : `${option} min`}
                </ButtonGroupItem>
              );
            })}
          </ButtonGroup>
        )}
      />
    </div>
  );
}
