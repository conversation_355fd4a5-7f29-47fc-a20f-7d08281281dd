import { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { useEmployees } from 'hooks/data-fetching/useEmployees';
import { HammrUser } from 'interfaces/user';
import { EditCrew, Crew } from 'interfaces/crew';
import { updateCrew } from 'services/crew';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import TeamLine from '@/hammr-icons/TeamLine';
import { FormV2 } from '../elements/Form';
import CrewForm from './CrewForm';

interface EditCrewModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  orgId: number;
  currentCrewData: Crew;
  callback?: () => void;
}

interface FormData {
  crewName: string;
  crewLead?: string;
  workerIds: number[];
}

export default function EditCrewModal({ open, setOpen, orgId, currentCrewData, callback }: EditCrewModalProps) {
  const { addToast } = useToast();
  const employees = useEmployees(Number(orgId), { simple: true });
  const {
    register,
    handleSubmit,
    control,
    watch,
    setError,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      crewName: '',
      crewLead: '',
      workerIds: [] as number[],
    },
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [eligibleCrewLeads, setEligibleCrewLeads] = useState<HammrUser[]>([]);

  useEffect(() => {
    const potentialCrewLeads = employees.filter((employee) => {
      return employee.role === 'ADMIN' || employee.role === 'FOREMAN';
    });

    setEligibleCrewLeads(potentialCrewLeads);
  }, [employees, orgId]);

  useEffect(() => {
    if (!open || !currentCrewData) {
      return;
    }

    reset({
      crewName: currentCrewData.name,
      crewLead: `${currentCrewData.crewLead}`,
      workerIds: currentCrewData.crewMembers.map((crewMember) => crewMember.crewMemberUser.id),
    });
  }, [open, currentCrewData, reset]);

  const crewLead = watch('crewLead');

  const onSubmit = async (data: FormData) => {
    setIsProcessing(true);

    if (!data.crewName) {
      setError('crewName', {
        type: 'manual',
        message: 'Please enter a crew name',
      });
      setIsProcessing(false);
      return;
    }

    const updateCrewPayload: Partial<EditCrew> = {};

    if (data.crewName) {
      updateCrewPayload.name = data.crewName;
    }

    if (data.crewLead && `${data.crewLead}` !== 'null') {
      updateCrewPayload.crewLead = parseInt(data.crewLead);
    }

    if (data.workerIds.length) {
      updateCrewPayload.crewMembers = data.workerIds.join(',');
    }

    try {
      await updateCrew(currentCrewData.id, updateCrewPayload);

      addToast({
        title: 'Edited Crew',
        description: (
          <>
            Successfully edited the crew <strong className="font-medium">{data.crewName}</strong>.
          </>
        ),
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      if (callback) {
        callback();
      }

      setOpen(false);
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Unable to update crew');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<TeamLine className="text-sub-600" />} title="Edit Crew" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Save"
        >
          <CrewForm
            control={control}
            register={register}
            errors={errors}
            eligibleCrewLeads={eligibleCrewLeads}
            crewLead={crewLead}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
