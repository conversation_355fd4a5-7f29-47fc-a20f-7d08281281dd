import { useState, useEffect, Dispatch, SetStateAction } from 'react';
import { useForm } from 'react-hook-form';

import { useToast } from 'hooks/useToast';
import { useEmployees } from 'hooks/data-fetching/useEmployees';

import { HammrUser } from 'interfaces/user';
import { CreateCrew } from 'interfaces/crew';
import { createCrew } from 'services/crew';

import { logError, showErrorToast } from 'utils/errorHandling';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import TeamLine from '@/hammr-icons/TeamLine';
import { FormV2 } from '../elements/Form';
import CrewForm from './CrewForm';

interface CreateCrewModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  orgId: number;
  callback?: () => void;
}

interface FormData {
  crewName: string;
  crewLead?: string;
  workerIds: number[];
}

export default function CreateCrewModal({ open, setOpen, orgId, callback }: CreateCrewModalProps) {
  const { addToast } = useToast();
  const employees = useEmployees(Number(orgId), { simple: true });
  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
    setError,
    reset,
  } = useForm({
    defaultValues: {
      crewName: '',
      crewLead: '',
      workerIds: [] as number[],
    },
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [eligibleCrewLeads, setEligibleCrewLeads] = useState<HammrUser[]>([]);

  const crewLead = watch('crewLead');

  useEffect(() => {
    const potentialCrewLeads = employees.filter((employee) => {
      return employee.role === 'ADMIN' || employee.role === 'FOREMAN';
    });

    setEligibleCrewLeads(potentialCrewLeads);
  }, [employees, orgId]);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  const onSubmit = async (data: FormData) => {
    setIsProcessing(true);

    if (!data.crewName) {
      setError('crewName', {
        type: 'manual',
        message: 'Please enter a crew name',
      });
      setIsProcessing(false);
      return;
    }

    const crewPayload: Partial<CreateCrew> = {
      name: data.crewName,
    };

    if (data.crewLead) {
      crewPayload.crewLead = parseInt(data.crewLead);
    }

    if (data.workerIds.length) {
      crewPayload.crewMembers = data.workerIds.join(',');
    }

    try {
      const res = await createCrew(crewPayload);

      if (res?.error) {
        addToast({
          type: 'error',
          title: res.error.message,
        });
        return;
      } else {
        addToast({
          type: 'success',
          title: 'Created Crew',
          description: (
            <>
              Successfully created the crew <strong className="font-medium">{data.crewName}</strong>.
            </>
          ),
        });
      }

      // should call refresh (callback) and then close modal
      if (callback) {
        callback();
      }

      setOpen(false);
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Unable to create crew');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<TeamLine className="text-sub-600" />} title="Create Crew" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Create"
        >
          <CrewForm
            register={register}
            control={control}
            errors={errors}
            crewLead={crewLead}
            eligibleCrewLeads={eligibleCrewLeads}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
