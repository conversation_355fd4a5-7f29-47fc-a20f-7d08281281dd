import { Dispatch, SetStateAction } from 'react';
import { useToast } from 'hooks/useToast';
import { deleteCrew } from 'services/crew';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';

interface ConfirmDeleteCrewModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentCrewData: any;
  callback: () => void;
}

export default function ConfirmDeleteCrewModal({
  open,
  setOpen,
  currentCrewData,
  callback,
}: ConfirmDeleteCrewModalProps) {
  const { addToast } = useToast();

  const confirmDeleteEventHandler = async () => {
    try {
      await deleteCrew(currentCrewData?.id);
      addToast({
        title: 'Deleted Crew',
        description: (
          <>
            Successfully deleted the crew{' '}
            <strong className="font-medium">{currentCrewData.name}</strong>.
          </>
        ),
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to delete crew');
    } finally {
      setOpen(false);
    }
  };

  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      onConfirm={confirmDeleteEventHandler}
      data={[]}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      title="Delete Crew"
      subtitle={
        <>
          You’re about to delete the crew{' '}
          {<span className="font-medium">{currentCrewData?.name}</span>}. Do you
          want to proceed?
        </>
      }
      confirmButton={{
        color: 'error',
      }}
      confirmButtonText="Delete"
    />
  );
}
