import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';
import { Combobox } from '@/hammr-ui/components/combobox';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { HammrUser } from '@/interfaces/user';
import { Control, Controller, FieldErrors, UseFormRegister } from 'react-hook-form';

type T = {
  crewName: string;
  crewLead: string;
  workerIds: number[];
};

interface Props {
  control: Control<T>;
  register: UseFormRegister<T>;
  errors: FieldErrors<T>;
  eligibleCrewLeads: HammrUser[];
  crewLead: string;
}

export default function CrewForm({ control, register, errors, eligibleCrewLeads, crewLead }: Props) {
  return (
    <>
      <FormItem required error={!!errors['crewName']}>
        <FormLabel>Crew Name</FormLabel>
        <FormControl>
          <Input
            name="crewName"
            placeholder="Enter crew name"
            {...register('crewName', {
              required: 'Please enter a crew name',
            })}
          />
        </FormControl>
        <FormMessage>{errors['crewName']?.message}</FormMessage>
      </FormItem>

      <FormItem error={!!errors['crewLead']} className="mt-5">
        <FormLabel>Crew Lead</FormLabel>
        <FormControl>
          <Controller
            control={control}
            name="crewLead"
            render={({ field }) => (
              <Combobox
                placeholder="Select an option"
                className="mt-1 w-full"
                items={eligibleCrewLeads.map((crewLead) => {
                  return {
                    label: crewLead.fullName,
                    value: crewLead.id.toString(),
                  };
                })}
                value={crewLead.toString()}
                onChange={(value) => {
                  field.onChange(Number(value));
                }}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['crewLead']?.message}</FormMessage>
      </FormItem>

      <Controller
        control={control}
        name="workerIds"
        render={({ field }) => (
          <FormItem className="mt-5">
            <FormLabel>
              Employees{' '}
              {field.value.length ? (
                <span className="font-normal text-sub-600">({field.value.length} selected)</span>
              ) : undefined}
            </FormLabel>
            <FormControl>
              <WorkerCrewSelect selectedWorkerIds={field.value} onChange={field.onChange} />
            </FormControl>
          </FormItem>
        )}
      />
    </>
  );
}
