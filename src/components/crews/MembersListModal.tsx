import TeamLine from '@/hammr-icons/TeamLine';
import { Dialog, DialogBody, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { Dispatch, SetStateAction } from 'react';

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentCrewData: any;
}

export default function MembersListModal({ open, setOpen, currentCrewData }: Props) {
  if (!currentCrewData) {
    return null;
  }

  const { crewLeadUser, crewMembers } = currentCrewData;
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface className="w-[25rem]">
        <DialogHeader title={`${currentCrewData.name} Members List`} icon={<TeamLine className="text-sub-600" />} />
        <DialogBody>
          <h2 className="text-xs text-sub-600">Crew Lead</h2>
          <span className="mt-1.5 block text-sm text-strong-950">
            {crewLeadUser ? <>{crewLeadUser.firstName + ' ' + crewLeadUser.lastName}</> : <>-</>}
          </span>
          <h2 className="mt-5 text-xs text-sub-600">Members List</h2>
          <div className="mt-1.5 flex flex-col gap-1.5">
            {crewMembers.length !== 0 ? (
              crewMembers.map(({ id, crewMemberUser }) => (
                <span key={id} className="block text-sm text-strong-950">
                  {crewMemberUser.firstName + ' ' + crewMemberUser.lastName}
                </span>
              ))
            ) : (
              <span className="block text-sm text-strong-950">-</span>
            )}
          </div>
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
}
