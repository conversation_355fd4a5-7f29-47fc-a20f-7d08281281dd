import CompactButton from '@/hammr-ui/components/CompactButton';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { Crew } from 'interfaces/crew';
import PencilLine from '@/hammr-icons/PencilLine';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { ColDef } from '@ag-grid-community/core';
import { ValueGetterParams } from '@ag-grid-community/core/dist/types/src/entities/colDef';

interface CrewViewProps {
  crews: Crew[];
  checkMembersCallback: (index: number) => void;
  rowActionCallback: (index: number) => void;
  deleteActionCallback: (index: number) => void;
}

export default function CrewsView({
  crews,
  checkMembersCallback,
  rowActionCallback,
  deleteActionCallback,
}: CrewViewProps) {
  const colDefs: ColDef[] = [
    {
      field: 'name',
      headerName: 'Crew Name',
    },
    {
      field: 'crewLead',
      headerName: 'Crew Lead',
      valueGetter: (params: ValueGetterParams<Crew>) => {
        const crew = params.data;
        return crew?.crewLeadUser ? `${crew.crewLeadUser.firstName} ${crew.crewLeadUser.lastName}` : '-';
      },
    },
    {
      field: 'members',
      headerName: 'Members',
      cellRenderer: (params: ValueGetterParams<Crew>) => {
        const crew = params.data;
        const index = crews.findIndex((c) => c.id === crew.id);

        return (
          <LinkButton
            className="text-left"
            key={`crew-${crew.id}`}
            style="primary"
            size="medium"
            onClick={() => checkMembersCallback(index)}
          >
            {crew.crewMembers.length} Members
          </LinkButton>
        );
      },
    },
    {
      field: 'actions',
      headerName: '',
      cellRenderer: (params: ValueGetterParams<Crew>) => {
        const crew = params.data;
        const index = crews.findIndex((c) => c.id === crew.id);

        return (
          <div key={`crew-${crew.id}`} className="flex gap-2">
            <CompactButton size="large" variant="ghost" onClick={() => rowActionCallback(index)}>
              <PencilLine />
            </CompactButton>
            <CompactButton size="large" variant="ghost" onClick={() => deleteActionCallback(index)}>
              <DeleteBinLine />
            </CompactButton>
          </div>
        );
      },
    },
  ];

  return (
    <div className="max-w-[728px]">
      <UpdatedTable colDefs={colDefs} rowData={crews} />
    </div>
  );
}
