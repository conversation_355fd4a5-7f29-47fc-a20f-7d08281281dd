import Link from 'next/link';
import Image from 'next/image';
import Button from 'components/elements/Button';

const Header = (): JSX.Element => {
  return (
    <header className="body-font m-auto max-w-7xl text-gray-700">
      <div className="container mx-auto flex flex-row flex-wrap items-center p-5">
        <Link href="/">
          <a className="title-font mb-4 flex items-center font-medium text-gray-900 md:mb-0">
            <Image
              width={121}
              height={32}
              className="mt-3 h-8 w-auto sm:h-9 md:mt-0"
              src="/img/hammr-logo.png"
              alt="Hammr Inc."
            />
          </a>
        </Link>
        <nav className="mr-auto flex flex-wrap items-center justify-center text-base md:ml-4 md:py-1 md:pl-4"></nav>
      </div>
    </header>
  );
};

export default Header;
