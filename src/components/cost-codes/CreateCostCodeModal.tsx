import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { CreateCostCode } from 'interfaces/cost-code';
import { createCostCode } from 'services/cost-codes';
import { logError, showErrorToast } from 'utils/errorHandling';
import CostCodeForm from './CostCodeForm';
import { FormV2 } from '../elements/Form';
import { ModalV2 } from '../elements/ModalV2';
import Wallet3Line from '@/hammr-icons/Wallet3Line';

interface CreateCostCodeModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
}

export default function CreateCostCodeModal({ open, setOpen, callback }: CreateCostCodeModalProps) {
  const { addToast } = useToast();
  const form = useForm();
  const { handleSubmit } = form;
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const { costCodeName, costCodeNumber } = data;

    const costCode: CreateCostCode = {
      name: costCodeName,
      number: costCodeNumber,
    };

    try {
      await createCostCode(costCode);

      addToast({
        title: 'Created cost code',
        description: (
          <span>
            Successfully created the cost code <strong>{costCodeName}</strong>
          </span>
        ),
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create cost code');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ModalV2 icon={<Wallet3Line />} open={open} setOpen={setOpen} title="Create Cost Code">
      <FormV2
        isLoading={isProcessing}
        onCancel={() => {
          setOpen(false);
        }}
        onSubmit={handleSubmit(onSubmit)}
        submitText="Create"
      >
        <CostCodeForm form={form} />
      </FormV2>
    </ModalV2>
  );
}
