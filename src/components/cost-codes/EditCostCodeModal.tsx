import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { useToast } from 'hooks/useToast';

import { EditCostCode, CostCode } from 'interfaces/cost-code';
import { updateCostCode } from 'services/cost-codes';
import { logError, showErrorToast } from 'utils/errorHandling';
import { ModalV2 } from '../elements/ModalV2';
import { FormV2 } from '../elements/Form';
import CostCodeForm from './CostCodeForm';
import PencilLine from '@/hammr-icons/PencilLine';

interface EditCostCodeModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentCostCode: CostCode;
  callback?: () => void;
}

export default function EditCostCodeModal({ open, setOpen, currentCostCode, callback }: EditCostCodeModalProps) {
  const { addToast } = useToast();
  const form = useForm({
    defaultValues: {
      costCodeName: currentCostCode?.name,
      costCodeNumber: currentCostCode?.number,
    },
  });
  const { handleSubmit } = form;
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open) {
      form.reset({});
    } else {
      form.reset({
        costCodeName: currentCostCode?.name,
        costCodeNumber: currentCostCode?.number,
      });
    }
  }, [open]);

  const onSubmit = async (data) => {
    const { costCodeName, costCodeNumber } = data;
    setIsProcessing(true);

    const costCode: EditCostCode = {
      name: costCodeName,
      number: costCodeNumber,
    };

    try {
      await updateCostCode(currentCostCode.id, costCode);

      addToast({
        title: 'Updated cost code',
        description: 'Successfully updated the cost code',
        type: 'success',
      });

      // should call refresh (callback) and then close modal
      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update cost code');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ModalV2 icon={<PencilLine />} open={open} setOpen={setOpen} title="Edit Cost Code">
      <FormV2
        isLoading={isProcessing}
        onCancel={() => {
          setOpen(false);
        }}
        onSubmit={handleSubmit(onSubmit)}
        submitText="Update"
      >
        <CostCodeForm form={form} />
      </FormV2>
    </ModalV2>
  );
}
