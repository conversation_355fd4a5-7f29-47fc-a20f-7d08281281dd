import { TextField } from '../elements/form/TextField';

const CostCodeForm = ({ form }) => {
  const {
    control,
    formState: { errors },
  } = form;
  return (
    <>
      <div className="flex flex-col gap-5">
        <section>
          <TextField
            control={control}
            name="costCodeName"
            label="Cost Code Name"
            rules={{ required: 'Please enter a cost code name' }}
            error={errors.costCodeName?.message}
            required
          />
        </section>

        <section>
          <TextField
            control={control}
            name="costCodeNumber"
            label="Cost Code Number"
            error={errors.costCodeNumber?.message}
          />
        </section>
      </div>
    </>
  );
};

export default CostCodeForm;
