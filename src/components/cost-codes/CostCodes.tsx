import { CostCode } from 'interfaces/cost-code';
import React from 'react';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { Tooltip } from '@hammr-ui/components/tooltip';
import PencilLine from '@/hammr-icons/PencilLine';
import CompactButton from '@/hammr-ui/components/CompactButton';
import Button from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import EmptyStateFinanceBanking from '@/hammr-icons/EmptyStateFinanceBanking';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { showErrorToast } from 'utils/errorHandling';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { assignWorkersCompCode } from '@/services/cost-codes';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';

interface CostCodeProps {
  costCodes: CostCode[];
  rowActionCallback: (index: number) => void;
  handleCreateClick: () => void;
  onUpdate: () => void;
}

function WorkersCompCodeCell({
  costCode,
  workersCompCodes,
  onUpdate,
}: {
  costCode: CostCode;
  workersCompCodes: WorkersCompCode[];
  onUpdate: (workersCompCodeId: number | null) => void;
}) {
  return (
    <Select value={costCode.workersCompCode?.id.toString()} onChange={(value) => onUpdate(value as number)}>
      {workersCompCodes.map((item) => (
        <SelectItem key={item.id} value={item.id.toString()}>
          {item.code} • {item.name}
        </SelectItem>
      ))}
    </Select>
  );
}

export default function CostCodes({ costCodes, rowActionCallback, onUpdate, handleCreateClick }: CostCodeProps) {
  const { user } = useAuth();
  const { company } = useCompany();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: workersCompCodes } = useQuery({
    queryKey: ['workersCompCodes', 'active'],
    queryFn: () => workersCompCodesService.get(),
  });

  const assignWorkersCompCodesMutation = useMutation({
    mutationFn: ({ costCodeId, workersCompCodeId }: { costCodeId: number; workersCompCodeId: number }) =>
      assignWorkersCompCode(costCodeId, workersCompCodeId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['costCodes'] });
      onUpdate?.();
      toast({
        title: 'Updated workers comp code',
        description: 'Successfully updated workers comp code assignment',
        toastVariant: 'success',
      });
    },
    onError: (error) => {
      showErrorToast(error, 'Failed to update workers comp code');
    },
  });

  const colDefs: ColDef[] = [
    {
      headerName: 'Name',
      field: 'name',
      cellRenderer: (params) => {
        return <div className="flex h-full items-center">{params.value}</div>;
      },
    },
    {
      headerName: 'Number',
      field: 'number',
      cellRenderer: (params) => {
        return <div className="flex h-full items-center">{params.value}</div>;
      },
    },
    {
      headerName: 'Workers Comp',
      field: 'workersCompCode',
      flex: 2,
      minWidth: 160,
      cellRenderer: (params) => {
        return (
          <WorkersCompCodeCell
            costCode={params.data}
            workersCompCodes={workersCompCodes || []}
            onUpdate={(workersCompCodeId) => {
              assignWorkersCompCodesMutation.mutate({
                costCodeId: params.data.id,
                workersCompCodeId,
              });
            }}
          />
        );
      },
    },
    {
      headerName: '',
      maxWidth: 100,
      resizable: false,
      suppressMovable: true,
      field: 'actions',
      sortable: false,
      cellStyle: { overflow: 'visible' },
      cellRenderer: (params: ValueFormatterParams) => {
        return costCodesActions(params.data, params.node);
      },
    },
  ];

  const costCodesActions = (project, node?) => {
    return (
      <div key={`project-${project.id}`} className="flex h-full items-center justify-start space-x-4">
        <a
          className="flex w-6 cursor-pointer items-center justify-center"
          onClick={() => {
            rowActionCallback(project.id);
          }}
        >
          <Tooltip content="Edit">
            <CompactButton size="large" variant="ghost">
              <PencilLine />
            </CompactButton>
          </Tooltip>
        </a>
      </div>
    );
  };

  if (!user || !company) {
    return (
      <div className="flex size-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!Boolean(costCodes)) {
    return (
      <div className="mt-32 flex flex-col items-center justify-center pb-8">
        <EmptyStateFinanceBanking />
        <div className="mt-5 text-center text-sm">
          <div className="text-soft-400">There is no cost code yet.</div>
          <div className="text-soft-400">Click the button below to add one.</div>
        </div>
        <Button beforeContent={<AddLine />} onClick={handleCreateClick} className="mt-5">
          Create Cost Code
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full max-w-[728px]">
      <UpdatedTable colDefs={colDefs} rowData={costCodes} />
    </div>
  );
}
