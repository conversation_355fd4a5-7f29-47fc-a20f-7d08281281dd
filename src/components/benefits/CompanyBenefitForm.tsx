import { ContributionType } from 'interfaces/benefit';
import { BENEFIT_CATEGORY_OPTIONS } from './constants';
import { Controller } from 'react-hook-form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';
import { DateInput } from '@/hammr-ui/components/date-input';
import { Switch } from '@/hammr-ui/components/Switch';
import InfoCustomFill from '@hammr-icons/InfoCustomFill';
import { Tooltip } from '@hammr-ui/components/tooltip';
import { Combobox } from '@/hammr-ui/components/combobox';
import { PhoneInput } from '@/components/shared/PhoneInput';

const CompanyBenefitForm = ({
  register,
  errors,
  control,
  watch,
  setValue,
  isEdit = false,
  parentContributionType = '',
}) => {
  const contributionType = isEdit ? parentContributionType : watch('contributionType');

  return (
    <>
      {isEdit && <h2 className="font-medium text-strong-950">General Information</h2>}

      <FormItem required error={!!errors['name']} className={isEdit && 'mt-5'}>
        <FormLabel>{!isEdit ? 'Name' : 'Benefit Name'}</FormLabel>
        <FormControl>
          <Input
            name="name"
            placeholder="Enter name"
            {...register('name', {
              required: 'Please benefit name',
            })}
          />
        </FormControl>
        <FormMessage>{errors['name']?.message}</FormMessage>
      </FormItem>

      {!isEdit && (
        <FormItem required error={!!errors['category']} className="mt-5">
          <FormLabel>Category</FormLabel>
          <FormControl>
            <Controller
              rules={{ required: 'Please select a category' }}
              control={control}
              name="category"
              render={({ field }) => (
                <Controller
                  rules={{ required: 'Please select a category' }}
                  control={control}
                  name="category"
                  render={({ field }) => (
                    <Combobox
                      items={BENEFIT_CATEGORY_OPTIONS}
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select an option"
                    />
                  )}
                />
              )}
            />
          </FormControl>
          <FormMessage>{errors['category']?.message}</FormMessage>
        </FormItem>
      )}

      <BenefitsContributionType
        register={register}
        watch={watch}
        errors={errors}
        control={control}
        isEdit={isEdit}
        contributionType={contributionType}
      />

      {!isEdit && <hr className="mt-5 border-soft-200" />}

      {!isEdit && (
        <FormItem required error={!!errors['benefitStartDate']} className="mt-5">
          <FormLabel>Benefit Start Date</FormLabel>
          <Controller
            name="benefitStartDate"
            rules={{ required: 'Please select a start date' }}
            control={control}
            render={({ field }) => (
              <FormControl>
                <DateInput {...field} value={field.value ? field.value : null} />
              </FormControl>
            )}
          />
          <FormMessage>{errors['benefitStartDate']?.message}</FormMessage>
        </FormItem>
      )}

      {/* TODO -> datepicker DEFAULT DATE */}
      <FormItem error={!!errors['benefitEndDate']} className="mt-5">
        <FormLabel>Expiration/Renewal Date</FormLabel>
        <Controller
          name="benefitEndDate"
          control={control}
          render={({ field }) => (
            <FormControl>
              <DateInput {...field} value={field.value ? field.value : null} />
            </FormControl>
          )}
        />
        <FormMessage>{errors['benefitEndDate']?.message}</FormMessage>
      </FormItem>

      {!isEdit && contributionType !== 'DYNAMIC' && (
        <>
          <FormItem className="mt-5 flex-row items-center justify-between">
            <FormLabel
              htmlFor="isApprovedFringe"
              tooltip="Marking a benefit as an approved fringe allows it to offset the fringe portion of the prevailing wage rate. This reduces the cash fringe pay required, helping you comply with prevailing wage laws while potentially lowering payroll costs."
            >
              Approved fringe
            </FormLabel>
            <FormControl>
              <Controller
                name="isApprovedFringe"
                control={control}
                render={({ field }) => (
                  <Switch id="isApprovedFringe" checked={field.value} onCheckedChange={field.onChange} />
                )}
              />
            </FormControl>
          </FormItem>
        </>
      )}

      <hr className="mt-5 border-soft-200" />

      <h3 className="mt-5 font-medium text-strong-950">Benefit Provider</h3>

      <FormItem error={!!errors['benefitProviderName']} className="mt-5">
        <FormLabel>Name</FormLabel>
        <FormControl>
          <Input placeholder="Enter name" {...register('benefitProviderName')} />
        </FormControl>
        <FormMessage>{errors['benefitProviderName']?.message}</FormMessage>
      </FormItem>

      <FormItem error={!!errors['benefitProviderAddress']} className="mt-5">
        <FormLabel>Address</FormLabel>
        <FormControl>
          <Input placeholder="Enter address" {...register('benefitProviderAddress')} />
        </FormControl>
        <FormMessage>{errors['benefitProviderAddress']?.message}</FormMessage>
      </FormItem>

      <Controller
        control={control}
        name="benefitProviderPhone"
        render={({ field }) => (
          <FormItem error={!!errors['benefitProviderPhone']} className="mt-5">
            <FormLabel>Phone number</FormLabel>
            <FormControl>
              <PhoneInput
                id="benefitProviderPhone"
                placeholder="Enter phone number"
                name={field.name}
                value={field.value}
                onChange={field.onChange}
                onBlur={field.onBlur}
              />
            </FormControl>
            <FormMessage>{errors['benefitProviderPhone']?.message as string}</FormMessage>
          </FormItem>
        )}
      />
    </>
  );
};

const BenefitsContributionType = ({ register, errors, control, watch, isEdit = false, contributionType }) => {
  const contributionOptions: { value: ContributionType; label: string }[] = [
    { value: 'PERCENT', label: 'Percentage of gross pay' },
    { value: 'AMOUNT', label: 'Fixed amount' },
    { value: 'DYNAMIC', label: 'Dynamic (prevailing wage cash fringe offset)' },
  ];

  const ContributionLabel = (option) => {
    return (
      <span className="flex items-center gap-1">
        {option.label}
        {option.value === 'DYNAMIC' && (
          <Tooltip
            content={
              <>
                This option allows you to offset the required fringe pay on prevailing wage projects by contributing to
                a bonafide fringe plan such as a 401(k) instead of paying it in cash.
                <br />
                <br />
                The amount is calculated based on the fringe pay for each classification, minus any other employer
                contributions, ensuring that no fringe pay is issued as cash.
              </>
            }
          >
            <span>
              <InfoCustomFill className="size-5 text-disabled-300" />
            </span>
          </Tooltip>
        )}
      </span>
    );
  };

  return (
    <>
      {!isEdit && (
        <FormItem error={!!errors['name']} className="mt-5">
          <FormLabel>Contribution</FormLabel>
          <FormControl>
            <Controller
              control={control}
              name="contributionType"
              render={({ field }) => (
                <RadioGroup value={field.value} onValueChange={field.onChange} className="!mt-3">
                  {contributionOptions.map((option) => (
                    <div className="flex items-center space-x-2" key={option.value}>
                      <RadioGroupItem value={option.value} id={option.value} />
                      <Label className="font-normal" htmlFor={option.value}>
                        <ContributionLabel {...option} />
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            />
          </FormControl>
          <FormMessage>{errors['name']?.message}</FormMessage>
        </FormItem>
      )}
    </>
  );
};

export default CompanyBenefitForm;
