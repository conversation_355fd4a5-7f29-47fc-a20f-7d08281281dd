import { FormV2 } from 'components/elements/Form';
import { addToast } from 'hooks/useToast';
import { ContributionType, EmployeeBenefit } from 'interfaces/benefit';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { editEmployeeBenefit } from 'services/employeeBenefits';
import { logError, showErrorToast } from 'utils/errorHandling';
import { EMPLOYEE_BENEFIT_CHANGE_REASON } from './constants';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import PencilLine from '@/hammr-icons/PencilLine';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { DateInput } from '@/hammr-ui/components/date-input';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { formatToYYYYMMDD } from '@/utils/dateHelper';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';

interface EditEmployeeBenefitModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  employeeBenefit: EmployeeBenefit;
  employeeName: string;
  contributionType: ContributionType;
}

const FormBody = ({ errors, control, contributionType, register, employeeBenefit, employeeName }) => {
  return (
    <>
      <section className="grid grid-cols-2 gap-5">
        <article>
          <h2 className="text-xs text-sub-600">Benefit Name</h2>
          <p className="text-sm text-strong-950">{employeeBenefit.name}</p>
        </article>
        <article>
          <h2 className="text-xs text-sub-600">Employee</h2>
          <p className="text-sm text-strong-950">{employeeName}</p>
        </article>
      </section>

      {contributionType === 'PERCENT' && (
        <FormItem required error={!!errors['companyContributionPercent']} className="mt-5">
          <FormLabel>Company Contribution</FormLabel>
          <FormControl key={contributionType}>
            <Input
              type="number"
              step="any"
              beforeContent="%"
              placeholder="0.00"
              {...register('companyContributionPercent', {
                required: 'Please enter company contribution percent',
                min: { value: 0, message: 'Enter a value 0 or above ' },
                max: { value: 100, message: 'Max value is 100%' },
              })}
            />
          </FormControl>
          <FormMessage>{errors['companyContributionPercent']?.message}</FormMessage>
        </FormItem>
      )}

      {(contributionType === 'PERCENT' || contributionType === 'DYNAMIC') && (
        <FormItem required error={!!errors['employeeContributionPercent']} className="mt-5">
          <FormLabel>Employee Contribution</FormLabel>
          <FormControl key={contributionType}>
            <Input
              type="number"
              step="any"
              beforeContent="%"
              placeholder="0.00"
              {...register('employeeContributionPercent', {
                required: 'Please enter employee contribution percent',
                min: { value: 0, message: 'Enter a value 0 or above ' },
                max: { value: 100, message: 'Max value is 100%' },
              })}
            />
          </FormControl>
          <FormMessage>{errors['employeeContributionPercent']?.message}</FormMessage>
        </FormItem>
      )}

      {contributionType === 'AMOUNT' && (
        <FormItem className="mt-5">
          <FormLabel>Contribution Frequency</FormLabel>
          <FormControl>
            <Controller
              name="frequency"
              control={control}
              render={({ field }) => (
                <RadioGroup value={field.value} onValueChange={field.onChange} className="!mt-3">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="monthly" id="monthly" />
                    <Label className="font-normal" htmlFor="monthly">
                      Monthly
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="payPeriod" id="payPeriod" />
                    <Label className="font-normal" htmlFor="payPeriod">
                      Pay Period
                    </Label>
                  </div>
                </RadioGroup>
              )}
            />
          </FormControl>
        </FormItem>
      )}

      {contributionType === 'AMOUNT' && (
        <>
          <FormItem required error={!!errors['companyAmount']} className="mt-5">
            <FormLabel>Company Contribution</FormLabel>
            <FormControl>
              <Input
                type="number"
                step="any"
                beforeContent="$"
                placeholder="0.00"
                {...register('companyAmount', {
                  required: 'Please enter company contribution amount',
                  min: { value: 0, message: 'Enter a value 0 or above ' },
                })}
              />
            </FormControl>
            <FormMessage>{errors['companyAmount']?.message}</FormMessage>
          </FormItem>

          <FormItem required error={!!errors['employeeAmount']} className="mt-5">
            <FormLabel>Employee Contribution</FormLabel>
            <FormControl>
              <Input
                type="number"
                step="any"
                beforeContent="$"
                placeholder="0.00"
                {...register('employeeAmount', {
                  required: 'Please enter employee contribution amount',
                  min: { value: 0, message: 'Enter a value 0 or above ' },
                })}
              />
            </FormControl>
            <FormMessage>{errors['employeeAmount']?.message}</FormMessage>
          </FormItem>
        </>
      )}

      <FormItem required error={!!errors['effectiveDate']} className="mt-5">
        <FormLabel>Effective Date</FormLabel>
        <Controller
          name="effectiveDate"
          rules={{ required: 'Please select an effective date' }}
          control={control}
          render={({ field }) => (
            <FormControl>
              <DateInput {...field} value={field.value ? field.value : null} />
            </FormControl>
          )}
        />
        <FormMessage>{errors['effectiveDate']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['changeReason']} className="mt-5">
        <FormLabel>Reason For Change</FormLabel>
        <FormControl>
          <Controller
            rules={{ required: 'Please select a reason' }}
            control={control}
            name="changeReason"
            render={({ field }) => (
              <DropdownPicker
                aria-invalid={!!errors['changeReason']}
                placeholder="Select an option"
                className="mt-1 w-full"
                items={EMPLOYEE_BENEFIT_CHANGE_REASON.map((option) => {
                  return {
                    label: option.label,
                    value: option.value,
                  };
                })}
                value={field.value}
                onChange={field.onChange}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['changeReason']?.message}</FormMessage>
      </FormItem>
    </>
  );
};

const EditEmployeeBenefitModal = ({
  open,
  setOpen,
  callback,
  employeeBenefit,
  employeeName,
  contributionType,
}: EditEmployeeBenefitModalProps) => {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm();
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open || !employeeBenefit) return;
    const formData: any = {
      ...employeeBenefit,
      frequency: employeeBenefit.period === 'MONTHLY' ? 'monthly' : 'payPeriod',
    };

    if (employeeBenefit.contributionType === 'AMOUNT') {
      if (employeeBenefit.period === 'MONTHLY') {
        formData.companyAmount = employeeBenefit.companyPeriodAmount;
        formData.employeeAmount = employeeBenefit.employeePeriodAmount;
      } else {
        formData.companyAmount = employeeBenefit.companyContributionAmount;
        formData.employeeAmount = employeeBenefit.employeeContributionAmount;
      }
    }

    reset(formData);
  }, [open, employeeBenefit, reset]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const commonPayloadData = {
      userId: data.userId,
      contributionType: data.contributionType,
      effectiveDate: formatToYYYYMMDD(data.effectiveDate),
      changeReason: data.changeReason,
    };

    let payloadData = {};

    if (data.contributionType === 'PERCENT') {
      payloadData = {
        ...commonPayloadData,
        companyContributionPercent: Number(data.companyContributionPercent),
        employeeContributionPercent: Number(data.employeeContributionPercent),
      };
    } else if (data.contributionType === 'AMOUNT') {
      if (data.frequency === 'monthly') {
        payloadData = {
          ...commonPayloadData,
          companyPeriodAmount: Number(data.companyAmount).toFixed(2),
          employeePeriodAmount: Number(data.employeeAmount).toFixed(2),
          period: 'MONTHLY',
        };
      } else {
        payloadData = {
          ...commonPayloadData,
          companyContributionAmount: Number(data.companyAmount).toFixed(2),
          employeeContributionAmount: Number(data.employeeAmount).toFixed(2),
          period: null,
        };
      }
    } else if (data.contributionType === 'DYNAMIC') {
      payloadData = {
        ...commonPayloadData,
        companyContributionPercent: 0,
        employeeContributionPercent: Number(data.employeeContributionPercent),
      };
    }

    try {
      await editEmployeeBenefit(employeeBenefit.id, payloadData);

      addToast({
        title: 'Edited Benefit',
        description: (
          <>
            Successfully edited the benefit <strong className="font-medium">{employeeBenefit.name}</strong> for{' '}
            <strong className="font-medium">{employeeName}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update employee benefit');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<PencilLine className="text-sub-600" />} title="Edit Employee Benefit" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Save"
        >
          <FormBody
            register={register}
            control={control}
            errors={errors}
            contributionType={contributionType}
            employeeBenefit={employeeBenefit}
            employeeName={employeeName}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default EditEmployeeBenefitModal;
