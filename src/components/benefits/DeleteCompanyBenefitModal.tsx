import { Dispatch, SetStateAction, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { CompanyBenefitModified } from 'interfaces/benefit';
import { updateBenefit } from 'services/benefits';
import dayjs from 'dayjs';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import Alert from '@/hammr-ui/components/Alert';

interface DeleteCompanyBenefitModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  companyBenefit: CompanyBenefitModified;
}

export default function DeleteCompanyBenefitModal({
  open,
  setOpen,
  callback,
  companyBenefit,
}: DeleteCompanyBenefitModalProps) {
  const { addToast } = useToast();
  const { handleSubmit } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  const onSubmit = async () => {
    setIsProcessing(true);

    try {
      await updateBenefit(companyBenefit.id, {
        name: companyBenefit.name,
        benefitEndDate: dayjs(new Date()).format('YYYY-MM-DD'),
      });

      addToast({
        title: 'Deactivated Benefit',
        description: (
          <>
            Successfully deactivated the benefit <strong className="font-medium">{companyBenefit.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to deactivate company benefit');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      onConfirm={handleSubmit(onSubmit)}
      icon={<KeyIcon icon={<CloseCircleLine />} color="red" />}
      title="Deactivate Benefit"
      subtitle={
        <>
          You’re about to deactivate the benefit {<span className="font-medium">{companyBenefit?.name}</span>}. Do you
          want to proceed?
        </>
      }
      confirmButton={{
        color: 'error',
      }}
      confirmButtonText="Deactivate"
    >
      <h3 className="text-sm text-strong-950">{companyBenefit.numberEnrolled} employees enrolled</h3>
      <Alert className="mt-5">
        Deactivating this benefit will remove the benefit for all employees enrolled in it with immediate effect.
      </Alert>
    </ConfirmDialog>
  );
}
