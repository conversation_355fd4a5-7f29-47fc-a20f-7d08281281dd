import { BenefitCategory } from 'interfaces/benefit';

export const BENEFIT_CATEGORY_OPTIONS: {
  value: BenefitCategory;
  label: string;
}[] = [
  { value: 'MEDICAL', label: 'Medical Insurance' },
  { value: 'DENTAL', label: 'Dental Insurance' },
  { value: 'VISION', label: 'Vision Insurance' },
  { value: 'DISABILITY', label: 'Disability Insurance' },
  { value: '401K', label: '401k' },
  { value: 'ROTH_401K', label: 'Roth 401k' },
  { value: 'LIFE', label: 'Life Insurance' },
  { value: 'HSA', label: 'HSA' },
  { value: 'FSA_MEDICAL', label: 'FSA' },
  { value: 'FSA_DEPENDENT_CARE', label: 'FSA Dependent Care' },
  { value: 'SIMPLE_IRA', label: 'Simple IRA' },
];

export const EMPLOYEE_BENEFIT_CHANGE_REASON = [
  { value: 'OPEN_ENROLLMENT', label: 'Open enrollment' },
  { value: 'VOLUNTARY_CHANGE', label: 'Voluntary change' },
  { value: 'QUALIFYING_LIFE_EVENT', label: 'Qualifying life event' },
  { value: 'ADMIN_CORRECTION', label: 'Administrative correction' },
];

export const EMPLOYEE_BENEFIT_UNENROLL_REASON = [
  { value: 'TERMINATION', label: 'Termination' },
  { value: 'OPEN_ENROLLMENT', label: 'Open enrollment' },
  { value: 'VOLUNTARY_UNENROLLMENT', label: 'Voluntary unenrollment' },
  { value: 'QUALIFYING_LIFE_EVENT', label: 'Qualifying life event' },
  { value: 'ADMIN_CORRECTION', label: 'Administrative correction' },
];
