import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { CompanyBenefit, CompanyBenefitModified } from 'interfaces/benefit';
import { updateBenefit } from 'services/benefits';
import CompanyBenefitForm from './CompanyBenefitForm';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import PencilLine from '@/hammr-icons/PencilLine';
import { formatToYYYYMMDD } from '@/utils/dateHelper';
import dayjs from 'dayjs';

interface EditCompanyBenefitModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  companyBenefit: CompanyBenefitModified;
}

export default function EditCompanyBenefitModal({
  open,
  setOpen,
  callback,
  companyBenefit,
}: EditCompanyBenefitModalProps) {
  const { addToast } = useToast();
  // TODO -> move validation to yup
  const {
    formState: { errors },
    handleSubmit,
    register,
    control,
    watch,
    setValue,
    reset,
  } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open || !companyBenefit) return;

    reset({
      ...companyBenefit,
      benefitStartDate: companyBenefit.benefitStartDate ? dayjs(companyBenefit.benefitStartDate).toDate() : null,
      benefitEndDate: companyBenefit.benefitEndDate ? dayjs(companyBenefit.benefitEndDate).toDate() : null,
    });
  }, [open, companyBenefit, reset]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const { name, benefitEndDate, benefitProviderName, benefitProviderAddress, benefitProviderPhone } = data;

    const payloadData = {
      name,
      benefitEndDate: formatToYYYYMMDD(benefitEndDate),
      benefitProviderName,
      benefitProviderAddress,
      benefitProviderPhone,
    };

    try {
      await updateBenefit(companyBenefit.id, payloadData as any as CompanyBenefit);

      addToast({
        title: 'Edited Benefit',
        description: (
          <>
            Successfully edited the benefit <strong className="font-bold">{data.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update company benefit');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<PencilLine className="text-sub-600" />} title="Edit Company Benefit" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Save"
        >
          <CompanyBenefitForm
            watch={watch}
            register={register}
            control={control}
            errors={errors}
            setValue={setValue}
            isEdit
            parentContributionType={companyBenefit.contributionType}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
