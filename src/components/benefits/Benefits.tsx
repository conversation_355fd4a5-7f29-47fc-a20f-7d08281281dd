import EmptyStateDonationProfile from '@/hammr-icons/EmptyStateDonationProfile';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import StatusBadge from '@/hammr-ui/components/StatusBadge';
import Button from '@/hammr-ui/components/button';
import { TabContent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { CompanyBenefit } from '@/interfaces/benefit';
import { getBenefits } from '@/services/benefits';
import { formatLocaleUsa, isItemActive } from '@/utils/dateHelper';
import { RiAddLine } from '@remixicon/react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';

interface Props {
  onCreateBenefitBtnClick: () => void;
}

export default function Benefit({ onCreateBenefitBtnClick }: Props) {
  const benefitsQuery = useQuery({
    queryKey: ['benefits'],
    async queryFn() {
      const data = await getBenefits();

      const activeBenefits = data.companyBenefits.filter((benefit) =>
        isItemActive(benefit.benefitStartDate, benefit.benefitEndDate)
      );
      const archivedBenefits = data.companyBenefits.filter(
        (benefit) => !isItemActive(benefit.benefitStartDate, benefit.benefitEndDate)
      );
      return {
        active: activeBenefits.sort((a, b) => (a.name > b.name ? 1 : a.name < b.name ? -1 : 0)),
        archived: archivedBenefits.sort((a, b) => (a.name > b.name ? 1 : a.name < b.name ? -1 : 0)),
      };
    },
  });

  return (
    <>
      {benefitsQuery.isPending ? (
        <section className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </section>
      ) : benefitsQuery.isError ? (
        <section className="flex size-full items-center justify-center">
          <p className="text-sm text-error-base">An error occurred while fetching the benefits.</p>
        </section>
      ) : benefitsQuery.data.active.length === 0 && benefitsQuery.data.archived.length === 0 ? (
        <section className="flex size-full flex-col items-center py-32">
          <EmptyStateDonationProfile />
          <article className="mt-5 flex flex-col items-center">
            <p className="h-fit text-sm text-soft-400">There is no benefit yet.</p>
            <p className="h-fit text-sm text-soft-400">Click the button below to add one.</p>
            <Button className="mt-5" beforeContent={<RiAddLine />} onClick={onCreateBenefitBtnClick}>
              Create Benefit
            </Button>
          </article>
        </section>
      ) : (
        <Tabs defaultValue="active">
          <TabList>
            <TabItem value="active">Active</TabItem>
            <TabItem value="archived">Archived</TabItem>
          </TabList>
          <TabContent value="active">
            {benefitsQuery.data.active.length === 0 ? (
              <section className="flex size-full flex-col items-center py-32">
                <EmptyStateDonationProfile />
                <article className="mt-5 flex flex-col items-center">
                  <p className="h-fit text-sm text-soft-400">There is no active benefit yet.</p>
                  <p className="h-fit text-sm text-soft-400">Click the button below to add one.</p>
                  <Button className="mt-5" beforeContent={<RiAddLine />} onClick={onCreateBenefitBtnClick}>
                    Create Benefit
                  </Button>
                </article>
              </section>
            ) : (
              <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6 py-6">
                {benefitsQuery.data.active.map((benefit) => (
                  <BenefitCard benefit={benefit} key={benefit.id} />
                ))}
              </section>
            )}
          </TabContent>
          <TabContent value="archived">
            {benefitsQuery.data.archived.length === 0 ? (
              <section className="flex size-full flex-col items-center py-32">
                <EmptyStateDonationProfile />
                <article className="mt-5 flex flex-col items-center">
                  <p className="h-fit text-sm text-soft-400">There is no archived benefit yet.</p>
                </article>
              </section>
            ) : (
              <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6 py-6">
                {benefitsQuery.data.archived.map((benefit) => (
                  <BenefitCard benefit={benefit} key={benefit.id} />
                ))}
              </section>
            )}
          </TabContent>
        </Tabs>
      )}
    </>
  );
}

function BenefitCard({ benefit }: { benefit: CompanyBenefit }) {
  const router = useRouter();

  return (
    <article
      className="flex cursor-pointer flex-col items-start justify-between rounded-16 border border-soft-200 bg-white-0 p-5 shadow-xs hover:bg-weak-50"
      onClick={() => {
        router.push(`/benefits/${benefit.id}`);
      }}
    >
      <header className="w-full">
        <hgroup className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-strong-950">{benefit.name}</h2>
          {isItemActive(benefit.benefitStartDate, benefit.benefitEndDate) ? (
            <StatusBadge>Active</StatusBadge>
          ) : (
            <StatusBadge status="disabled">Inactive</StatusBadge>
          )}
        </hgroup>
        {benefit.benefitStartDate && (
          <p className="mt-1.5 h-fit text-sub-600">Start: {formatLocaleUsa(benefit.benefitStartDate)}</p>
        )}
        {benefit.benefitEndDate && (
          <p className="mt-1.5 h-fit text-sub-600">End: {formatLocaleUsa(benefit.benefitEndDate)}</p>
        )}
      </header>
      <footer className="mt-5 flex w-full items-center justify-between">
        <LinkButton
          size="medium"
          style="primary"
          onClick={(e) => {
            e.stopPropagation();
            router.push(`/benefits/${benefit.id}?tab=enrolled`);
          }}
        >
          {benefit.numberEnrolled} Enrolled
        </LinkButton>
        <span className="text-xs text-sub-600">{benefit.category.split('_').join(' ')}</span>
      </footer>
    </article>
  );
}
