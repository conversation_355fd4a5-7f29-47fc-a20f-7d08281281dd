import Heart3Line from '@/hammr-icons/Heart3Line';
import { Combobox } from '@/hammr-ui/components/combobox';
import { DateInput } from '@/hammr-ui/components/date-input';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@/hammr-ui/components/label';
import { formatToYYYYMMDD } from '@/utils/dateHelper';
import { FormV2 } from 'components/elements/Form';
import { useEmployees } from 'hooks/data-fetching/useEmployees';
import { useCompany } from 'hooks/useCompany';
import { addToast } from 'hooks/useToast';
import { CompanyBenefitModified } from 'interfaces/benefit';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { createEmployeeBenefit } from 'services/employeeBenefits';
import { logError, showErrorToast } from 'utils/errorHandling';
import { TextField } from '../elements/form/TextField';

interface CreateEmployeeBenefitModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  companyBenefit: CompanyBenefitModified;
  enrolledEmployeesIds: number[];
}

const FormBody = ({ errors, control, companyBenefit, employees, register }) => {
  const { contributionType } = companyBenefit;

  return (
    <>
      <FormItem required error={!!errors['userId']}>
        <FormLabel>Employee</FormLabel>
        <FormControl>
          <Controller
            rules={{ required: 'Please select an employee' }}
            control={control}
            name="userId"
            render={({ field }) => (
              <Combobox
                aria-invalid={!!errors['userId']}
                placeholder="Select an option"
                className="mt-1 w-full"
                items={employees.map((employee) => {
                  return {
                    label: employee.fullName,
                    value: employee.id.toString(),
                  };
                })}
                value={field.value?.toString()}
                onChange={(value) => {
                  field.onChange(Number(value));
                }}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['userId']?.message}</FormMessage>
      </FormItem>

      {contributionType === 'PERCENT' && (
        <FormItem required error={!!errors['companyContributionPercent']} className="mt-5">
          <FormLabel>Company Contribution</FormLabel>
          <FormControl key={contributionType}>
            <Input
              type="number"
              step="any"
              beforeContent="%"
              placeholder="0.00"
              {...register('companyContributionPercent', {
                required: 'Please enter company contribution percent',
                min: { value: 0, message: 'Enter a value 0 or above ' },
                max: { value: 100, message: 'Max value is 100%' },
              })}
            />
          </FormControl>
          <FormMessage>{errors['companyContributionPercent']?.message}</FormMessage>
        </FormItem>
      )}

      {(contributionType === 'PERCENT' || contributionType === 'DYNAMIC') && (
        <FormItem required error={!!errors['employeeContributionPercent']} className="mt-5">
          <FormLabel>Employee Contribution</FormLabel>
          <FormControl key={contributionType}>
            <Input
              type="number"
              step="any"
              beforeContent="%"
              placeholder="0.00"
              {...register('employeeContributionPercent', {
                required: 'Please enter employee contribution percent',
                min: { value: 0, message: 'Enter a value 0 or above ' },
                max: { value: 100, message: 'Max value is 100%' },
              })}
            />
          </FormControl>
          <FormMessage>{errors['employeeContributionPercent']?.message}</FormMessage>
        </FormItem>
      )}

      {contributionType === 'AMOUNT' && (
        <>
          <FormItem className="mt-5">
            <FormLabel>Contribution Frequency</FormLabel>
            <FormControl>
              <Controller
                name="frequency"
                control={control}
                render={({ field }) => (
                  <RadioGroup value={field.value} onValueChange={field.onChange} className="!mt-3">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="monthly" id="monthly" />
                      <Label className="font-normal" htmlFor="monthly">Monthly</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="payPeriod" id="payPeriod" />
                      <Label className="font-normal" htmlFor="payPeriod">Pay Period</Label>
                    </div>
                  </RadioGroup>
                )}
              />
            </FormControl>
          </FormItem>
        </>
      )}

      {contributionType === 'AMOUNT' && (
        <>
          <FormItem required error={!!errors['companyAmount']} className="mt-5">
            <FormLabel>Company Contribution</FormLabel>
            <FormControl>
              <Input
                type="number"
                step="any"
                beforeContent="$"
                placeholder="0.00"
                {...register('companyAmount', {
                  required: 'Please enter company contribution amount',
                })}
              />
            </FormControl>
            <FormMessage>{errors['companyAmount']?.message}</FormMessage>
          </FormItem>

          <FormItem required error={!!errors['employeeAmount']} className="mt-5">
            <FormLabel>Employee Contribution</FormLabel>
            <FormControl>
              <Input
                type="number"
                step="any"
                beforeContent="$"
                placeholder="0.00"
                {...register('employeeAmount', {
                  required: 'Please enter employee contribution amount',
                })}
              />
            </FormControl>
            <FormMessage>{errors['employeeAmount']?.message}</FormMessage>
          </FormItem>
        </>
      )}

      <FormItem required error={!!errors['benefitStartDate']} className="mt-5">
        <FormLabel>Effective Date</FormLabel>
        <Controller
          name="benefitStartDate"
          rules={{ required: 'Please select a start date' }}
          control={control}
          render={({ field }) => (
            <FormControl>
              <DateInput {...field} value={field.value ? field.value : null} />
            </FormControl>
          )}
        />
        <FormMessage>{errors['benefitStartDate']?.message}</FormMessage>
      </FormItem>
    </>
  );
};

const CreateEmployeeBenefitModal = ({
  open,
  setOpen,
  callback,
  companyBenefit,
  enrolledEmployeesIds,
}: CreateEmployeeBenefitModalProps) => {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm();
  const [isProcessing, setIsProcessing] = useState(false);
  const { company } = useCompany();
  const employees = useEmployees(Number(company?.id), {
    simple: true,
    workerClassification: 'EMPLOYEE',
  });
  const selectableEmployees = useMemo(() => {
    return employees.filter((employee) => !enrolledEmployeesIds.includes(employee.id));
  }, [employees, enrolledEmployeesIds]);

  useEffect(() => {
    if (!open || !companyBenefit) {
      setIsProcessing(false);
      reset();
      return;
    }

    reset({
      benefitStartDate: null,
      frequency: 'monthly',
    });
  }, [open]);

  const onSubmit = async (data) => {
    setIsProcessing(true);
    const selectedEmployee = employees.find((e) => e.id === data.userId);

    const commonPayloadData = {
      userId: data.userId,
      benefitStartDate: formatToYYYYMMDD(data.benefitStartDate),
      companyBenefitId: companyBenefit.id,
      contributionType: companyBenefit.contributionType,
      name: companyBenefit.name,
    };

    let payloadData = {};

    if (companyBenefit.contributionType === 'PERCENT') {
      payloadData = {
        ...commonPayloadData,
        companyContributionPercent: Number(data.companyContributionPercent),
        employeeContributionPercent: Number(data.employeeContributionPercent),
      };
    } else if (companyBenefit.contributionType === 'AMOUNT') {
      if (data.frequency === 'monthly') {
        payloadData = {
          ...commonPayloadData,
          companyPeriodAmount: Number(data.companyAmount).toFixed(2),
          employeePeriodAmount: Number(data.employeeAmount).toFixed(2),
          period: 'MONTHLY',
        };
      } else {
        payloadData = {
          ...commonPayloadData,
          companyContributionAmount: Number(data.companyAmount).toFixed(2),
          employeeContributionAmount: Number(data.employeeAmount).toFixed(2),
          period: null,
        };
      }
    } else if (companyBenefit.contributionType === 'DYNAMIC') {
      payloadData = {
        ...commonPayloadData,
        employeeContributionPercent: Number(data.employeeContributionPercent),
        companyContributionPercent: 0,
      };
    }

    try {
      await createEmployeeBenefit(payloadData);

      addToast({
        title: 'Enrolled Employee',
        description: (
          <>
            Successfully enrolled{' '}
            <strong className="font-medium">
              {selectedEmployee.firstName} {selectedEmployee.lastName}
            </strong>{' '}
            in <strong className="font-medium">{companyBenefit.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to enroll employee');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<Heart3Line className="text-sub-600" />} title="Enroll Employee" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Enroll"
        >
          <FormBody
            register={register}
            control={control}
            errors={errors}
            companyBenefit={companyBenefit}
            employees={selectableEmployees}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default CreateEmployeeBenefitModal;
