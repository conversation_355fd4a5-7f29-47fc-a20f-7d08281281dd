import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { CompanyBenefitModified, EmployeeBenefit, EmployeeBenefitResponse } from 'interfaces/benefit';
import { useMemo, useState } from 'react';
import { formatLocaleUsa } from 'utils/dateHelper';
import EditEmployeeBenefitModal from './EditEmployeeBenefitModal';
import DeleteEmployeeBenefitModal from './DeleteEmployeeBenefitModal';
import { isUserEnrolled } from 'utils/benefits';
import { EMPLOYEE_BENEFIT_UNENROLL_REASON, EMPLOYEE_BENEFIT_CHANGE_REASON } from './constants';
import CompactButton from '@/hammr-ui/components/CompactButton';
import StatusBadge from '@/hammr-ui/components/StatusBadge';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import dayjs from 'dayjs';
import { RiCloseCircleLine, RiHistoryLine, RiPencilLine } from '@remixicon/react';
import EmployeeBenefitHistoryModal from './EmployeeBenefitHistoryModal';

type Props = CompanyBenefitModified & {
  setBenefitsRequest: () => void;
  employeeBenefits: EmployeeBenefitResponse[];
};

export default function EnrolledEmployees({ contributionType, setBenefitsRequest, employeeBenefits }: Props) {
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedBenefitRow, setSelectedBenefitRow] = useState<EmployeeBenefit | null>(null);

  const mostRecentEmployeeBenefits = useMemo(() => {
    const benefitsMap: Record<string, EmployeeBenefitResponse> = {};
    employeeBenefits.forEach((benefit) => {
      const key = benefit.userId;
      if (!benefitsMap[key]) {
        benefitsMap[key] = benefit;
      }
      if (benefitsMap[key] && dayjs(benefit.updatedAt).isAfter(dayjs(benefitsMap[key].updatedAt))) {
        benefitsMap[key] = benefit;
      }
    });
    return Object.values(benefitsMap);
  }, [employeeBenefits]);

  const rowData = useMemo(() => {
    return mostRecentEmployeeBenefits.map((benefit) => ({
      ...benefit,
      employeeName: `${benefit.user.firstName} ${benefit.user.lastName}`,
      status: isUserEnrolled(benefit.benefitEndDate),
      benefitStartDateTimestamp: benefit.benefitStartDate ? dayjs(benefit.benefitStartDate).valueOf() : null,
      benefitEndDateTimestamp: benefit.benefitEndDate ? dayjs(benefit.benefitEndDate).valueOf() : null,
    }));
  }, [mostRecentEmployeeBenefits]);

  type TableRow = (typeof rowData)[number];

  const colDefs: ColDef<TableRow>[] = [
    { field: 'employeeName', headerName: 'Name', minWidth: 120, initialSort: 'asc' },
    {
      field: 'companyPeriodAmount',
      headerName: 'Company Contribution',
      hide: contributionType !== 'AMOUNT',
      minWidth: 220,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['companyPeriodAmount']>) => {
        if (params.data?.period === 'MONTHLY') {
          return <div>${params.value}/month</div>;
        }
        return <div>${params.data?.companyContributionAmount}/pay period</div>;
      },
    },
    {
      field: 'companyContributionPercent',
      headerName: 'Company Contribution',
      hide: contributionType !== 'PERCENT',
      minWidth: 220,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['companyContributionPercent']>) => (
        <div>{params.value}%</div>
      ),
    },
    {
      field: 'employeePeriodAmount',
      headerName: 'Employee Contribution',
      hide: contributionType !== 'AMOUNT',
      minWidth: 220,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['employeePeriodAmount']>) => {
        if (params.data?.period === 'MONTHLY') {
          return <div>${params.value}/month</div>;
        }
        return <div>${params.data?.employeeContributionAmount}/pay period</div>;
      },
    },
    {
      field: 'employeeContributionPercent',
      headerName: 'Employee Contribution',
      hide: contributionType === 'AMOUNT',
      minWidth: 220,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['employeeContributionPercent']>) => (
        <div>{params.value}%</div>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      minWidth: 150,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['status']>) => {
        return params.value ? (
          <div className="flex h-full min-h-10 w-full items-center">
            <StatusBadge status="completed">Enrolled</StatusBadge>
          </div>
        ) : (
          <div className="flex">
            <Tooltip
              content={
                <span className="capitalize">
                  {
                    [...EMPLOYEE_BENEFIT_UNENROLL_REASON, ...EMPLOYEE_BENEFIT_CHANGE_REASON].find(
                      (v) => v.value === params.data?.changeReason
                    )?.label
                  }
                </span>
              }
            >
              <StatusBadge status="disabled">Inactive</StatusBadge>
            </Tooltip>
          </div>
        );
      },
      comparator: (a: boolean, b: boolean) => {
        return a === b ? 0 : a ? -1 : 1;
      },
    },
    {
      field: 'benefitStartDateTimestamp',
      headerName: 'Start',
      minWidth: 120,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['benefitStartDateTimestamp']>) => {
        if (!params.value) return null;
        return formatLocaleUsa(params.value);
      },
    },
    {
      field: 'benefitEndDateTimestamp',
      headerName: 'End',
      minWidth: 120,
      cellRenderer: (params: ValueFormatterParams<TableRow, TableRow['benefitEndDateTimestamp']>) => {
        if (!params.value) return null;
        return formatLocaleUsa(params.value);
      },
    },
    {
      headerName: '',
      maxWidth: 140,
      sortable: false,
      cellClass: '!flex-row items-center gap-2 !px-5 !justify-end',
      cellRenderer: (params: ValueFormatterParams<TableRow>) => {
        return (
          <>
            <Tooltip content="History">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedBenefitRow(params.data ?? null);
                  setIsHistoryModalOpen(true);
                }}
              >
                <RiHistoryLine />
              </CompactButton>
            </Tooltip>
            {params.data?.status === true && (
              <>
                <Tooltip content="Edit">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      setSelectedBenefitRow(params.data ?? null);
                      setIsEditModalOpen(true);
                    }}
                  >
                    <RiPencilLine />
                  </CompactButton>
                </Tooltip>
                <Tooltip content="Unenroll">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      setSelectedBenefitRow(params.data ?? null);
                      setIsDeleteModalOpen(true);
                    }}
                  >
                    <RiCloseCircleLine />
                  </CompactButton>
                </Tooltip>
              </>
            )}
          </>
        );
      },
    },
  ];

  const history = useMemo(() => {
    if (!selectedBenefitRow) return [];
    return employeeBenefits
      .filter((employeeBenefit) => employeeBenefit.userId === selectedBenefitRow.userId)
      .map((employeeBenefit) => ({
        companyContribution:
          employeeBenefit.contributionType === 'AMOUNT'
            ? employeeBenefit.period === 'MONTHLY'
              ? '$' + employeeBenefit.companyPeriodAmount
              : '$' + employeeBenefit.companyContributionAmount
            : employeeBenefit.companyContributionPercent + '%',
        employeeContribution:
          employeeBenefit.contributionType === 'AMOUNT'
            ? employeeBenefit.period === 'MONTHLY'
              ? '$' + employeeBenefit.employeePeriodAmount
              : '$' + employeeBenefit.employeeContributionAmount
            : employeeBenefit.employeeContributionPercent + '%',
        startDate: employeeBenefit.benefitStartDate ? dayjs(employeeBenefit.benefitStartDate).toDate() : null,
        endDate: employeeBenefit.benefitEndDate ? dayjs(employeeBenefit.benefitEndDate).toDate() : null,
        reason: employeeBenefit.changeReason
          ? employeeBenefit.changeReason
              .split('_')
              .map((str, index) =>
                index === 0 ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : str.toLowerCase()
              )
              .join(' ')
          : 'Enrollment',
      }))
      .sort((a, b) => {
        return dayjs(b.endDate ?? new Date()).isAfter(dayjs(a.endDate)) ? 1 : -1;
      });
  }, [selectedBenefitRow, employeeBenefits]);

  return (
    <div className="flex flex-grow">
      <UpdatedTable
        fitToWindow
        colDefs={colDefs}
        rowData={rowData}
        hideSidebar={true}
        emptyRowsText="No employee benefits"
      />
      {selectedBenefitRow && (
        <EmployeeBenefitHistoryModal
          open={isHistoryModalOpen}
          setOpen={setIsHistoryModalOpen}
          employeeName={selectedBenefitRow.employeeName}
          benefitName={selectedBenefitRow.name}
          history={history}
        />
      )}
      {selectedBenefitRow && (
        <EditEmployeeBenefitModal
          open={isEditModalOpen}
          setOpen={setIsEditModalOpen}
          employeeBenefit={selectedBenefitRow}
          employeeName={selectedBenefitRow.employeeName}
          contributionType={contributionType}
          callback={() => setBenefitsRequest()}
        />
      )}
      {selectedBenefitRow && (
        <DeleteEmployeeBenefitModal
          open={isDeleteModalOpen}
          setOpen={setIsDeleteModalOpen}
          employeeBenefit={selectedBenefitRow}
          employeeName={selectedBenefitRow.employeeName}
          contributionType={contributionType}
          callback={() => setBenefitsRequest()}
        />
      )}
    </div>
  );
}
