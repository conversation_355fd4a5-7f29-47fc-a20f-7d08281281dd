import { RiHistoryLine } from '@remixicon/react';
import { ModalV2 } from '../elements/ModalV2';
import dayjs from 'dayjs';
import { Fragment } from 'react';

interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
  employeeName: string;
  benefitName: string;
  history: {
    companyContribution: string | number;
    employeeContribution: string | number;
    startDate: Date | null;
    endDate: Date | null;
    reason: string;
  }[];
}

export default function EmployeeBenefitHistoryModal({ open, setOpen, employeeName, benefitName, history }: Props) {
  return (
    <ModalV2 open={open} setOpen={setOpen} title="Benefit Enrollment History" icon={<RiHistoryLine />} className="p-5">
      <section className="grid grid-cols-4 gap-y-5">
        <div className="col-span-2">
          <h3 className="text-xs text-sub-600">Employee</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{employeeName}</p>
        </div>
        <div className="col-span-2">
          <h3 className="text-xs text-sub-600">Benefit</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{benefitName}</p>
        </div>
        <hr className="col-span-4 border-soft-200" />
        {history.map((entry, index) => (
          <Fragment key={index}>
            <div className="col-span-1">
              <p className="h-fit text-sm text-strong-950">{entry.companyContribution}</p>
              <h3 className="mt-1.5 text-xs text-sub-600">Company</h3>
            </div>
            <div className="col-span-1">
              <p className="h-fit text-sm text-strong-950">{entry.employeeContribution}</p>
              <h3 className="mt-1.5 text-xs text-sub-600">Employee</h3>
            </div>
            <div className="col-span-2">
              <p className="h-fit text-sm text-strong-950">
                {dayjs(entry.startDate).format('MMM DD, YYYY')} -{' '}
                {entry.endDate ? dayjs(entry.endDate).format('MMM DD, YYYY') : 'Today'}
              </p>
              <h3 className="mt-1.5 text-xs text-sub-600">{entry.reason}</h3>
            </div>
            <hr className="col-span-4 border-soft-200 last:hidden" />
          </Fragment>
        ))}
      </section>
    </ModalV2>
  );
}
