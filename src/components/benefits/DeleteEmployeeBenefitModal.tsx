import { FormV2 } from 'components/elements/Form';
import { addToast } from 'hooks/useToast';
import { ContributionType, EmployeeBenefit } from 'interfaces/benefit';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { unenrollEmployeeBenefit } from 'services/employeeBenefits';
import { logError, showErrorToast } from 'utils/errorHandling';
import { EMPLOYEE_BENEFIT_UNENROLL_REASON } from './constants';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import ProhibitedLine from '@/hammr-icons/ProhibitedLine';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { DateInput } from '@/hammr-ui/components/date-input';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { formatToYYYYMMDD } from '@/utils/dateHelper';

interface DeleteEmployeeBenefitModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  employeeBenefit: EmployeeBenefit;
  employeeName: string;
  contributionType: ContributionType;
}

const FormBody = ({ errors, control, employeeBenefit, employeeName }) => {
  return (
    <>
      <section className="grid grid-cols-2 gap-5">
        <article>
          <h2 className="text-xs text-sub-600">Benefit Name</h2>
          <p className="text-sm text-strong-950">{employeeBenefit.name}</p>
        </article>
        <article>
          <h2 className="text-xs text-sub-600">Employee</h2>
          <p className="text-sm text-strong-950">{employeeName}</p>
        </article>
      </section>

      <FormItem required error={!!errors['effectiveDate']} className="mt-5">
        <FormLabel>Effective Date</FormLabel>
        <Controller
          name="effectiveDate"
          rules={{ required: 'Please select an effective date' }}
          control={control}
          defaultValue={new Date()}
          render={({ field }) => (
            <FormControl>
              <DateInput {...field} value={field.value ? field.value : null} />
            </FormControl>
          )}
        />
        <FormMessage>{errors['effectiveDate']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['changeReason']} className="mt-5">
        <FormLabel>Reason For Change</FormLabel>
        <FormControl>
          <Controller
            rules={{ required: 'Please select a reason' }}
            control={control}
            name="changeReason"
            render={({ field }) => (
              <DropdownPicker
                aria-invalid={!!errors['changeReason']}
                placeholder="Select an option"
                className="mt-1 w-full"
                items={EMPLOYEE_BENEFIT_UNENROLL_REASON.map((option) => {
                  return {
                    label: option.label,
                    value: option.value,
                  };
                })}
                value={field.value}
                onChange={field.onChange}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['changeReason']?.message}</FormMessage>
      </FormItem>
    </>
  );
};

const DeleteEmployeeBenefitModal = ({
  open,
  setOpen,
  callback,
  employeeBenefit,
  employeeName,
}: DeleteEmployeeBenefitModalProps) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm();
  // TODO -> set is processing
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open) reset();
  }, [open, reset]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const formData = {
      ...data,
      effectiveDate: formatToYYYYMMDD(data.effectiveDate),
    };

    try {
      await unenrollEmployeeBenefit(employeeBenefit.id, formData);

      addToast({
        title: 'Unenrolled Employee',
        description: (
          <>
            Successfully unenrolled <strong className="font-medium">{employeeName}</strong> from{' '}
            <strong className="font-medium">{employeeBenefit.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to unenroll employee');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<ProhibitedLine className="text-sub-600" />} title="Unenroll Employee" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Unenroll"
        >
          <FormBody control={control} errors={errors} employeeBenefit={employeeBenefit} employeeName={employeeName} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default DeleteEmployeeBenefitModal;
