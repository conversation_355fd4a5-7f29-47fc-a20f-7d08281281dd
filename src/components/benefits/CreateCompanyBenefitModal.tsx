import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { CompanyBenefit } from 'interfaces/benefit';
import { createBenefit } from 'services/benefits';
import CompanyBenefitForm from './CompanyBenefitForm';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Heart3Line from '@/hammr-icons/Heart3Line';
import { formatToYYYYMMDD } from '@/utils/dateHelper';
import { useQueryClient } from '@tanstack/react-query';

interface CreateCompanyBenefitModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

export default function CreateCompanyBenefitModal({ open, setOpen }: CreateCompanyBenefitModalProps) {
  const { addToast } = useToast();
  // TODO -> move validation to yup
  const {
    formState: { errors },
    handleSubmit,
    register,
    control,
    watch,
    setValue,
    reset,
  } = useForm({
    defaultValues: {
      contributionType: 'PERCENT',
    },
  });

  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  const queryClient = useQueryClient();

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const {
      name,
      category,
      contributionType,
      benefitStartDate,
      benefitEndDate,
      isApprovedFringe,
      benefitProviderName,
      benefitProviderAddress,
      benefitProviderPhone,
    } = data;

    const commonPayloadData = {
      name,
      category,
      contributionType,
      benefitStartDate: formatToYYYYMMDD(benefitStartDate),
      benefitEndDate: formatToYYYYMMDD(benefitEndDate),
      benefitProviderName,
      benefitProviderAddress,
      benefitProviderPhone,
    };

    let payloadData = {};

    if (contributionType === 'PERCENT' || contributionType === 'AMOUNT') {
      payloadData = {
        ...commonPayloadData,
        isApprovedFringe,
      };
    } else if (contributionType === 'DYNAMIC') {
      payloadData = {
        ...commonPayloadData,
        isApprovedFringe: true,
      };
    }

    try {
      await createBenefit(payloadData as any as CompanyBenefit);

      addToast({
        title: 'Created Company Benefit',
        description: (
          <>
            Successfully created the company benefit <strong className="font-medium">{data.name}</strong>.
          </>
        ),
        type: 'success',
      });

      queryClient.invalidateQueries({ queryKey: ['benefits'] });
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create company benefit');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<Heart3Line className="text-sub-600" />} title="Create Benefit" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Create"
        >
          <CompanyBenefitForm watch={watch} register={register} control={control} errors={errors} setValue={setValue} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
