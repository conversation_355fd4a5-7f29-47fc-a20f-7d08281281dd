import { CompanyBenefitModified } from 'interfaces/benefit';
import { formatLocaleUsa, isItemActive } from 'utils/dateHelper';
import { BENEFIT_CATEGORY_OPTIONS } from './constants';
import { Badge } from '@/hammr-ui/components/badge';
import CalendarLine from '@/hammr-icons/CalendarLine';
import { formatPhoneNumber } from '@/utils/format';

const BenefitsDetails = (props: CompanyBenefitModified) => {
  const isActive = isItemActive(props.benefitStartDate, props.benefitEndDate);

  const {
    name,
    category,
    benefitStartDate,
    benefitEndDate,
    contributionType,
    isApprovedFringe,
    benefitProviderName,
    benefitProviderAddress,
    benefitProviderPhone,
  } = props;

  return (
    <section className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs">
      <h2 className="font-medium text-strong-950">General Information</h2>

      <h3 className="mt-5 text-xs text-sub-600">Benefit Name</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">{name}</p>

      <h3 className="mt-5 text-xs text-sub-600">Cateogory</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        {BENEFIT_CATEGORY_OPTIONS.find((v) => v.value === category)?.label}
      </p>

      <h3 className="mt-5 text-xs text-sub-600">Status</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        {isActive ? (
          <Badge color="green" variant="lighter">
            ACTIVE
          </Badge>
        ) : (
          <Badge color="red" variant="lighter">
            INACTIVE
          </Badge>
        )}
      </p>

      <h3 className="mt-5 text-xs text-sub-600">Start Date</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        <CalendarLine className="mr-2 size-5 text-sub-600" />
        {benefitStartDate ? formatLocaleUsa(benefitStartDate) : '-'}
      </p>

      <h3 className="mt-5 text-xs text-sub-600">End Date</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        <CalendarLine className="mr-2 size-5 text-sub-600" />
        {benefitEndDate ? formatLocaleUsa(benefitEndDate) : '-'}
      </p>

      <h3 className="mt-5 text-xs text-sub-600">Approved Fringe</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        {isApprovedFringe ? (
          <Badge color="green" variant="lighter">
            ENABLED
          </Badge>
        ) : (
          <Badge color="red" variant="lighter">
            DISABLED
          </Badge>
        )}
      </p>

      <hr className="mt-5 border-soft-200" />

      <h2 className="mt-5 font-medium text-strong-950">Benefit Provider</h2>

      <h3 className="mt-5 text-xs text-sub-600">Provider Name</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">{benefitProviderName ? benefitProviderName : '-'}</p>

      <h3 className="mt-5 text-xs text-sub-600">Address</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">{benefitProviderAddress ? benefitProviderAddress : '-'}</p>

      <h3 className="mt-5 text-xs text-sub-600">Phone Number</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        {benefitProviderPhone ? formatPhoneNumber(benefitProviderPhone) : '-'}
      </p>
    </section>
  );
};

export default BenefitsDetails;
