import { useEffect, useRef, useState } from 'react';
import { paginateContractors } from 'services/contractor';
import { useAuth } from 'hooks/useAuth';
import { OnboardStatus } from 'interfaces/onboard';
import { UpdatedTable } from '../shared/UpdatedTable';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { StatusBadge } from './StatusBadge';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { useRouter } from 'next/router';
import { logError, showErrorToast } from 'utils/errorHandling';
import EmptyStateHRNotes from '@/hammr-icons/EmptyStateHRNotes';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { useEmployeesQuery } from '@/hooks/data-fetching/useEmployees';

interface TableItem {
  id: string;
  name: string;
  email: string;
  status: OnboardStatus;
  actions: any;
}

const ContractorTaxDocuments: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const gridRef = useRef(null);
  const [nextUrl, setNextUrl] = useState<string | null>(null);
  const [allRows, setAllRows] = useState<TableItem[]>(undefined);
  const { data: employees } = useEmployeesQuery({
    companyId: user?.companyId,
    simple: true,
    includeIsArchived: true,
  });

  const contractorTableColumns: ColDef<TableItem>[] = [
    { field: 'name', headerName: 'Contractor Name' },
    {
      field: 'status',
      headerName: 'Onboarding Status',
      width: 300,
      resizable: false,
      cellRenderer: (params: ICellRendererParams) => {
        const { status } = params.data;
        return <StatusBadge status={status} />;
      },
    },
    {
      field: 'actions',
      sortable: false,
      width: 130,
      resizable: false,
      headerName: '',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <LinkButton
            className="text-primary-base"
            onClick={() => router.push(`/people/contractor/${params.data.hammrId}?tab=documents`)}
            size="medium"
          >
            View Documents
          </LinkButton>
        );
      },
    },
  ];

  useEffect(() => {
    if (employees?.length > 0) {
      fetchContractors();
    }
  }, [user?.checkCompanyId, nextUrl, employees]);

  const fetchContractors = async () => {
    if (allRows && allRows.length > 0 && !nextUrl) {
      return;
    }

    try {
      const response = await paginateContractors(user?.checkCompanyId, nextUrl ?? undefined);
      setNextUrl(response.next);

      const mappedRows = response.results.map((contractor) => ({
        id: contractor.checkContractor?.id,
        name: `${contractor.checkContractor?.first_name} ${contractor.checkContractor?.last_name}`,
        email: contractor.checkContractor?.email,
        status: contractor.checkContractor?.onboard.status,
        hammrId: employees?.find((employee) => employee.checkContractorId === contractor.checkContractor?.id)?.id,
      }));

      setAllRows((prevRows) => (prevRows ? [...prevRows, ...mappedRows] : mappedRows));
    } catch (err) {
      logError(err);
      showErrorToast(err);
    }
  };

  if (!user) return null;

  if (!allRows) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (allRows.length === 0) {
    return (
      <>
        <div className="mt-32">
          <div className="flex h-full flex-col items-center justify-start">
            <EmptyStateHRNotes />
            <div className="mt-5 text-center text-sm text-soft-400">
              <div>There are no contractor documents yet.</div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="mt-6 flex max-w-[728px] flex-col">
      <UpdatedTable<TableItem>
        rowData={allRows}
        colDefs={contractorTableColumns}
        parentRef={gridRef}
        enablePagination
      />
    </div>
  );
};

export default ContractorTaxDocuments;
