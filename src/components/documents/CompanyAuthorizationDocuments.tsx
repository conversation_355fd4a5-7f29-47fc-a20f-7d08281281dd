import Spinner from 'components/icons/Spinner';
import { useState } from 'react';
import { DocumentCard } from './DocumentCard';
import { companyAuthorizationDocumentDownload } from '@/services/documents';
import { ListResponse } from '@/interfaces/check';
import { CompanyAuthorizationDocument } from '@/interfaces/documents';

const CompanyAuthorizationDocuments: React.FC<{
  paginateDocuments: ListResponse<CompanyAuthorizationDocument>;
}> = ({ paginateDocuments }) => {
  const [isDownloadingIndex, setIsDownloadingIndex] = useState<number>(null);

  if (!paginateDocuments?.results) {
    return (
      <div>
        <div className="mt-1/5 w-full">
          <div className="mx-auto">
            <Spinner width="40" fill="blue" className="mx-auto my-10 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pb-6">
      <div className="py-6 font-medium text-strong-950">Company Authorization Forms</div>
      <div className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6">
        {paginateDocuments.results.map((document, index) => (
          <DocumentCard
            key={document.id}
            document={document}
            index={index}
            setIsDownloadingIndex={setIsDownloadingIndex}
            isDownloadingIndex={isDownloadingIndex}
            downloadFunction={companyAuthorizationDocumentDownload}
          />
        ))}
      </div>
    </div>
  );
};

export default CompanyAuthorizationDocuments;
