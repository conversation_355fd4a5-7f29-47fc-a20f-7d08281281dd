import { useEffect, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { CompanyAuthorizationDocument, CompanyTaxDocument } from 'interfaces/documents';
import { listCompanyAuthorizationDocuments, listCompanyTaxDocuments } from 'services/documents';
import CompanyAuthorizationDocuments from './CompanyAuthorizationDocuments';
import EmployeeTaxDocuments from './EmployeeTaxDocuments';
import ContractorTaxDocuments from './ContractorTaxDocuments';
import CompanyTaxDocuments from './CompanyTaxDocuments';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Tabs, TabsContent, TabsList, TabsTrigger } from 'hammr-ui/components/FlatTabs';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import EmptyStateHRNotes from '@/hammr-icons/EmptyStateHRNotes';
import { ListResponse } from '@/interfaces/check';

type Tabs = 'company' | 'employee' | 'contractor';

const DocumentsView: React.FC = () => {
  const { user } = useAuth();
  const { company } = useCompany();
  const [selectedTab, setSelectedTab] = useState<Tabs>(null);
  const [paginateCompanyTaxDocuments, setPaginateCompanyTaxDocuments] =
    useState<ListResponse<CompanyTaxDocument>>(null);
  const [paginateCompanyAuthorizationDocuments, setPaginateCompanyAuthorizationDocuments] =
    useState<ListResponse<CompanyAuthorizationDocument>>(null);

  const tabs = [
    { key: 'company', name: 'Company Documents' },
    { key: 'employee', name: 'Employee Documents' },
    { key: 'contractor', name: 'Contractor Documents' },
  ];

  const refresh = () => {
    // Fetch auth docs -> tax docs -> set tab
    listCompanyAuthorizationDocuments(user.checkCompanyId)
      .then((res) => {
        setPaginateCompanyAuthorizationDocuments(res);
        listCompanyTaxDocuments(user.checkCompanyId).then((res) => {
          setPaginateCompanyTaxDocuments(res);
          if (!selectedTab) {
            setSelectedTab('company');
          }
        });
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  if (!user) return null;

  useEffect(() => {
    if (user.checkCompanyId) {
      refresh();
    }
  }, [user.checkCompanyId]);

  if ((!paginateCompanyAuthorizationDocuments && !paginateCompanyTaxDocuments) || !user.checkCompanyId || !company) {
    return (
      <div className="flex h-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (paginateCompanyAuthorizationDocuments.results.length === 0 && paginateCompanyTaxDocuments.results.length === 0) {
    return (
      <div className="mt-32">
        <div className="flex h-full flex-col items-center justify-start">
          <EmptyStateHRNotes />
          <div className="mt-5 text-center text-sm text-soft-400">
            <div>There is no company documents yet because we are preparing</div>
            <div>{`your company's onboarding right now.`}</div>

            <div className="mt-5 text-start">
              <div>If you expect documents to show up here,</div>
              <div>
                please reach out to Hammr customer support at{' '}
                <span className="cursor-pointer font-medium text-primary-base hover:text-primary-darker hover:underline">
                  <a href="mailto:<EMAIL>"><EMAIL>.</a>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (user.checkCompanyId && company) {
    return (
      <div className="mt-5 h-full pb-12">
        <div className="flex h-full">
          <Tabs className="w-full" value={selectedTab} onValueChange={(value: Tabs) => setSelectedTab(value)}>
            <TabsList>
              {tabs.map((tab) => (
                <TabsTrigger key={tab.key} value={tab.key}>
                  {tab.name}
                </TabsTrigger>
              ))}
            </TabsList>
            <TabsContent className="p-0" value="company">
              <div>
                <CompanyAuthorizationDocuments paginateDocuments={paginateCompanyAuthorizationDocuments} />
                <CompanyTaxDocuments paginateDocuments={paginateCompanyTaxDocuments} />
              </div>
            </TabsContent>
            <TabsContent className="h-full p-0" value="employee">
              <EmployeeTaxDocuments />
            </TabsContent>
            <TabsContent className="h-full p-0" value="contractor">
              <ContractorTaxDocuments />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }
};

export default DocumentsView;
