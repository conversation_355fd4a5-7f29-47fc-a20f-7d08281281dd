import Spinner from 'components/icons/Spinner';
import { useState } from 'react';
import { DocumentCard } from './DocumentCard';
import { companyTaxDocumentDownload } from '@/services/documents';
import { CompanyTaxDocument } from 'interfaces/documents';
import { ListResponse } from '@/interfaces/check';

const CompanyTaxDocuments: React.FC<{
  paginateDocuments: ListResponse<CompanyTaxDocument>;
}> = ({ paginateDocuments }) => {
  const [isDownloadingIndex, setIsDownloadingIndex] = useState<number>(null);

  if (!paginateDocuments?.results) {
    return (
      <div>
        <div className="mt-1/5 w-full">
          <div className="mx-auto">
            <Spinner width="40" fill="blue" className="mx-auto my-10 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  if (paginateDocuments.results.length === 0) {
    return (
      <div>
        <div className="py-6 font-medium text-strong-950">Company Tax Forms</div>
        <div className="text-sm text-sub-600">Your company has no tax documents.</div>
      </div>
    );
  }

  return (
    <div>
      <div className="py-6 font-medium text-strong-950">Company Tax Forms</div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {paginateDocuments.results.map((document, index) => (
          <DocumentCard
            key={document.id}
            document={document}
            index={index}
            setIsDownloadingIndex={setIsDownloadingIndex}
            isDownloadingIndex={isDownloadingIndex}
            downloadFunction={companyTaxDocumentDownload}
          />
        ))}
      </div>
    </div>
  );
};

export default CompanyTaxDocuments;
