import { useEffect, useRef, useState } from 'react';
import { paginateEmployees } from 'services/employee';
import { OnboardStatus } from 'interfaces/onboard';
import { UpdatedTable } from '../shared/UpdatedTable';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { StatusBadge } from './StatusBadge';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { useRouter } from 'next/router';
import EmptyStateHRNotes from '@/hammr-icons/EmptyStateHRNotes';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { useEmployees } from '@/hooks/data-fetching/useEmployees';
import { useAuth } from '@/hooks/useAuth';

interface TableItem {
  id: string;
  name: string;
  email: string;
  status: OnboardStatus;
  actions: any;
}

const EmployeeTaxDocuments: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const gridRef = useRef(null);
  const [nextUrl, setNextUrl] = useState<string | null>(null);
  const [allRows, setAllRows] = useState<TableItem[]>(undefined);
  const hammrEmployees = useEmployees(Number(user?.companyId), { includeIsArchived: true });

  const employeeTableColumns: ColDef<TableItem>[] = [
    { field: 'name', headerName: 'Employee Name' },
    {
      field: 'status',
      headerName: 'Onboarding Status',
      width: 300,
      resizable: false,
      cellRenderer: (params: ICellRendererParams) => {
        const { status } = params.data;
        return <StatusBadge status={status} />;
      },
    },
    {
      field: 'actions',
      sortable: false,
      width: 130,
      resizable: false,
      headerName: '',
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <LinkButton
            className="text-primary-base"
            onClick={() => router.push(`/people/employee/${params.data.id}?tab=documents`)}
            size="medium"
          >
            View Documents
          </LinkButton>
        );
      },
    },
  ];

  useEffect(() => {
    if (hammrEmployees && hammrEmployees.length > 0) {
      fetchEmployees();
    }
  }, [user?.checkCompanyId, nextUrl, hammrEmployees]);

  const fetchEmployees = async () => {
    if (allRows && allRows.length > 0 && !nextUrl) {
      return;
    }

    const response = await paginateEmployees(user?.checkCompanyId, nextUrl ?? undefined, true);

    if (response.next !== nextUrl) {
      setNextUrl(response.next);
    }

    const mappedRows = response.results.map((employee) => {
      const checkId = employee.checkEmployee?.id;
      const hammrEmployee = hammrEmployees.find(
        (e) => e.checkEmployeeId === checkId || e.checkContractorId === checkId
      );

      return {
        id: hammrEmployee?.id,
        name: `${employee.checkEmployee?.first_name} ${employee.checkEmployee?.last_name}`,
        email: employee.checkEmployee?.email,
        status: employee.checkEmployee?.onboard.status,
      };
    });

    const newEmployees = mappedRows.filter((employee) => allRows && !allRows.some((e) => e.id === employee.id));
    const updatedRows = allRows ? [...allRows, ...newEmployees] : mappedRows;
    setAllRows(updatedRows);
  };

  if (!user) return null;

  if (!allRows) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (allRows.length === 0) {
    return (
      <>
        <div className="mt-32">
          <div className="flex h-full flex-col items-center justify-start">
            <EmptyStateHRNotes />
            <div className="mt-5 text-center text-sm text-soft-400">
              <div>There are no employee documents yet.</div>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <div className="mt-6 flex max-w-[728px] flex-col">
      <UpdatedTable<TableItem> rowData={allRows} colDefs={employeeTableColumns} parentRef={gridRef} enablePagination />
    </div>
  );
};

export default EmployeeTaxDocuments;
