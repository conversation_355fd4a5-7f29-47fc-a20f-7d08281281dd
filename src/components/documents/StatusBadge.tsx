import { cn } from '@/hammr-ui/lib/utils';
import { OnboardStatus } from '@/interfaces/onboard';

export const StatusBadge = ({ status, className }: { status: OnboardStatus; className?: string }) => {
  return (
    <div
      className={cn(
        getBackgroundColor(status),
        getTextColor(status),
        'flex h-6 items-center gap-2 self-start rounded-full px-2 py-1',
        className
      )}
    >
      <span className="text-xs font-medium capitalize">{status.replace('_', ' ')}</span>
    </div>
  );
};

export const getTextColor = (status: OnboardStatus) => {
  switch (status) {
    case 'needs_attention':
      return 'text-away-base';
    case 'completed':
      return 'text-success-base';
    case 'blocking':
      return 'text-error-base';
    default:
      return '';
  }
};

export const getBackgroundColor = (status: OnboardStatus) => {
  switch (status) {
    case 'needs_attention':
      return 'bg-away-lighter';
    case 'completed':
      return 'bg-success-lighter';
    case 'blocking':
      return 'bg-error-lighter';
    default:
      return '';
  }
};
