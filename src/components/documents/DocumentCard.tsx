import { CompanyAuthorizationDocument, CompanyTaxDocument } from 'interfaces/documents';
import { companyTaxDocumentDownload } from 'services/documents';
import { logError } from 'utils/errorHandling';
import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import Spinner from 'components/icons/Spinner';
import dayjs from 'dayjs';

export const DocumentCard = ({
  document,
  index,
  setIsDownloadingIndex,
  isDownloadingIndex,
  downloadFunction,
}: {
  document: CompanyTaxDocument | CompanyAuthorizationDocument;
  index: number;
  setIsDownloadingIndex: (index: number) => void;
  isDownloadingIndex: number;
  downloadFunction: (documentId: string, documentLabel: string) => Promise<void>;
}) => {
  return (
    <Card className="flex h-32 max-w-[352px] flex-col">
      <CardHeader>
        <div className="relative">
          <CardTitle className="pr-10 text-sm">{document.label}</CardTitle>
          <div className="absolute right-0 top-0 text-xs text-sub-600">{document.year}</div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow px-5 pb-6 pt-0">
        <div className="flex h-full items-end justify-between gap-2">
          {isDownloadingIndex === index ? (
            <Spinner width="20" fill="blue" className="animate-spin" />
          ) : (
            <div
              onClick={() => {
                setIsDownloadingIndex(index);
                downloadFunction(document.id, document.label)
                  .then(() => setIsDownloadingIndex(null))
                  .catch((err) => {
                    logError(err);
                    setIsDownloadingIndex(null);
                  });
              }}
              className="cursor-pointer text-sm font-medium text-primary-base hover:text-primary-darker hover:underline"
            >
              Download
            </div>
          )}

          <div className="text-xs text-sub-600">Signed on {dayjs(document.filed_on).format('MM/DD/YYYY')}</div>
        </div>
      </CardContent>
    </Card>
  );
};
