import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { WageTable, UpdateWageTable } from 'interfaces/wage-table';
import { updateWageTable } from 'services/wage-tables';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Table2Line from '@/hammr-icons/Table2Line';
import { TextField } from '../elements/form/TextField';

interface EditWageTableModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  wageTable: WageTable;
  callback?: () => void;
}

const FormBody = ({ errors, register, control }) => {
  return (
    <div className="flex flex-col gap-5">
      <TextField required name="name" label="Wage Table Name" placeholder="Enter wage table name" control={control} />

      <FormItem error={!!errors['description']}>
        <FormLabel>Description</FormLabel>
        <FormControl>
          <Textarea
            name="description"
            placeholder="Enter description"
            {...register('description', {
              maxLength: {
                value: 200,
                message: 'Description should not exceed 200 characters',
              },
            })}
          />
        </FormControl>
        <FormMessage>{errors['description']?.message}</FormMessage>
      </FormItem>
    </div>
  );
};

const EditWageTableModal = ({ open, setOpen, wageTable, callback }: EditWageTableModalProps) => {
  const { addToast } = useToast();
  const {
    handleSubmit,
    formState: { errors },
    register,
    reset,
    control,
  } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!open || !wageTable) return;

    reset({
      name: wageTable.name,
      description: wageTable.description,
    });
  }, [open, wageTable, reset]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const formattedPayloadData = {
      ...data,
    };

    try {
      await updateWageTable(wageTable.id, formattedPayloadData as UpdateWageTable);

      addToast({
        title: 'Edited Wage Table',
        description: (
          <>
            Successfully edited the wage table <strong className="font-medium">{data.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update wage table');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Edit Wage Table" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Save"
        >
          <FormBody errors={errors} register={register} control={control} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default EditWageTableModal;
