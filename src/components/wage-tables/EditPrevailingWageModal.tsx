import { <PERSON><PERSON>, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormV2 } from '../elements/Form';
import { useForm } from 'react-hook-form';
import { Dispatch, SetStateAction, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { EmployeeClassification } from '@/interfaces/classifications';
import { NumberFieldV2 } from '../elements/form/NumberFieldV2';
import { updateEmployeeClassifications } from '@/services/classifications';
import { addToast } from '@/hooks/useToast';
import Table2Line from '@/hammr-icons/Table2Line';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { FormItem, FormLabel, FormControl, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  employeeClassification: EmployeeClassification;
}

type FormValues = {
  basePay: string;
  fringePay: string;
  effectiveDate: Date;
};

const FormBody = ({ control, employeeClassification, errors, register }) => {
  return (
    <>
      <section className="grid grid-cols-2 gap-5">
        <article>
          <h2 className="text-xs text-sub-600">Classification</h2>
          <p className="text-sm text-strong-950">{employeeClassification.classification.name}</p>
        </article>
        <article>
          <h2 className="text-xs text-sub-600">Employee</h2>
          <p className="text-sm text-strong-950">
            {employeeClassification.user.firstName} {employeeClassification.user.lastName}
          </p>
        </article>
      </section>

      <FormItem required error={!!errors['basePay']} className="mt-5">
        <FormLabel>Base Pay</FormLabel>
        <FormControl>
          <Input
            type="number"
            step="any"
            beforeContent="$"
            placeholder="0.00"
            {...register('basePay', {
              required: 'Please enter a base pay',
              min: { value: 0, message: 'Enter a value 0 or above ' },
            })}
          />
        </FormControl>
        <FormMessage>{errors['basePay']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['fringePay']} className="mt-5">
        <FormLabel>Fringe Pay</FormLabel>
        <FormControl>
          <Input
            type="number"
            step="any"
            beforeContent="$"
            placeholder="0.00"
            {...register('fringePay', {
              required: 'Please enter a fringe pay',
            })}
          />
        </FormControl>
        <FormMessage>{errors['fringePay']?.message}</FormMessage>
      </FormItem>
      <ControlledDateInput
        label="Effective Date"
        control={control}
        name="effectiveDate"
        rules={{ required: 'Please select an effective date' }}
        className="mt-5 w-full"
        required
      />
    </>
  );
};

export default function EditPrevailingWageModal({ open, setOpen, employeeClassification }: Props) {
  const form = useForm({
    defaultValues: {
      basePay: employeeClassification.basePay,
      fringePay: employeeClassification.fringePay,
      effectiveDate: null,
    },
  });

  const queryClient = useQueryClient();

  const updatePrevailingWageMutation = useMutation({
    async mutationFn(formData: FormValues) {
      await updateEmployeeClassifications({
        effectiveDate: formData.effectiveDate.getTime(),
        payload: [
          {
            userClassificationId: employeeClassification.id,
            basePay: parseFloat(formData.basePay).toFixed(2),
            fringePay: parseFloat(formData.fringePay).toFixed(2),
          },
        ],
      });
    },
    onSuccess() {
      setOpen(false);

      addToast({
        title: 'Updated Prevailing Wage',
        description: `Successfully updated employee prevailing wage.`,
        type: 'success',
      });

      queryClient.invalidateQueries({ queryKey: ['employee-classification'] });
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        basePay: employeeClassification.basePay,
        fringePay: employeeClassification.fringePay,
        effectiveDate: null,
      });
    }
  }, [open, form, employeeClassification]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Update Prevailing Wage" />
        <FormV2
          onSubmit={form.handleSubmit((formData) => updatePrevailingWageMutation.mutate(formData))}
          onCancel={() => setOpen(false)}
          isLoading={updatePrevailingWageMutation.isPending}
          submitText="Save"
        >
          <FormBody
            control={form.control}
            employeeClassification={employeeClassification}
            errors={form.formState.errors}
            register={form.register}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
