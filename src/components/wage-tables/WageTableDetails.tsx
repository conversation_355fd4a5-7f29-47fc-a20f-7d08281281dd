import { WageTable } from 'interfaces/wage-table';
import { formatLocaleUsa } from 'utils/dateHelper';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { useRouter } from 'next/router';

const WageTableDetails = (props: WageTable) => {
  const router = useRouter();
  const { name, description, projects, createdAt } = props;

  return (
    <section className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs">
      <h2 className="text-xs text-sub-600">Wage Table Name</h2>
      <p className="mt-1.5 h-fit text-sm text-strong-950">{name}</p>

      <h2 className="mt-3.5 text-xs text-sub-600">Created On</h2>
      <p className="mt-1.5 h-fit text-sm text-strong-950">{formatLocaleUsa(createdAt)}</p>

      {description && (
        <>
          <h2 className="mt-3.5 text-xs text-sub-600">Description</h2>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{description}</p>
        </>
      )}

      <h2 className="mt-3.5 text-xs text-sub-600">Assigned Projects</h2>
      <div className="mt-1.5 flex flex-col items-start gap-2 w-full">
        {projects?.length > 0 ? (
          projects
            .sort((a, b) => {
              // Sort by project name in ascending order
              return a.name.localeCompare(b.name);
            })
            .map((project) => (
              <LinkButton
                key={project.id}
                size="medium"
                style="primary"
                className="text-left justify-start w-full"
                onClick={() => {
                  router.push(`/projects/${project.id}`);
                }}
              >
                {project.name}
              </LinkButton>
            ))
        ) : (
          <span className="text-sm text-strong-950">-</span>
        )}
      </div>
    </section>
  );
};

export default WageTableDetails;
