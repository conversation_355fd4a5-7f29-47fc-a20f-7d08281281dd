import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { WageTable } from 'interfaces/wage-table';
import { createWageTable } from 'services/wage-tables';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Table2Line from '@/hammr-icons/Table2Line';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { useRouter } from 'next/router';

interface CreateWageTableModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback?: () => void;
}

const FormBody = ({ errors, register }) => {
  return (
    <div className="flex flex-col gap-5">
      <FormItem required error={!!errors['name']}>
        <FormLabel>Wage Table Name</FormLabel>
        <FormControl>
          <Input
            name="name"
            placeholder="Enter wage table name"
            {...register('name', {
              required: 'Please enter a wage table name',
            })}
          />
        </FormControl>
        <FormMessage>{errors['name']?.message}</FormMessage>
      </FormItem>

      <FormItem error={!!errors['description']}>
        <FormLabel>Description</FormLabel>
        <FormControl>
          <Textarea
            name="description"
            placeholder="Enter description"
            {...register('description', {
              maxLength: {
                value: 200,
                message: 'Description should not exceed 200 characters',
              },
            })}
          />
        </FormControl>
        <FormMessage>{errors['description']?.message}</FormMessage>
      </FormItem>
    </div>
  );
};

const CreateWageTableModal = ({ open, setOpen, callback }: CreateWageTableModalProps) => {
  const { addToast } = useToast();
  const {
    handleSubmit,
    formState: { errors },
    register,
    reset,
  } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const formattedPayloadData = {
      ...data,
    };

    try {
      await createWageTable(formattedPayloadData as any as WageTable);

      addToast({
        title: 'Created Wage Table',
        description: (
          <>
            Successfully created the wage table <strong className="font-medium">{data.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create wage table');
    } finally {
      setIsProcessing(false);
    }
  };

  const router = useRouter();

  function onOpenChangeHandler(open: boolean) {
    setOpen(open);
    if (!open) {
      reset();
      router.replace('/wage-tables', undefined, { shallow: true });
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChangeHandler}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Create Wage Table" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={isProcessing}
          submitText="Create"
        >
          <FormBody errors={errors} register={register} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default CreateWageTableModal;
