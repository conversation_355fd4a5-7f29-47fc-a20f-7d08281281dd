import { Dispatch, SetStateAction } from 'react';
import { useToast } from 'hooks/useToast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { logError, showErrorToast } from 'utils/errorHandling';
import { WageTable } from 'interfaces/wage-table';
import { updateWageTable } from 'services/wage-tables';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import Alert from '@/hammr-ui/components/Alert';

interface ArchiveWageTableModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  wageTable: WageTable;
}

export default function ArchiveWageTableModal({ open, setOpen, callback, wageTable }: ArchiveWageTableModalProps) {
  const { addToast } = useToast();
  const queryClient = useQueryClient();

  const isArchived = !!wageTable?.isArchived;

  const toggleArchive = useMutation({
    mutationFn: () => updateWageTable(wageTable.id, { isArchived: !isArchived }),
    onSuccess: () => {
      addToast({
        title: `${!isArchived ? 'Archived' : 'Unarchived'} Wage Table`,
        description: (
          <>
            Successfully {!isArchived ? 'archived' : 'unarchived'} the wage table{' '}
            <strong className="font-medium">{wageTable.name}</strong>.
          </>
        ),
        type: 'success',
      });

      // Invalidate and refetch wage tables queries
      queryClient.invalidateQueries({ queryKey: ['wage-tables'] });

      callback?.();
      setOpen(false);
    },
    onError: (err) => {
      logError(err);
      showErrorToast(err, `Unable to ${!isArchived ? 'archive' : 'unarchive'} wage table`);
    },
  });

  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      onConfirm={() => toggleArchive.mutate()}
      icon={<KeyIcon icon={<ArchiveLine />} color={isArchived ? 'green' : 'red'} />}
      title={`${isArchived ? 'Unarchive' : 'Archive'} Wage Table`}
      subtitle={
        <>
          You are about to {isArchived ? 'unarchive' : 'archive'} the wage table{' '}
          {<span className="font-medium">{wageTable?.name}</span>}. Do you want to proceed?
        </>
      }
      confirmButton={{
        color: isArchived ? 'primary' : 'error',
      }}
      confirmButtonText={isArchived ? 'Unarchive' : 'Archive'}
    >
      <h3 className="text-sm text-strong-950">{wageTable.projects?.length || 0} projects assigned</h3>
      <Alert className="mt-5">
        {isArchived
          ? 'Unarchiving will make this Wage Table available for use again.'
          : 'Archived Wage Tables will no longer be available for use.'}
      </Alert>
    </ConfirmDialog>
  );
}
