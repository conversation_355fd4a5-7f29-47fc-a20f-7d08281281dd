import { cn } from '@/utils/cn';

interface DetailsItemProps {
  title: string;
  children?: JSX.Element | string;
  icon?: JSX.Element;
  className?: string;
}

const DetailsItem = ({ title, children, icon, className }: DetailsItemProps) => {
  const calculatedChildren = children || '-';
  return (
    <div className={cn('flex flex-col justify-between gap-1.5', className)}>
      <div className="text-xs font-medium capitalize text-sub-600">{title}</div>
      <div className="flex text-sm text-foreground">
        {icon && <span className="mr-2">{icon}</span>}
        {calculatedChildren}
      </div>
    </div>
  );
};

export default DetailsItem;
