import { LicenseManager } from '@ag-grid-enterprise/core';
import {
  ColDef,
  ColumnMovedEvent,
  ColumnRowGroupChangedEvent,
  GridOptions,
  GridReadyEvent,
  ICellRendererParams,
  ModuleRegistry,
  PaginationChangedEvent,
  RowClickedEvent,
  SideBarDef,
} from '@ag-grid-community/core';
import { AgGridReact, CustomCellRendererProps } from '@ag-grid-community/react'; // AG Grid Component
import '@ag-grid-community/styles/ag-grid.css'; // Core CSS
import '@ag-grid-community/styles/ag-theme-quartz.css'; // Theme
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CsvExportModule } from '@ag-grid-community/csv-export';
import { SideBarModule } from '@ag-grid-enterprise/side-bar';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { ServerSideRowModelModule } from '@ag-grid-enterprise/server-side-row-model';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { filterComparator } from 'utils/table';
import type { AgGridReactProps } from '@ag-grid-community/react/dist/types/src/shared/interfaces';
import { CustomPagination } from '@hammr-ui/components/pagination.custom';
import Spinner from '@/hammr-ui/components/spinner';
import { renderToString } from 'react-dom/server';
import ArrowUpSFill from '@hammr-icons/ArrowUpSFill';
import ArrowDownSFill from '@hammr-icons/ArrowDownSFill';
import { cn } from '@/utils/cn';

LicenseManager.setLicenseKey(
  'Using_this_{AG_Grid}_Enterprise_key_{AG-058924}_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_changing_this_key_please_contact_info@ag-grid.com___{Hammr_Inc}_is_granted_a_{Single_Application}_Developer_License_for_the_application_{Hammr}_only_for_{1}_Front-End_JavaScript_developer___All_Front-End_JavaScript_developers_working_on_{Hammr}_need_to_be_licensed___{Hammr}_has_been_granted_a_Deployment_License_Add-on_for_{1}_Production_Environment___This_key_works_with_{AG_Grid}_Enterprise_versions_released_before_{22_April_2025}____[v3]_[01]_MTc0NTI3NjQwMDAwMA==1d26ca1761a332a52e0dd0c7d156fe09'
);

function mapRenderFunction<T>(columnValueMappings: ColDef<T>[]) {
  return columnValueMappings.map((column) => {
    return {
      ...column,
      cellRenderer: (params: CustomCellRendererProps) => {
        const computedRenderer = column.cellRenderer
          ? () => column.cellRenderer(params)
          : () => <span>{params.valueFormatted ? params.valueFormatted : params.value}</span>;

        return column.field === 'actions' ? (
          <div
            className="w-full"
            onClick={(event) => {
              event.preventDefault(); // hack to not trigger other RowClick handler on AgGrid
            }}
          >
            {computedRenderer()}
          </div>
        ) : (
          computedRenderer()
        );
      },
    };
  });
}

export interface PaginationOptions {
  total: number;
  page: number;
  pageSize: number;
  onPageChanged: (page: number) => void;
  onPageSizeChanged: (size: number) => void;
  pageSizeOptions?: number[];
}

export const UpdatedTable = <T,>({
  rowData,
  colDefs,
  defaultColDef,
  gridOptions,
  groupRenderer,
  parentRef,
  emptyRowsText,
  noRowsOverlayComponent,
  isLoading,
  onFilterChange,
  hideSidebar = true,
  showRowGroupPanel,
  getRowHeight,
  onGridReady,
  onRowGroupChange,
  onRowClicked,
  enablePagination = false,
  disableCustomRendering = false,
  autoGroupColumnDef,
  tableProps,
  pagination,
  onPaginationChanged,
  enableGroupCheckbox = false,
  parentContainerClassName,
  fitToWindow = false,
  stickyHeaders = true,
  hideChevron = false,
}: {
  rowData?: T[];
  colDefs: ColDef<T>[];
  defaultColDef?: ColDef<T>;
  onRowClicked?: (event: RowClickedEvent<T>) => void;
  gridOptions?: GridOptions<T>;
  groupRenderer?: (params: ICellRendererParams<T>) => JSX.Element;
  parentRef?: React.MutableRefObject<AgGridReact<T> | null>;
  emptyRowsText?: string;
  noRowsOverlayComponent?: () => JSX.Element;
  isLoading?: boolean;
  onFilterChange?: (params: Record<string, number[]>) => void;
  hideSidebar?: boolean;
  showRowGroupPanel?: boolean;
  getRowHeight?: (params: any) => any;
  onGridReady?: (params: GridReadyEvent<T>) => void;
  enablePagination?: boolean;
  pagination?: Partial<PaginationOptions>;
  disableCustomRendering?: boolean;
  onRowGroupChange?: (params: ColumnRowGroupChangedEvent<T>) => void;
  autoGroupColumnDef?: ColDef<T>;
  tableProps?: AgGridReactProps<T>;
  onPaginationChanged?: (event: PaginationChangedEvent) => void;
  enableGroupCheckbox?: boolean;
  parentContainerClassName?: string;
  fitToWindow?: boolean;
  stickyHeaders?: boolean;
  hideChevron?: boolean;
}) => {
  const [internalSortTimestamp, setInternalSortTimestamp] = useState<number>(0);
  // tracks the changes and keep the custom render functions up to date.
  const [internalVisibleColumnsTimestamp, setInternalVisibleColumnsTimestamp] = useState<number>(Date.now());
  const [internalColumnOrder, setInternalColumnOrder] = useState<string[]>([]);
  const [internalFiltersAppliedCount, setInternalFiltersAppliedCount] = useState<number>(0);
  const [internalFiltersTimestamp, setInternalFiltersTimestamp] = useState<number>(0);
  const [internalSidebarOpened, setInternalSidebarOpened] = useState<boolean>(false);

  ModuleRegistry.registerModules([
    ClientSideRowModelModule,
    RowGroupingModule,
    CsvExportModule,
    SideBarModule,
    ColumnsToolPanelModule,
    FiltersToolPanelModule,
    SetFilterModule,
    MenuModule,
    ServerSideRowModelModule,
  ]);

  const gridRef = parentRef ? parentRef : useRef<AgGridReact<T>>(null);

  // the way the columns are defined is the sorting order, but when there's a grouping or column moving this is done to persist it
  const colDefsSorted = useMemo(() => {
    if (internalColumnOrder.length === 0) return colDefs;

    // sort mutates the array so we need to clone it first
    return colDefs.slice().sort((a, b) => {
      const aIndex = internalColumnOrder.indexOf(a.field ?? '');
      const bIndex = internalColumnOrder.indexOf(b.field ?? '');

      if (aIndex === -1 && bIndex === -1) {
        return 0;
      }

      if (aIndex === -1) {
        return 1;
      }

      if (bIndex === -1) {
        return -1;
      }

      return aIndex - bIndex;
    });
  }, [colDefs, internalColumnOrder]);

  useEffect(() => {
    if (!gridRef.current?.api) {
      return;
    }
    // this is a hack because when we update the filter applied the sidebar gets re-rendered and hidden again
    if (internalSidebarOpened) {
      gridRef.current?.api.setSideBarVisible(true);
      gridRef.current?.api.openToolPanel('filters');
    }
  }, [internalFiltersAppliedCount]);

  // custom render function to add indentation to the first column
  const colDefsMapped = useMemo(() => {
    // ag-grid has some custom rendering i.e "agGroupCellRenderer" that won't work with our custom rendering functions
    if (disableCustomRendering) {
      return colDefsSorted;
    }

    return mapRenderFunction(colDefsSorted);
  }, [colDefsSorted, internalVisibleColumnsTimestamp, internalSortTimestamp, internalFiltersTimestamp]);

  const handleColumnMoved = (event: ColumnMovedEvent) => {
    if (event.finished) {
      const colIds = event.api.getColumnState().map((col) => col.colId);
      setInternalColumnOrder(colIds);
    }
  };

  const defaultColDefCalculated = useMemo<ColDef<T>>(() => {
    return {
      comparator: filterComparator,
      flex: 1,
      menuTabs: [],
      suppressHeaderMenuButton: true,
      minWidth: 120,
      headerClass: 'capitalize',
      suppressMovable: true,
      ...defaultColDef,
    };
  }, [defaultColDef]);

  const groupRowRendererParams = useMemo(() => {
    return {
      innerRenderer: groupRenderer,
      suppressCount: true,
      checkbox: enableGroupCheckbox,
    };
  }, []);

  const sideBar = useMemo<SideBarDef | string | string[] | boolean | null>(() => {
    if (hideSidebar) return null;
    const filtersLabel = internalFiltersAppliedCount ? `Filters (${internalFiltersAppliedCount})` : 'Filters';
    return {
      toolPanels: [
        {
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          iconKey: 'columns',
          toolPanel: 'agColumnsToolPanel',
          toolPanelParams: {
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnExpandAll: true,
          },
        },
        {
          id: 'filters',
          labelDefault: filtersLabel,
          labelKey: 'filters',
          iconKey: 'filter',
          toolPanel: 'agFiltersToolPanel',
          minWidth: 180,
          maxWidth: 400,
          width: 250,
        },
      ],
      defaultToolPanel: '',
    };
  }, [internalFiltersAppliedCount, hideSidebar]);

  const LoadingComponent = useCallback(() => {
    return <Spinner className="h-10 w-10 text-strong-950" />;
  }, []);

  const handleSetFilterCount = () => {
    const filters = gridRef.current?.api.getFilterModel();
    const filtersCount = Object.keys(filters ?? {}).length;
    const isSidebarOpened = gridRef.current?.api.isToolPanelShowing();
    setInternalSidebarOpened(Boolean(isSidebarOpened));

    setInternalFiltersAppliedCount(filtersCount);
  };
  const pageSizeOptions = pagination?.pageSizeOptions ?? [10, 25, 50, 100];

  const [paginationState, setPaginationState] = useState({
    currentPage: 1,
    totalPages: pagination?.total ? Math.ceil(pagination.total / pageSizeOptions[0]) : 1,
    pageSize: pageSizeOptions[0],
  });

  const handlePaginationChanged = useCallback(
    (event: PaginationChangedEvent) => {
      if (pagination?.total) {
        // this means we are using server side pagination
        return;
      }
      const { api } = event;
      const newPaginationState = {
        currentPage: api.paginationGetCurrentPage() + 1,
        totalPages: api.paginationGetTotalPages(),
        pageSize: api.paginationGetPageSize(),
      };
      setPaginationState(newPaginationState);
      onPaginationChanged?.(event);
    },
    [onPaginationChanged, pagination?.total]
  );

  const handlePageChange = (page: number) => {
    gridRef.current?.api.paginationGoToPage(page - 1);
    setPaginationState((prevState) => ({
      ...prevState,
      currentPage: page,
    }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPaginationState((prevState) => ({
      ...prevState,
      pageSize: newPageSize,
      currentPage: 1,
    }));
  };

  const finalGridOptions = {
    groupDisplayType: 'groupRows',
    groupDefaultExpanded: -1,
    suppressRowTransform: true,
    suppressCellFocus: true,
    draggableColumns: false,
    ...gridOptions,
  } as GridOptions<T>;

  return (
    <div
      className={cn(
        'ag-theme-quartz flex flex-grow flex-col',
        stickyHeaders && !fitToWindow && 'make-ag-grid-header-sticky',
        parentContainerClassName,
        hideChevron && 'ag-grid-hide-chevron'
      )} // applying the grid theme
    >
      <AgGridReact<T>
        className="ag-grid-hammr flex-grow"
        loadingOverlayComponent={() => <LoadingComponent />}
        ref={gridRef}
        headerHeight={36}
        domLayout={fitToWindow ? 'normal' : 'autoHeight'}
        defaultColDef={defaultColDefCalculated}
        rowData={rowData}
        loading={isLoading}
        columnDefs={colDefsMapped}
        onColumnMoved={handleColumnMoved}
        rowGroupPanelShow={showRowGroupPanel ? 'always' : 'never'}
        groupRowRendererParams={groupRowRendererParams}
        gridOptions={{
          ...finalGridOptions,
        }}
        localeText={{
          rowGroupColumnsEmptyMessage: 'To group by a column, drag it here',
          noRowsToShow: emptyRowsText || 'No rows to show',
        }}
        noRowsOverlayComponent={noRowsOverlayComponent}
        sideBar={sideBar}
        onColumnVisible={(e) => {
          setInternalVisibleColumnsTimestamp(Date.now());
        }}
        onSortChanged={() => {
          setInternalSortTimestamp(Date.now());
        }}
        onFilterChanged={(e) => {
          if (onFilterChange) {
            onFilterChange({});
          }
          handleSetFilterCount();
          setInternalFiltersTimestamp(Date.now());
        }}
        stopEditingWhenCellsLoseFocus={true}
        rowSelection="multiple"
        rowMultiSelectWithClick={true}
        suppressRowClickSelection={true}
        autoGroupColumnDef={autoGroupColumnDef}
        groupSelectsChildren={true}
        pagination={enablePagination}
        paginationPageSize={paginationState.pageSize}
        cacheBlockSize={paginationState.pageSize}
        onPaginationChanged={handlePaginationChanged}
        suppressPaginationPanel={true}
        onRowClicked={(row) => {
          if (row.event.defaultPrevented) {
            return null;
          }

          onRowClicked?.(row);
        }}
        rowHeight={52}
        rowClass={cn(onRowClicked ? 'cursor-pointer' : undefined)}
        icons={{
          sortAscending: renderToString(<ArrowUpSFill className="size-5" />),
          sortDescending: renderToString(<ArrowDownSFill className="size-5" />),
        }}
        suppressContextMenu
        {...(onGridReady && { onGridReady })}
        {...(getRowHeight && { getRowHeight })}
        {...(onRowGroupChange && { onColumnRowGroupChanged: onRowGroupChange })}
        {...tableProps}
      />
      {enablePagination || pagination?.total ? (
        <CustomPagination
          currentPage={pagination?.page ?? paginationState.currentPage}
          totalPages={
            pagination?.total ? Math.ceil(pagination.total / pagination.pageSize) : paginationState.totalPages
          }
          pageSize={pagination?.pageSize ?? paginationState.pageSize}
          pageSizeOptions={pagination?.pageSizeOptions ?? [10, 25, 50, 100]}
          onPageChange={pagination?.onPageChanged ?? handlePageChange}
          onPageSizeChange={
            pagination?.onPageSizeChanged
              ? (pageSize) => {
                  pagination?.onPageChanged(1);
                  pagination?.onPageSizeChanged(pageSize);
                }
              : handlePageSizeChange
          }
        />
      ) : undefined}
    </div>
  );
};
