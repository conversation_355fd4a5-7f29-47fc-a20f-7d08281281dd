import * as React from 'react';
import { debounce } from 'lodash';
import Autocomplete from '@hammr-ui/components/Autocomplete';
import MapPinLine from '@/hammr-icons/MapPinLine';

const autocompleteService = { current: null };

interface GoogleMapsPlacesAutocompleteProps {
  existingAddress?: string;
  callback?: (data) => void;
}

interface MainTextMatchedSubstrings {
  offset: number;
  length: number;
}
interface StructuredFormatting {
  main_text: string;
  secondary_text: string;
  main_text_matched_substrings?: readonly MainTextMatchedSubstrings[];
}
export interface PlaceType {
  description: string;
  structured_formatting: StructuredFormatting;
  place_id: string;
}

function GoogleMapsPlacesAutocomplete({ existingAddress, callback }: GoogleMapsPlacesAutocompleteProps) {
  const [value, setValue] = React.useState<PlaceType | null>(null);
  const [isAutocompleteInitialized, setIsAutocompleteInitialized] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');
  const [options, setOptions] = React.useState<readonly PlaceType[]>([]);

  const fetchDebounced = React.useMemo(
    () =>
      debounce((request: { input: string }, callback: (results?: readonly PlaceType[]) => void) => {
        (autocompleteService.current as any).getPlacePredictions(request, callback);
      }, 400),
    []
  );

  // handling default address or existing address
  React.useEffect(() => {
    // assumes script is loaded
    if (existingAddress) {
      // make sure autocompleteService is available
      if (!autocompleteService.current && (window as any).google) {
        autocompleteService.current = new (window as any).google.maps.places.AutocompleteService();
        setIsAutocompleteInitialized(true);
        return;
      }

      // craft request - the problem is address needs to be standardized
      const request = {
        input: existingAddress,
      };

      (autocompleteService.current as any)?.getPlacePredictions(request, (results?: readonly PlaceType[]) => {
        setValue(results[0]);
        if (callback) {
          callback(results[0]);
        }
      });
    }
  }, [callback, existingAddress, isAutocompleteInitialized]);

  React.useEffect(() => {
    let active = true;

    if (!autocompleteService.current && (window as any).google) {
      autocompleteService.current = new (window as any).google.maps.places.AutocompleteService();
      setIsAutocompleteInitialized(true);
    }

    if (!autocompleteService.current) {
      return undefined;
    }

    if (inputValue === '') {
      setOptions(value ? [value] : []);
      return undefined;
    }

    fetchDebounced({ input: inputValue }, (results?: readonly PlaceType[]) => {
      if (active) {
        let newOptions: readonly PlaceType[] = [];

        if (value) {
          newOptions = [value];
        }

        if (results) {
          newOptions = [...newOptions, ...results];
        }

        setOptions(newOptions);
      }
    });

    return () => {
      active = false;
    };
  }, [value, inputValue, fetchDebounced]);

  return (
    <>
      <Autocomplete
        items={options.map((option) => ({
          value: option.place_id,
          label: option.structured_formatting.main_text,
          subTitle: option.structured_formatting.secondary_text,
          avatar: {
            type: 'avatar',
            content: <MapPinLine />,
          },
        }))}
        withSubTitle
        searchText={inputValue}
        onSearchTextChange={(searchText) => {
          setInputValue(searchText);
        }}
        placeholder="Type an address and click on the map to adjust"
        onSelectItem={(item) => {
          callback({
            place_id: item.value,
          });
        }}
      />
    </>
  );
}

export default GoogleMapsPlacesAutocomplete;
