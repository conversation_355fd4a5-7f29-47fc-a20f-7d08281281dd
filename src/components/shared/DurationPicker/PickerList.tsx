import classNames from 'classnames';
import { useEffect, useMemo, useRef } from 'react';

interface PickerListProps {
  value: number;
  onChange?: (value: number) => void;
  min: number;
  max: number;
}

const PickerList: React.FC<PickerListProps> = ({
  value,
  min,
  max,
  onChange,
}) => {
  const container = useRef<HTMLDivElement>(null);
  const valueRef = useRef(value);
  const minRef = useRef(min);

  valueRef.current = value;
  minRef.current = min;

  useEffect(() => {
    container.current?.scrollTo({
      top: (valueRef.current - minRef.current) * 32,
    });
  }, []);

  const options = useMemo(() => {
    const options = [];

    for (let i = min; i <= max; i++) {
      options.push(i);
    }

    return options;
  }, [min, max]);

  return (
    <div ref={container} className="flex flex-col flex-1 overflow-y-auto">
      {options.map((option) => (
        <button
          key={option}
          type="button"
          onClick={() => onChange?.(option)}
          className={classNames(
            'p-1',
            option === value
              ? 'bg-hammr-orange-500 text-white'
              : 'hover:bg-gray-100'
          )}
        >
          {option}
        </button>
      ))}
    </div>
  );
};

export default PickerList;
