import Modal from 'components/elements/Modal';
import { useEffect } from 'react';
import { ComponentEvent } from 'interfaces/component';
import { logError } from 'utils/errorHandling';

const EditTaxSetup: React.FC<{
  componentLink: string;
  handleSuccess: any;
  handleCancel: any;
}> = ({ componentLink, handleSuccess, handleCancel }) => {
  useEffect(() => {
    try {
      const handler = window.CheckComponent.create({
        link: componentLink,
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        onClose: () => {},
        onEvent: (event: ComponentEvent) => {
          if (event === 'check-component-app-loaded') {
          } else if (event === 'check-component-company-tax-setup-complete') {
            handleSuccess();
          }
        },
      });
      handler.open();
      const embeddedComponentElement = document.getElementById('check-component-embedded-div');
      if (embeddedComponentElement) {
        const componentHolder = document.getElementById('component-holder');
        componentHolder.appendChild(embeddedComponentElement);
      }
    } catch (err) {
      logError(err);
      handleCancel();
    }
  }, []);

  return (
    <Modal>
      <div className="flex flex-col p-3" style={{ height: '80vh' }}>
        <div id="component-holder" className="flex-grow"></div>
        <div className="flex flex-shrink-0 flex-col">
          <div onClick={handleCancel} className="mx-auto mt-4 cursor-pointer text-sm text-black hover:underline">
            Close
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditTaxSetup;
