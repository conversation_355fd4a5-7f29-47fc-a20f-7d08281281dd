import { useAuth } from 'hooks/useAuth';

const Modal: React.FC<{ children: any; width?: string }> = ({ children, width }) => {
  const { user } = useAuth();
  if (!user) return null;

  return (
    <div
      className={`fixed inset-0 z-10 mx-auto overflow-y-auto ${width ? width : 'w-5/5 md:w-2/5'}`}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
          &#8203;
        </span>
        <div className="inline-block transform overflow-hidden rounded-2xl bg-white text-left align-bottom shadow transition-all sm:my-8 sm:w-full sm:max-w-5xl sm:align-middle">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
