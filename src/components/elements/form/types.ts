import { Control } from 'react-hook-form';
import { ReactNode } from 'react';

export type ControlledFormItemProps = {
  control: Control<any>;
  name: string;
  label?: ReactNode;
  capitalizeLabel?: boolean;
  tooltip?: ReactNode;
  error?: string | any;
  className?: string;
  rules?: Record<string, any>;
  onChange?: (value: any) => void;
  disabled?: boolean;
  placeholder?: string;
  required?: boolean;
};
