import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { Controller } from 'react-hook-form';
import { ControlledFormItemProps } from './types';
import { ComponentProps } from 'react';

export const TextField = ({
  name,
  label = '',
  required = false,
  rules = {},
  tooltip,
  placeholder = '',
  control,
  error,
  capitalizeLabel = true,
  autoTrimOnBlur = true,
  className,
  defaultValue,
  ...inputProps
}: ControlledFormItemProps & ComponentProps<typeof Input> & { autoTrimOnBlur?: boolean }) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      defaultValue={defaultValue}
      render={({ field, fieldState }) => {
        // Ensure the value is properly handled, including empty strings
        const value = field.value === undefined || field.value === null ? '' : field.value;

        return (
          <FormItem key={name} required={required} error={!!error || !!fieldState.error} className={className}>
            {label ? (
              <FormLabel tooltip={tooltip} capitalize={capitalizeLabel}>
                {label}
              </FormLabel>
            ) : undefined}
            <FormControl>
              <Input
                aria-invalid={fieldState.invalid}
                placeholder={placeholder}
                value={value}
                onChange={(e) => {
                  field.onChange(e.target.value);
                }}
                onBlur={() => {
                  if (autoTrimOnBlur && field.value) {
                    field.onChange(field.value.trim());
                  }
                  field.onBlur();
                }}
                name={field.name}
                ref={field.ref}
                {...inputProps}
              />
            </FormControl>
            <FormMessage>{error ?? fieldState.error?.message}</FormMessage>
          </FormItem>
        );
      }}
    />
  );
};
