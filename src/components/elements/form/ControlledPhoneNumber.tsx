import { Controller } from 'react-hook-form';
import { FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { ControlledFormItemProps } from './types';
import { PhoneNumberInput } from '@hammr-ui/components/phonenumberinput';

export function ControlledPhoneNumber({
  control,
  error = undefined,
  name,
  label,
  rules = {},
  onChange = undefined,
  disabled = false,
  children = null,
  className = '',
  required = false,
  placeholder = undefined,
}: Partial<ControlledFormItemProps> & { children?: React.ReactNode }) {
  return (
    <Controller
      name={name}
      control={control}
      rules={!disabled ? rules : {}}
      render={({ field }) => {
        return (
          <FormItem required={required} error={!!error}>
            <FormLabel className="mb-1">{label}</FormLabel>
            <PhoneNumberInput
              onChange={(value) => {
                field.onChange(value);
                onChange?.(value);
              }}
              error={error}
              className={className}
              placeholder={placeholder}
              value={field.value}
            >
              {children}
            </PhoneNumberInput>
            {error && <FormMessage className="mt-1">{error}</FormMessage>}
          </FormItem>
        );
      }}
    ></Controller>
  );
}
