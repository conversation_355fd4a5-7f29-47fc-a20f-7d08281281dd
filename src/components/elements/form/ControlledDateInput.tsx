import { DateInput } from '@/hammr-ui/components/date-input';
import { Control, Controller, FieldError } from 'react-hook-form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import { DayPickerProps } from 'react-day-picker';
import dayjs from 'dayjs';

interface Props {
  control: Control<any>;
  name: string;
  label?: string;
  rules?: Record<string, any>;
  required?: boolean;
  disabled?: boolean;
  dayPickerProps?: DayPickerProps;
  className?: string;
  tooltip?: React.ReactNode;
}

export default function ControlledDateInput({
  control,
  name,
  label,
  rules = {},
  required,
  disabled,
  dayPickerProps,
  className,
  tooltip,
}: Props) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, formState }) => {
        const error = getNestedError(formState.errors, name) as FieldError | undefined;

        return (
          <FormItem required={required} error={!!error} className={className}>
            {label && <FormLabel tooltip={tooltip}>{label}</FormLabel>}

            <FormControl>
              <DateInput
                value={field.value ? dayjs(field.value).toDate() : null}
                onChange={field.onChange}
                dayPickerProps={dayPickerProps}
                disabled={disabled}
              />
            </FormControl>

            {error?.message && <FormMessage className="mt-1">{error.message}</FormMessage>}
          </FormItem>
        );
      }}
    />
  );
}

function getNestedError(errors: any, path: string) {
  return path.split('.').reduce((acc, part) => acc && acc[part], errors);
}
