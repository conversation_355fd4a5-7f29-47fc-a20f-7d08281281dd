import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { Controller } from 'react-hook-form';
import { ControlledFormItemProps } from './types';
import { ReactNode } from 'react';

export const NumberFieldV2 = ({
  name,
  label = '',
  required = false,
  rules = {},
  placeholder = '',
  control,
  error,
  prefix,
  labelHelper,
  afterContent = '',
  isFloat = false,
  className,
}: ControlledFormItemProps & {
  prefix?: ReactNode;
  isFloat?: boolean;
  labelHelper?: ReactNode;
  afterContent?: string;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={rules}
      render={({ field }) => {
        return (
          <FormItem required={required} error={!!error} className={className}>
            <FormLabel labelHelper={labelHelper}>{label}</FormLabel>
            <FormControl>
              <Input
                step="any"
                placeholder={placeholder}
                type={isFloat ? 'float' : 'number'}
                min={0}
                beforeContent={prefix}
                afterContent={afterContent}
                {...field}
              />
            </FormControl>
            <FormMessage>{error}</FormMessage>
          </FormItem>
        );
      }}
    />
  );
};
