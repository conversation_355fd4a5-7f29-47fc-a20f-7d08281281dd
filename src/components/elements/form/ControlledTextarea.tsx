import { Controller } from 'react-hook-form';
import { Textarea, TextareaProps } from '@/hammr-ui/components/Textarea';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { ControlledFormItemProps } from './types';

export const ControlledTextarea = ({
  control,
  error,
  name,
  label,
  rules = {},
  onChange,
  disabled = false,
  className = '',
  required = false,
  placeholder,
  ...props
}: Partial<ControlledFormItemProps> & TextareaProps) => {
  return (
    <FormItem required={required} error={!!error} className={className}>
      {label && <FormLabel className="mb-1">{label}</FormLabel>}
      <Controller
        name={name}
        control={control}
        rules={!disabled ? rules : {}}
        render={({ field }) => (
          <FormControl>
            <Textarea
              placeholder={placeholder}
              {...field}
              {...props}
              onChange={(e) => {
                field.onChange(e);
                onChange?.(e.target.value);
              }}
              disabled={disabled}
            />
          </FormControl>
        )}
      />
      {error && <FormMessage className="mt-1">{error}</FormMessage>}
    </FormItem>
  );
};
