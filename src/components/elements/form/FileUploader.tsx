import UploadCloud2Line from '@/hammr-icons/UploadCloud2Line';
import { FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { FormControl } from '@/hammr-ui/components/form';
import { Controller, Control, FieldErrors, UseFormReturn } from 'react-hook-form';
import { FormItem } from '@/hammr-ui/components/form';
import { formatFileSize } from '@/utils/format';
import FileFormatIcon from '@/hammr-icons/FileFormatIcon';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import { Progress } from '@/hammr-ui/components/progress';
import { cn } from '@/hammr-ui/lib/utils';
import CheckboxCircleFill from '@/hammr-icons/CheckboxCircleFill';

export const ControlledFileUploader = ({
  name,
  error,
  label,
  form,
  appendFiles,
  disabled,
  inputProps,
}: {
  name: string;
  error: string;
  label: string;
  form: UseFormReturn<any>;
  appendFiles?: (files: File[]) => void;
  disabled?: boolean;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
}) => {
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);

    if (appendFiles) {
      // Use the custom append function if provided
      appendFiles(files);
    } else {
      // Get current files and append new ones
      const currentFiles = form.getValues(name) || [];
      form.setValue(name, [...currentFiles, ...files], { shouldValidate: true });
    }
  };

  const handleFileInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    if (appendFiles) {
      // Use the custom append function if provided
      appendFiles(files);
    } else {
      // Get current files and append new ones
      const currentFiles = form.getValues(name) || [];
      form.setValue(name, [...currentFiles, ...files], { shouldValidate: true });
    }

    event.target.value = null;
  };

  return (
    <FormItem error={!!error} disabled={disabled}>
      <FormLabel>{label}</FormLabel>
      <FormControl>
        <div
          className="cursor-pointer rounded-xl border border-dashed border-sub-300 bg-white-0 p-6 text-center"
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          onClick={() => document.getElementById('fileInput')?.click()}
        >
          <input
            disabled={disabled}
            id="fileInput"
            type="file"
            className="hidden"
            {...form.register(name)}
            onChange={handleFileInput}
            {...inputProps}
          />
          <div className="flex flex-col items-center justify-center">
            <UploadCloud2Line className="mb-4 h-8 w-8 text-sub-600" />
            <p className="mb-2 gap-1 text-base font-semibold text-strong-950">
              <span className="text-primary-base underline">Choose a file</span>
              or drag & drop it here
            </p>
            <p className="text-sm text-sub-600">Max size: 50 MB</p>
          </div>
        </div>
      </FormControl>
      <FormMessage>{error}</FormMessage>
    </FormItem>
  );
};

export const FileUploaderProgress = ({
  filesToUpload,
  uploadProgress,
  form,
  name,
  showSelectedStyle = false,
  disabled,
}: {
  filesToUpload: File[];
  uploadProgress: { [key: string]: number };
  form: UseFormReturn<any>;
  name: string;
  showSelectedStyle?: boolean;
  disabled?: boolean;
}) => {
  const removeFile = (index: number) => {
    const updatedFiles = [...form.getValues(name)];
    updatedFiles.splice(index, 1);
    form.setValue(name, updatedFiles);
  };

  if (filesToUpload.length === 0) return null;

  return (
    <ul className="space-y-2">
      {filesToUpload.map((file: File, index: number) => (
        <li
          key={index}
          className={cn(
            'flex flex-col rounded-xl border border-soft-200 bg-white-0 p-4',
            showSelectedStyle && 'border-primary-base'
          )}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {file.type.startsWith('image/') ? (
                <img src={URL.createObjectURL(file)} alt={file.name} className="h-10 w-10 rounded object-cover" />
              ) : (
                <FileFormatIcon className="h-10 w-10 text-sub-600" type={file.name.split('.').at(-1)} />
              )}

              <div className="flex flex-col gap-1">
                <span className="text-sm font-medium text-strong-950">{file.name}</span>
                <span className="text-xs text-sub-600">{formatFileSize(file.size)}</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {showSelectedStyle ? <CheckboxCircleFill className="text-primary-base" /> : null}
              <button
                type="button"
                onClick={() => removeFile(index)}
                className={cn('text-sub-600 transition-colors hover:text-error-base', disabled && 'cursor-not-allowed')}
                disabled={disabled}
              >
                <DeleteBinLine className="h-5 w-5" />
              </button>
            </div>
          </div>
          {uploadProgress[file.name] !== undefined && (
            <div className="mt-2">
              <Progress value={uploadProgress[file.name]} className="w-full" />
            </div>
          )}
        </li>
      ))}
    </ul>
  );
};

const FileUploader = ({
  handleDrop,
  handleFileInput,
}: {
  handleDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  handleFileInput: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) => {
  return (
    <div
      className="cursor-pointer rounded-xl border border-dashed border-sub-300 bg-white-0 p-6 text-center"
      onDrop={handleDrop}
      onDragOver={(e) => e.preventDefault()}
      onClick={() => document.getElementById('fileInput')?.click()}
    >
      <input id="fileInput" type="file" className="hidden" onChange={handleFileInput} />
      <div className="flex flex-col items-center justify-center">
        <UploadCloud2Line className="mb-4 h-8 w-8 text-sub-600" />
        <p className="mb-2 gap-1 text-base font-semibold text-strong-950">
          <span className="text-primary-base underline">Choose a file</span>
          or drag & drop it here
        </p>
        <p className="text-sm text-sub-600">Max size: 50 MB</p>
      </div>
    </div>
  );
};

export default FileUploader;
