import { Controller } from 'react-hook-form';

import { Select } from '@/hammr-ui/components/select';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { ControlledFormItemProps } from './types';

const ControlledSelect = ({
  control,
  error = undefined,
  name,
  label,
  tooltip,
  rules = {},
  onChange = undefined,
  disabled = false,
  children = null,
  className = '',
  required = false,
  placeholder = undefined,
}: ControlledFormItemProps & { children?: React.ReactNode }) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={!disabled ? rules : {}}
      render={({ field, formState }) => {
        return (
          <FormItem required={required} error={!!error || !!formState.errors[name]} className={className}>
            <FormLabel className="mb-1" tooltip={tooltip}>
              {label}
            </FormLabel>
            <FormControl>
              <Select
                disabled={disabled}
                onChange={(value) => {
                  field.onChange(value);
                  onChange?.(value);
                }}
                error={error}
                className="w-full"
                placeholder={placeholder}
                value={field.value}
              >
                {children}
              </Select>
            </FormControl>
            {(error || formState.errors[name]) && (
              <FormMessage className="mt-1">{error ?? formState.errors[name]?.message}</FormMessage>
            )}
          </FormItem>
        );
      }}
    ></Controller>
  );
};

export default ControlledSelect;
