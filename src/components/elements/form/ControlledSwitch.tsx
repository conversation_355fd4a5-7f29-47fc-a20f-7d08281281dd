import { FormItem, FormLabel, FormControl } from '@/hammr-ui/components/form';
import { Switch } from '@/hammr-ui/components/Switch';
import { cn } from '@/utils/cn';
import { Control, Controller } from 'react-hook-form';

interface Props {
  control: Control<any>;
  name: string;
  label?: string;
  className?: string;
  rules?: Record<string, any>;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
}

function ControlledSwitch({ control, name, label = '', className = '', rules, onChange, disabled = false }: Props) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      defaultValue={false}
      render={({ field }) => {
        return (
          <FormItem className={cn('flex-row items-center justify-between', className)}>
            <FormLabel aria-disabled={disabled}>{label}</FormLabel>
            <FormControl>
              <Switch
                checked={field.value}
                name={field.name}
                onCheckedChange={(checked) => {
                  field.onChange(checked);
                  onChange?.(checked);
                }}
                disabled={disabled}
              />
            </FormControl>
          </FormItem>
        );
      }}
    />
  );
}

export default ControlledSwitch;
