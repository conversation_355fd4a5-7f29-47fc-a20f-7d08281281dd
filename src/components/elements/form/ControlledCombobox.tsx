import { FormItem, FormLabel, FormControl, FormMessage } from '@/hammr-ui/components/form';
import { Controller } from 'react-hook-form';
import { Combobox, ComboboxProps } from '@/hammr-ui/components/combobox';

interface ControlledComboboxProps extends Partial<ComboboxProps> {
  control: any;
  error?: string;
  label: string;
  name: string;
  rules?: object;
  placeholder?: string;
  className?: string;
  required?: boolean;
  disabled?: boolean;
  itemClassName?: string;
  enableTextAsFirstOption?: boolean;
}

const ControlledCombobox = ({
  control,
  error,
  label,
  name,
  items,
  rules,
  placeholder = 'Select an option',
  className = '',
  required = false,
  disabled = false,
  itemClassName = '',
  enableTextAsFirstOption = false,
  ...rest
}: ControlledComboboxProps) => {
  return (
    <Controller
      rules={rules}
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem required={required} error={!!fieldState.error || Boolean(error)}>
          <FormLabel className="mb-1">{label}</FormLabel>
          <FormControl>
            <Combobox
              aria-invalid={Boolean(error)}
              placeholder={placeholder}
              className={className}
              disabled={disabled}
              error={fieldState.error || error}
              items={items}
              onChange={(value) => {
                field.onChange(value);
              }}
              value={field.value}
              itemClassName={itemClassName}
              enableTextAsFirstOption={enableTextAsFirstOption}
              {...rest}
            />
          </FormControl>
          <FormMessage>{fieldState.error?.message ?? error}</FormMessage>
        </FormItem>
      )}
    />
  );
};

export default ControlledCombobox;
