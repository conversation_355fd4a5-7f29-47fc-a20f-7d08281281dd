import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { cn } from '@/utils/cn';
import { ReactNode } from 'react';

interface Props {
  children: ReactNode;
  open: boolean;
  setOpen: (open: boolean) => void;
  title: ReactNode;
  icon?: ReactNode;
  className?: string;
  shouldCloseOnInteractOutside?: boolean;
}

export function ModalV2({
  children,
  open,
  setOpen,
  title,
  icon,
  className,
  shouldCloseOnInteractOutside = true,
}: Props) {
  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogSurface onInteractOutside={shouldCloseOnInteractOutside ? undefined : (e) => e.preventDefault()}>
        <DialogHeader icon={<div className="text-sub-600">{icon} </div>} title={title} showCloseButton />
        <div className={cn('overflow-y-auto', className)}>{children}</div>
      </DialogSurface>
    </Dialog>
  );
}
