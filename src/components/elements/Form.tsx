import { DialogB<PERSON>, DialogFooter } from '@hammr-ui/components/dialog';
import Button from '@hammr-ui/components/button';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import { ComponentProps, ReactNode } from 'react';
import { cn } from '@/utils/cn';

type FormProps = {
  children: ReactNode;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  submitProps?: ComponentProps<typeof Button>;
  onCancel?: () => void;
  isLoading?: boolean;
  submitText?: string;
  cancelText?: string;
  isDisabled?: boolean;
  showFooter?: boolean;
  className?: string;
};

export const FormV2 = ({
  children,
  onSubmit,
  onCancel,
  isLoading,
  submitText,
  isDisabled,
  cancelText,
  showFooter = true,
  submitProps,
  className,
}: FormProps) => {
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit(e);
      }}
      className="flex flex-1 flex-col overflow-hidden"
    >
      <ScrollArea>
        <DialogBody>
          <div className="flex items-center justify-between space-x-4">
            <div className={cn('w-full', className)}>{children}</div>
          </div>
        </DialogBody>
      </ScrollArea>

      {showFooter ? (
        <DialogFooter>
          {onCancel && (
            <Button type="button" fullWidth variant="outline" color="neutral" onClick={onCancel}>
              {cancelText ?? 'Cancel'}
            </Button>
          )}
          <Button type="submit" loading={isLoading} fullWidth disabled={isLoading || isDisabled} {...submitProps}>
            {submitText ? submitText : 'Submit'}
          </Button>
        </DialogFooter>
      ) : undefined}
    </form>
  );
};
