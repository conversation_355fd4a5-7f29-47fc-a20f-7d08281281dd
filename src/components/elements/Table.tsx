import Button from 'components/elements/Button';
import { formatMinutesToHoursWorked, formatTotalWages } from 'utils/format';
import { RiArrowLeftSLine, RiArrowRightSLine } from '@remixicon/react';

interface Props {
  elements: any[][];
  columns: string[];
  nextUrl?: string;
  previousUrl?: string;
  handlePagination?: (arg: any) => void;
  relativeHeight?: boolean;
  search?: boolean;
  className?: string;
}

const Table: React.FC<Props> = ({
  elements,
  columns,
  nextUrl,
  previousUrl,
  handlePagination,
  relativeHeight,
  search,
  className,
}) => {
  return (
    <div className={className}>
      <div>
        {search && (
          <div className="mb-1 flex w-full flex-row justify-between sm:mb-0">
            <div className="ml-auto text-end">
              <form className="flex w-full max-w-sm space-x-3">
                <div className=" relative ">
                  <input
                    type="text"
                    id='"form-subscribe-Filter'
                    className="input-padding focus:shadow-outline-blue block w-full appearance-none rounded-md border border-gray-300 bg-gray-50 placeholder-gray-400 shadow-sm transition duration-150 ease-in-out focus:border-blue-300 focus:outline-none sm:text-sm sm:leading-5"
                    placeholder="Search..."
                  />
                </div>
                <div className="r-6">
                  <Button
                    right={true}
                    title="Search"
                    onClick={() => {
                      // To-do
                    }}
                  />
                </div>
              </form>
            </div>
          </div>
        )}
        <div className={`-mx-4 px-4 pb-4 sm:-mx-8 sm:px-8 ${search && 'pt-4'} overflow-x-auto`}>
          <div className={`min-w-full overflow-y-auto ${relativeHeight && 'max-h-65vh'}`}>
            <table className="w-full leading-normal">
              <thead>
                <tr>
                  {columns.map((column, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="top-0 z-10 whitespace-nowrap bg-weak-50 px-3 py-2 text-left text-sm font-normal capitalize text-sub-600 first:rounded-l-lg last:w-[5.5rem] last:rounded-r-lg"
                    >
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className='before:block before:h-2 before:content-[""]'>
                {elements?.map((element, index) => (
                  <tr key={index} className="group">
                    {element.map((item, key) => {
                      if (item?.key?.startsWith('header-row')) {
                        return (
                          <td
                            key={key}
                            className="capitalize-first border-b border-gray-200 bg-royal-gray-50 px-3 py-5 text-sm font-medium text-royal-gray-900"
                            colSpan={columns.length}
                          >
                            <span className="mr-4">{`${item?.props['data-Data']}`}</span>
                            <span className="font-normal lowercase">{`${
                              formatMinutesToHoursWorked(item?.props['data-Data-Minutes']) || ''
                            } | ${formatTotalWages(item?.props['data-Data-Wages'])}`}</span>
                          </td>
                        );
                      } else {
                        return (
                          <td
                            key={key}
                            className="h-[52px] border-b border-soft-200 px-3 text-sm first:rounded-l-xl first:font-medium last:rounded-r-xl group-hover:bg-weak-50"
                          >
                            <div className="whitespace-no-wrap flex w-full items-center text-strong-950">{item}</div>
                          </td>
                        );
                      }
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {(previousUrl || nextUrl) && (
            <div className="min-w-full rounded-b-xl">
              <div className="xs:flex-row xs:justify-between flex flex-col items-end px-6 py-4">
                <div className="flex items-center text-sub-600">
                  <button
                    type="button"
                    onClick={handlePagination ? () => handlePagination(previousUrl) : undefined}
                    className="rounded-l-xl border border-soft-200 p-2 hover:bg-weak-50 disabled:cursor-not-allowed disabled:text-disabled-300"
                    disabled={!previousUrl}
                  >
                    <RiArrowLeftSLine />
                  </button>
                  <button
                    type="button"
                    onClick={handlePagination ? () => handlePagination(nextUrl) : undefined}
                    className="rounded-r-xl border-b border-r border-t border-soft-200 p-2 hover:bg-weak-50 disabled:cursor-not-allowed disabled:text-disabled-300"
                    disabled={!nextUrl}
                  >
                    <RiArrowRightSLine />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Table;
