import { Rectangle, TooltipProps } from 'recharts';
import { formatUSD, formatUSDWithoutDecimals } from '@/utils/format';
import { ChartContainer } from '@/hammr-ui/components/chart';
import { Bar<PERSON>hart as RechartsBar<PERSON><PERSON>, <PERSON>, XAxis, YAxis, Tooltip } from 'recharts';

const chartConfig = {
  amount: {
    label: '',
    color: 'hsl(var(--chart-5))',
  },
};
const CustomCursor = (props) => {
  const { x, y, width, height, stroke } = props;
  return (
    <Rectangle
      className="cursor-pointer overflow-hidden"
      fill="rgb(var(--raw-weak-100))"
      stroke="transparent"
      x={x}
      y={y}
      width={width}
      height={height}
    />
  );
};

const RoundedBar = (props) => {
  const { fill, x, y, width, height, value } = props;

  const radius = 4;

  if (value === 0) {
    return null;
  }

  return (
    <path
      d={`
        M${x},${y + height}
        L${x},${y + radius}
        Q${x},${y} ${x + radius},${y}
        L${x + width - radius},${y}
        Q${x + width},${y} ${x + width},${y + radius}
        L${x + width},${y + height}
        Z
      `}
      fill={fill}
      style={{ cursor: 'pointer' }}
    />
  );
};

const CustomTooltip: React.FC<TooltipProps<number, string>> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-4 bg-strong-950 px-2 py-1">
        <div className="text-xs text-disabled-300">{label}</div>
        <div className="mt-1 text-xs font-medium text-white-0">{formatUSD.format(payload[0].value)}</div>
      </div>
    );
  }

  return null;
};

export const BarChart = ({ mappedChartData }: { mappedChartData: any }) => {
  return (
    <ChartContainer config={chartConfig} className="aspect-auto h-[250px] w-full">
      <RechartsBarChart accessibilityLayer data={mappedChartData} margin={{ left: 32, right: 24 }}>
        <YAxis
          axisLine={false}
          tickLine={false}
          padding={{ top: 12, bottom: 12 }}
          tickFormatter={(value) => (value > 0 ? formatUSDWithoutDecimals.format(value) : value)}
        />
        <XAxis
          dataKey="period"
          tickLine={false}
          axisLine={false}
          hide={true}
          tickFormatter={(value) => {
            return value;
          }}
        />

        <Tooltip cursor={<CustomCursor />} content={<CustomTooltip />} />

        <Bar
          maxBarSize={32}
          dataKey="amount"
          fill={`var(--color-amount)`}
          shape={<RoundedBar />}
          onMouseEnter={(data, index, e) => {
            // @ts-expect-error TODO: fix this
            e.target.style.fill = '#FF5733';
          }}
          onMouseLeave={(data, index, e) => {
            // @ts-expect-error TODO: fix this
            e.target.style.fill = 'var(--color-amount)';
          }}
        />
      </RechartsBarChart>
    </ChartContainer>
  );
};
