import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';

import { useAuth } from 'hooks/useAuth';
import { Button } from '@hammr-ui/components/button';
import { TextField } from 'components/elements/form/TextField';
import { logError } from 'utils/errorHandling';
import { addToast } from '@/hooks/useToast';
import { User } from '@/interfaces/user';

interface VerifyOtpData {
  code: string;
}

const VerifyOtpForm: React.FC = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<VerifyOtpData>({
    defaultValues: {
      code: '',
    },
  });
  const { push } = useRouter();
  const { signInMobile, signOut } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // potentially modify or extend this to make a subsequent call to organization data - need to know if they're subscribed to payroll or not / etc - then can decide if push to dashboard or not
  const onSubmit = async (data: VerifyOtpData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response: User & { error?: { message: string } } = await signInMobile(data);

      if (response.error) {
        setError(response.error);
        setIsLoading(false);
        return;
      }

      const isPayrollEnabled = !!response.checkCompanyId;
      if (response.role === 'WORKER' && !isPayrollEnabled) {
        signOut();
        addToast({
          type: 'error',
          title: 'Access Denied',
          description:
            'Hammr’s web version is currently available only for admins and foremans. To continue using Hammr, please download our mobile app.',
        });
        push('/login');
        return;
      }

      if (response.role === 'ADMIN') {
        push('/dashboard');
      } else if (response.role === 'FOREMAN') {
        push('/timesheets');
      } else if (response.role === 'WORKER') {
        if (response.workerClassification === 'EMPLOYEE') {
          push('/paystubs');
        } else {
          push('/payments');
        }
      }
    } catch (err) {
      logError(err);
      setError(err);
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {error?.message && (
        <div className="rounded border border-dashed border-error-base bg-error-base/10 p-2 text-center text-error-base">
          <span>{error.message}</span>
        </div>
      )}

      <TextField
        control={control}
        name="code"
        label="One-time Password"
        placeholder="Enter 6-digit code"
        required
        error={errors.code?.message}
        rules={{
          required: 'Please enter your one-time password',
          pattern: {
            value: /^[0-9]{1,6}$/,
            message: 'Not a valid one-time password',
          },
        }}
        autoFocus
      />

      <Button
        type="submit"
        loading={isLoading}
        loadingText="Verifying..."
        fullWidth
        variant="default"
        disabled={isLoading}
      >
        Verify OTP
      </Button>
    </form>
  );
};

export default VerifyOtpForm;
