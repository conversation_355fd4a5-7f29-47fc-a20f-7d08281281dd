import { useCallback } from 'react';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import { UpdatedTable } from 'components/shared/UpdatedTable';

import { HammrUser } from 'interfaces/user';
import { useEffect, useMemo, useRef } from 'react';
import { Badge } from '@hammr-ui/components/badge';
import { capitalize } from '@/utils/stringHelper';
import { HammrEarningRatePeriod } from '@/interfaces/earning-rate';
import { formatSalary } from '@/utils/calculateEarnings';
import CompactButton from '@hammr-ui/components/CompactButton';
import { Tooltip } from '@hammr-ui/components/tooltip';
import { useRouter } from 'next/router';
import ArchiveLine from '@hammr-icons/ArchiveLine';
import ArrowGoBackLine from '@/hammr-icons/ArrowGoBackLine';
import { Profile } from './sections/ProfileImage';
import { Employee } from '@/interfaces/employee';
import { Contractor } from '@/interfaces/contractor';
import { useQuery } from '@tanstack/react-query';
import { getBulkEmployees } from '@/services/employee';
import { getBulkContractors } from '@/services/contractor';
import { useAuth } from '@/hooks/useAuth';
import { useCompany } from '@/hooks/useCompany';
import OnboardStatus from './OnboardStatus';
import { getActiveEarningRate } from '@/utils/temporalUtils';

interface PeopleProps {
  active?: boolean;
  users: HammrUser[];
  archiveActionCallback: (user: HammrUser) => void;
}

export default function People({ active, users, archiveActionCallback }: PeopleProps) {
  const { user } = useAuth();
  const { company } = useCompany();
  const router = useRouter();
  type TableItem = ReturnType<typeof mapUsersForTable>[number];

  const employeesQuery = useQuery({
    queryKey: ['employees', user?.checkCompanyId, active],
    async queryFn() {
      const res = await getBulkEmployees(`company=${user?.checkCompanyId}&active=${active}&limit=100`);
      return res.results as Employee[];
    },
    enabled: !!company?.isPayrollEnabled && !!user?.checkCompanyId,
  });

  const contractorsQuery = useQuery({
    queryKey: ['contractors', user?.checkCompanyId, active],
    async queryFn() {
      const res = await getBulkContractors(`company=${user?.checkCompanyId}&active=${active}&limit=100`);
      return res.results as Contractor[];
    },
    enabled: !!company?.isPayrollEnabled && !!user?.checkCompanyId,
  });

  const colDefs: ColDef<TableItem>[] = [
    {
      headerName: 'Name',
      field: 'name',
      sortable: true,
      initialSort: 'asc',

      cellRenderer(params: ValueFormatterParams) {
        return (
          <span className="flex items-center gap-3 font-medium">
            <Profile user={params.data} />
            {params.data.firstName} {params.data.lastName}
          </span>
        );
      },
    },
    {
      headerName: 'Type',
      field: 'workerClassification',
      sortable: true,
      maxWidth: 150,

      cellRenderer: (params: ValueFormatterParams) => (
        <div>
          <Badge color="gray" variant="lighter">
            {capitalize(params.value.toLowerCase())}
          </Badge>
        </div>
      ),
    },
    {
      headerName: 'Role',
      field: 'role',
      sortable: true,
      maxWidth: 120,
      cellRenderer: (params: ValueFormatterParams) => capitalize(String(params.value).toLowerCase()),
    },
    {
      headerName: 'Position',
      field: 'position',
      sortable: true,
      maxWidth: 160,
    },
    {
      headerName: 'Salary',
      field: 'pay',
      sortable: true,
      maxWidth: 160,
      cellRenderer: (params: ValueFormatterParams<TableItem>) => {
        if (params.value === 0) {
          return '-';
        }
        return formatSalary(params.value, params.data?.payPeriod);
      },
    },
    {
      initialHide: !company?.isPayrollEnabled,
      headerName: 'Onboarding Status',
      minWidth: 160,
      cellRenderer: (params: ValueFormatterParams<TableItem>) => {
        function SkeletonLoader() {
          return <div className="h-6 w-24 animate-pulse rounded-6 bg-soft-200/50"></div>;
        }
        if (params.data?.workerClassification === 'EMPLOYEE') {
          if (employeesQuery.isPending) {
            return <SkeletonLoader />;
          }
          const employee = employeesQuery.data?.find((employee) => employee.id === params.data?.checkEmployeeId);
          if (!employee) return;
          return <OnboardStatus user={employee} />;
        } else if (params.data?.workerClassification === 'CONTRACTOR') {
          if (contractorsQuery.isPending) {
            return <SkeletonLoader />;
          }
          const contractor = contractorsQuery.data?.find(
            (contractor) => contractor.id === params.data?.checkContractorId
          );
          if (!contractor) return;
          return <OnboardStatus user={contractor} />;
        }
      },
    },
    {
      headerName: '',
      field: 'actions',
      maxWidth: 120,
      sortable: false,
      cellStyle: { overflow: 'visible' },
      cellRenderer: (params: ValueFormatterParams) => {
        return peopleActions(params.data);
      },
    },
  ].filter((colDef) => colDef) as ColDef[];

  const defaultColDef = {
    sortable: false,
    menuTabs: [],
    onCellClicked: (params) => {
      // we can't edit a grouping
      if (params.node.group) return;
      // in order to avoid interfering with the actions header
      if (params.colDef.field === 'actions') return;

      handleEditClick(params.data);
    },
    cellClass: (params) => (params.colDef.field === 'actions' ? '' : 'cursor-pointer'),
  };

  const gridRef = useRef<AgGridReact>(null);

  const handleEditClick = (user: HammrUser) => {
    if (user.checkContractorId) {
      return router.push(`/people/contractor/${user.id}`);
    }

    router.push(`/people/employee/${user.id}`);
  };

  const peopleActions = (user: HammrUser) => {
    return (
      <div
        className="flex cursor-pointer justify-center text-hammr-orange-600 hover:underline"
        onClick={async () => {
          // show confirm archive/unarchive modal
          // this is used for both archive and unarchive
          archiveActionCallback(user);
        }}
      >
        {active ? (
          <Tooltip content="Archive">
            <CompactButton size="large">
              <ArchiveLine />
            </CompactButton>
          </Tooltip>
        ) : (
          <Tooltip content="Unarchive">
            <CompactButton size="large">
              <ArrowGoBackLine />
            </CompactButton>
          </Tooltip>
        )}
      </div>
    );
  };

  const mapUsersForTable = useCallback(
    (users: HammrUser[]) => {
      if (!users) return [];

      return users.map((user) => {
        // Get the active earning rate using timezone-aware logic
        const activeEarningRate = getActiveEarningRate(
          user.earningRates || [],
          company?.timezone || 'America/Los_Angeles',
          'REG'
        );

        // Fallback to the old logic if no active rate found
        const earningRate =
          activeEarningRate ||
          (user.earningRates && user.earningRates?.length > 0
            ? user.earningRates.reverse().find((er) => er.type === 'REG')
            : {
                amount: user.hourlyRate?.toString() ?? '0',
                period: 'HOURLY' as HammrEarningRatePeriod,
              });

        return {
          ...user,
          name: user.firstName + ' ' + user.lastName,
          pay: parseFloat(earningRate?.amount ?? '0'),
          payPeriod: earningRate?.period ?? 'HOURLY',
        };
      });
    },
    [company?.timezone]
  );

  const mappedRowData = useMemo(() => mapUsersForTable(users), [mapUsersForTable, users]);

  useEffect(() => {
    gridRef.current?.api?.setFilterModel(null);
  }, [active]);

  if (!Boolean(users)) {
    return (
      <div className="mt-5 pb-8">
        <div className="px-2 pt-8 text-sm text-gray-500">No {active ? 'active' : 'archived'} employees.</div>
      </div>
    );
  }

  return (
    <div className="mt-4 flex flex-grow">
      <UpdatedTable
        colDefs={colDefs}
        rowData={mappedRowData}
        hideSidebar={true}
        defaultColDef={defaultColDef}
        parentRef={gridRef}
        emptyRowsText={active ? 'No active employees.' : 'No archived employees.'}
      />
    </div>
  );
}
