import { <PERSON>u, <PERSON>u<PERSON>ontent, <PERSON>uI<PERSON>, MenuTrigger } from '@/hammr-ui/components/Menu';
import Button from '@/hammr-ui/components/button';
import Spinner from '@/hammr-ui/components/spinner';
import { addToast } from '@/hooks/useToast';
import { generateContractorOnboardLink } from '@/services/contractor';
import { generateEmployeeOnboardLink } from '@/services/employee';
import { apiRequest } from '@/utils/requestHelpers';
import { RiArrowDownSLine, RiLinkM, RiMessage2Line, RiSmartphoneLine } from '@remixicon/react';
import { useMutation } from '@tanstack/react-query';
import { useState } from 'react';

interface Props {
  /** check employee or contractor id */
  checkId: string;
  userId: number;
}

export default function OnboardingLink({ checkId, userId }: Props) {
  const isEmployee = checkId.startsWith('emp_');
  const [onboardButtonTitle, setOnboardButtonTitle] = useState('Copy Onboarding Link');

  const copyOnboardLink = useMutation({
    async mutationFn() {
      return isEmployee ? generateEmployeeOnboardLink(checkId) : generateContractorOnboardLink(checkId);
    },
    onSuccess(res) {
      navigator.clipboard.writeText(res.url);
      setOnboardButtonTitle('Copied to Clipboard');
      setTimeout(() => {
        setOnboardButtonTitle('Copy Onboarding Link');
      }, 5000);
    },
  });

  const textOnboardLink = useMutation({
    async mutationFn() {
      const res = isEmployee
        ? await generateEmployeeOnboardLink(checkId)
        : await generateContractorOnboardLink(checkId);
      return apiRequest(`/users/${userId}/send-onboarding-link`, {
        method: 'POST',
        body: {
          onboardingLink: res.url,
        },
      });
    },
    onSuccess() {
      addToast({
        title: 'Text Sent',
        description: `The onboarding link has been sent to the ${isEmployee ? 'employee' : 'contractor'}.`,
        type: 'success',
      });
    },
  });

  const textMobileAppSetupInstruction = useMutation({
    async mutationFn() {
      return apiRequest(`/users/${userId}/resend-invite`, {
        method: 'POST',
      });
    },
    onSuccess() {
      addToast({
        title: 'Mobile App Setup Instructions Sent',
        description: `The mobile app setup instructions have been sent to the ${isEmployee ? 'employee' : 'contractor'}.`,
        type: 'success',
      });
    },
  });

  return (
    <Menu>
      <MenuTrigger asChild>
        <Button variant="outline" afterContent={<RiArrowDownSLine />}>
          Onboarding Link
        </Button>
      </MenuTrigger>
      <MenuContent className={'w-auto'} align="end">
        <MenuItem
          title={onboardButtonTitle}
          beforeContent={copyOnboardLink.isPending ? <Spinner /> : <RiLinkM className="size-5" />}
          onClick={() => {
            copyOnboardLink.mutate();
          }}
        />
        <MenuItem
          title="Text Onboarding Link"
          beforeContent={textOnboardLink.isPending ? <Spinner /> : <RiMessage2Line className="size-5" />}
          onClick={() => {
            textOnboardLink.mutate();
          }}
        />
        <MenuItem
          title="Text Mobile App Setup Instructions"
          beforeContent={
            textMobileAppSetupInstruction.isPending ? <Spinner /> : <RiSmartphoneLine className="size-5" />
          }
          onClick={() => {
            textMobileAppSetupInstruction.mutate();
          }}
        />
      </MenuContent>
    </Menu>
  );
}
