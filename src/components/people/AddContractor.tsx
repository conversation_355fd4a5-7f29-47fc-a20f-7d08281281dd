import * as React from 'react';
import { useForm } from 'react-hook-form';
import { createHammrUser } from 'services/user';
import { CreateHammrUser, WorkerClassification } from 'interfaces/user';
import * as yup from 'yup';
import { yupResolver } from 'utils/yupResolver';
import { FormV2 } from '@/components/elements/Form';
import { NumberFieldV2 } from '@/components/elements/form/NumberFieldV2';
import Alert from '@/hammr-ui/components/Alert';
import {
  GeneralInfoForm,
  generalInfoFormSchema,
  GeneralInfoValues,
} from '@/components/people/sections/GeneralInfoForm';
import { UserPhotoForm, userPhotoFormSchema, UserPhotoFormValuesValues } from './sections/UserPhotoForm';
import { useMutation } from '@tanstack/react-query';
import { addToast } from '@/hooks/useToast';

interface FormData extends Omit<GeneralInfoValues, 'dob' | 'workersCompCodeId'>, UserPhotoFormValuesValues {
  pay: string;
}

const formSchema = yup
  .object<FormData>()
  .shape({
    pay: yup.string().required('Please set an hourly rate for your contractor.'),
  })
  .concat(generalInfoFormSchema.omit(['dob', 'workersCompCodeId']))
  .concat(userPhotoFormSchema);

export default function AddContractor({
  handleCancel,
  handleSuccess,
}: {
  handleCancel: () => void;
  handleSuccess: () => void;
  checkCompanyId: string;
}) {
  const form = useForm<FormData>({
    defaultValues: {
      firstName: '',
      lastName: '',
      phone: '',
      sendOnboardingLink: true,
      address: '',
      city: '',
      state: '',
      postalCode: '',
      pay: '',
      profilePhotoObjectId: null,
      email: '',
      ethnicity: null,
      gender: null,
      veteranStatus: null,
    },
    // eslint-disable-next-line
    // @ts-ignore TS is standing in our way here. Might be due to TS non-strict mode
    resolver: yupResolver(formSchema),
  });

  const {
    formState: { errors },
  } = form;

  const contractorMutation = useMutation({
    mutationFn(data: FormData) {
      const newContractor: CreateHammrUser = {
        lastName: data.lastName,
        firstName: data.firstName,
        phoneNumber: data.phone,
        sendOnboardingLink: data.sendOnboardingLink,
        email: data.email,
        ethnicity: data.ethnicity,
        gender: data.gender,
        veteranStatus: data.veteranStatus,
        profilePhotoObjectId: data.profilePhotoObjectId,
        hourlyRate: parseFloat(data.pay),
        workerClassification: WorkerClassification.Contractor,
        role: 'WORKER',
        address1: data.address,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        shouldAddToPayroll: true,
        target: 'contractors',
      };

      return createHammrUser(newContractor);
    },
    onSuccess(res) {
      const { firstName, lastName } = res.user;
      addToast({
        title: 'Contractor Added',
        description: `Successfully added contractor ${firstName} ${lastName}.`,
        type: 'success',
      });

      handleSuccess();
    },
  });

  return (
    <FormV2
      onSubmit={form.handleSubmit((formData) => contractorMutation.mutate(formData))}
      onCancel={handleCancel}
      isLoading={contractorMutation.isPending}
      submitText="Add Contractor"
    >
      <div className="space-y-6">
        <Alert status="information" size="x-small">
          Please provide some basic info about your contractor. Separate instructions will be sent to the contractor to
          set up payout options.
        </Alert>

        <UserPhotoForm form={form} />

        <GeneralInfoForm
          form={form}
          showDOB={false}
          showWorkersCompCode={false}
          userExists={false}
          showPhone={true}
          showAddress={true}
        />

        <div>
          <NumberFieldV2
            control={form.control}
            name="pay"
            label="Compensation"
            placeholder="0.00"
            required
            error={errors.pay?.message}
            isFloat
            prefix="$"
            labelHelper="(Hourly Rate)"
          />
        </div>
      </div>
    </FormV2>
  );
}
