import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { useAwsS3 } from 'hooks/useAwsS3';
import { useMemo, useRef, useState } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { ColDef, SortDirection, ValueFormatterParams } from '@ag-grid-community/core';
import { formatLocaleUsa } from 'utils/dateHelper';
import { AgGridReact } from '@ag-grid-community/react';
import { Tooltip } from '@hammr-ui/components/tooltip';
import FileFormatIcon from '@hammr-icons/FileFormatIcon';
import DeleteBinLine from '@hammr-icons/DeleteBinLine';
import Spinner from '@/hammr-ui/components/spinner';
import CompactButton from '@hammr-ui/components/CompactButton';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import Button from '@/hammr-ui/components/button';
import { cn } from '@/hammr-ui/lib/utils';
import { useRouter } from 'next/router';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { addToast } from '@/hooks/useToast';
import { userService } from '@/services/user';
import { useQuery } from '@tanstack/react-query';
import { getFileAndOpen } from '@/components/project-documents/utils';
import { EmployeeCertification } from '@/interfaces/employee-certifications';
import { employeeCertifications } from '@/services/employee-certifications';
import AddLine from '@/hammr-icons/AddLine';
import UploadEmployeeCertificationsModal from './UploadEmployeeCertificationsModal';
import AlertLine from '@/hammr-icons/AlertLine';
import CourseProgress from '@/hammr-icons/CourseProgress';

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

interface EmployeeCertificationsViewProps {
  openUploadModal: boolean;
  setOpenUploadModal: (open: boolean) => void;
}

interface TableItem extends EmployeeCertification {
  actions: string;
}

export default function EmployeeCertificationsView({
  openUploadModal,
  setOpenUploadModal,
}: EmployeeCertificationsViewProps) {
  const { user } = useAuth();
  const { company } = useCompany();
  const { s3 } = useAwsS3();
  const router = useRouter();
  const { employee_id } = router.query;
  const [openingCertification, setOpeningCertification] = useState<string>();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCertification, setSelectedCertification] = useState<EmployeeCertification>();
  const employeeIdNumber = parseInt(Array.isArray(employee_id) ? employee_id[0] : employee_id);
  const gridRef = useRef<AgGridReact>(null);

  const certifications = useQuery({
    queryKey: ['employeeCertifications', employeeIdNumber],
    queryFn: () => employeeCertifications.list({ userId: employeeIdNumber.toString() }),
    enabled: !!employeeIdNumber,
  });

  const users = useQuery({
    queryKey: ['users', user?.companyId],
    queryFn: () =>
      userService
        .list({
          organizationId: user?.companyId,
          simple: true,
        })
        .then((users) => users.sort((a, b) => a.firstName.localeCompare(b.firstName))),
    enabled: !!user?.companyId,
  });

  const colDefs: ColDef<TableItem>[] = useMemo(
    () => [
      {
        headerName: 'Certification Title',
        field: 'title',
        minWidth: 400,
        cellRenderer: (params: ValueFormatterParams<TableItem>) => {
          return (
            <div className="flex cursor-pointer flex-row items-center gap-2">
              {openingCertification === params.data.objectId ? (
                <div>
                  <Spinner />
                </div>
              ) : undefined}
              <FileFormatIcon type={params.data.objectId.split('.').at(-1)} />
              <span className="ml-2">{params.value}</span>
            </div>
          );
        },
      },
      {
        headerName: 'Certification No.',
        field: 'number',
        cellClass: 'text-sub-600',
        minWidth: 150,
      },
      {
        headerName: 'Issuing Entity',
        field: 'issuingEntity',
        cellClass: 'text-sub-600',
        minWidth: 250,
      },
      {
        headerName: 'Completion Date',
        field: 'completionDate',
        cellClass: 'text-sub-600',
        minWidth: 150,
        initialSort: 'desc' as SortDirection,
        cellRenderer: (params: ValueFormatterParams) => {
          return formatLocaleUsa(params.value);
        },
      },
      {
        headerName: 'Expiration Date',
        field: 'expirationDate',
        cellClass: 'text-sub-600',
        minWidth: 150,
        cellRenderer: (params: ValueFormatterParams) => {
          // if expired, show expired
          if (params.value && new Date(params.value) < new Date()) {
            return (
              <div className="flex items-center gap-x-4">
                <div className="text-sub-600">{formatLocaleUsa(params.value)}</div>
                <Tooltip content="Expired certificate">
                  <KeyIcon icon={<AlertLine />} color="yellow" size="small" />
                </Tooltip>
              </div>
            );
          }

          return formatLocaleUsa(params.value);
        },
      },
      {
        headerName: '',
        field: 'actions',
        sortable: false,
        cellStyle: { overflow: 'visible' },
        maxWidth: 75,
        cellRenderer: (params: ValueFormatterParams) => {
          const isDocumentOwner = params.data.createdBy === parseInt(user.uid);
          const showDelete = isDocumentOwner || user.isCompanyAdmin;
          return (
            <div key={`project-${params.data.id}`} className="flex h-full items-center justify-center space-x-4">
              {showDelete && (
                <Tooltip content="Remove">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      setShowDeleteModal(true);
                      setSelectedCertification(params.data);
                    }}
                  >
                    <DeleteBinLine />
                  </CompactButton>
                </Tooltip>
              )}
            </div>
          );
        },
      },
    ],
    [openingCertification, user.isCompanyAdmin, user.uid]
  );

  const handleDelete = async (certification: EmployeeCertification) => {
    if (!user?.companyId) return;

    await employeeCertifications.delete(certification.id);
    certifications.refetch();
    addToast({
      title: 'Certification Deleted',
      description: 'The certification has been successfully deleted.',
      type: 'success',
    });
  };

  const handleViewFile = async (key: string) => {
    setOpeningCertification(key);
    await getFileAndOpen(s3, 'employee-certifications', key, user?.companyId, CUSTOMER_BUCKET);
    setOpeningCertification(undefined);
  };

  if (!user) return null;

  if (user && company) {
    return (
      <>
        <div className="relative">
          <div className={cn('relative max-w-8xl', certifications.data?.length === 0 && 'invisible')}>
            <UpdatedTable<TableItem>
              colDefs={colDefs}
              parentRef={gridRef}
              rowData={mapCertificationsForTable(certifications.data)}
              onRowClicked={(event) => {
                handleViewFile(event.data.objectId);
              }}
            />
          </div>
          {certifications.data?.length === 0 && (
            <div className="absolute inset-0 bg-background">
              <div className="mt-24 flex flex-col items-center">
                <CourseProgress />
                <div className="mt-4 text-center text-sm text-soft-400">
                  {users.data?.find((user) => user.id == employeeIdNumber)
                    ? `${users.data?.find((user) => user.id == employeeIdNumber)?.firstName} ${users.data?.find((user) => user.id == employeeIdNumber)?.lastName} has no certifications yet.`
                    : 'No employee certifications found.'}
                  <br />
                  Click the button below to add one.
                  <br />
                  <br />
                  <Button onClick={() => setOpenUploadModal(true)} beforeContent={<AddLine />}>
                    Add Certification
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
        <UploadEmployeeCertificationsModal
          open={openUploadModal}
          setOpen={setOpenUploadModal}
          onSuccess={() => {
            certifications.refetch();
          }}
        />
        <ConfirmDialog
          open={showDeleteModal}
          setOpen={setShowDeleteModal}
          onConfirm={() => {
            if (selectedCertification) {
              handleDelete(selectedCertification);
            }
          }}
          data={selectedCertification}
          icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
          confirmButtonText="Delete"
          confirmButton={{
            color: 'error',
          }}
          title="Delete certification"
          subtitle={
            selectedCertification ? (
              <div>
                You’re about to delete the certification
                <span className="font-medium">{selectedCertification.name}</span>. Do you want to proceed?
              </div>
            ) : undefined
          }
        />
      </>
    );
  } else {
    return (
      <div className="flex size-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }
}

const mapCertificationsForTable = (certifications: EmployeeCertification[]): TableItem[] => {
  return certifications?.map((certification) => {
    return {
      ...certification,
      actions: '',
    };
  });
};
