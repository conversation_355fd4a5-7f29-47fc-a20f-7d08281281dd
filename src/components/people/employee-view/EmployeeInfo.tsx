import { EnrichedEmployee } from 'interfaces/employee';
import { useState } from 'react';
import importScript from 'hooks/importScript';
import { GeneralInfo } from '../sections/GeneralInfo';
import { CompensationCard, PaymentInformationCard } from '../sections/FinancialInfo';
import { Workplaces } from '../sections/Workplaces';
import { formatSalary } from '@/utils/calculateEarnings';
import GeneralInfoEdit from '@/components/people/sections/GeneralInfoEdit';
import FinancialInfoEdit from '@/components/people/sections/FinancialInfoEdit';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import NonPayrollUserGeneralAndFinancialInfoEdit from '../sections/NonPayrollUserGeneralAndFinancialInfoEdit';
import { EmergencyContactInfo } from '../sections/EmergencyContactInfo';
import EmergencyContactInfoEdit from '../sections/EmergencyContactInfoEdit';
import { EmploymentInfo } from '../sections/EmploymentInfo';
import EmployementInfoEdit from '../sections/EmployementInfoEdit';
import { useCompany } from '@/hooks/useCompany';
import { getActiveEarningRate } from '@/utils/temporalUtils';
import { getCompensationTypeFromPeriod } from '@/utils/format';

export default function EmployeeInfo({
  employee,
  refresh,
  allowEdit,
  isFetchingEmployee,
  pointInTime,
}: {
  employee: EnrichedEmployee;
  refresh: any;
  allowEdit: boolean;
  isFetchingEmployee: boolean;
  pointInTime?: Date | string | number; // Optional point in time to check earning rates
}) {
  importScript('https://cdn.checkhq.com/component-initialize.js');

  const [editEmployeeInfo, setEditEmployeeInfo] = useState(false);
  const [editEmergencyInfo, setEditEmergencyInfo] = useState(false);
  const [editEmployementInfo, setEditEmployementInfo] = useState(false);
  const [editFinancialInfo, setEditFinancialInfo] = useState(false);
  const [editNonPayrollUserGeneralAndFinancialInfo, setEditNonPayrollUserGeneralAndFinancialInfo] = useState(false);

  const { company } = useCompany();

  const currentActiveEarningRate = getActiveEarningRate(
    employee.hammrUser.earningRates || [],
    company?.timezone || 'America/Los_Angeles',
    'REG',
    pointInTime // Use the provided point in time or default to current time
  );

  const handleEmployeeInfoSave = () => {
    setEditEmployeeInfo(false);
    refresh();
  };

  const handleEmergencyInfoSave = () => {
    setEditEmergencyInfo(false);
    refresh();
  };

  const handleCompensationInfoSave = () => {
    setEditFinancialInfo(false);
    refresh();
  };

  if (!employee) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }
  const isOnPayroll = Boolean(employee.checkEmployee);
  return (
    <div>
      <GeneralInfoEdit
        open={editEmployeeInfo}
        employee={employee}
        handleSuccess={handleEmployeeInfoSave}
        setOpen={() => setEditEmployeeInfo(false)}
        isOnPayroll={isOnPayroll}
      />
      <EmergencyContactInfoEdit
        open={editEmergencyInfo}
        user={employee.hammrUser}
        handleSuccess={handleEmergencyInfoSave}
        setOpen={() => setEditEmergencyInfo(false)}
      />
      <EmployementInfoEdit
        open={editEmployementInfo}
        checkEmployee={employee.checkEmployee}
        user={employee.hammrUser}
        handleSuccess={handleEmergencyInfoSave}
        setOpen={() => setEditEmployementInfo(false)}
      />
      <FinancialInfoEdit
        open={editFinancialInfo}
        employee={employee}
        handleSuccess={handleCompensationInfoSave}
        setOpen={() => setEditFinancialInfo(false)}
      />

      <NonPayrollUserGeneralAndFinancialInfoEdit
        open={editNonPayrollUserGeneralAndFinancialInfo}
        employee={employee}
        handleSuccess={handleCompensationInfoSave}
        setOpen={() => setEditNonPayrollUserGeneralAndFinancialInfo(false)}
      />

      <div className="grid max-w-md grid-cols-1 gap-6 lg:max-w-screen-xl lg:grid-cols-2 xl:grid-cols-3">
        <div className="col-span-1 gap-x-6 space-y-6 xl:col-span-2 xl:columns-2 [&>*]:break-inside-avoid">
          <GeneralInfo
            firstName={employee.hammrUser.firstName}
            lastName={employee.hammrUser.lastName}
            dob={employee.checkEmployee?.dob}
            phone={employee.hammrUser.phone}
            email={employee.hammrUser.email}
            gender={employee.hammrUser.gender}
            ethnicity={employee.hammrUser.ethnicity}
            veteranStatus={employee.hammrUser.veteranStatus}
            workersCompCode={employee.hammrUser.workersCompCode}
            address={employee.checkEmployee?.residence}
            ssn_last_four={employee.checkEmployee?.ssn_last_four}
            onEdit={() =>
              isOnPayroll ? setEditEmployeeInfo(true) : setEditNonPayrollUserGeneralAndFinancialInfo(true)
            }
            allowEdit={allowEdit}
            isOnPayroll={isOnPayroll}
          />
          <EmploymentInfo
            onEdit={() => setEditEmployementInfo(true)}
            allowEdit={allowEdit}
            employeeId={employee.hammrUser.employeeId}
            position={employee.hammrUser.position}
            role={employee.hammrUser.role}
            managerName={[employee.hammrUser.manager?.firstName, employee.hammrUser.manager?.lastName]
              .filter(Boolean)
              .join(' ')}
            startDate={employee.checkEmployee?.start_date}
            workerClassification={employee.hammrUser.workerClassification}
            departmentName={employee.hammrUser?.department?.name || null}
          />
          <EmergencyContactInfo
            firstName={employee.hammrUser.emergencyContactFirstName}
            lastName={employee.hammrUser.emergencyContactLastName}
            relationship={employee.hammrUser.emergencyContactRelationship}
            phone={employee.hammrUser.emergencyContactPhone}
            onEdit={() => setEditEmergencyInfo(true)}
            allowEdit={allowEdit}
          />
        </div>
        <div className="flex flex-col gap-5">
          {employee.checkEmployee && (
            <PaymentInformationCard
              paymentPreference={employee.checkEmployee.payment_method_preference}
              isEmployee={true}
              checkId={employee.checkEmployee.id}
              bankInfo={employee.bankAccount?.plaid_bank_account || employee.bankAccount?.raw_bank_account}
            />
          )}
          <CompensationCard
            compensationType={getCompensationTypeFromPeriod(currentActiveEarningRate?.period)}
            compensation={
              currentActiveEarningRate
                ? formatSalary(parseFloat(currentActiveEarningRate.amount), currentActiveEarningRate.period)
                : undefined
            }
            startDate={currentActiveEarningRate?.startDate}
            weeklyHours={currentActiveEarningRate?.weeklyHours}
            reimbursementPerDiem={employee.hammrUser.employeeReimbursement?.reimbursementAmount}
            onEdit={() =>
              isOnPayroll ? setEditFinancialInfo(true) : setEditNonPayrollUserGeneralAndFinancialInfo(true)
            }
            userId={employee.hammrUser.id}
          />
          {employee.checkEmployee && employee.workplaces ? (
            <>
              <Workplaces
                userId={employee.hammrUser.id}
                employeeId={employee.checkEmployee.id}
                isFetchingEmployee={isFetchingEmployee}
                selectedWorkplaceIds={employee.workplaces.map((item) => item.id)}
                companyId={employee.checkEmployee.company}
                workersCompCodeId={employee.hammrUser.workersCompCode?.id}
                fullName={`${employee.checkEmployee.first_name} ${employee.checkEmployee.last_name}`}
                allowEdit={allowEdit}
              />
            </>
          ) : undefined}
        </div>
      </div>
    </div>
  );
}
