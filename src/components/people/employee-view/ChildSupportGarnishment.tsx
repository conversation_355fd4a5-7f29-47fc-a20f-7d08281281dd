import { Controller, UseFormReturn } from 'react-hook-form';
import { TextField } from '@/components/elements/form/TextField';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { RadioGroup, RadioGroupItem } from '@hammr-ui/components/Radio';
import { FormControl, FormItem } from '@hammr-ui/components/form';
import { Label } from '@hammr-ui/components/label';
import type { FormValues } from '@/components/post-tax-deductions/AddPostTaxDeductionModal';

interface Props {
  form: UseFormReturn<FormValues>;
}

export default function ChildSupportGarnishment({ form }: Props) {
  return (
    <>
      <div className="text-strong-900 mt-5 font-medium">General Information</div>
      <TextField
        control={form.control}
        name="child_support.external_id"
        label="External ID"
        required
        tooltip="The unique identifier of the garnishment order, listed as the case number on the order"
        className="mt-5"
        rules={{ required: 'Please enter an external ID' }}
      />
      <TextField
        control={form.control}
        name="child_support.agency"
        label="Agency State"
        required
        tooltip="The state abbreviation of the agency that issued the order"
        className="mt-5"
        rules={{ required: 'Please enter an agency state' }}
      />
      <ControlledDateInput
        control={form.control}
        name="child_support.issue_date"
        label="Issue Date"
        required
        tooltip="The date the collections agency issued the order"
        className="mt-5"
        rules={{ required: 'Please enter an issue date' }}
      />

      <hr className="mt-5 border-soft-200" />

      <div className="text-strong-900 mt-5 font-medium">Effective Dates</div>

      <ControlledDateInput
        control={form.control}
        name="effective_start"
        label="Start Date"
        required
        className="mt-5"
        rules={{ required: 'Please enter a start date' }}
      />

      <ControlledDateInput
        control={form.control}
        name="effective_end"
        label="End Date"
        className="mt-5"
        dayPickerProps={{
          disabled: form.watch('effective_start') ? { before: form.watch('effective_start') } : undefined,
        }}
      />

      <hr className="mt-5 border-soft-200" />

      <div className="text-strong-900 mt-5 font-medium">Amount</div>
      <TextField
        control={form.control}
        name="child_support.amount"
        label="Amount Per Pay Period"
        required
        placeholder="0.00"
        className="mt-5"
        rules={{ required: 'Please enter an amount', min: { value: 0, message: 'Amount must be positive' } }}
        type="number"
      />
      <TextField
        control={form.control}
        name="child_support.max_percent"
        label="Max Percentage"
        required
        placeholder="0.00"
        className="mt-5"
        type="number"
        rules={{
          required: 'Please enter a max percent',
          pattern: {
            value: /^(100|[0-9]{1,2})(\.[0-9]{1,2})?$/,
            message: 'Please enter a number between 0 and 100',
          },
        }}
      />
    </>
  );
}
