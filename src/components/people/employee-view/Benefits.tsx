import { useMemo, useState } from 'react';
import { getUserBenefits } from '@/services/user';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { formatLocaleUsa } from 'utils/dateHelper';
import StatusBadge from '@/hammr-ui/components/StatusBadge';
import { CompanyBenefit, EmployeeBenefit } from 'interfaces/benefit';
import { CompactButton } from '@/hammr-ui/components/CompactButton';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import PencilLine from '@/hammr-icons/PencilLine';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import EditEmployeeBenefitModal from '@/components/benefits/EditEmployeeBenefitModal';
import DeleteEmployeeBenefitModal from '@/components/benefits/DeleteEmployeeBenefitModal';
import { useQuery } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import EmptyStateDonationProfile from '@/hammr-icons/EmptyStateDonationProfile';
import Link from 'next/link';
import { Button } from '@/hammr-ui/components/button';
import { RiHistoryLine } from '@remixicon/react';
import EmployeeBenefitHistoryModal from '@/components/benefits/EmployeeBenefitHistoryModal';
import dayjs from 'dayjs';

function mapBenefit(employeeBenefits: (EmployeeBenefit & { companyBenefit: CompanyBenefit })[]) {
  return employeeBenefits.map((employeeBenefit) => ({
    ...employeeBenefit,
    name: employeeBenefit.companyBenefit.name,
    category: employeeBenefit.companyBenefit.category.split('_').join(' ').toLowerCase(),
    status: !employeeBenefit.benefitEndDate,
  }));
}

interface Props {
  hammrEmployeeId: number;
  employeeFullName: string;
}

export default function Benefits({ hammrEmployeeId, employeeFullName }: Props) {
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUnenrollModalOpen, setIsUnenrollModalOpen] = useState(false);
  const [selectedBenefitRow, setSelectedBenefitRow] = useState<EmployeeBenefit | null>(null);

  const { data, refetch, isLoading } = useQuery({
    queryKey: ['employee-benefits', hammrEmployeeId],
    queryFn: async () => {
      const benefits = await getUserBenefits(hammrEmployeeId);
      return mapBenefit(benefits.employeeBenefits);
    },
  });

  const benefits = data;

  const mostRecentEmployeeBenefits = useMemo(() => {
    if (!benefits || benefits.length === 0) return [];
    type BenefitType = (typeof benefits)[number];
    const benefitsMap: Record<string, BenefitType> = {};
    benefits?.forEach((benefit) => {
      const key = benefit.companyBenefitId;
      if (!benefitsMap[key]) {
        benefitsMap[key] = benefit;
      }
      if (benefitsMap[key] && dayjs(benefit.updatedAt).isAfter(dayjs(benefitsMap[key].updatedAt))) {
        benefitsMap[key] = benefit;
      }
    });
    return Object.values(benefitsMap);
  }, [benefits]);

  type TableItem = (typeof mostRecentEmployeeBenefits)[number];

  const colDefs: ColDef<TableItem>[] = [
    {
      field: 'name',
      headerName: 'Benefit',
      minWidth: 150,
      width: 200,
      initialSort: 'asc',
      sortable: true,
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        return (
          <div className="">
            <div className="h-5 text-sm font-medium">{params.value}</div>
            <div className="mt-1 h-5 text-xs capitalize text-sub-600">{params.data?.category}</div>
          </div>
        );
      },
    },
    {
      field: 'companyPeriodAmount',
      headerName: 'Company Contribution',
      minWidth: 190,
      maxWidth: 190,
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        if (params.data?.contributionType === 'PERCENT' || params.data?.contributionType === 'DYNAMIC') {
          return <div>{params.data.companyContributionPercent}%</div>;
        }

        if (params.data?.period === 'MONTHLY') {
          return <div>${params.value}/month</div>;
        } else {
          return <div>${params.data?.companyContributionAmount}/pay period</div>;
        }
      },
    },
    {
      field: 'employeePeriodAmount',
      headerName: 'Employee Contribution',
      minWidth: 190,
      maxWidth: 190,
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        if (params.data?.contributionType === 'PERCENT' || params.data?.contributionType === 'DYNAMIC') {
          return <>{params.data.employeeContributionPercent}%</>;
        }

        if (params.data?.period === 'MONTHLY') {
          return <div>${params.value}/month</div>;
        } else {
          return <div>${params.data?.employeeContributionAmount}/pay period</div>;
        }
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 130,
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        return (
          <div className="flex h-full min-h-10 w-full items-center">
            {params.value ? (
              <StatusBadge status="completed">Enrolled</StatusBadge>
            ) : (
              <StatusBadge status="disabled">Inactive</StatusBadge>
            )}
          </div>
        );
      },
    },
    {
      field: 'benefitStartDate',
      headerName: 'Start',
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        if (!params.value) return null;
        return formatLocaleUsa(params.value as Date);
      },
    },
    {
      field: 'benefitEndDate',
      headerName: 'End',
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        if (!params.value) return null;
        return formatLocaleUsa(params.value as Date);
      },
    },
    {
      headerName: '',
      maxWidth: 140,
      pinned: 'right',
      cellClass: '!flex-row items-center gap-2 !px-5 !justify-end',
      cellRenderer: (params: ICellRendererParams<TableItem>) => {
        const isActive = !params.data?.benefitEndDate;
        return (
          <>
            <Tooltip content="History">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedBenefitRow(params.data ?? null);
                  setIsHistoryModalOpen(true);
                }}
              >
                <RiHistoryLine />
              </CompactButton>
            </Tooltip>
            {isActive && (
              <>
                <Tooltip content="Edit">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      setSelectedBenefitRow(params.data ?? null);
                      setIsEditModalOpen(true);
                    }}
                  >
                    <PencilLine />
                  </CompactButton>
                </Tooltip>
                <Tooltip content="Unenroll">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      setSelectedBenefitRow(params.data ?? null);
                      setIsUnenrollModalOpen(true);
                    }}
                  >
                    <CloseCircleLine />
                  </CompactButton>
                </Tooltip>
              </>
            )}
          </>
        );
      },
    },
  ];

  const history = useMemo(() => {
    if (!selectedBenefitRow || !benefits) return [];
    return benefits
      ?.filter((benefit) => benefit.companyBenefitId === selectedBenefitRow.companyBenefitId)
      .map((benefit) => ({
        companyContribution:
          benefit.contributionType === 'AMOUNT'
            ? benefit.period === 'MONTHLY'
              ? '$' + benefit.companyPeriodAmount
              : '$' + benefit.companyContributionAmount
            : benefit.companyContributionPercent + '%',
        employeeContribution:
          benefit.contributionType === 'AMOUNT'
            ? benefit.period === 'MONTHLY'
              ? '$' + benefit.employeePeriodAmount
              : '$' + benefit.employeeContributionAmount
            : benefit.employeeContributionPercent + '%',
        startDate: benefit.benefitStartDate ? dayjs(benefit.benefitStartDate).toDate() : null,
        endDate: benefit.benefitEndDate ? dayjs(benefit.benefitEndDate).toDate() : null,
        reason: benefit.changeReason
          ? benefit.changeReason
              .split('_')
              .map((str, index) =>
                index === 0 ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : str.toLowerCase()
              )
              .join(' ')
          : 'Enrollment',
      }))
      .sort((a, b) => {
        return dayjs(b.endDate ?? new Date()).isAfter(dayjs(a.endDate)) ? 1 : -1;
      });
  }, [selectedBenefitRow, benefits]);

  if (isLoading)
    return (
      <div className="mt-1/5 flex justify-center">
        <LoadingIndicator />
      </div>
    );

  if (!benefits || benefits.length === 0)
    return (
      <div className="mt-32 flex flex-col items-center justify-center gap-6">
        <EmptyStateDonationProfile />
        <div className="flex flex-col items-center">
          <div className="text-sm text-soft-400">{employeeFullName} is not enrolled in any benefit.</div>
          <div className="text-sm text-soft-400">Click the button below to enroll them.</div>
        </div>
        <Link href="/benefits">
          <Button>Enroll Employee</Button>
        </Link>
      </div>
    );

  return (
    <div>
      <UpdatedTable
        colDefs={colDefs}
        rowData={mostRecentEmployeeBenefits}
        hideSidebar={true}
        emptyRowsText="No benefits enrolled"
        getRowHeight={() => 64}
        defaultColDef={{
          sortable: false,
        }}
      />
      {selectedBenefitRow && (
        <EmployeeBenefitHistoryModal
          open={isHistoryModalOpen}
          setOpen={setIsHistoryModalOpen}
          employeeName={employeeFullName}
          benefitName={selectedBenefitRow.name}
          history={history}
        />
      )}
      {selectedBenefitRow && (
        <EditEmployeeBenefitModal
          open={isEditModalOpen}
          setOpen={setIsEditModalOpen}
          employeeBenefit={selectedBenefitRow}
          employeeName={employeeFullName}
          contributionType={selectedBenefitRow.contributionType}
          callback={refetch}
        />
      )}
      {selectedBenefitRow && (
        <DeleteEmployeeBenefitModal
          open={isUnenrollModalOpen}
          setOpen={setIsUnenrollModalOpen}
          employeeBenefit={selectedBenefitRow}
          employeeName={employeeFullName}
          contributionType={selectedBenefitRow.contributionType}
          callback={refetch}
        />
      )}
    </div>
  );
}
