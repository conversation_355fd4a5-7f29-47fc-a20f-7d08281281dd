import { Chart<PERSON>ontainer } from '@/hammr-ui/components/chart';
import { cn } from '@hammr-ui/lib/utils';
import { PolarAngleAxis, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts';

interface Props {
  value: number;
  max: number;
  label: string;
  className?: string;
}

export default function CircleProgress({ value, max, label, className }: Props) {
  const chartData = [{ value }];
  const chartConfig = {
    chart: {
      label: '',
      color: 'rgb(var(--raw-primary-base))',
    },
  };

  return (
    <div className={cn('relative flex h-[120px] w-[120px] items-center justify-center', className)}>
      <ChartContainer config={chartConfig} className="aspect-square w-full">
        <RadialBarChart data={chartData} endAngle={-270} startAngle={90} innerRadius={50} outerRadius={70} barSize={20}>
          <PolarRadiusAxis tick={false} tickLine={false} axisLine={false} />
          <PolarAngleAxis type="number" domain={[0, max]} tick={false} />
          <RadialBar background dataKey="value" fill="var(--color-chart)" className="stroke-transparent stroke-2" />
        </RadialBarChart>
      </ChartContainer>

      <div className="absolute flex flex-col items-center gap-1">
        <span className="text-xl font-semibold text-strong-950">{value !== undefined ? `${value}h` : ''}</span>
        <span className="text-xs font-medium text-sub-600">{label}</span>
      </div>
    </div>
  );
}
