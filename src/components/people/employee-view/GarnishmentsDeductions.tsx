import { useMemo, useState } from 'react';
import { PostTaxDeduction } from 'interfaces/post-tax-deduction';
import { formatterTwoDecimal } from 'utils/format';
import Table from 'components/elements/Table';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import { useQuery } from '@tanstack/react-query';
import { deletePostTaxDeduction, listPostTaxDeductionsByQueryParams } from 'services/post-tax-deduction';
import EmptyStateFinanceBanking from '@hammr-icons/EmptyStateFinanceBanking';
import { ListResponse } from '@/interfaces/check';
import Button from '@hammr-ui/components/button';
import CompactButton from '@/hammr-ui/components/CompactButton';
import { RiAddLine, RiDeleteBinLine, RiPencilLine } from '@remixicon/react';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { addToast } from '@/hooks/useToast';
import AddPostTaxDeductionModal from '@/components/post-tax-deductions/AddPostTaxDeductionModal';
import EditPostTaxDeductionModal from '@/components/post-tax-deductions/EditPostTaxDeductionModal';
import { useCompany } from '@/hooks/useCompany';
import { userService } from '@/services/user';
import { useAuth } from '@/hooks/useAuth';

interface Props {
  employeeCheckId?: string;
}

export default function GarnishmentsDeductions({ employeeCheckId }: Props) {
  // if `employeeCheckId` is present we can assume that this component is used in people/employee/[employee_id] page
  const showEmployeeColumnInTable = !employeeCheckId;
  const showEmployeeFieldInDialog = !employeeCheckId;
  const fetchDeductionsByEmployee = !!employeeCheckId;

  const { company } = useCompany();
  const [paginationCursor, setPaginationCursor] = useState<string | null>(null);

  const queryParams = {
    ...(fetchDeductionsByEmployee ? { employee: employeeCheckId } : { company: company?.checkCompanyId }),
    ...(paginationCursor ? { cursor: paginationCursor } : {}),
  } as Record<string, string>;

  const deductionsQuery = useQuery<ListResponse<PostTaxDeduction>>({
    queryKey: ['postTaxDeductions', queryParams],
    queryFn: () => {
      const searchParams = new URLSearchParams(queryParams);
      return listPostTaxDeductionsByQueryParams(searchParams.toString());
    },
    enabled: !!company?.checkCompanyId,
  });

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<PostTaxDeduction | null>(null);

  const columns = [
    ...(showEmployeeColumnInTable ? ['Employee'] : []),
    'type',
    'description',
    'effective start',
    'effective end',
    'amount',
    '',
  ];

  const { user } = useAuth();

  const employeesQuery = useQuery({
    queryKey: ['check-employees'],
    async queryFn() {
      const allUsers = await userService.list({ organizationId: user.companyId, simple: true });
      return allUsers.filter((user) => user.workerClassification === 'EMPLOYEE' && user.checkEmployeeId);
    },
  });

  const deductionsMappedForTable = useMemo(() => {
    const arr = deductionsQuery.data?.results.map((postTaxDeduction) => {
      const employee = employeesQuery.data?.find((employee) => employee.checkEmployeeId === postTaxDeduction.employee);
      return [
        ...(showEmployeeColumnInTable
          ? employee
            ? [`${employee?.firstName} ${employee?.lastName}`]
            : [<SkeletonLoader key="" />]
          : []),
        postTaxDeduction.type === 'miscellaneous' ? 'Miscellaneous' : 'Child Support',
        <div key="description" className="capitalize">
          {postTaxDeduction.description}
        </div>,
        postTaxDeduction.effective_start,
        postTaxDeduction.effective_end ? postTaxDeduction.effective_end : <div className="text-gray-500">-</div>,
        postTaxDeduction.type === 'miscellaneous'
          ? postTaxDeduction.miscellaneous?.amount
            ? formatterTwoDecimal(parseFloat(postTaxDeduction.miscellaneous.amount))
            : `${postTaxDeduction.miscellaneous?.percent?.toFixed(2)}%`
          : formatterTwoDecimal(parseFloat(postTaxDeduction.child_support?.amount ?? '0')),
        <div key={postTaxDeduction.id} className="flex items-center justify-end space-x-2">
          <Tooltip content="Edit">
            <CompactButton
              size="large"
              onClick={() => {
                setSelectedRow(postTaxDeduction);
                setShowEditModal(true);
              }}
            >
              <RiPencilLine />
            </CompactButton>
          </Tooltip>
          <Tooltip content="Delete">
            <CompactButton
              size="large"
              onClick={() => {
                setSelectedRow(postTaxDeduction);
                setShowDeleteModal(true);
              }}
            >
              <RiDeleteBinLine />
            </CompactButton>
          </Tooltip>
        </div>,
      ];
    });
    return arr ?? [];
  }, [deductionsQuery.data?.results, employeesQuery.data, showEmployeeColumnInTable]);

  return (
    <>
      {deductionsQuery.isPending ? (
        <div className="mt-1/5 flex flex-col items-center">
          <LoadingIndicator />
        </div>
      ) : deductionsQuery.isError ? (
        <div className="mt-1/5 flex flex-col items-center">
          <p className="text-sm text-error-base">An error occurred while fetching deductions.</p>
        </div>
      ) : deductionsQuery.data.results.length === 0 ? (
        <div className="mt-24 flex flex-col items-center">
          <EmptyStateFinanceBanking />
          <div className="mt-4 text-soft-400">There is no garnishment or post-tax deduction.</div>
          <div className="text-soft-400">Click the button below to add one.</div>
          <div className="mt-6">
            <Button color="primary" beforeContent={<RiAddLine />} onClick={() => setShowAddModal(true)}>
              Add Post-Tax Deduction
            </Button>
          </div>
        </div>
      ) : (
        <Table
          columns={columns}
          elements={deductionsMappedForTable}
          nextUrl={deductionsQuery.data?.next}
          previousUrl={deductionsQuery.data?.previous}
          handlePagination={(url) => {
            const _url = new URL(url);
            const urlParams = new URLSearchParams(_url.search);
            const cursor = urlParams.get('cursor');
            setPaginationCursor(cursor);
          }}
        />
      )}

      <AddPostTaxDeductionModal
        open={showAddModal}
        setOpen={setShowAddModal}
        employeeCheckId={employeeCheckId}
        showEmployee={showEmployeeFieldInDialog} // as employee column is only shown on the benefits page
      />
      {selectedRow && (
        <EditPostTaxDeductionModal
          open={showEditModal}
          setOpen={setShowEditModal}
          refresh={() => deductionsQuery.refetch()}
          postTaxDeduction={selectedRow}
          showEmployee={showEmployeeFieldInDialog} // as employee column is only shown on the benefits page
        />
      )}

      <ConfirmDialog
        open={showDeleteModal}
        setOpen={setShowDeleteModal}
        onConfirm={async () => {
          try {
            if (!selectedRow) throw new Error('No row selected');
            await deletePostTaxDeduction(selectedRow.id);
            deductionsQuery.refetch();
            addToast({
              title: 'Deleted Post-Tax Deduction',
              description: `Successfully deleted the post-tax deduction.`,
              type: 'success',
            });
          } catch (err: any) {
            addToast({
              title: 'Error Deleting Post-Tax Deduction',
              description: err.message || 'An error occurred while deleting the post-tax deduction.',
              type: 'error',
            });
          }
        }}
        icon={<KeyIcon icon={<RiDeleteBinLine />} color="red" />}
        title="Delete Post-Tax Deduction"
        subtitle={"You're about to delete the post-tax deduction. Do you want to proceed?"}
        confirmButton={{
          color: 'error',
        }}
        confirmButtonText="Delete"
      />
    </>
  );
}

function SkeletonLoader() {
  return <div className="h-6 w-24 animate-pulse rounded-6 bg-soft-200/50"></div>;
}
