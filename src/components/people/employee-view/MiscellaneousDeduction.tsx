import { Controller, UseFormReturn } from 'react-hook-form';
import { RadioGroup, RadioGroupItem } from '@hammr-ui/components/Radio';
import { TextField } from '@/components/elements/form/TextField';
import { FormControl, FormItem, FormLabel } from '@hammr-ui/components/form';
import { Label } from '@hammr-ui/components/label';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import type { FormValues } from '@/components/post-tax-deductions/AddPostTaxDeductionModal';

interface Props {
  form: UseFormReturn<FormValues>;
}

export default function MiscellaneousDeduction({ form }: Props) {
  return (
    <>
      <ControlledDateInput
        control={form.control}
        name="effective_start"
        label="Start Date"
        required
        className="mt-5"
        rules={{ required: 'Please enter a start date' }}
      />

      <ControlledDateInput
        control={form.control}
        name="effective_end"
        label="End Date"
        className="mt-5"
        dayPickerProps={{
          disabled: form.watch('effective_start') ? { before: form.watch('effective_start') } : undefined,
        }}
      />

      <hr className="mt-5 border-soft-200" />

      <FormItem className="mt-5 gap-3">
        <FormLabel>Amount</FormLabel>
        <FormControl>
          <Controller
            name="miscellaneous.amountType"
            control={form.control}
            render={({ field }) => (
              <RadioGroup
                value={field.value}
                onValueChange={(value) => {
                  field.onChange(value);
                }}
              >
                <div className="flex items-center gap-2">
                  <RadioGroupItem value="fixed" id="fixed" />
                  <Label htmlFor="fixed">Fixed Amount</Label>
                </div>
                <div className="flex items-center gap-2">
                  <RadioGroupItem value="percentage" id="percentage" />
                  <Label htmlFor="percentage">Percentage of Pay</Label>
                </div>
              </RadioGroup>
            )}
          />
        </FormControl>
      </FormItem>

      {form.watch('miscellaneous.amountType') === 'fixed' ? (
        <TextField
          control={form.control}
          name="miscellaneous.amount"
          label="Amount Per Pay Period"
          required
          beforeContent="$"
          placeholder="0.00"
          type="number"
          step="0.01"
          className="mt-5"
          rules={{ required: 'Please enter an amount', min: { value: 0, message: 'Amount must be positive' } }}
        />
      ) : (
        <TextField
          control={form.control}
          name="miscellaneous.percent"
          label="Percentage Per Pay Period"
          required
          beforeContent="%"
          placeholder="0.00"
          type="number"
          step="0.01"
          className="mt-5"
          rules={{
            required: 'Please enter a percentage',
            min: { value: 0, message: 'Percentage must be positive' },
            max: { value: 100, message: 'Value must be less than or equal to 100' },
          }}
        />
      )}

      <TextField
        control={form.control}
        name="miscellaneous.total_amount"
        label="Total Amount"
        beforeContent="$"
        placeholder="0.00"
        type="number"
        step="0.01"
        className="mt-5"
      />

      <TextField
        control={form.control}
        name="miscellaneous.annual_limit"
        label="Annual Limit"
        beforeContent="$"
        placeholder="0.00"
        type="number"
        step="0.01"
        className="mt-5"
      />
    </>
  );
}
