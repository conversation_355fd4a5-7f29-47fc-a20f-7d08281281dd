import { EmployeeDocument } from 'interfaces/documents';
import { employeeDocumentDownload, listEmployeeTaxDocuments } from 'services/documents';
import { useQuery } from '@tanstack/react-query';
import { logError } from 'utils/errorHandling';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import EmptyStateHRNotes from '@hammr-icons/EmptyStateHRNotes';
import Button from '@hammr-ui/components/button';
import { formatLocaleUsa } from 'utils/dateHelper';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { useState } from 'react';
import { Spinner } from '@/hammr-ui/components/spinner';
import FileFormatIcon from '@/hammr-icons/FileFormatIcon';

interface EmployeeTaxDocumentsProps {
  checkEmployeeId: string;
  employeeFullName?: string;
}

export default function EmployeeTaxDocuments({ checkEmployeeId, employeeFullName }: EmployeeTaxDocumentsProps) {
  const [downloadingId, setDownloadingId] = useState<string>();

  const documentsQuery = useQuery({
    queryKey: ['taxDocuments', checkEmployeeId],
    queryFn: () => listEmployeeTaxDocuments(checkEmployeeId),
    enabled: !!checkEmployeeId,
  });

  const documents = documentsQuery.data?.results || [];

  const handleDownload = (document: EmployeeDocument) => {
    setDownloadingId(document.id);
    employeeDocumentDownload(document.id, document.label)
      .then(() => setDownloadingId(undefined))
      .catch((err) => {
        logError(err);
        setDownloadingId(undefined);
      });
  };

  const colDefs: ColDef<EmployeeDocument>[] = [
    {
      headerName: 'Document',
      field: 'label',
      cellRenderer: (params: ValueFormatterParams<EmployeeDocument>) => {
        return (
          <div className="flex cursor-pointer flex-row items-center gap-2">
            {downloadingId === params.data.id ? (
              <div>
                <Spinner />
              </div>
            ) : undefined}
            <FileFormatIcon type={'pdf'} />
            <span className="ml-2">{params.value}</span>
          </div>
        );
      },
    },
    {
      headerName: 'Signed On',
      field: 'filed_on',
      cellRenderer: (params) => formatLocaleUsa(params.value),
    },
  ];

  if (documentsQuery.isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!documents.length) {
    return (
      <div className="mt-24 flex flex-col items-center">
        <EmptyStateHRNotes />
        <div className="mt-4 text-soft-400">
          {employeeFullName ? `${employeeFullName} has no documents yet.` : 'No documents found.'}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-3xl">
      <UpdatedTable<EmployeeDocument>
        onRowClicked={(event) => {
          handleDownload(event.data);
        }}
        colDefs={colDefs}
        rowData={documents}
        enablePagination={true}
      />
    </div>
  );
}
