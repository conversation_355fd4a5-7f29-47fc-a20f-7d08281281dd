import { Chart<PERSON>ontainer } from '@/hammr-ui/components/chart';
import { cn } from '@hammr-ui/lib/utils';
import { PolarAngleAxis, PolarRadiusAxis, RadialBar, RadialBarChart } from 'recharts';

interface Props {
  value: number;
  max: number;
  label: string;
  className?: string;
}

export default function SemiCircleProgress({ value, max, label, className }: Props) {
  const chartConfig = {
    chart: {
      label: '',
      color: 'rgb(var(--raw-primary-base))',
    },
  };

  const isInfinite = !isFinite(value);

  const chartData = [{ value: isInfinite ? 100 : value }];

  return (
    <div className={cn('relative flex h-[104px] w-fit items-start justify-center overflow-hidden', className)}>
      <ChartContainer config={chartConfig} className="aspect-square w-[208px]">
        <RadialBarChart data={chartData} endAngle={0} startAngle={180} innerRadius={90} outerRadius={140}>
          <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}></PolarRadiusAxis>
          <PolarAngleAxis type="number" domain={[0, isInfinite ? 100 : max]} />
          <RadialBar background dataKey="value" cornerRadius={2} fill="var(--color-chart)" />
        </RadialBarChart>
      </ChartContainer>

      <div className="absolute bottom-0 mt-4 flex flex-col items-center gap-1">
        {isFinite(value) && <span className="text-3xl font-semibold text-strong-950">{value}</span>}
        <span className="text-xs font-medium text-sub-600">{label}</span>
      </div>
    </div>
  );
}
