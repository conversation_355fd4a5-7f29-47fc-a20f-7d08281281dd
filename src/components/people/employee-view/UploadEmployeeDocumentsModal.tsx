import { Dispatch, FC, SetStateAction, useEffect, useMemo, useRef, useState } from 'react';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { useToast } from 'hooks/useToast';
import { Project } from 'interfaces/project';
import { logError, showErrorToast } from 'utils/errorHandling';
import { v4 as uuidv4 } from 'uuid';
import { Controller, NestedValue, useForm } from 'react-hook-form';
import { useAuth } from 'hooks/useAuth';
import { useAwsS3 } from 'hooks/useAwsS3';
import { createProjectDocument } from 'services/project-documents';
import { yupResolver } from 'utils/yupResolver';
import FileFormatIcon from '@hammr-icons/FileFormatIcon';
import { FormV2 } from 'components/elements/Form';
import { DropdownPicker } from '@hammr-ui/components/Dropdown';
import UploadCloud2Line from '@hammr-icons/UploadCloud2Line';
import DeleteBinLine from '@hammr-icons/DeleteBinLine';
import { Progress } from '@hammr-ui/components/progress';
import { FormControl, FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import * as yup from 'yup';
import { employeeDocuments } from '@/services/employee-documents';
import { Input } from '@/hammr-ui/components/input';
import { useRouter } from 'next/router';
import { formatFileSize } from '@/utils/format';
import { TextField } from '@/components/elements/form/TextField';

export const formSchema = yup.object().shape({
  documents: yup.array().min(1, 'Please select document').max(1, 'Please select only one document'),
  documentName: yup.string().required('Please enter a document name'),
});

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

interface UploadEmployeeDocumentsModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  defaultFiles?: File[];
  onSuccess?: () => void;
}

export default function UploadEmployeeDocumentsModal({ open, setOpen, onSuccess }: UploadEmployeeDocumentsModalProps) {
  const form = useForm<FormData>({
    defaultValues: {
      documents: [],
      documentName: '',
    },
    shouldUnregister: false,
    mode: 'all',
    resolver: yupResolver(formSchema as any),
  });

  const router = useRouter();
  const { employee_id } = router.query;
  const { addToast } = useToast();
  const { user } = useAuth();
  const { s3 } = useAwsS3();
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});

  async function uploadDocuments(data: FormData) {
    const updatedProgress = { ...uploadProgress };

    await Promise.all(
      data.documents.filter(Boolean).map(async (document) => {
        const fileExtension = document.name.indexOf('.') ? document.name.split('.').pop() : '';

        const objectId = uuidv4() + (fileExtension ? '.' + fileExtension : '');

        const params = {
          Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/employee-documents`,
          Key: objectId,
          Body: document,
        };

        await s3
          .putObject(params)
          .on('httpUploadProgress', (evt) => {
            const progress = Math.round((evt.loaded * 100) / evt.total);
            updatedProgress[document.name] = progress;
            setUploadProgress({ ...updatedProgress });
          })
          .promise();

        const payload = {
          name: data.documentName,
          objectId: objectId,
          userId: employee_id as string,
        };
        await employeeDocuments.create(payload);
      })
    );

    addToast({
      title: 'Upload Successful',
      description: 'We have successfully uploaded your document.',
      type: 'success',
    });
  }

  const onSubmit = async (data: FormData) => {
    try {
      setUploadProgress({});
      await uploadDocuments(data);
      setOpen(false);
      onSuccess?.();
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to upload document');
    } finally {
      setUploadProgress({});
    }
  };

  useEffect(() => {
    if (!open) {
      form.reset({
        documents: [],
        documentName: '',
      });
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<UploadCloud2Line className="text-sub-600" />} title="Upload Document" showCloseButton />
        <FormV2
          onSubmit={form.handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={form.formState.isSubmitting}
          submitText="Upload"
        >
          <UploadDocumentsForm form={form} uploadProgress={uploadProgress} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}

interface FormData {
  documents: File[];
  documentName: string;
}

interface UploadDocumentsFormProps {
  form: ReturnType<typeof useForm<FormData>>;
  uploadProgress: { [key: string]: number };
}

const UploadDocumentsForm: FC<UploadDocumentsFormProps> = ({ form, uploadProgress }) => {
  const {
    control,
    register,
    formState: { errors },
    watch,
    setValue,
  } = form;
  const documents = watch('documents');

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files).slice(0, 1); // Limit to 1 file
    setValue('documents', files, { shouldValidate: true }); // Replace the file
  };

  const handleFileInput = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []).slice(0, 1); // Limit to 1 file
    setValue('documents', files, { shouldValidate: true }); // Replace the file
    event.target.value = null; // Clear input value
  };

  const removeFile = (index: number) => {
    const newDocuments = [...documents];
    newDocuments.splice(index, 1);
    setValue('documents', newDocuments);
  };

  return (
    <div className="flex flex-col gap-6">
      <TextField
        control={control}
        name="documentName"
        label="Document Name"
        placeholder="Enter document name"
        rules={{ required: 'Please enter a document name' }}
        error={errors.documentName?.message}
        required
      />

      <FormItem required error={!!errors['documents']}>
        <FormControl>
          <div
            className="cursor-pointer rounded-xl border border-dashed border-sub-300 bg-white-0 p-6 text-center"
            onDrop={handleDrop}
            onDragOver={(e) => e.preventDefault()}
            onClick={() => document.getElementById('fileInput')?.click()}
          >
            <input
              id="fileInput"
              type="file"
              className="hidden"
              {...register('documents')}
              onChange={handleFileInput}
            />
            <div className="flex flex-col items-center justify-center">
              <UploadCloud2Line className="mb-4 h-8 w-8 text-sub-600" />
              <p className="mb-2 gap-1 text-base font-semibold text-strong-950">
                <span className="text-primary-base underline">Choose a file</span>
                or drag & drop it here
              </p>
              <p className="text-sm text-sub-600">Max size: 50 MB</p>
            </div>
          </div>
        </FormControl>
        <FormMessage>{errors['documents']?.message}</FormMessage>
      </FormItem>

      {documents.length > 0 && (
        <div className="flex flex-col rounded-xl border border-soft-200 bg-white-0 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileFormatIcon className="h-10 w-10 text-sub-600" type={documents[0].name.split('.').at(-1)} />
              <div className="flex flex-col gap-1">
                <span className="text-sm font-medium text-strong-950">{documents[0].name}</span>
                <span className="text-xs text-sub-600">{formatFileSize(documents[0].size)}</span>
              </div>
            </div>
            <button
              type="button"
              onClick={() => removeFile(0)}
              className="text-sub-600 transition-colors hover:text-error-base"
            >
              <DeleteBinLine className="h-5 w-5" />
            </button>
          </div>
          {uploadProgress[documents[0].name] !== undefined && (
            <div className="mt-2">
              <Progress value={uploadProgress[documents[0].name]} className="w-full" />
            </div>
          )}
        </div>
      )}
    </div>
  );
};
