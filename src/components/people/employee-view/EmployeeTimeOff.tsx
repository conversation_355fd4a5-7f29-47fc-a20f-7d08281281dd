import { useQuery, useQueryClient } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { apiRequest } from '@/utils/requestHelpers';
import { TimeOffPolicy, TimeOffPolicyType, TimeOffRequest } from '@/interfaces/timeoff';
import { PaginatedData } from '@/interfaces/pagination';
import { TabItem, TabList, Tabs } from '@hammr-ui/components/tabs';
import { useState } from 'react';
import CircleProgress from './CircleProgress';
import EmptyStateScheduleHoliday from '@hammr-icons/EmptyStateScheduleHoliday';
import TimeOffRequestsTable from '@/components/time-off-requests/RequestsTable';
import { getPolicyName } from '@/services/time-off-policies';
import { formatHours } from '@/utils/format';

interface Props {
  userId: number;
}

export default function EmployeeTimeOff({ userId }: Props) {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedTab, setSelectedTab] = useState<'pending' | 'approved' | 'declined'>('pending');
  const queryClient = useQueryClient();

  const timeOffRequestsQuery = useQuery<PaginatedData<TimeOffRequest, 'timeOffRequests'>>({
    queryKey: ['time-off-requests', userId, selectedTab, page, pageSize],
    queryFn() {
      return apiRequest(`time-off-requests`, {
        urlParams: {
          userId,
          limit: pageSize,
          page,
          status: selectedTab.toUpperCase(),
        },
      });
    },
    enabled: !!userId,
  });

  const pendingRequestsCount = queryClient.getQueryData<PaginatedData<TimeOffRequest, 'timeOffRequests'>>([
    'time-off-requests',
    userId,
    'pending',
    1,
    pageSize,
  ]);

  const userPoliciesQuery = useQuery({
    queryKey: ['user', userId, 'time-off-policies'],
    queryFn: () =>
      apiRequest<{
        timeOffPolicies: TimeOffPolicy[];
      }>(`users/${userId}/time-off-policies`).then((response) => response.timeOffPolicies),
    enabled: !!userId,
  });

  return (
    <>
      {userPoliciesQuery.isPending ? (
        <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6">
          <SkeletonLoader />
          <SkeletonLoader />
        </section>
      ) : userPoliciesQuery.isError ? (
        <div className="text-sm text-error-base">
          An error occurred when fetching the policies the user is enrolled in.
        </div>
      ) : userPoliciesQuery.data.length === 0 ? (
        <div className="text-sm text-soft-400">
          This employee is not enrolled in any active Time Off policy. Please{' '}
          <a className="text-primary-base underline" href="/time-off?tab=policies">
            enroll
          </a>
          .
        </div>
      ) : (
        <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6">
          {userPoliciesQuery.data.map((policy) => (
            <div key={policy.id} className="rounded-16 border border-soft-200 p-5 pb-4 shadow-xs">
              <div className="flex flex-col gap-5">
                <div className="flex items-center justify-between text-sm">
                  <h3 className="font-medium text-strong-950">{policy.name}</h3>
                  <span className="text-sub-600">{getPolicyName(policy.type as TimeOffPolicyType)}</span>
                </div>
                <div className="flex items-center justify-between gap-8">
                  <CircleProgress
                    value={policy.isLimited ? Math.floor((policy.availableHours as number) * 100) / 100 : undefined}
                    max={policy.accrualLimit}
                    label={policy.isLimited ? 'AVAILABLE' : 'NO LIMIT'}
                  />
                  {policy.isLimited ? (
                    <div className="flex h-min flex-row gap-2 text-sm ">
                      <div className="flex flex-col justify-end gap-2 text-sub-600">
                        <span>+</span>
                        <span>-</span>
                        <span>=</span>
                      </div>
                      <div className="flex flex-col gap-2 text-right font-medium">
                        <span>{formatHours(policy.accruedHoursBasedOnAccrualLimit ?? 0)}</span>
                        <span>{formatHours(policy.carryoverHours ?? 0)}</span>
                        <span>{formatHours(policy.usedHours ?? 0)}</span>
                        <span>{formatHours(policy.availableHours ?? 0)}</span>
                      </div>
                      <div className="ml-1 flex flex-col gap-2 text-sub-600">
                        <span>Accrued</span>
                        <span>Carryover</span>
                        <span>Used</span>
                        <span>Available</span>
                      </div>
                    </div>
                  ) : undefined}
                </div>
              </div>
            </div>
          ))}
        </section>
      )}

      <Tabs value={selectedTab} onValueChange={(tab) => setSelectedTab(tab as typeof selectedTab)}>
        <TabList className="my-6 [&>*]:w-[130px]">
          <TabItem value="pending">Pending {pendingRequestsCount ? `(${pendingRequestsCount.total})` : ''}</TabItem>
          <TabItem value="approved">Approved</TabItem>
          <TabItem value="declined">Declined</TabItem>
          <TabItem value="paid">Paid</TabItem>
        </TabList>
      </Tabs>

      {timeOffRequestsQuery.isPending ? (
        <div className="flex flex-col items-center py-[120px]">
          <LoadingIndicator text="Loading Requests..." />
        </div>
      ) : timeOffRequestsQuery.isError ? (
        <div className="flex items-center justify-center py-[120px] text-sm text-error-base">
          An error occured when fetching the user&apos;s time off requests.
        </div>
      ) : timeOffRequestsQuery.data.total === 0 ? (
        <div className="flex flex-col items-center py-[92px]">
          <EmptyStateScheduleHoliday />
          <p className="my-3 h-fit text-center text-sm text-soft-400">
            There is no Time Off requested or scheduled yet.
          </p>
        </div>
      ) : (
        <TimeOffRequestsTable
          rowData={timeOffRequestsQuery.data.timeOffRequests}
          requestStatus={selectedTab}
          showEmployeeName={false}
          pagination={{
            total: timeOffRequestsQuery.data.total,
            page,
            pageSize,
            onPageChanged: setPage,
            onPageSizeChanged: setPageSize,
          }}
        />
      )}
    </>
  );
}

function SkeletonLoader() {
  return (
    <div className="flex h-[198px] animate-pulse flex-col justify-between rounded-16 border border-soft-200 p-5 shadow-xs">
      <div className="flex justify-between">
        <div className="h-5 w-24 rounded bg-weak-100"></div>
        <div className="h-5 w-20 rounded bg-weak-100"></div>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex size-[120px] flex-col items-center justify-center gap-1 rounded-full border-[10px] border-weak-100">
          <div className="mt-2 h-5 w-8 rounded bg-weak-50" />
          <div className="h-4 w-16 rounded bg-weak-100" />
        </div>
        <div className="flex flex-col items-end gap-2">
          <div className="h-4 w-24 rounded bg-weak-100" />
          <div className="h-4 w-24 rounded bg-weak-100" />
          <div className="h-4 w-20 rounded bg-weak-100" />
          <div className="h-4 w-28 rounded bg-weak-100" />
        </div>
      </div>
    </div>
  );
}
