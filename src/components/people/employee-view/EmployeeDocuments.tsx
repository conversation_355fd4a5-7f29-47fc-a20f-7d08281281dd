import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { useAwsS3 } from 'hooks/useAwsS3';
import { useEffect, useMemo, useRef, useState } from 'react';
import { logError, showErrorToast } from 'utils/errorHandling';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { ColDef, SortDirection, ValueFormatterParams } from '@ag-grid-community/core';
import { formatLocaleUsa } from 'utils/dateHelper';
import { AgGridReact } from '@ag-grid-community/react';
import { Tooltip } from '@hammr-ui/components/tooltip';
import FileFormatIcon from '@hammr-icons/FileFormatIcon';
import DeleteBinLine from '@hammr-icons/DeleteBinLine';
import Spinner from '@/hammr-ui/components/spinner';
import CompactButton from '@hammr-ui/components/CompactButton';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import Button from '@/hammr-ui/components/button';
import { EmptytStateDocument } from '@/hammr-ui/components/empty-states/Document';
import UploadCloud2Line from '@/hammr-icons/UploadCloud2Line';
import { cn } from '@/hammr-ui/lib/utils';
import UploadEmployeeDocumentsModal from './UploadEmployeeDocumentsModal';
import { useRouter } from 'next/router';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { employeeDocuments } from '@/services/employee-documents';
import { EmployeeDocuments } from '@/interfaces/employee-documents';
import EmptyStateHRNotes from '@/hammr-icons/EmptyStateHRNotes';
import { addToast } from '@/hooks/useToast';
import { userService } from '@/services/user';
import { useQuery } from '@tanstack/react-query';
import { getFileAndOpen } from '@/components/project-documents/utils';

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

interface EmployeeDocumentsViewProps {
  openUploadModal: boolean;
  setOpenUploadModal: (open: boolean) => void;
}

interface TableItem extends EmployeeDocuments {
  userName: string;
  actions: string;
}

export default function EmployeeDocumentsView({ openUploadModal, setOpenUploadModal }: EmployeeDocumentsViewProps) {
  const { user } = useAuth();
  const { company } = useCompany();
  const { s3 } = useAwsS3();
  const router = useRouter();
  const { employee_id } = router.query;
  const [openingDocument, setOpeningDocument] = useState<string>();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<EmployeeDocuments>();
  const employeeIdNumber = parseInt(Array.isArray(employee_id) ? employee_id[0] : employee_id);

  const documents = useQuery({
    queryKey: ['employeeDocuments', employeeIdNumber],
    queryFn: () => employeeDocuments.list({ userId: employeeIdNumber.toString() }),
    enabled: !!employeeIdNumber,
  });

  const users = useQuery({
    queryKey: ['users', user?.companyId],
    queryFn: async () => {
      const users = await userService.list({
        organizationId: user?.companyId,
        simple: true,
      });
      return users.sort((a, b) => a.firstName.localeCompare(b.firstName));
    },
    enabled: !!user?.companyId,
  });

  const colDefs: ColDef<TableItem>[] = useMemo(
    () => [
      {
        headerName: 'Document',
        field: 'name',
        initialWidth: 300,
        cellRenderer: (params: ValueFormatterParams<TableItem>) => {
          return (
            <div className="flex cursor-pointer flex-row items-center gap-2">
              {openingDocument === params.data.objectId ? (
                <div>
                  <Spinner />
                </div>
              ) : undefined}
              <FileFormatIcon type={params.data.objectId.split('.').at(-1)} />
              <span className="ml-2">{params.value}</span>
            </div>
          );
        },
      },
      {
        headerName: 'Uploaded On',
        field: 'createdAt',
        cellClass: 'text-sub-600',
        initialSort: 'desc' as SortDirection,
        cellRenderer: (params: ValueFormatterParams) => {
          return formatLocaleUsa(params.value);
        },
      },
      {
        headerName: 'Uploaded By',
        field: 'userName',
        cellClass: 'text-sub-600',
      },
      {
        headerName: '',
        field: 'actions',
        sortable: false,
        cellStyle: { overflow: 'visible' },
        maxWidth: 120,
        cellRenderer: (params: ValueFormatterParams) => {
          const isDocumentOwner = params.data.createdBy === parseInt(user.uid);
          const showDelete = isDocumentOwner || user.isCompanyAdmin;
          return (
            <div key={`project-${params.data.id}`} className="flex h-full items-center justify-center space-x-4">
              {showDelete && (
                <Tooltip content="Remove">
                  <CompactButton
                    size="large"
                    onClick={() => {
                      setShowDeleteModal(true);
                      setSelectedDocument(params.data);
                    }}
                  >
                    <DeleteBinLine />
                  </CompactButton>
                </Tooltip>
              )}
            </div>
          );
        },
      },
    ],
    [openingDocument, user.isCompanyAdmin, user.uid]
  );

  const handleDelete = async (document: EmployeeDocuments) => {
    if (!user?.companyId) return;

    await employeeDocuments.delete(document.id);
    documents.refetch();
    addToast({
      title: 'Document Deleted',
      description: 'The document has been successfully deleted.',
      type: 'success',
    });
  };

  const handleViewFile = async (key: string) => {
    setOpeningDocument(key);
    await getFileAndOpen(s3, 'employee-documents', key, user?.companyId, CUSTOMER_BUCKET);
    setOpeningDocument(undefined);
  };

  if (!user) return null;

  if (user && company) {
    return (
      <>
        <div className="relative">
          <div className={cn('relative max-w-5xl', documents.data?.length === 0 && 'invisible')}>
            <UpdatedTable<TableItem>
              colDefs={colDefs}
              rowData={mapDocumentsForTable(documents.data ?? [])}
              onRowClicked={(event) => {
                handleViewFile(event.data.objectId);
              }}
            />
          </div>
          {documents.data?.length === 0 && (
            <div className="absolute inset-0 bg-background">
              <div className="mt-24 flex flex-col items-center">
                <EmptyStateHRNotes />
                <div className="mt-4 text-center text-sm text-soft-400">
                  {users.data?.find((user) => user.id == employeeIdNumber)
                    ? `${users.data?.find((user) => user.id == employeeIdNumber)?.firstName} ${users.data?.find((user) => user.id == employeeIdNumber)?.lastName} has no employee documents yet.`
                    : 'No employee documents found.'}
                  <br />
                  Click the button below to add one.
                  <br />
                  <br />
                  <Button onClick={() => setOpenUploadModal(true)} beforeContent={<UploadCloud2Line />}>
                    Upload Document
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
        <UploadEmployeeDocumentsModal
          open={openUploadModal}
          setOpen={setOpenUploadModal}
          onSuccess={() => {
            documents.refetch();
          }}
        />
        <ConfirmDialog
          open={showDeleteModal}
          setOpen={setShowDeleteModal}
          onConfirm={() => {
            if (selectedDocument) {
              handleDelete(selectedDocument);
            }
          }}
          data={selectedDocument}
          icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
          confirmButtonText="Delete"
          confirmButton={{
            color: 'error',
          }}
          title="Delete document"
          subtitle={
            selectedDocument ? (
              <div>
                You’re about to delete the document <span className="font-medium">{selectedDocument.name}</span>. Do you
                want to proceed?
              </div>
            ) : undefined
          }
        />
      </>
    );
  } else {
    return (
      <div className="flex size-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }
}

const mapDocumentsForTable = (documents: EmployeeDocuments[]): TableItem[] => {
  return documents?.map((document) => {
    return {
      ...document,
      userName: document.user ? `${document.user.firstName} ${document.user.lastName}` : '',
      actions: '',
    };
  });
};
