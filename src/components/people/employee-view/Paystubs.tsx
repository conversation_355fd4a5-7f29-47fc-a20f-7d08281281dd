import Spinner from 'components/icons/Spinner';
import { formatterTwoDecimalString } from 'utils/format';
import Table from 'components/elements/Table';
import { Earning, EmployeePaystubs } from 'interfaces/paystub';
import { getEmployeePaystubPdf, listEmployeePaystubs } from 'services/employee';
import { useReducer } from 'react';
import { logError } from 'utils/errorHandling';
import { ListResponse } from '@/interfaces/check';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import { useQuery } from '@tanstack/react-query';
import EmptyStateHRNotes from '@hammr-icons/EmptyStateHRNotes';

enum DownloadingIndicesActionType {
  ADD,
  REMOVE,
}

const downloadingIndicesReducer = (
  state: Set<number>,
  action: { type: DownloadingIndicesActionType; payload: number }
) => {
  switch (action.type) {
    case DownloadingIndicesActionType.ADD: {
      const downloadingIndices = new Set<number>(state);
      downloadingIndices.add(action.payload);
      return downloadingIndices;
    }
    case DownloadingIndicesActionType.REMOVE: {
      const downloadingIndices = new Set<number>(state);
      downloadingIndices.delete(action.payload);
      return downloadingIndices;
    }
  }
};

const downloadingIndicesInitialState = new Set<number>();

export default function Paystubs({
  checkEmployeeId,
  employeeFullName,
}: {
  checkEmployeeId: string;
  employeeFullName: string;
}) {
  const [downloadingIndicesState, downloadingIndicesDispatch] = useReducer(
    downloadingIndicesReducer,
    downloadingIndicesInitialState
  );

  const paystubs = useQuery<ListResponse<EmployeePaystubs>>({
    queryKey: ['employeePaystubs', checkEmployeeId],
    queryFn: () => listEmployeePaystubs(checkEmployeeId),
    enabled: !!checkEmployeeId,
  });

  const columns = ['date', 'total amount', 'total hours', ''];

  const getTotalHours = (earnings: Earning[]): number => {
    let totalHours = 0;
    earnings.forEach((earning) => {
      if (earning.hours !== null) {
        totalHours += earning.hours;
      }
    });
    return totalHours;
  };

  const mapPaystubsForTable = () => {
    return paystubs.data?.results.map((paystub: EmployeePaystubs, index: number) => [
      paystub.payday,
      formatterTwoDecimalString(paystub.summary.earnings),
      getTotalHours(paystub.earnings).toString(),
      <div
        key={index}
        className="w-20 cursor-pointer text-hammr-orange-600 hover:underline"
        onClick={() => {
          downloadingIndicesDispatch({
            payload: index,
            type: DownloadingIndicesActionType.ADD,
          });

          getEmployeePaystubPdf(checkEmployeeId, paystub.payroll, `${paystub.payday} Paystub`)
            .catch(logError)
            .finally(() => {
              downloadingIndicesDispatch({
                payload: index,
                type: DownloadingIndicesActionType.REMOVE,
              });
            });
        }}
      >
        {downloadingIndicesState.has(index) ? (
          <Spinner width="20" fill="blue" className="mx-auto animate-spin" />
        ) : (
          'Download'
        )}
      </div>,
    ]);
  };

  if (paystubs.isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!paystubs.data?.results.length) {
    return (
      <div className="mt-24 flex flex-col items-center">
        <EmptyStateHRNotes />
        <div className="mt-4 text-soft-400">{employeeFullName} has no paystubs yet.</div>
      </div>
    );
  }

  return <Table columns={columns} elements={mapPaystubsForTable()} className="max-w-[540px]" />;
}
