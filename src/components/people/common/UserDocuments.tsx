import Table from 'components/elements/Table';
import { CheckDocument, EmployeeDocument } from 'interfaces/documents';
import { useState } from 'react';
import { logError } from 'utils/errorHandling';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import EmptyStateHRNotes from '@hammr-icons/EmptyStateHRNotes';
import Button from '@hammr-ui/components/button';

interface UserDocumentsProps {
  documents: CheckDocument[];
  isLoading: boolean;
  userName?: string;
  onDownload: (documentId: string, documentLabel: string) => Promise<void>;
  nextUrl?: string;
  previousUrl?: string;
}

export default function UserDocuments({
  documents,
  isLoading,
  userName,
  onDownload,
  nextUrl,
  previousUrl,
}: UserDocumentsProps) {
  const [isDownloadingIndex, setIsDownloadingIndex] = useState<number>();
  const columns = ['document', 'date signed', ''];

  const mapDocumentsForTable = () => {
    return documents?.map((document: EmployeeDocument, index: number) => [
      document.label,
      document.filed_on,
      <div className="flex w-full flex-row items-center justify-center" key={index}>
        <Button
          variant="link"
          onClick={() => {
            setIsDownloadingIndex(index);
            onDownload(document.id, document.label)
              .then(() => setIsDownloadingIndex(undefined))
              .catch((err) => {
                logError(err);
                setIsDownloadingIndex(undefined);
              });
          }}
          loading={isDownloadingIndex !== undefined && isDownloadingIndex === index}
        >
          Download
        </Button>
      </div>,
    ]);
  };

  if (isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!documents?.length) {
    return (
      <div className="mt-24 flex flex-col items-center">
        <EmptyStateHRNotes />
        <div className="mt-4 text-soft-400">
          {userName ? `${userName} has no documents yet.` : 'No documents found.'}
        </div>
      </div>
    );
  }

  return (
    <Table
      columns={columns}
      className="max-w-[540px]"
      elements={mapDocumentsForTable()}
      nextUrl={nextUrl}
      previousUrl={previousUrl}
    />
  );
}
