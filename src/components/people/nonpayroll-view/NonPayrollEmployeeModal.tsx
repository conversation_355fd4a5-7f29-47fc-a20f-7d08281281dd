import { Controller, useForm } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import * as yup from 'yup';

import { useToast } from '@/hammr-ui/hooks/use-toast';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { TextField } from '@/components/elements/form/TextField';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { NumberFieldV2 } from '@/components/elements/form/NumberFieldV2';
import { SelectItem } from '@/hammr-ui/components/select';
import { PhoneInput, VALID_PHONE_NUMBER_REGEX } from '@/components/shared/PhoneInput';
import { handleFormError, logError } from '@/utils/errorHandling';
import UserLine from '@/hammr-icons/UserLine';
import { yupResolver } from '@/utils/yupResolver';
import { useAuth } from '@/hooks/useAuth';
import { FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import { useEffect } from 'react';

const roleOptions = [
  { name: 'Admin', value: 'ADMIN' },
  { name: 'Foreman', value: 'FOREMAN' },
  { name: 'Worker', value: 'WORKER' },
];

interface FormData {
  firstName: string;
  lastName: string;
  phone: string;
  role: string;
  position?: string;
  employeeId?: string;
  hourlyRate?: string;
}

const formSchema = yup.object().shape({
  firstName: yup.string().required('Please enter a first name'),
  lastName: yup.string().required('Please enter a last name'),
  phone: yup
    .string()
    .matches(VALID_PHONE_NUMBER_REGEX, {
      message: 'Not a valid phone number',
      excludeEmptyString: true,
    })
    .nullable()
    .notRequired(),
  role: yup.string().required('Please select a role'),
  position: yup.string(),
  employeeId: yup.string(),
  hourlyRate: yup.string(),
});

interface AddNonPayrollEmployeeModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback?: () => void;
}

const defaultValues = {
  firstName: '',
  lastName: '',
  phone: '',
  role: '',
  position: '',
  employeeId: '',
  hourlyRate: '',
};

export default function NonPayrollEmployeeModal({ open, setOpen, callback }: AddNonPayrollEmployeeModalProps) {
  const { toast } = useToast();
  const { addEmployeeToHammr } = useAuth();

  const form = useForm<FormData>({
    defaultValues,
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    form.reset(defaultValues);
  }, [open, form]);

  const addEmployeeMutation = useMutation({
    mutationFn: async (form: FormData) => {
      const data = {
        first_name: form.firstName,
        last_name: form.lastName,
        phone: form.phone,
        role: form.role,
        position: form.position,
        employeeId: form.employeeId,
        hourlyRate: form.hourlyRate ? parseFloat(form.hourlyRate) : undefined,
        fromPayroll: false,
      };

      addEmployeeToHammr(data);
    },
    onSuccess: () => {
      toast({
        title: 'Added Employee',
        description: (
          <div>
            Successfully added the employee{' '}
            <span className="font-medium">{`${form.getValues('firstName')} ${form.getValues('lastName')}`}</span>
          </div>
        ),
        toastVariant: 'success',
      });
      form.reset();
      callback?.();
      setOpen(false);
    },
    onError: (error) => {
      handleFormError(error, form.setError);
      logError(error);
      toast({
        title: 'Error',
        description: 'Unable to create employee',
        toastVariant: 'error',
      });
    },
  });

  const onSubmit = (data: FormData) => {
    addEmployeeMutation.mutate(data);
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Add Employee" icon={<UserLine />}>
      <FormV2
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setOpen(false)}
        isLoading={addEmployeeMutation.isPending}
        submitText="Add Employee"
      >
        <div className="space-y-5">
          <div className="grid grid-cols-2 gap-4">
            <TextField
              control={form.control}
              name="firstName"
              label="First Name"
              placeholder="Enter first name"
              required
              error={form.formState.errors.firstName?.message}
            />
            <TextField
              control={form.control}
              name="lastName"
              label="Last Name"
              placeholder="Enter last name"
              required
              error={form.formState.errors.lastName?.message}
            />
          </div>

          <div>
            <Controller
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem error={!!form.formState.errors.phone}>
                  <FormLabel className="mb-1">Phone number</FormLabel>
                  <PhoneInput
                    id="phone"
                    name={field.name}
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="(*************"
                    onBlur={field.onBlur}
                  />
                  {form.formState.errors.phone && (
                    <FormMessage className="mt-1">{form.formState.errors.phone?.message}</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <ControlledSelect
            control={form.control}
            name="role"
            label="Role"
            placeholder="Select a role"
            required
            error={form.formState.errors.role?.message}
          >
            {roleOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.name}
              </SelectItem>
            ))}
          </ControlledSelect>

          <TextField
            control={form.control}
            name="position"
            label="Position"
            placeholder="Enter position"
            error={form.formState.errors.position?.message}
          />

          <TextField
            control={form.control}
            name="employeeId"
            label="Employee ID"
            placeholder="Enter employee ID"
            error={form.formState.errors.employeeId?.message}
          />

          <NumberFieldV2
            control={form.control}
            name="hourlyRate"
            label="Hourly Rate"
            isFloat
            placeholder="0.00"
            prefix="$"
            error={form.formState.errors.hourlyRate?.message}
          />
        </div>
      </FormV2>
    </ModalV2>
  );
}
