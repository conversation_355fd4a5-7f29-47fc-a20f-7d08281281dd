import { Dispatch, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { Item, MultiSelect } from '@/hammr-ui/components/multi-select';
import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { HammrUser } from 'interfaces/user';
import { Input } from '@hammr-ui/components/input';
import Search2Line from '@hammr-icons/Search2Line';
import { capitalize } from '@/utils/stringHelper';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';

export type UsersTab = 'Active' | 'Archived';

interface UsersViewProps {
  tabs: UsersTab[];
  selectedTab: string;
  users: HammrUser[];
  children: ReactNode;
  onFilteredUsersChange: (users: HammrUser[]) => void;
  toggleView: Dispatch<SetStateAction<string>>;
}

export default function UsersView({
  tabs,
  selectedTab,
  users,
  children,
  onFilteredUsersChange,
  toggleView,
}: UsersViewProps) {
  const { user } = useAuth();
  const { company } = useCompany();
  const [searchTerm, setSearchTerm] = useState('');
  const [roles, setRoles] = useState<Item[]>([]);
  const [positions, setPositions] = useState<Item[]>([]);

  // Initialize roles and positions from users
  useEffect(() => {
    if (!users) {
      return;
    }
    const uniqueRoles = Array.from(new Set(users.map((user) => user.role).filter(Boolean)));
    const uniquePositions = Array.from(new Set(users.map((user) => user.position).filter(Boolean)));

    setRoles(
      uniqueRoles.map((role) => ({
        label: capitalize(role.toLowerCase()),
        value: role,
        isSelected: false,
      }))
    );

    setPositions(
      uniquePositions.map((position) => ({
        label: position,
        value: position,
        isSelected: false,
      }))
    );
  }, [users]);

  // Filter users based on search term and selected filters
  useEffect(() => {
    if (!users) {
      return;
    }
    const filteredUsers = users.filter((user) => {
      const matchesSearch = (user.firstName + ' ' + user.lastName).toLowerCase().includes(searchTerm.toLowerCase());

      const selectedRoles = roles.filter((role) => role.isSelected);
      const selectedPositions = positions.filter((position) => position.isSelected);
      const matchesRoles = selectedRoles.length ? selectedRoles.some((role) => user.role === role.value) : true;
      const matchesPositions = selectedPositions.length
        ? selectedPositions.some((position) => user.position === position.value)
        : true;
      return matchesSearch && matchesRoles && matchesPositions;
    });
    onFilteredUsersChange(filteredUsers);
  }, [users, searchTerm, roles, positions, onFilteredUsersChange]);

  if (!user) return null;

  if (user && company) {
    return (
      <div className="flex flex-grow flex-col">
        <div className="flex flex-row gap-3">
          <Tabs value={selectedTab} onValueChange={(value) => toggleView(value)} className="flex-1">
            <TabList>
              {tabs.map((tab) => (
                <TabItem key={tab} value={tab}>
                  {tab}
                </TabItem>
              ))}
            </TabList>
          </Tabs>
          <Input
            boxSize="small"
            beforeContent={<Search2Line className="size-5" />}
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <MultiSelect label="All Roles" items={roles} onChange={setRoles} buttonProps={{ className: 'w-48' }} />
          <MultiSelect
            label="All Positions"
            items={positions}
            onChange={setPositions}
            buttonProps={{ className: 'w-48' }}
          />
        </div>
        {children}
      </div>
    );
  }

  return (
    <div className="mt-1/5 flex flex-col items-center">
      <LoadingIndicator />
    </div>
  );
}
