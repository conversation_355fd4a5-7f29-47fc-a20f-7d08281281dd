import React, { Dispatch, SetStateAction } from 'react';
import { useToast } from 'hooks/useToast';
import { HammrUser } from 'interfaces/user';
import { updateHammrUser } from 'services/user';
import { logError, showErrorToast } from 'utils/errorHandling';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import ArchiveLine from '@hammr-icons/ArchiveLine';
import ArrowGoBackLine from '@hammr-icons/ArrowGoBackLine';
import { KeyIcon } from '@hammr-ui/components/KeyIcon';

interface ConfirmArchiveUserModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  currentRowData: HammrUser;
  isArchived: boolean;
  callback?: (isArchived: boolean) => void;
}

export default function ConfirmArchiveUserModal({
  open,
  setOpen,
  currentRowData,
  isArchived,
  callback,
}: ConfirmArchiveUserModalProps) {
  const { addToast } = useToast();

  const handleConfirm = async (user: HammrUser) => {
    try {
      await updateHammrUser(user.id, {
        isArchived: !isArchived,
      });

      addToast({
        title: `User ${isArchived ? 'unarchived' : 'archived'}`,
        description: `We have successfully ${isArchived ? 'unarchived' : 'archived'} the user`,
        type: 'success',
      });

      callback?.(!isArchived);
    } catch (err) {
      logError(err);
      showErrorToast(err, `Unable to ${isArchived ? 'unarchive' : 'archive'} user`);
      throw err;
    }
  };

  const getSubtitle = () => {
    const userName = (
      <span className="font-semibold">
        {currentRowData.firstName} {currentRowData.lastName}
      </span>
    );

    if (isArchived) {
      return <>Are you sure you want to unarchive {userName}? This user will be moved back to the active users list.</>;
    }
    return (
      <>
        Are you sure you want to archive {userName}? This user will no longer appear in the active users list but can be
        unarchived later.
      </>
    );
  };

  return (
    <ConfirmDialog
      open={open}
      setOpen={setOpen}
      data={currentRowData}
      onConfirm={handleConfirm}
      icon={<KeyIcon icon={isArchived ? <ArrowGoBackLine /> : <ArchiveLine />} />}
      title={`${isArchived ? 'Unarchive' : 'Archive'} User`}
      subtitle={open ? getSubtitle() : ''}
      confirmButtonText={isArchived ? 'Unarchive' : 'Archive'}
      confirmButton={{
        color: isArchived ? 'primary' : 'error',
      }}
    />
  );
}
