import { useEffect, useState } from 'react';

import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import Button from '@/hammr-ui/components/button';
import UsersView from 'components/people/nonpayroll-view/UsersView';
import ConfirmArchiveUserModal from 'components/people/nonpayroll-view/ConfirmArchiveUserModal';
import AddPayrollEmployeeModal from './AddPayrollEmployeeModal';
import AddPayrollContractorModal from './AddPayrollContractorModal';

import { HammrUser } from 'interfaces/user';
import People from './People';
import { usePeopleManagement } from 'pages/people';
import { PageHeader } from '@hammr-ui/components/PageHeader';
import UserLine from '@hammr-icons/UserLine';
import AddLine from '@hammr-icons/AddLine';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import EmptyStateTrainingAnalyis from '@hammr-icons/EmptyStateTrainingAnalyis';
import { WashingtonFieldsModal } from '@/components/people/sections/WashingtonStateEmployment';
import { useRouter } from 'next/router';

export default function PayrollUsers() {
  const { user } = useAuth();
  const { company } = useCompany();
  const router = useRouter();
  const { usersQuery, selectedTab, setSelectedTab, tabs } = usePeopleManagement();

  const [showWashingtonFieldsForm, setShowWashingtonFieldsForm] = useState<{
    userId: number;
    name: string;
    workersCompCodeId: number;
    checkEmployeeId: string;
  }>();

  const [showCreateEmployeeModal, setShowCreateEmployeeModal] = useState(false);
  const [showCreateContractorModal, setShowCreateContractorModal] = useState(false);
  const [showConfirmArchiveActionModal, setShowConfirmArchiveActionModal] = useState(false);

  const [selectedUser, setSelectedUser] = useState<HammrUser>();

  const [filteredUsers, setFilteredUsers] = useState<HammrUser[]>(usersQuery.data ?? []);

  const handleAddContractorCancel = () => {
    setShowCreateContractorModal(false);
    usersQuery.refetch();
  };

  useEffect(() => {
    if (router.query.modal === 'add') {
      setShowCreateEmployeeModal(true);
    }
  }, [router.query.modal]);

  const handleCloseCreateEmployeeModal = () => {
    setShowCreateEmployeeModal(false);
    router.replace('/people', undefined, { shallow: true });
  };

  if (!user || !user?.isCompanyAdmin || company?.isPayrollEnabled === false) return null;

  return (
    <>
      <PageHeader
        noPadding
        title="People"
        icon={<UserLine className="text-strong-950" />}
        headerRight={
          <div className="flex flex-row gap-4">
            <Button
              variant="outline"
              color="primary"
              beforeContent={<AddLine />}
              onClick={() => {
                setShowCreateContractorModal(true);
              }}
            >
              Add Contractor
            </Button>
            <Button
              color="primary"
              beforeContent={<AddLine />}
              onClick={() => {
                setShowCreateEmployeeModal(true);
              }}
            >
              Add Employee
            </Button>
          </div>
        }
      />

      <div className="mt-4 flex flex-grow">
        <UsersView
          tabs={tabs}
          selectedTab={selectedTab}
          users={usersQuery.data ?? []}
          onFilteredUsersChange={setFilteredUsers}
          toggleView={setSelectedTab}
        >
          {usersQuery.isPending ? (
            <div className="flex justify-center py-32">
              <LoadingIndicator />
            </div>
          ) : usersQuery.isError ? (
            <div className="flex justify-center py-32">
              <p className="text-sm text-error-base">
                An error occurred when fetching the employees or contractors data.
              </p>
            </div>
          ) : usersQuery.data?.length === 0 ? (
            <div className="flex flex-col items-center justify-center gap-4 py-32">
              <EmptyStateTrainingAnalyis />
              {selectedTab === 'Active' ? (
                <div className="flex flex-col items-center">
                  <div className="text-soft-400">There are no people yet.</div>
                  <div className="text-soft-400">Click the buttons below to add an employee or a contractor.</div>
                  <div className="mt-5 flex gap-4">
                    <Button
                      variant="outline"
                      color="primary"
                      beforeContent={<AddLine />}
                      onClick={() => {
                        setShowCreateEmployeeModal(true);
                      }}
                    >
                      Add Employee
                    </Button>
                    <Button
                      color="primary"
                      beforeContent={<AddLine />}
                      onClick={() => {
                        setShowCreateContractorModal(true);
                      }}
                    >
                      Add Contractor
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="text-soft-400">No archived people</div>
                  <div className="text-soft-400">There are no archived employees or contractors.</div>
                </div>
              )}
            </div>
          ) : (
            <People
              active={selectedTab === 'Active'}
              users={filteredUsers}
              archiveActionCallback={(user) => {
                setSelectedUser(user);
                setShowConfirmArchiveActionModal(true);
              }}
            />
          )}
        </UsersView>
      </div>

      <AddPayrollEmployeeModal
        open={showCreateEmployeeModal}
        setOpen={(isOpen) => {
          if (isOpen === false) {
            handleCloseCreateEmployeeModal();
          } else {
            setShowCreateEmployeeModal(true);
          }
        }}
        checkCompanyId={user?.checkCompanyId}
        existingEmployee={selectedUser ? { hammrUser: selectedUser } : undefined}
        handleSuccess={() => {
          usersQuery.refetch();
          setShowCreateEmployeeModal(false);
        }}
        showWashingtonFormStep={(data) => {
          handleCloseCreateEmployeeModal();
          setShowWashingtonFieldsForm(data);
        }}
      />

      <WashingtonFieldsModal
        open={!!showWashingtonFieldsForm}
        setOpen={() => setShowWashingtonFieldsForm(undefined)}
        userId={showWashingtonFieldsForm?.userId ?? 0}
        workersCompCodeId={showWashingtonFieldsForm?.workersCompCodeId}
        checkEmployeeId={showWashingtonFieldsForm?.checkEmployeeId ?? ''}
        onSuccess={() => {
          usersQuery.refetch();

          setShowWashingtonFieldsForm(undefined);
        }}
      />

      <AddPayrollContractorModal
        open={showCreateContractorModal}
        setOpen={(isOpen) => {
          setShowCreateContractorModal(isOpen);
        }}
        handleCancel={handleAddContractorCancel}
        handleSuccess={() => {
          usersQuery.refetch();
          setShowCreateContractorModal(false);
        }}
        checkCompanyId={user?.checkCompanyId}
      />

      {selectedUser ? (
        <ConfirmArchiveUserModal
          open={showConfirmArchiveActionModal}
          setOpen={setShowConfirmArchiveActionModal}
          isArchived={selectedTab === 'Archived'}
          currentRowData={selectedUser}
          callback={() => usersQuery.refetch()}
        />
      ) : undefined}
    </>
  );
}
