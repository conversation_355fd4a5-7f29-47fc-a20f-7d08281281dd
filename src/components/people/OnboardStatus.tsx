import StatusBadge from '@/hammr-ui/components/StatusBadge';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { Contractor } from '@/interfaces/contractor';
import { Employee } from '@/interfaces/employee';
import { Onboard } from '@/interfaces/onboard';

const OnboardStatusOptions = {
  completed: 'Completed',
  needs_attention: 'Needs Attention',
  blocking: 'Blocking',
};

interface Props {
  user?: Employee | Contractor;
}

export default function OnboardStatus({ user }: Props) {
  const status = user?.onboard?.status;

  if (!status) {
    return (
      <StatusBadge status="failed" className="w-fit">
        Not Added
      </StatusBadge>
    );
  }

  if (status === 'completed') {
    return (
      <StatusBadge status="completed" className="w-fit">
        Completed
      </StatusBadge>
    );
  }

  const blockingReasons = getBlockingReasons(user.onboard);
  const needsAttentionReasons = getNeedsAttentionReasons(user.onboard);

  return (
    <Tooltip
      contentClassName="p-3 !rounded-12"
      content={
        <section className="flex flex-col gap-1">
          {blockingReasons.length > 0 && (
            <>
              <h3 className="font-medium">Blocking</h3>
              {blockingReasons.map((reason, i) => (
                <p className="h-fit text-soft-400" key={i}>
                  {reason}
                </p>
              ))}
            </>
          )}
          {needsAttentionReasons.length > 0 && (
            <>
              <h3 className="font-medium">Needs Attention</h3>
              {needsAttentionReasons.map((reason, i) => (
                <p className="h-fit text-soft-400" key={i}>
                  {reason}
                </p>
              ))}
            </>
          )}
        </section>
      }
    >
      <StatusBadge status={status === 'needs_attention' ? 'pending' : 'failed'} className="w-fit">
        {OnboardStatusOptions[status]}
      </StatusBadge>
    </Tooltip>
  );
}

function getBlockingReasons(onboard: Onboard) {
  const reasons: string[] = [];

  onboard.blocking_steps.forEach((blockStep) => {
    switch (blockStep) {
      // for employees
      case 'company_defined_attributes':
        onboard.company_defined_attributes.forEach((attribute: { id: string; name: string; status: string }) => {
          reasons.push(`Employee is missing ${attribute.name} company defined attributes`);
        });
        break;

      case 'workplaces':
        reasons.push(`Employee does not have a primary workplace set`);
        break;

      case 'withholdings':
        onboard.withholdings.forEach((withholding: { id: string; name: string; status: string }) => {
          reasons.push(`Employee is missing ${withholding.name} withholdings setup`);
        });
        break;

      case 'employee_details':
        onboard.employee_details.forEach((employee_detail: string) => {
          if (employee_detail === 'start_date') reasons.push(`Employee is missing a start date`);
          else if (employee_detail === 'dob') reasons.push(`Employee is missing a date of birth`);
          else if (employee_detail === 'residence') reasons.push(`Employee is missing a residence`);
        });
        break;

      // for contractors
      case 'type':
        reasons.push("Contractor has not indicated whether they're an individual or business");
        break;
      case 'ein':
        reasons.push('Contractor has not submitted their Federal EIN');
        break;
      case 'address':
        reasons.push('Contractor has not provided an address');
        break;
    }
  });

  return reasons;
}

function getNeedsAttentionReasons(onboard: Onboard) {
  const needsAttentionSteps = onboard.remaining_steps.filter((step: string) => !onboard.blocking_steps.includes(step));

  const reasons: string[] = [];

  needsAttentionSteps.forEach((step) => {
    switch (step) {
      case 'ssn':
        onboard.ssn.forEach((substep: string) => {
          if (substep === 'ssn_submitted') reasons.push('Employee has not submitted their SSN');
          else if (substep === 'ssn_validated') reasons.push('The submitted SSN is invalid');
        });
        reasons.push(`Employee is missing a Social Security Number`);
        break;

      case 'withholdings':
        onboard.withholdings.forEach((withholding: { id: string; name: string; status: string }) => {
          reasons.push(`Employee is missing ${withholding.name} withholdings setup`);
        });
        break;

      case 'payment_method':
        onboard.payment_method.forEach((substep: string) => {
          if (substep === 'payment_method_preference_set')
            reasons.push('The employee has not chosen to either be paid manually or via direct deposit');
          else if (substep === 'bank_account_exists')
            reasons.push("The employee's bank account has not been created in the Check system");
          else if (substep === 'bank_account_verified')
            reasons.push("The employee's bank account has not been verified by Plaid");
        });
        break;

      case 'company_defined_attributes':
        onboard.company_defined_attributes.forEach((attribute: { id: string; name: string; status: string }) => {
          reasons.push(`Employee is missing ${attribute.name} company defined attributes`);
        });
        break;
    }
  });

  return reasons;
}
