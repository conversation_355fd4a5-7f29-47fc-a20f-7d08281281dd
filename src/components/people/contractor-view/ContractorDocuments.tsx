import { ContractorTaxDocument } from 'interfaces/documents';
import { contractorDocumentDownload, listContractorTaxDocuments } from 'services/documents';
import { useQuery } from '@tanstack/react-query';
import UserDocuments from '../common/UserDocuments';
import { ListResponse } from '@/interfaces/check';

interface ContractorDocumentsProps {
  contractorId: string;
  contractorName?: string;
}

export default function ContractorDocuments({ contractorId, contractorName }: ContractorDocumentsProps) {
  const documentsQuery = useQuery<ListResponse<ContractorTaxDocument>>({
    queryKey: ['contractorTaxDocuments', contractorId],
    queryFn: () => listContractorTaxDocuments(contractorId),
    enabled: !!contractorId,
  });

  return (
    <UserDocuments
      documents={documentsQuery.data?.results || []}
      isLoading={documentsQuery.isLoading}
      userName={contractorName}
      onDownload={contractorDocumentDownload}
      nextUrl={documentsQuery.data?.next}
      previousUrl={documentsQuery.data?.previous}
    />
  );
}
