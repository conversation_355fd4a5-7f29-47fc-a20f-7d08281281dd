import { EnrichedContractor } from 'interfaces/contractor';
import Spinner from 'components/icons/Spinner';
import { useState } from 'react';
import EditContractorInfo from './EditContractorInfo';
import importScript from 'hooks/importScript';
import { useRouter } from 'next/router';
import { GeneralInfo } from '../sections/GeneralInfo';
import { EmergencyContactInfo } from '../sections/EmergencyContactInfo';
import { EmploymentInfo } from '../sections/EmploymentInfo';
import { CompensationCard, PaymentInformationCard } from '../sections/FinancialInfo';
import { formatSalary } from '@/utils/calculateEarnings';
import EmergencyContactInfoEdit from '../sections/EmergencyContactInfoEdit';
import EmployementInfoEdit from '../sections/EmployementInfoEdit';
import FinancialInfoEdit from '../sections/FinancialInfoEdit';
import { useCompany } from '@/hooks/useCompany';
import { getActiveEarningRate } from '@/utils/temporalUtils';
import { getCompensationTypeFromPeriod } from '@/utils/format';

export default function ContractorInfo({
  contractor,
  refresh,
  allowEdit,
}: {
  contractor: EnrichedContractor;
  refresh: any;
  allowEdit: boolean;
}) {
  importScript('https://cdn.checkhq.com/component-initialize.js');

  const router = useRouter();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editEmergencyInfo, setEditEmergencyInfo] = useState(false);
  const [editEmployementInfo, setEditEmployementInfo] = useState(false);
  const [editFinancialInfo, setEditFinancialInfo] = useState(false);

  const { company } = useCompany();

  // Get the current active earning rate using timezone-aware logic
  const currentActiveEarningRate = getActiveEarningRate(
    contractor.hammrUser.earningRates || [],
    company?.timezone || 'America/Los_Angeles',
    'REG'
  );

  const handleContractorInfoSave = () => {
    setIsEditModalOpen(false);
    refresh();
  };

  const handleEmergencyInfoSave = () => {
    setEditEmergencyInfo(false);
    refresh();
  };

  const handleCompensationInfoSave = () => {
    setEditFinancialInfo(false);
    refresh();
  };

  if (!contractor) {
    return (
      <div>
        <div className="mt-1/5 w-full">
          <div className="mx-auto">
            <Spinner width="40" fill="blue" className="mx-auto my-10 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="cards-grid">
        <GeneralInfo
          firstName={contractor.checkContractor.first_name}
          lastName={contractor.checkContractor.last_name}
          phone={contractor.hammrUser.phone}
          email={contractor.hammrUser.email}
          gender={contractor.hammrUser.gender}
          ethnicity={contractor.hammrUser.ethnicity}
          veteranStatus={contractor.hammrUser.veteranStatus}
          address={contractor.checkContractor.address}
          ssn_last_four={contractor.checkContractor.ssn_last_four}
          onEdit={() => setIsEditModalOpen(true)}
          allowEdit={allowEdit}
          isContractor={true}
        />
        <EmploymentInfo
          onEdit={() => setEditEmployementInfo(true)}
          allowEdit={allowEdit}
          employeeId={contractor.hammrUser.employeeId}
          position={contractor.hammrUser.position}
          role={contractor.hammrUser.role}
          managerName={[contractor.hammrUser.manager?.firstName, contractor.hammrUser.manager?.lastName]
            .filter(Boolean)
            .join(' ')}
          startDate={contractor.checkContractor?.start_date}
          workerClassification={contractor.hammrUser.workerClassification}
          departmentName={contractor.hammrUser?.department?.name || null}
        />
        <EmergencyContactInfo
          firstName={contractor.hammrUser.emergencyContactFirstName}
          lastName={contractor.hammrUser.emergencyContactLastName}
          relationship={contractor.hammrUser.emergencyContactRelationship}
          phone={contractor.hammrUser.emergencyContactPhone}
          onEdit={() => setEditEmergencyInfo(true)}
          allowEdit={allowEdit}
        />
        {contractor.checkContractor.id && (
          <PaymentInformationCard
            paymentPreference={contractor.checkContractor?.payment_method_preference}
            isEmployee={false}
            checkId={contractor.checkContractor.id}
            bankInfo={contractor.bankAccount?.plaid_bank_account || contractor.bankAccount?.raw_bank_account}
          />
        )}
        <CompensationCard
          compensationType={getCompensationTypeFromPeriod(currentActiveEarningRate?.period)}
          compensation={
            currentActiveEarningRate
              ? formatSalary(parseFloat(currentActiveEarningRate.amount), currentActiveEarningRate.period)
              : undefined
          }
          startDate={currentActiveEarningRate?.startDate}
          weeklyHours={currentActiveEarningRate?.weeklyHours}
          reimbursementPerDiem={contractor.hammrUser.employeeReimbursement?.reimbursementAmount}
          onEdit={() => setEditFinancialInfo(true)}
          userId={contractor.hammrUser.id}
        />
      </div>

      <EditContractorInfo
        contractor={contractor}
        handleSuccess={handleContractorInfoSave}
        handleCancel={() => setIsEditModalOpen(false)}
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
      />

      <EmergencyContactInfoEdit
        open={editEmergencyInfo}
        user={contractor.hammrUser}
        handleSuccess={handleEmergencyInfoSave}
        setOpen={() => setEditEmergencyInfo(false)}
      />
      <EmployementInfoEdit
        open={editEmployementInfo}
        checkEmployee={contractor.checkContractor}
        user={contractor.hammrUser}
        handleSuccess={handleEmergencyInfoSave}
        setOpen={() => setEditEmployementInfo(false)}
      />
      <FinancialInfoEdit
        open={editFinancialInfo}
        employee={contractor}
        handleSuccess={handleCompensationInfoSave}
        setOpen={() => setEditFinancialInfo(false)}
      />
    </div>
  );
}
