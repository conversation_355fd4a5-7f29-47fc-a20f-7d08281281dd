import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { updateHammrUser } from 'services/user';
import { UpdateUser } from 'interfaces/user';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { EnrichedContractor } from 'interfaces/contractor';
import Alert from '@hammr-ui/components/Alert';
import {
  GeneralInfoForm,
  generalInfoFormSchema,
  GeneralInfoValues,
} from '@/components/people/sections/GeneralInfoForm';
import { yupResolver } from 'utils/yupResolver';
import { useMutation } from '@tanstack/react-query';
import { RiUserLine } from '@remixicon/react';
import { addToast } from '@/hooks/useToast';

type FormData = Omit<GeneralInfoValues, 'dob' | 'workersCompCodeId'>;

const formSchema = generalInfoFormSchema.omit(['dob', 'workersCompCodeId']);

const EditContractorInfo: React.FC<{
  contractor: EnrichedContractor;
  handleSuccess: () => void;
  handleCancel: () => void;
  open: boolean;
  setOpen: (open: boolean) => void;
}> = ({ contractor, handleSuccess, handleCancel, open, setOpen }) => {
  const form = useForm<FormData>({
    // eslint-disable-next-line
    // @ts-ignore TS is standing in our way here. Might be due to TS non-strict mode
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    const defaultValues: FormData = {
      firstName: contractor?.checkContractor?.first_name || '',
      lastName: contractor?.checkContractor?.last_name || '',
      phone: contractor.hammrUser.phone,
      email: contractor?.hammrUser.email ?? '',
      gender: contractor?.hammrUser.gender ?? null,
      ethnicity: contractor?.hammrUser.ethnicity ?? null,
      veteranStatus: contractor?.hammrUser.veteranStatus ?? null,
      address: contractor?.checkContractor?.address?.line1 || '',
      city: contractor?.checkContractor?.address?.city || '',
      state: contractor?.checkContractor?.address?.state || '',
      postalCode: contractor?.checkContractor?.address?.postal_code || '',
      sendOnboardingLink: false,
    };
    form.reset(defaultValues);
  }, [contractor, form]);

  const contractorMutation = useMutation({
    mutationFn(data: FormData) {
      const updatedHammrUser: UpdateUser = {
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phone,
        address1: data.address,
        email: data.email,
        gender: data.gender,
        ethnicity: data.ethnicity,
        veteranStatus: data.veteranStatus,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        company: contractor.checkContractor.company,
        type: 'contractor',
      };

      return updateHammrUser(contractor.hammrUser.id, updatedHammrUser);
    },
    onSuccess(updatedContractor) {
      handleSuccess();

      addToast({
        title: 'Edited Contractor Info',
        description: (
          <div>
            Successfully updated the info of the contractor{' '}
            <span className="font-medium">{`${updatedContractor.firstName} ${updatedContractor.lastName}`}</span>
          </div>
        ),
        type: 'success',
      });
    },
  });

  useEffect(() => {
    if (open) {
      form.reset();
    }
  }, [open, form]);

  return (
    <ModalV2 icon={<RiUserLine />} open={open} setOpen={setOpen} title="Edit Contractor Information">
      <FormV2
        onSubmit={form.handleSubmit((formData) => contractorMutation.mutate(formData))}
        onCancel={handleCancel}
        isLoading={contractorMutation.isPending}
        submitText="Save"
      >
        <div className="space-y-6">
          <Alert status="information" size="small">
            After edits, {contractor.checkContractor.first_name} will need to re-onboard for payroll purposes.
          </Alert>

          <GeneralInfoForm
            form={form}
            showDOB={false}
            showWorkersCompCode={false}
            showPhone={true}
            showAddress={true}
            userExists={true}
          />
        </div>
      </FormV2>
    </ModalV2>
  );
};

export default EditContractorInfo;
