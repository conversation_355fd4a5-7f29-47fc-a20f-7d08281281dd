import { formatterTwoDecimalString } from 'utils/format';
import Table from 'components/elements/Table';
import { ContractorPayment } from 'interfaces/contractor-payment';
import { getContractorPaymentPdf, listContractorPayments } from 'services/contractor';
import { useReducer } from 'react';
import { logError } from 'utils/errorHandling';
import { ListResponse } from '@/interfaces/check';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import { useQuery } from '@tanstack/react-query';
import EmptyStateHRNotes from '@hammr-icons/EmptyStateHRNotes';
import Spinner from 'components/icons/Spinner';

enum DownloadingIndicesActionType {
  ADD,
  REMOVE,
}

const downloadingIndicesReducer = (
  state: Set<number>,
  action: { type: DownloadingIndicesActionType; payload: number }
) => {
  switch (action.type) {
    case DownloadingIndicesActionType.ADD: {
      const downloadingIndices = new Set<number>(state);
      downloadingIndices.add(action.payload);
      return downloadingIndices;
    }
    case DownloadingIndicesActionType.REMOVE: {
      const downloadingIndices = new Set<number>(state);
      downloadingIndices.delete(action.payload);
      return downloadingIndices;
    }
  }
};

const downloadingIndicesInitialState = new Set<number>();

export default function Payments({ checkContractorId }: { checkContractorId: string }) {
  const [downloadingIndicesState, downloadingIndicesDispatch] = useReducer(
    downloadingIndicesReducer,
    downloadingIndicesInitialState
  );

  const contractorPayments = useQuery<ListResponse<ContractorPayment>>({
    queryKey: ['contractor-payments', checkContractorId],
    queryFn: () => listContractorPayments(checkContractorId),
    enabled: !!checkContractorId,
  });

  const columns = ['date', 'total amount', ''];

  const mapPaymentsForTable = () => {
    return contractorPayments.data?.results.map((payment: ContractorPayment, index: number) => [
      payment.payday,
      formatterTwoDecimalString(payment.net_pay),
      <div
        key={index}
        className="w-20 cursor-pointer text-hammr-orange-600 hover:underline"
        onClick={() => {
          downloadingIndicesDispatch({
            payload: index,
            type: DownloadingIndicesActionType.ADD,
          });

          getContractorPaymentPdf(checkContractorId, payment.payroll, `${payment.payday} Payment`)
            .catch(logError)
            .finally(() => {
              downloadingIndicesDispatch({
                payload: index,
                type: DownloadingIndicesActionType.REMOVE,
              });
            });
        }}
      >
        {downloadingIndicesState.has(index) ? (
          <Spinner width="20" fill="blue" className="mx-auto animate-spin" />
        ) : (
          'Download'
        )}
      </div>,
    ]);
  };

  if (contractorPayments.isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!contractorPayments.data?.results.length) {
    return (
      <div className="mt-24 flex flex-col items-center">
        <EmptyStateHRNotes />
        <div className="mt-4 text-soft-400">No payments found.</div>
      </div>
    );
  }

  return <Table columns={columns} elements={mapPaymentsForTable()} />;
}
