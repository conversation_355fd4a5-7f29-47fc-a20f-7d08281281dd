import { ComponentProps, Dispatch, SetStateAction } from 'react';
import AddEmployee from './AddEmployee';
import UserLine from '@hammr-icons/UserLine';
import { EnrichedEmployee } from '@/interfaces/employee';
import { ModalV2 } from '@/components/elements/ModalV2';

interface AddPayrollEmployeeModalProps {
  handleSuccess: ComponentProps<typeof AddEmployee>['handleSuccess'];
  checkCompanyId: string;
  open: boolean;
  showWashingtonFormStep: ComponentProps<typeof AddEmployee>['showWashingtonFormStep'];
  setOpen: Dispatch<SetStateAction<boolean>>;
  existingEmployee?: EnrichedEmployee;
  isForAddingToPayroll?: boolean;
}

export default function AddPayrollEmployeeModal({
  handleSuccess,
  checkCompanyId,
  open,
  setOpen,
  showWashingtonFormStep,
  existingEmployee,
  isForAddingToPayroll,
}: AddPayrollEmployeeModalProps) {
  return (
    <ModalV2
      icon={<UserLine />}
      open={open}
      setOpen={setOpen}
      title={isForAddingToPayroll ? 'Add To Payroll' : 'Add Employee'}
    >
      <AddEmployee
        handleCancel={() => setOpen(false)}
        handleSuccess={handleSuccess}
        checkCompanyId={checkCompanyId}
        existingEmployee={existingEmployee}
        showWashingtonFormStep={showWashingtonFormStep}
        isForAddingToPayroll={isForAddingToPayroll}
      />
    </ModalV2>
  );
}
