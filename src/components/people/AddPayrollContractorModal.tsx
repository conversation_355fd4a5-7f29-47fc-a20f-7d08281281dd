import { Dispatch, SetStateAction } from 'react';
import AddContractor from './AddContractor';
import { ModalV2 } from '@/components/elements/ModalV2';
import UserLine from '@hammr-icons/UserLine';
import { HammrUser } from 'interfaces/user';

interface AddPayrollContractorModalProps {
  handleCancel: () => void;
  handleSuccess: () => void;
  checkCompanyId: string;
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  selectedUser?: HammrUser;
}

export default function AddPayrollContractorModal({
  handleCancel,
  handleSuccess,
  checkCompanyId,
  open,
  setOpen,
}: AddPayrollContractorModalProps) {
  return (
    <ModalV2 icon={<UserLine />} open={open} setOpen={setOpen} title="Add Contractor">
      <AddContractor handleCancel={handleCancel} handleSuccess={handleSuccess} checkCompanyId={checkCompanyId} />
    </ModalV2>
  );
}
