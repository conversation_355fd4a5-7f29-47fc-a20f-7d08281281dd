import { useForm } from 'react-hook-form';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { addHammrUserToPayroll, createHammrUser } from 'services/user';
import { CreateHammrUser, HammrUser, WorkerClassification } from 'interfaces/user';
import * as yup from 'yup';
import { FormV2 } from '@/components/elements/Form';
import Alert from '@/hammr-ui/components/Alert';
import { useMutation, useQuery } from '@tanstack/react-query';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';
import { listWorkplaces } from 'services/workplace';
import {
  GeneralInfoForm,
  generalInfoFormSchema,
  GeneralInfoValues,
} from '@/components/people/sections/GeneralInfoForm';
import {
  FinancialInfoForm,
  financialInfoFormSchema,
  FinancialInfoValues,
} from '@/components/people/sections/FinancialInfoForm';
import { EnrichedEmployee } from '@/interfaces/employee';
import dayjs from 'dayjs';
import { getCompensationTypeFromHammrEarningRate } from '@/services/earning-rate';
import { FormSection } from './sections/FormSection';
import { UserPhotoForm, userPhotoFormSchema, UserPhotoFormValuesValues } from './sections/UserPhotoForm';
import {
  EmergencyContactForm,
  emergencyContactInfoFormSchema,
  EmergencyContactValues,
} from './sections/EmergencyContactForm';
import {
  EmployementContactForm,
  employementContactInfoFormSchema,
  EmployementContactValues,
} from './sections/EmployementInfoForm';
import { CompanyDefinedAttributesForm } from './sections/WashingtonStateEmployment';
import { yupResolver } from '@hookform/resolvers/yup';
import { departmentsService } from '@/services/departments';

interface FormData
  extends GeneralInfoValues,
    UserPhotoFormValuesValues,
    FinancialInfoValues,
    EmergencyContactValues,
    EmployementContactValues {
  workplace: string;
  workplaceState?: string;
  companyDefinedAttributes?: CompanyDefinedAttributesForm;
  departmentId: number;
}

const formSchema = yup
  .object()
  .concat(generalInfoFormSchema)
  .concat(userPhotoFormSchema)
  .concat(financialInfoFormSchema)
  .concat(emergencyContactInfoFormSchema)
  .concat(employementContactInfoFormSchema)
  .shape({
    workplace: yup.string().required('Please select a workplace'),
  });

export default function AddEmployee({
  handleCancel,
  handleSuccess,
  checkCompanyId,
  existingEmployee,
  showWashingtonFormStep,
  isForAddingToPayroll,
}: {
  handleCancel: () => void;
  checkCompanyId: string;
  handleSuccess: (info: { name: string }) => void;
  showWashingtonFormStep: (data: {
    userId: number;
    workersCompCodeId: number;
    checkEmployeeId: string;
    name: string;
  }) => void;
  existingEmployee?: EnrichedEmployee;
  isForAddingToPayroll?: boolean;
}) {
  const form = useForm<FormData>({
    defaultValues: {
      profilePhotoObjectId: existingEmployee?.hammrUser.profilePhotoObjectId,
      firstName: existingEmployee?.hammrUser.firstName,
      lastName: existingEmployee?.hammrUser.lastName,
      dob: existingEmployee?.checkEmployee?.dob,
      phone: existingEmployee?.hammrUser.phone,
      sendOnboardingLink: true,
      address: existingEmployee?.checkEmployee?.residence.line1,
      city: existingEmployee?.checkEmployee?.residence.city,
      state: existingEmployee?.checkEmployee?.residence.state,
      postalCode: existingEmployee?.checkEmployee?.residence.postal_code,
      departmentId: existingEmployee?.hammrUser.department?.id,
      workplace: existingEmployee?.checkEmployee?.primary_workplace,
      position: existingEmployee?.hammrUser.position,
      startDate: existingEmployee?.checkEmployee?.start_date
        ? dayjs(existingEmployee?.checkEmployee?.start_date).toDate()
        : null,
      employmentType: existingEmployee?.primaryEarningRate
        ? getCompensationTypeFromHammrEarningRate(existingEmployee.primaryEarningRate)
        : 'Hourly',
      pay: existingEmployee?.primaryEarningRate?.amount
        ? parseFloat(existingEmployee?.primaryEarningRate?.amount).toFixed(2)
        : undefined,
      weeklyHours: existingEmployee?.primaryEarningRate?.weeklyHours ?? 40,
      workersCompCodeId: existingEmployee?.hammrUser.workersCompCode?.id.toString(),
      email: existingEmployee?.hammrUser.email,
      emergencyContactFirstName: existingEmployee?.hammrUser.emergencyContactFirstName,
      emergencyContactLastName: existingEmployee?.hammrUser.emergencyContactLastName,
      emergencyContactPhone: existingEmployee?.hammrUser.emergencyContactPhone,
      emergencyContactRelationship: existingEmployee?.hammrUser.emergencyContactRelationship,
      employeeId: existingEmployee?.hammrUser.employeeId,
      ethnicity: existingEmployee?.hammrUser.ethnicity,
      gender: existingEmployee?.hammrUser.gender,
      veteranStatus: existingEmployee?.hammrUser.veteranStatus,
      managerId: existingEmployee?.hammrUser.managerId,
      role: existingEmployee?.hammrUser.role,
    },
    // eslint-disable-next-line
    // @ts-ignore TS is standing in our way here. Might be due to TS non-strict mode
    resolver: yupResolver(formSchema),
  });

  const { toast } = useToast();

  const workplaces = useQuery({
    queryKey: ['workplaces'],
    queryFn: () => listWorkplaces(checkCompanyId),
  });

  const departments = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const result = await departmentsService.list();
      return {
        departments: result.departments.sort((a, b) => a.name.localeCompare(b.name)),
      };
    },
  });

  const employeeMutation = useMutation({
    async mutationFn(data: FormData) {
      const employee: CreateHammrUser = {
        lastName: data.lastName,
        firstName: data.firstName,
        dob: dayjs(data.dob).format('YYYY-MM-DD'),
        phoneNumber: data.phone,
        sendOnboardingLink: data.sendOnboardingLink,
        email: data.email,
        emergencyContactFirstName: data.emergencyContactFirstName,
        emergencyContactLastName: data.emergencyContactLastName,
        emergencyContactPhone: data.emergencyContactPhone,
        emergencyContactRelationship: data.emergencyContactRelationship,
        employeeId: data.employeeId,
        ethnicity: data.ethnicity,
        gender: data.gender,
        profilePhotoObjectId: data.profilePhotoObjectId,
        veteranStatus: data.veteranStatus,
        managerId: data.managerId,
        workerClassification: WorkerClassification.Employee,
        role: data.role?.toUpperCase() ?? 'WORKER',
        address1: data.address,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        position: data.position,
        startDate: dayjs(data.startDate).format('YYYY-MM-DD'),
        workplaces: [data.workplace],
        shouldAddToPayroll: true,
        target: 'employees',
        workersCompCodeId: data.workersCompCodeId ? parseInt(data.workersCompCodeId) : undefined,
        hourlyRate: data.employmentType === 'Hourly' ? parseFloat(data.pay.replace(/[^\d.]/g, '')) : undefined,
        salary: data.employmentType === 'Salaried' ? parseFloat(data.pay.replace(/[^\d.]/g, '')) : undefined,
        weeklyHours: data.employmentType === 'Salaried' ? data.weeklyHours : undefined,
        departmentId: data.departmentId,
      };

      let user: HammrUser;
      if (existingEmployee) {
        user = await addHammrUserToPayroll(existingEmployee.hammrUser.id, employee).then((response) => response.user);
      } else {
        user = await createHammrUser(employee).then((response) => response.user);
      }

      return user;
    },
    onSuccess(user, formData) {
      const isAnySelectedWorkplaceInWashington =
        workplaces.data?.find((workplace) => formData.workplace.includes(workplace.id))?.address.state === 'WA';

      const name = `${user.firstName} ${user.lastName}`;

      if (isAnySelectedWorkplaceInWashington) {
        showWashingtonFormStep({
          userId: user.id,
          workersCompCodeId: user.workersCompCode?.id,
          checkEmployeeId: user.checkEmployeeId,
          name,
        });
      } else {
        handleSuccess({ name });
      }

      toast({
        title: 'Success',
        description: 'Employee successfully added to payroll',
        toastVariant: 'success',
      });
    },
  });

  return (
    <FormV2
      onSubmit={form.handleSubmit((formData) => employeeMutation.mutate(formData))}
      onCancel={handleCancel}
      isLoading={employeeMutation.isPending}
      submitText={isForAddingToPayroll ? 'Add To Payroll' : 'Add Employee'}
    >
      <div className="space-y-6">
        <Alert status="information" size="x-small">
          Please provide some basic info about your employee. Separate instructions will be sent to the employee to set
          up payout options.
        </Alert>

        <FormSection title="Personal Information">
          <UserPhotoForm form={form} />
          <GeneralInfoForm
            form={form}
            userExists={false}
            showWorkersCompCode={true}
            showPhone={true}
            showAddress={true}
            showDOB={true}
          />
        </FormSection>

        <hr className="border-soft-200" />

        <FormSection title="Emergency Contact">
          <EmergencyContactForm form={form} />
        </FormSection>

        <hr className="border-soft-200" />

        <FormSection title="Employment Information">
          <EmployementContactForm form={form} workerClassification={WorkerClassification.Employee} />
        </FormSection>

        <ControlledSelect
          control={form.control}
          name="workplace"
          label="Workplace"
          placeholder="Select a workplace"
          required
          error={form.formState.errors.workplace?.message}
        >
          {workplaces.data?.map((workplace) => (
            <SelectItem key={workplace.id} value={workplace.id}>
              {workplace.name}
            </SelectItem>
          ))}
        </ControlledSelect>

        <hr className="border-soft-200" />

        <FormSection title="Financial Information">
          <FinancialInfoForm form={form} isOnPayroll={true} />
        </FormSection>
      </div>
    </FormV2>
  );
}
