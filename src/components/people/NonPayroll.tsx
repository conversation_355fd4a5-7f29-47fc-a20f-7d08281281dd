import { useState } from 'react';

import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import Button from '@/hammr-ui/components/button';
import People from 'components/people/People';
import UsersView from 'components/people/nonpayroll-view/UsersView';
import { HammrUser } from 'interfaces/user';
import NonPayrollEmployeeModal from '@/components/people/nonpayroll-view/NonPayrollEmployeeModal';
import ConfirmArchiveUserModal from 'components/people/nonpayroll-view/ConfirmArchiveUserModal';
import { usePeopleManagement } from 'pages/people';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import EmptyStateTrainingAnalyis from '@/hammr-icons/EmptyStateTrainingAnalyis';
import AddLine from '@hammr-icons/AddLine';
import UserLine from '@hammr-icons/UserLine';
import { PageHeader } from '@hammr-ui/components/PageHeader';

export default function NonPayrollUsers() {
  const { user } = useAuth();
  const { company } = useCompany();
  const { usersQuery, selectedTab, setSelectedTab, tabs } = usePeopleManagement();

  const [showCreateEmployeeModal, setShowCreateEmployeeModal] = useState(false);
  const [showConfirmArchiveActionModal, setShowConfirmArchiveActionModal] = useState(false);
  const [filteredUsers, setFilteredUsers] = useState<HammrUser[]>(usersQuery.data);
  const [selectedEmployee, setSelectedEmployee] = useState<HammrUser>(null);

  if (!user || !user?.isCompanyAdmin || company.isPayrollEnabled) return null;

  return (
    <>
      <PageHeader
        title="People"
        noPadding
        icon={<UserLine className="text-strong-950" />}
        headerRight={
          <div className="flex flex-row gap-4">
            <Button
              color="primary"
              beforeContent={<AddLine />}
              onClick={() => {
                setShowCreateEmployeeModal(true);
                setSelectedEmployee(null);
              }}
            >
              Add Employee
            </Button>
          </div>
        }
      />
      <div className="mt-5 flex flex-grow">
        <UsersView
          tabs={tabs}
          selectedTab={selectedTab}
          users={usersQuery.data}
          onFilteredUsersChange={setFilteredUsers}
          toggleView={setSelectedTab}
        >
          {usersQuery.isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingIndicator size="lg" showText={false} />
            </div>
          ) : usersQuery.data?.length === 0 ? (
            <div className="flex flex-col items-center justify-center gap-4 py-12">
              <EmptyStateTrainingAnalyis />
              {selectedTab === 'Active' ? (
                <div className="flex flex-col items-center">
                  <div className="text-soft-400">There are no people yet.</div>
                  <div className="text-soft-400">Click the button below to add an employee.</div>
                  <Button
                    variant="filled"
                    color="primary"
                    beforeContent={<AddLine />}
                    onClick={() => setShowCreateEmployeeModal(true)}
                  >
                    Add Employee
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="text-soft-400">No archived people</div>
                  <div className="text-soft-400">There are no archived employees.</div>
                </div>
              )}
            </div>
          ) : (
            <People
              active={selectedTab === 'Active'}
              users={filteredUsers}
              archiveActionCallback={(user) => {
                setSelectedEmployee(user);
                setShowConfirmArchiveActionModal(true);
              }}
            />
          )}
        </UsersView>
      </div>

      <NonPayrollEmployeeModal
        open={showCreateEmployeeModal}
        setOpen={setShowCreateEmployeeModal}
        callback={() => usersQuery.refetch()}
      />
      <ConfirmArchiveUserModal
        open={showConfirmArchiveActionModal}
        setOpen={setShowConfirmArchiveActionModal}
        isArchived={selectedTab === 'Archived'}
        currentRowData={selectedEmployee}
        callback={() => usersQuery.refetch()}
      />
    </>
  );
}
