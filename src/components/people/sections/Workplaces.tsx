import { useEffect, useState } from 'react';
import Button from '@hammr-ui/components/button';
import Alert from '@hammr-ui/components/Alert';
import WorkplacesForm from './WorkplacesForm';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { listWorkplaces } from '@/services/workplace';
import { useToast } from '@hammr-ui/hooks/use-toast';
import { SelectItem } from '@hammr-ui/components/select';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import PencilLine from '@hammr-icons/PencilLine';
import { FormV2 } from '@/components/elements/Form';
import { useForm, useWatch } from 'react-hook-form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { apiRequestCheck } from '@/utils/requestHelpers';
import { CheckCompanyDefinedAttribute, CheckCompanyDefinedAttributeValue } from '@/interfaces/check';
import {
  CompanyDefinedAttributesForm,
  companyDefinedAttributesSchema,
  getCompanyDefinedAttributesFormData,
  saveWashingtonFields,
  WashingtonStateEmploymentFormFields,
} from './WashingtonStateEmployment';
import * as yup from 'yup';
import { InferType } from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import Promise from 'lie';
import dayjs from 'dayjs';
import { formatUSD } from '@/utils/format';
import { updateHammrUser } from '@/services/user';

interface UpdateWorkplaceRequest {
  workplaces: string[];
  workersCompCode: string;
  companyDefinedAttributes?: CheckCompanyDefinedAttributeValue[];
}

interface WorkplacesProps {
  title?: string;
  selectedWorkplaceIds: string[];
  allowEdit?: boolean;
  isFetchingEmployee: boolean;
  companyId: string;
  userId: number;
  workersCompCodeId: number;
  employeeId: string;
  fullName: string;
}

const waStateFieldsToJSXMappings = [
  {
    field: 'wa_ltc_exemption',
    title: 'Washington LTC Exemption',
    value: (field: CheckCompanyDefinedAttribute) => {
      return field?.value ? (field.value === 'true' ? 'Yes' : 'No') : '-';
    },
  },

  {
    field: 'wa_ltc_exemption',
    title: 'Washington LTC Exemption Effective Date',
    isActive: (field: CheckCompanyDefinedAttribute) => {
      return field?.value === 'true';
    },
    value: (field: CheckCompanyDefinedAttribute) => {
      return field?.effective_start ? dayjs(field.effective_start).format('MM/DD/YYYY') : '-';
    },
  },
  {
    field: 'labor_and_industries_total_rate',
    title: 'L&I Total Hourly Rate',
    value: (field: CheckCompanyDefinedAttribute) => {
      return field?.value ? formatUSD.format(parseFloat(field.value)) : '-';
    },
  },
  {
    field: 'labor_and_industries_employee_rate',
    title: 'L&I Hourly Employee Withholding Rate',
    value: (field: CheckCompanyDefinedAttribute) => {
      return field?.value ? formatUSD.format(parseFloat(field.value)) : '-';
    },
  },
  {
    field: 'wa_corporate_officer',
    title: 'Corporate officer status (WA)',
    value: (field: CheckCompanyDefinedAttribute) => {
      return field?.value ? (field.value === 'true' ? 'Yes' : 'No') : '-';
    },
  },
  {
    field: 'wa_soc_code',
    title: 'Standard Occupational Classification (SOC) code',
    isActive: (_: CheckCompanyDefinedAttribute, attributes: CheckCompanyDefinedAttribute[]) => {
      return attributes.find((field) => field.name === 'wa_corporate_officer')?.value === 'false';
    },
    value: (field: CheckCompanyDefinedAttribute) => {
      return field?.value ? field.value : '-';
    },
  },
];

export function Workplaces({
  title = 'Workplaces',
  selectedWorkplaceIds: initialWorkplaceIds = [],
  allowEdit = false,
  companyId,
  workersCompCodeId,
  isFetchingEmployee,
  fullName,
  userId,
  employeeId,
}: WorkplacesProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedWorkplaces, setSelectedWorkplaces] = useState(initialWorkplaceIds);

  const { data: workplaces = [] } = useQuery({
    queryKey: ['workplaces', companyId],
    queryFn: () => listWorkplaces(companyId),
  });

  const workplace = workplaces.filter((workplace) => selectedWorkplaces.includes(workplace.id))[0];
  const isAnyWorkplaceInWashington =
    workplaces.find((workplace) => selectedWorkplaces.includes(workplace.id))?.address.state === 'WA';

  const companyDefinedAttributes = useQuery<CheckCompanyDefinedAttribute[]>({
    queryKey: ['companyDefinedAttributes', employeeId],
    queryFn: () =>
      apiRequestCheck(`employees/${employeeId}/company_defined_attributes`).then(
        (response) => response.company_defined_attributes
      ),
    enabled: isAnyWorkplaceInWashington,
  });

  return (
    <div className="flex flex-col gap-5 rounded-16 border border-soft-200 px-5 py-3 shadow-xs">
      <div className="flex flex-row items-center justify-between">
        <div>{title}</div>
        {allowEdit && (
          <Button
            onClick={() => setIsEditing(true)}
            variant="link"
            color="primary"
            loading={isFetchingEmployee || companyDefinedAttributes.isFetching}
          >
            Edit
          </Button>
        )}
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-1.5">
          <div className="text-xs text-sub-600">Name</div>
          <div className="text-sm">{workplace?.name ?? '-'}</div>
        </div>

        <div className="flex flex-col gap-1.5">
          <div className="text-xs text-sub-600">Work Address</div>
          <div className="flex flex-col">
            {workplace ? (
              <>
                <div className="text-sm">{workplace.address.line1}</div>
                {workplace.address.line2 && <div className="text-sm">{workplace.address.line2}</div>}
                <div className="text-sm">{`${workplace.address.city}, ${workplace.address.state}`}</div>
                <div className="text-sm">{workplace.address.postal_code}</div>
              </>
            ) : (
              '-'
            )}
          </div>
        </div>
      </div>

      {isAnyWorkplaceInWashington && !companyDefinedAttributes.isLoading && companyDefinedAttributes.data ? (
        <>
          <div className="">Washington State Employment Data</div>

          {waStateFieldsToJSXMappings
            .filter((fieldMapping) => {
              if (!fieldMapping.isActive) return true;
              return fieldMapping.isActive(
                companyDefinedAttributes.data.find((field) => field.name === fieldMapping.field),
                companyDefinedAttributes.data
              );
            })
            .map((fieldMapping, index) => {
              const field = companyDefinedAttributes.data?.find((field) => field.name === fieldMapping.field);
              if (!field) return null;

              return (
                <div key={index} className="flex flex-col gap-1.5">
                  <div className="text-xs text-sub-600">{fieldMapping.title}</div>
                  <div className="flex flex-col text-sm">{fieldMapping.value(field)}</div>
                </div>
              );
            })}
        </>
      ) : undefined}

      <EditWorkplacesModal
        open={isEditing}
        setOpen={setIsEditing}
        selectedWorkplaceIds={selectedWorkplaces}
        companyId={companyId}
        userId={userId}
        companyDefinedAttributes={companyDefinedAttributes.data}
        workersCompCodeId={workersCompCodeId}
        checkEmployeeId={employeeId}
        fullName={fullName}
        onSuccess={(workplaces) => {
          setIsEditing(false);
          setSelectedWorkplaces(workplaces);
        }}
      />
    </div>
  );
}

interface EditWorkplacesModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  selectedWorkplaceIds: string[];
  companyId: string;
  userId: number;
  companyDefinedAttributes?: CheckCompanyDefinedAttribute[];
  workersCompCodeId?: number;
  checkEmployeeId: string;
  fullName: string;
  onSuccess: (workplaces: string[]) => void;
}

export function EditWorkplacesModal({
  open,
  setOpen,
  selectedWorkplaceIds,
  companyId,
  workersCompCodeId,
  userId,
  checkEmployeeId,
  companyDefinedAttributes = [],
  fullName,
  onSuccess,
}: EditWorkplacesModalProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const isRiskClassCodeRequired = companyDefinedAttributes.some((field) => field.name === 'wa_risk_class_code');

  const selectedWorkplaceId = selectedWorkplaceIds[0]; // for now, we support a single workplace

  const formSchema = yup.object().shape({
    workplace: yup.string(),
    workplaceState: yup.string(),
    companyDefinedAttributes: yup.mixed<CompanyDefinedAttributesForm>().when('workplaceState', (state, schema) => {
      return state?.[0] === 'WA'
        ? companyDefinedAttributesSchema.pick(companyDefinedAttributes.map((field) => field.name) as any).required()
        : schema.notRequired();
    }),
    workersCompCode: yup.mixed<string>().when('workplaceState', (state, schema) => {
      return state?.[0] === 'WA' && isRiskClassCodeRequired
        ? yup
            .string()
            .matches(/^-*([0-9]-*){6}$/, 'Must contain 6 digits for WA state employees (can have dashes)')
            .required('Workers Comp Code is required for WA state')
        : schema.notRequired();
    }),
  });

  const defaultValues = {
    workplace: selectedWorkplaceId,
    workplaceState: '',
    workersCompCode: undefined,
    companyDefinedAttributes: {
      wa_soc_code: '',
      wa_ltc_exemption: '',
      labor_and_industries_total_rate: undefined,
      labor_and_industries_employee_rate: undefined,
      wa_corporate_officer: '',
    } as CompanyDefinedAttributesForm,
  } as InferType<typeof formSchema>;

  const form = useForm({
    defaultValues,
    resolver: yupResolver(formSchema),
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = form;

  const { data: workplaces = [] } = useQuery({
    queryKey: ['workplaces', companyId],
    queryFn: () => listWorkplaces(companyId),
  });

  const workplaceValue = useWatch({ name: 'workplace', control });

  const isAnySelectedWorkplaceInWashington =
    workplaces.find((workplace) => workplaceValue?.includes(workplace.id))?.address.state === 'WA';

  // used to load the Washington state data in the form
  const isInitialSelectedWorkplaceInWashington =
    workplaces.find((workplace) => workplace.id === selectedWorkplaceId)?.address.state === 'WA';

  const workersCompCodes = useQuery({
    queryKey: ['workersCompCodes'],
    queryFn: () => workersCompCodesService.get(),
    enabled: isAnySelectedWorkplaceInWashington && open,
  });

  const updateWorkplacesMutation = useMutation<any, Error, UpdateWorkplaceRequest>({
    mutationFn: async (data) => {
      await apiRequestCheck(`employees/${checkEmployeeId}`, {
        method: 'PATCH',
        body: {
          workplaces: data.workplaces,
        },
      });

      if (isAnySelectedWorkplaceInWashington) {
        await saveWashingtonFields(
          data.workersCompCode,
          checkEmployeeId,
          data.companyDefinedAttributes,
          isRiskClassCodeRequired
        );

        await updateHammrUser(userId, {
          workersCompCodeId: workersCompCodes.data?.find((item) => item.code === data.workersCompCode)?.id,
        });
      }
    },
    onSuccess: async (_response, data) => {
      toast({
        title: 'Success',
        description: 'Workplaces updated successfully',
        toastVariant: 'success',
      });
      setOpen(false);
      reset({ ...defaultValues, workplace: data.workplaces[0] });
      onSuccess(data.workplaces);

      // refresh the data in the UI
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['workplaces'] }),
        queryClient.invalidateQueries({ queryKey: ['employee'] }),
        queryClient.invalidateQueries({ queryKey: ['companyDefinedAttributes'] }),
      ]);
    },
  });

  useEffect(() => {
    // If the WorkersComp was archived after it was assigned to the employee and the employee still has it assigned,
    //   we need to unselect it from the select box
    if (workersCompCodes.data) {
      const selectedWorkersComp = workersCompCodes.data.find((item) => item.id === workersCompCodeId);
      form.setValue('workersCompCode', selectedWorkersComp?.code, {
        shouldDirty: true,
      });
    }
  }, [form, open, workersCompCodeId, workersCompCodes.data]);

  useEffect(() => {
    if (open) {
      form.setValue('workplaceState', isAnySelectedWorkplaceInWashington ? 'WA' : undefined);
    }
  }, [form, isAnySelectedWorkplaceInWashington, open]);

  const onSubmit = (formData: typeof defaultValues) => {
    const companyDefinedAttributes = getCompanyDefinedAttributesFormData(formData.companyDefinedAttributes);

    updateWorkplacesMutation.mutate({
      workplaces: [formData.workplace],
      workersCompCode: formData.workersCompCode,
      companyDefinedAttributes: isAnySelectedWorkplaceInWashington ? companyDefinedAttributes : undefined,
    });
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        reset(defaultValues);
        setOpen(isOpen);
      }}
    >
      <DialogSurface>
        <DialogHeader title="Edit Workplaces" showCloseButton icon={<PencilLine />} />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={updateWorkplacesMutation.isPending}
        >
          <div className="flex flex-col gap-6">
            <Alert status="information" size="x-small">
              After edits, {fullName} will need to re-onboard for payroll purposes.
            </Alert>

            <ControlledSelect
              name="workplace"
              label="Workplace"
              control={control}
              required
              rules={{ required: 'Please select a workplace' }}
              error={errors.workplace?.message as string}
            >
              {workplaces.map((workplace) => (
                <SelectItem key={workplace.id} value={workplace.id}>
                  {workplace.name}
                </SelectItem>
              ))}
            </ControlledSelect>

            {isAnySelectedWorkplaceInWashington && isRiskClassCodeRequired ? (
              <ControlledSelect
                name="workersCompCode"
                placeholder="Select a workers comp code"
                label="Workers Comp Code"
                control={form.control}
                required
                error={form.formState.errors.workersCompCode?.message}
              >
                {workersCompCodes.data?.map((item: WorkersCompCode) => (
                  <SelectItem key={item.code} value={item.code}>{`${item.name} • ${item.code}`}</SelectItem>
                ))}
              </ControlledSelect>
            ) : undefined}

            {isAnySelectedWorkplaceInWashington ? (
              <WashingtonStateEmploymentFormFields
                form={form}
                employeeId={checkEmployeeId}
                preFillForm={isInitialSelectedWorkplaceInWashington}
              />
            ) : undefined}

            <Button
              variant="ghost"
              type="button"
              color="primary"
              className="w-full"
              onClick={(event) => {
                event.stopPropagation();
                setShowAddForm(true);
              }}
            >
              + Add New Workplace
            </Button>
          </div>
        </FormV2>
      </DialogSurface>
      <WorkplacesForm open={showAddForm} setOpen={setShowAddForm} checkCompanyId={companyId} />
    </Dialog>
  );
}
