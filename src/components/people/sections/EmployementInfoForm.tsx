import { Controller, Path, UseFormReturn } from 'react-hook-form';
import { TextField } from '@/components/elements/form/TextField';
import * as yup from 'yup';
import { FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { AppRoleOptions } from '@/options';
import { useEmployees } from '@/hooks/data-fetching/useEmployees';
import { useCompany } from '@/hooks/useCompany';
import { DateInput } from '@/hammr-ui/components/date-input';
import { Input } from '@/hammr-ui/components/input';
import { WorkerClassification } from '@/interfaces/user';
import { useQuery } from '@tanstack/react-query';
import { departmentsService } from '@/services/departments';

export const employementContactInfoFormSchema = yup.object().shape({
  startDate: yup.date().required('Start Date is required'),
  employeeId: yup.string().nullable().notRequired(),
  position: yup.string().nullable().notRequired(),
  role: yup.string().nullable().notRequired(),
  managerId: yup.number().nullable().notRequired(),
  departmentId: yup.number().nullable().notRequired(),
});

export interface EmployementContactValues {
  startDate: Date | null;
  position: string;
  employeeId: string;
  role: string;
  managerId?: number;
  departmentId?: number;
}

export function EmployementContactForm<T extends EmployementContactValues>({
  form,
  workerClassification,
}: {
  form: UseFormReturn<T>;
  workerClassification: WorkerClassification;
}) {
  const { company } = useCompany();
  const employees = useEmployees(Number(company?.id), {
    simple: true,
  });

  const departments = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const result = await departmentsService.list();
      return {
        departments: result.departments.sort((a, b) => a.name.localeCompare(b.name)),
      };
    },
  });

  return (
    <div className="flex flex-col gap-5">
      <Controller
        control={form.control}
        name={'startDate' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error} required>
            <FormLabel>Start Date</FormLabel>
            <DateInput value={field.value as Date} onChange={field.onChange} />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'position' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Position</FormLabel>
            <Input placeholder="Enter position" value={field.value as string} onChange={field.onChange} />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <TextField
        control={form.control}
        name="employeeId"
        label={workerClassification === WorkerClassification.Contractor ? 'Contractor ID' : 'Employee ID'}
        placeholder={
          'Enter ' + (workerClassification === WorkerClassification.Contractor ? 'Contractor ID' : 'Employee ID')
        }
        error={form.formState.errors.employeeId?.message}
      />
      <Controller
        control={form.control}
        name={'role' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error} required>
            <FormLabel tooltip="App Role">App Role</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              items={AppRoleOptions}
              value={field.value as string}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'managerId' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Manager</FormLabel>
            <DropdownPicker
              allowClear
              placeholder="Select an option"
              items={employees.map((employee) => {
                return {
                  label: employee.fullName,
                  value: employee.id,
                };
              })}
              value={field.value as string}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'departmentId' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Department</FormLabel>
            <DropdownPicker
              allowClear
              placeholder="Select an option"
              items={
                departments?.data?.departments?.map((department) => {
                  return {
                    label: department.name,
                    value: department.id,
                  };
                }) || []
              }
              value={field.value as string}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
    </div>
  );
}
