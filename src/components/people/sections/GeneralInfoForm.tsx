import { TextField } from '@/components/elements/form/TextField';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { Controller, Path, UseFormReturn } from 'react-hook-form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { SelectItem } from '@hammr-ui/components/select';
import { useQuery } from '@tanstack/react-query';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import * as yup from 'yup';
import { PhoneInput, VALID_PHONE_NUMBER_REGEX } from '@/components/shared/PhoneInput';
import { FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { Ethnicity, Gender, VeteranStatus } from '@/interfaces/user';
import { EthnicityOptions, GenderOptions, VeteranStatusOptions } from '@/options';
import { Checkbox } from '@/hammr-ui/components/checkbox';

export const generalInfoFormSchema = yup.object().shape({
  firstName: yup.string().required('Please enter a first name'),
  lastName: yup.string().required('Please enter a last name'),
  dob: yup.string().required('Please enter a date of birth'),
  email: yup.string().nullable(),
  gender: yup.mixed<Gender>().oneOf(Object.values(Gender)).nullable(),
  ethnicity: yup.mixed<Ethnicity>().oneOf(Object.values(Ethnicity)).nullable(),
  veteranStatus: yup.mixed<VeteranStatus>().oneOf(Object.values(VeteranStatus)).nullable(),
  address: yup.string().required('Please enter a street address'),
  city: yup.string().required('Please enter a city'),
  state: yup
    .string()
    .matches(/^[A-Za-z]{2}$/, 'Not a valid state abbreviation')
    .required('Please enter a state')
    .transform((value) => value?.toUpperCase()),
  postalCode: yup
    .string()
    .matches(/^\d{5}(?:[-\s]\d{4})?$/, 'Not a valid postal code')
    .required('Please enter a postal code'),
  phone: yup
    .string()
    .matches(VALID_PHONE_NUMBER_REGEX, {
      message: 'Not a valid phone number',
      excludeEmptyString: true,
    })
    .nullable()
    .notRequired(),
  workersCompCodeId: yup.string().nullable(),
});

export interface GeneralInfoValues {
  firstName: string;
  lastName: string;
  dob?: string;
  phone?: string;
  sendOnboardingLink: boolean;
  email?: string;
  gender?: Gender;
  ethnicity?: Ethnicity;
  veteranStatus?: VeteranStatus;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  workersCompCodeId?: string;
}

export function GeneralInfoForm<T extends GeneralInfoValues>({
  form,
  showDOB,
  showWorkersCompCode,
  isWorkersCompCodeRequired,
  showPhone,
  showAddress,
  userExists,
}: {
  form: UseFormReturn<T>;
  userExists: boolean;
  showDOB: boolean;
  showPhone: boolean;
  isWorkersCompCodeRequired?: boolean;
  showAddress: boolean;
  showWorkersCompCode: boolean;
}) {
  const workersCompCodes = useQuery({
    queryKey: ['workersCompCodes'],
    queryFn: () => workersCompCodesService.get(),
    enabled: showWorkersCompCode,
  });

  return (
    <div className="flex flex-col gap-5">
      <div className="grid grid-cols-2 gap-4">
        <TextField
          control={form.control}
          name="firstName"
          label="First Name"
          placeholder="Enter first name"
          required
          error={form.formState.errors.firstName?.message}
        />
        <TextField
          control={form.control}
          name="lastName"
          label="Last Name"
          placeholder="Enter last name"
          required
          error={form.formState.errors.lastName?.message}
        />
      </div>

      {showDOB ? (
        <ControlledDateInput
          control={form.control}
          name="dob"
          label="Date of Birth"
          required
          rules={{ required: 'Please enter a date of birth' }}
        />
      ) : undefined}

      {showPhone ? (
        <div>
          <Controller
            control={form.control}
            name={'phone' as Path<T>}
            render={({ field }) => (
              <FormItem error={!!form.formState.errors.phone}>
                <FormLabel className="mb-1">Phone number</FormLabel>
                <PhoneInput
                  id="phone"
                  name={field.name}
                  value={field.value as string}
                  onChange={field.onChange}
                  placeholder="(*************"
                  onBlur={field.onBlur}
                />
                {form.formState.errors.phone && (
                  <FormMessage className="mt-1">{form.formState.errors.phone?.message as string}</FormMessage>
                )}
              </FormItem>
            )}
          />

          {!userExists ? (
            <Controller
              name={'sendOnboardingLink' as Path<T>}
              control={form.control}
              render={({ field }) => (
                <div className="mt-5">
                  <label className="flex gap-2">
                    <span className="flex size-5 items-center justify-center">
                      <Checkbox checked={field.value as boolean} onCheckedChange={field.onChange} />
                    </span>
                    <h3 className="text-sm text-strong-950">Text Onboarding Link</h3>
                  </label>
                  {showWorkersCompCode ? (
                    <p className="ml-7 mt-1 text-xs text-sub-600">
                      The employee will receive a text message with an onboarding link to complete their Form W-4 and
                      set up direct deposit.
                    </p>
                  ) : (
                    <p className="ml-7 mt-1 text-xs text-sub-600">
                      The contractor will receive a text message with an onboarding link to complete their Form W-9 and
                      set up direct deposit.
                    </p>
                  )}
                </div>
              )}
            />
          ) : undefined}
        </div>
      ) : undefined}

      <Controller
        control={form.control}
        name={'email' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Email</FormLabel>
            <Input
              id="email"
              type="email"
              name={field.name}
              value={field.value as string}
              onChange={field.onChange}
              placeholder="Enter email address"
              onBlur={field.onBlur}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'gender' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Gender</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              items={GenderOptions}
              value={field.value as string}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'ethnicity' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Ethnicity</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              items={EthnicityOptions}
              value={field.value as string}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'veteranStatus' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Veteran Status</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              items={VeteranStatusOptions}
              value={field.value as string}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />

      {showAddress ? (
        <>
          <hr className="border-soft-200" />

          <div className="space-y-2">
            <TextField
              control={form.control}
              name="address"
              label="Home Address"
              placeholder="Street Address"
              required
              error={form.formState.errors.address?.message}
            />

            <TextField
              control={form.control}
              name="city"
              placeholder="City"
              required
              error={form.formState.errors.city?.message}
            />

            <div className="grid grid-cols-2 gap-4">
              <TextField
                control={form.control}
                name="state"
                placeholder="State Code"
                required
                error={form.formState.errors.state?.message}
              />
              <TextField
                control={form.control}
                name="postalCode"
                placeholder="Postal Code"
                required
                error={form.formState.errors.postalCode?.message}
              />
            </div>
          </div>
        </>
      ) : undefined}

      {showWorkersCompCode ? (
        <ControlledSelect
          name="workersCompCodeId"
          placeholder="Select a workers comp code"
          label="Workers Comp Code"
          control={form.control}
          required={isWorkersCompCodeRequired}
          error={form.formState.errors.workersCompCodeId?.message}
        >
          {workersCompCodes.data?.map((item: WorkersCompCode) => (
            <SelectItem key={item.id} value={item.id.toString()}>{`${item.name} • ${item.code}`}</SelectItem>
          ))}
        </ControlledSelect>
      ) : undefined}
    </div>
  );
}
