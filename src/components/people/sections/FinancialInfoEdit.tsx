import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { EnrichedEmployee } from '@/interfaces/employee';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { handleFormError, logError } from '@/utils/errorHandling';
import { FinancialInfoForm, financialInfoFormSchema, FinancialInfoValues } from './FinancialInfoForm';
import { MutateHammrUser } from '@/interfaces/user';
import { useAuth } from '@/hooks/useAuth';
import WalletLine from '@hammr-icons/WalletLine';
import { yupResolver } from '@/utils/yupResolver';
import { getCompensationTypeFromHammrEarningRate } from '@/services/earning-rate';
import dayjs from 'dayjs';
import { apiRequest } from '@/utils/requestHelpers';

export default function FinancialInfoEdit({
  employee,
  open,
  setOpen,
  handleSuccess,
}: {
  employee: EnrichedEmployee;
  open: boolean;
  setOpen: (open: boolean) => void;
  handleSuccess: () => void;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { updateUser } = useAuth();
  const isOnPayroll = Boolean(employee.checkEmployee);
  const [initialValues, setInitialValues] = useState<Partial<FinancialInfoValues>>({});
  const [showEffectiveDate, setShowEffectiveDate] = useState(false);

  const reimbursementPerDiem = employee?.hammrUser.employeeReimbursement?.reimbursementAmount;

  const defaultValues = useMemo(
    () => ({
      jobTitle: employee?.hammrUser.position,
      startDate: employee?.checkEmployee?.start_date,
      employmentType: employee?.primaryEarningRate
        ? getCompensationTypeFromHammrEarningRate(employee.primaryEarningRate)
        : 'Hourly',
      pay: parseFloat(employee?.primaryEarningRate?.amount).toFixed(2),
      weeklyHours: employee?.primaryEarningRate?.weeklyHours ?? 40,
      payEffectiveDate: null,
      reimbursementPerDiem: reimbursementPerDiem ? parseFloat(reimbursementPerDiem).toFixed(2) : '',
    }),
    [employee]
  );
  const form = useForm<FinancialInfoValues>({
    defaultValues,
    resolver: yupResolver(isOnPayroll ? financialInfoFormSchema : financialInfoFormSchema.pick(['pay'])),
  });

  const selectedEmploymentType = form.watch('employmentType') as string;
  const currentPay = form.watch('pay') as string;

  // Check if compensation-related fields have changed from initial values
  useEffect(() => {
    if (initialValues) {
      const hasCompensationTypeChanged =
        initialValues.employmentType !== undefined && initialValues.employmentType !== selectedEmploymentType;

      const hasCompensationChanged = initialValues.pay !== undefined && initialValues.pay !== currentPay;

      setShowEffectiveDate(hasCompensationTypeChanged || hasCompensationChanged);
    }
  }, [selectedEmploymentType, currentPay, initialValues]);

  useEffect(() => {
    setInitialValues(form.getValues());

    return () => {
      setInitialValues({});
    };
  }, [form]);

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form, open]);

  const onSubmit = async (data: FinancialInfoValues) => {
    setIsLoading(true);
    try {
      const updatePayload: Partial<MutateHammrUser> = {};

      if (data.employmentType === 'Hourly') {
        updatePayload.hourlyRate = parseFloat(data.pay.replace(/,/g, ''));
      } else if (data.employmentType === 'Salaried') {
        updatePayload.salary = parseFloat(data.pay.replace(/,/g, ''));
        updatePayload.weeklyHours = data.weeklyHours;
      }

      if (data.payEffectiveDate) {
        updatePayload.payEffectiveDate = dayjs(data.payEffectiveDate).startOf('day').valueOf();
        await updateUser(employee.hammrUser.id.toString(), updatePayload);
      } else if (showEffectiveDate) {
        throw new Error('Effective date is required when compensation information is updated');
      }

      // update reimbursement
      if (data.reimbursementPerDiem) {
        await apiRequest('employee-reimbursements', {
          method: 'POST',
          body: {
            userId: employee.hammrUser.id.toString(),
            reimbursementAmount: data.reimbursementPerDiem,
          },
        });
      }

      handleSuccess();
      setOpen(false);
      toast({
        title: 'Success',
        description: 'Financial information successfully updated',
        toastVariant: 'success',
      });
    } catch (err) {
      handleFormError(err, form.setError);
      logError(err);
      toast({
        title: 'Error',
        description: (err as Error)?.message || 'An error occurred while updating financial information',
        toastVariant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Edit Financial Information" icon={<WalletLine />}>
      <FormV2
        submitText="Save"
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setOpen(false)}
        isLoading={isLoading}
      >
        <FinancialInfoForm showEffectiveDate={showEffectiveDate} form={form} isOnPayroll={isOnPayroll} />
      </FormV2>
    </ModalV2>
  );
}
