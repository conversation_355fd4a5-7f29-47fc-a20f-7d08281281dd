import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { EnrichedEmployee } from '@/interfaces/employee';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { handleFormError, logError } from '@/utils/errorHandling';
import { GeneralInfoForm, generalInfoFormSchema, GeneralInfoValues } from './GeneralInfoForm';
import { HammrUser, UpdateUser } from '@/interfaces/user';
import { updateHammrUser } from '@/services/user';
import UserLine from '@hammr-icons/UserLine';
import { yupResolver } from '@/utils/yupResolver';
import { useCompany } from '@/hooks/useCompany';
import dayjs from 'dayjs';
import { saveWashingtonFields } from '@/components/people/sections/WashingtonStateEmployment';
import { listWorkplaces } from '@/services/workplace';
import { useQuery } from '@tanstack/react-query';
import * as yup from 'yup';
import { CheckCompanyDefinedAttribute } from '@/interfaces/check';
import { apiRequestCheck } from '@/utils/requestHelpers';

export default function GeneralInfoEdit({
  employee,
  open,
  setOpen,
  handleSuccess,
  isOnPayroll,
}: {
  employee: EnrichedEmployee;
  open: boolean;
  setOpen: (open: boolean) => void;
  handleSuccess: () => void | Promise<void>;
  isOnPayroll: boolean;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { company } = useCompany();

  const defaultValues = useMemo(
    () => ({
      firstName: employee?.hammrUser.firstName,
      lastName: employee?.hammrUser.lastName,
      dob: employee?.checkEmployee?.dob,
      phone: employee?.hammrUser.phone,
      email: employee?.hammrUser.email,
      gender: employee?.hammrUser.gender,
      ethnicity: employee?.hammrUser.ethnicity,
      veteranStatus: employee?.hammrUser.veteranStatus,
      address: employee?.checkEmployee?.residence.line1,
      city: employee?.checkEmployee?.residence.city,
      state: employee?.checkEmployee?.residence.state,
      postalCode: employee?.checkEmployee?.residence.postal_code,
      workersCompCodeId: employee?.hammrUser.workersCompCode?.id.toString(),
    }),
    [employee]
  );
  const workplaces = useQuery({
    queryKey: ['workplaces', company.checkCompanyId],
    queryFn: () => listWorkplaces(company.checkCompanyId),
    enabled: company?.isPayrollEnabled,
  });

  const isAnySelectedWorkplaceInWashington =
    workplaces.data?.find((workplace) => employee.checkEmployee?.workplaces.includes(workplace.id))?.address.state ===
    'WA';

  const companyDefinedAttributes = useQuery<CheckCompanyDefinedAttribute[]>({
    queryKey: ['companyDefinedAttributes', employee.checkEmployee?.id],
    queryFn: () =>
      apiRequestCheck(`employees/${employee.checkEmployee?.id}/company_defined_attributes`).then(
        (response) => response.company_defined_attributes
      ),
    enabled: isAnySelectedWorkplaceInWashington,
  });

  const isRiskClassCodeRequired = companyDefinedAttributes.data?.some((field) => field.name === 'wa_risk_class_code');

  const isWorkersCompCodeIdRequired = isAnySelectedWorkplaceInWashington && isRiskClassCodeRequired;

  const validationSchema = isOnPayroll
    ? generalInfoFormSchema
    : generalInfoFormSchema.pick(['firstName', 'lastName', 'phone', 'workersCompCodeId']);

  const form = useForm<GeneralInfoValues>({
    defaultValues,
    // eslint-disable-next-line
    // @ts-ignore TS is standing in our way here. Might be due to TS non-strict mode
    resolver: yupResolver(
      validationSchema.shape({
        workersCompCodeId: isWorkersCompCodeIdRequired
          ? yup.string().required('This field is required for Washington employees')
          : yup.string().nullable(),
      })
    ),
  });

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form, open]);

  const onSubmit = async (data: GeneralInfoValues) => {
    if (!company) {
      return;
    }
    setIsLoading(true);
    try {
      const updatedUser: UpdateUser = {
        firstName: data.firstName,
        lastName: data.lastName,
        dob: dayjs(data.dob).format('YYYY-MM-DD'),
        phoneNumber: data.phone,
        email: data.email,
        gender: data.gender,
        ethnicity: data.ethnicity,
        veteranStatus: data.veteranStatus,
        address1: data.address,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        workersCompCodeId: data.workersCompCodeId ? parseInt(data.workersCompCodeId) : undefined,
        company: company.checkCompanyId,
        type: 'employee',
      };

      const user: HammrUser = await updateHammrUser(employee.hammrUser.id, updatedUser).then(
        (response) => response.user
      );

      if (employee.checkEmployee) {
        const isAnySelectedWorkplaceInWashington =
          workplaces.data?.find((workplace) => employee.checkEmployee.workplaces?.includes(workplace.id))?.address
            .state === 'WA';

        if (isAnySelectedWorkplaceInWashington) {
          await saveWashingtonFields(
            user.workersCompCode?.code,
            employee.hammrUser.checkEmployeeId,
            [],
            isRiskClassCodeRequired
          );
        }
      }

      await handleSuccess();
      setOpen(false);
      toast({
        title: 'Edited Employee Info',
        description: (
          <div>
            Successfully updated the info of the employee{' '}
            <span className="font-medium">{`${data.firstName} ${data.lastName}`}</span>
          </div>
        ),
        toastVariant: 'success',
      });
    } catch (err) {
      handleFormError(err, form.setError);
      logError(err);
      toast({
        title: 'Error',
        description: (err as Error)?.message || 'An error occurred while updating the employee',
        toastVariant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Edit General Information" icon={<UserLine />}>
      <FormV2
        submitText="Save"
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setOpen(false)}
        isDisabled={workplaces.isLoading}
        isLoading={isLoading}
      >
        <GeneralInfoForm
          form={form}
          showDOB={true}
          showWorkersCompCode={true}
          isWorkersCompCodeRequired={isWorkersCompCodeIdRequired}
          showPhone={true}
          showAddress={true}
          userExists={Boolean(employee)}
        />
      </FormV2>
    </ModalV2>
  );
}
