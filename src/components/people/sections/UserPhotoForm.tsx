import { ImageUpload } from '@/hammr-ui/components/ImageUpload';
import { useSignedUrl } from '@/hooks/useSignedUrl';
import { useUploadFile } from '@/hooks/useUploadFile';
import { Path, PathValue, UseFormReturn } from 'react-hook-form';
import * as yup from 'yup';
import { v4 as uuidv4 } from 'uuid';

export interface UserPhotoFormValuesValues {
  profilePhotoObjectId: string;
}

export const userPhotoFormSchema = yup.object().shape({
  profilePhotoObjectId: yup.string().nullable().notRequired(),
});

export function UserPhotoForm<T extends UserPhotoFormValuesValues>({ form }: { form: UseFormReturn<T> }) {
  const profilePhotoObjectId = form.watch('profilePhotoObjectId' as Path<T>);
  const [signedUrl] = useSignedUrl('employee-profile-photos', profilePhotoObjectId);
  const { uploadAsync, isUploading } = useUploadFile('employee-profile-photos');

  async function uploadProfilePhoto(document: File) {
    const fileExtension = document.name.indexOf('.') ? document.name.split('.').pop() : '';

    const objectId = uuidv4() + (fileExtension ? '.' + fileExtension : '');

    await uploadAsync({ key: objectId, content: document });

    form.setValue('profilePhotoObjectId' as Path<T>, objectId as PathValue<T, Path<T>>);
  }

  return (
    <ImageUpload
      title="Employee Photo"
      onUpload={(file) => uploadProfilePhoto(file)}
      onChange={(file) => uploadProfilePhoto(file)}
      onRemove={() => form.setValue('profilePhotoObjectId' as Path<T>, null as PathValue<T, Path<T>>)}
      imageUrl={signedUrl}
      isUploading={isUploading}
    />
  );
}
