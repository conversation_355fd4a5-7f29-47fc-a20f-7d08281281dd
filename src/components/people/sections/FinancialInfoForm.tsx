import * as yup from 'yup';
import { Path, UseFormReturn } from 'react-hook-form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { SelectItem } from '@hammr-ui/components/select';
import { NumberFieldV2 } from '@/components/elements/form/NumberFieldV2';
import { employmentOptions } from '@/interfaces/earning-rate';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';

export const financialInfoFormSchema = yup.object<FormData>().shape({
  employmentType: yup.string().required('Please select an employment type'),
  pay: yup.string().required('Please set compensation'),
  payEffectiveDate: yup.date().optional().nullable(),
  weeklyHours: yup
    .number()
    .transform((value) => (isNaN(value) ? undefined : value))
    .when('employmentType', {
      is: 'Salaried',
      then: (schema) =>
        schema
          .required('Please set weekly hours')
          .min(1, 'Weekly hours must be at least 1')
          .max(168, 'Weekly hours cannot exceed 168'),
      otherwise: (schema) => schema.optional(),
    }),
  reimbursementPerDiem: yup.string().optional(),
});

export interface FinancialInfoValues {
  employmentType: string;
  pay: string;
  payEffectiveDate: Date;
  weeklyHours?: number;
  reimbursementPerDiem?: string;
}

export function FinancialInfoForm<T extends FinancialInfoValues>({
  form,
  isOnPayroll,
  showEffectiveDate,
}: {
  form: UseFormReturn<T>;
  isOnPayroll?: boolean;
  showEffectiveDate?: boolean;
}) {
  const selectedEmploymentType = form.watch('employmentType' as Path<T>) as string;

  return (
    <div className="flex flex-col gap-5">
      {isOnPayroll ? (
        <ControlledSelect
          control={form.control}
          name="employmentType"
          label="Compensation Type"
          placeholder="Select an option"
          required
          error={form.formState.errors.employmentType?.message}
        >
          {employmentOptions.map((option) => (
            <SelectItem key={option.type} value={option.type}>
              {option.type}
            </SelectItem>
          ))}
        </ControlledSelect>
      ) : undefined}

      <NumberFieldV2
        control={form.control}
        name="pay"
        label="Compensation"
        placeholder="0.00"
        required
        isFloat
        prefix="$"
        labelHelper={
          <span className="font-normal">{selectedEmploymentType === 'Hourly' ? '(Hourly Rate)' : '(Salary)'}</span>
        }
        error={form.formState.errors.pay?.message}
      />

      {selectedEmploymentType === 'Salaried' ? (
        <NumberFieldV2
          control={form.control}
          name="weeklyHours"
          label="Weekly Hours"
          placeholder="40.0"
          required
          isFloat
          afterContent="hours"
          error={form.formState.errors.weeklyHours?.message}
        />
      ) : null}

      {/* Show Effective Date only when compensation-related values change */}
      {showEffectiveDate && (
        <ControlledDateInput
          label="Effective Date"
          control={form.control}
          name="payEffectiveDate"
          rules={{ required: 'Please select an effective date' }}
          className="w-full"
          required
        />
      )}

      <hr className="border-soft-200" />

      {/* Reimbursement/Per Diem section */}
      <NumberFieldV2
        control={form.control}
        name="reimbursementPerDiem"
        label="Reimbursement/Per Diem"
        labelHelper={<span className="font-normal">(Per Pay Cycle)</span>}
        placeholder="0.00"
        isFloat
        prefix="$"
        error={form.formState.errors.reimbursementPerDiem?.message}
      />
    </div>
  );
}
