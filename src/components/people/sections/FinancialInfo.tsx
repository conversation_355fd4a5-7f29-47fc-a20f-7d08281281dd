import dayjs from 'dayjs';
import HistoryLine from '@hammr-icons/HistoryLine';
import { useState } from 'react';
import CompensationHistoryModal from '@/components/employees/CompensationHistoryModal';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { RiArrowRightUpLine } from '@remixicon/react';
import { useMutation } from '@tanstack/react-query';
import { generateEmployeeOnboardLink } from '@/services/employee';
import { generateContractorOnboardLink } from '@/services/contractor';
import Spinner from '@/hammr-ui/components/spinner';
import Button from '@/hammr-ui/components/button';

interface PaymentInformationProps {
  paymentPreference?: string;
  isEmployee: boolean;
  checkId: string;
  bankInfo?: {
    institution_name?: string;
    subtype?: string;
    mask?: string;
  };
}

export function PaymentInformationCard({ paymentPreference, isEmployee, checkId, bankInfo }: PaymentInformationProps) {
  const openOnboardLink = useMutation({
    async mutationFn() {
      return isEmployee ? generateEmployeeOnboardLink(checkId) : generateContractorOnboardLink(checkId);
    },
    onSuccess(res) {
      window.open(res.url, '_blank')?.focus();
    },
  });

  return (
    <section className="rounded-16 border border-soft-200 p-5">
      <hgroup className="flex justify-between">
        <h2>Payment Information</h2>
        <LinkButton
          className="flex items-center gap-1"
          size="medium"
          style="primary"
          onClick={() => openOnboardLink.mutate()}
        >
          Edit {openOnboardLink.isPending ? <Spinner className="size-5" /> : <RiArrowRightUpLine className="size-5" />}
        </LinkButton>
      </hgroup>
      <h3 className="mt-5 text-xs text-sub-600">Payment Preference</h3>
      <p className="mt-1.5 h-fit text-strong-950">
        {!paymentPreference ? 'Onboarding not complete' : paymentPreference === 'manual' ? 'Manual' : 'Direct deposit'}
      </p>
      <h3 className="mt-5 text-xs text-sub-600">Account Type</h3>
      <p className="mt-1.5 h-fit text-strong-950">
        <span className="capitalize">{bankInfo?.subtype}</span>
        {bankInfo?.mask && <> - {bankInfo.mask}</>}
      </p>
      <h3 className="mt-5 text-xs text-sub-600">Institution</h3>
      <p className="mt-1.5 h-fit text-strong-950">{bankInfo?.institution_name || '-'}</p>
    </section>
  );
}

interface CompensationProps {
  compensationType?: string;
  userId: number;
  compensation?: string;
  startDate?: Date;
  weeklyHours?: number;
  reimbursementPerDiem?: string;
  onEdit: () => void;
}

export function CompensationCard({
  compensationType,
  userId,
  compensation,
  startDate,
  weeklyHours,
  reimbursementPerDiem,
  onEdit,
}: CompensationProps) {
  const [showCompensationHistoryModal, setShowCompensationHistoryModal] = useState(false);

  return (
    <section className="rounded-16 border border-soft-200 p-5">
      <div className="flex justify-between">
        <h2>Compensation</h2>
        <LinkButton size="medium" style="primary" onClick={onEdit}>
          Edit
        </LinkButton>
      </div>
      <div className="mt-5 grid grid-cols-2">
        <div>
          <h3 className="text-xs text-sub-600">Compensation Type</h3>
          <p className="mt-1.5 h-fit text-strong-950">{compensationType || '-'}</p>
        </div>
        <Button
          variant="ghost"
          color="neutral"
          onClick={() => setShowCompensationHistoryModal(true)}
          className="-ml-[5px] -mt-1.5 w-fit justify-start"
          beforeContent={<HistoryLine />}
          size="2x-small"
          title="View compensation history"
        >
          History
        </Button>
      </div>
      <div className="mt-5 grid grid-cols-2">
        <div>
          <h3 className="text-xs text-sub-600">Compensation</h3>
          <p className="mt-1.5 h-fit text-strong-950">{compensation || '-'}</p>
        </div>
        <div>
          <h3 className="text-xs text-sub-600">Effective Date</h3>
          <p className="mt-1.5 h-fit text-strong-950">{startDate ? dayjs(startDate).format('MM/DD/YYYY') : '-'}</p>
        </div>
      </div>
      {compensationType === 'Salaried' && (
        <>
          <h3 className="mt-5 text-xs text-sub-600">Weekly Hours</h3>
          <p className="mt-1.5 h-fit text-strong-950">{weeklyHours ? `${weeklyHours} hours` : '-'}</p>
        </>
      )}
      <h3 className="mt-5 text-xs text-sub-600">Reimbursement/Per Diem</h3>
      <p className="mt-1.5 h-fit text-strong-950">
        {reimbursementPerDiem ? `$${parseFloat(reimbursementPerDiem).toFixed(2)} / pay cycle` : '-'}
      </p>

      <CompensationHistoryModal
        isOpen={showCompensationHistoryModal}
        onClose={() => setShowCompensationHistoryModal(false)}
        userId={userId}
      />
    </section>
  );
}
