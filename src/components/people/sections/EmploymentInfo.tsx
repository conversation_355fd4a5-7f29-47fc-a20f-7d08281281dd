import Button from '@/hammr-ui/components/button';
import { DetailItem } from './DetailItem';
import { AppRoleOptions, getOptionLabel } from '@/options';
import dayjs from 'dayjs';
import { WorkerClassification } from '@/interfaces/user';

interface EmploymentInfoProps {
  title?: string;
  onEdit?: () => void;
  allowEdit?: boolean;
  employeeId?: string;
  position?: string;
  role?: string;
  managerName?: string;
  startDate?: string;
  workerClassification: WorkerClassification;
  departmentName?: string;
}

export function EmploymentInfo({
  title = 'Employment Information',
  onEdit,
  allowEdit = false,
  employeeId,
  position,
  role,
  managerName,
  startDate,
  workerClassification,
  departmentName,
}: EmploymentInfoProps) {
  return (
    <div className="flex flex-col gap-5 rounded-16 border border-soft-200 px-5 py-3 shadow-xs">
      <div className="flex flex-row justify-between">
        <div className="self-center">{title}</div>
        <div className="flex gap-4">
          {allowEdit && (
            <Button onClick={onEdit} variant="link" color="primary">
              Edit
            </Button>
          )}
        </div>
      </div>

      <DetailItem title="Start Date">{startDate ? dayjs(startDate).format('MM/DD/YYYY') : '-'}</DetailItem>
      <DetailItem title="Position">{position ? position : '-'}</DetailItem>
      <DetailItem title={workerClassification === WorkerClassification.Contractor ? 'Contractor ID' : 'Employee ID'}>
        {employeeId ? employeeId : '-'}
      </DetailItem>
      <DetailItem title="App Role">{getOptionLabel(role, AppRoleOptions) ?? '-'}</DetailItem>
      <DetailItem title="Manager">{managerName || '-'}</DetailItem>
      <DetailItem title="Department">{departmentName || '-'}</DetailItem>
    </div>
  );
}
