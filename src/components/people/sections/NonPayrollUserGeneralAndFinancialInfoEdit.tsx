import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { EnrichedEmployee } from '@/interfaces/employee';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { handleFormError, logError } from '@/utils/errorHandling';
import { GeneralInfoForm, generalInfoFormSchema, GeneralInfoValues } from './GeneralInfoForm';
import { MutateHammrUser } from '@/interfaces/user';
import { updateHammrUser } from '@/services/user';
import UserLine from '@hammr-icons/UserLine';
import { yupResolver } from '@/utils/yupResolver';
import { useCompany } from '@/hooks/useCompany';
import {
  FinancialInfoForm,
  financialInfoFormSchema,
  FinancialInfoValues,
} from '@/components/people/sections/FinancialInfoForm';
import { ObjectSchema } from 'yup';
import { getCompensationTypeFromHammrEarningRate } from '@/services/earning-rate';
import { employementContactInfoFormSchema, EmployementContactValues } from './EmployementInfoForm';
import dayjs from 'dayjs';

type FormValues = GeneralInfoValues & FinancialInfoValues & Pick<EmployementContactValues, 'position'>;

export default function NonPayrollUserGeneralAndFinancialInfoEdit({
  employee,
  open,
  setOpen,
  handleSuccess,
}: {
  employee: EnrichedEmployee;
  open: boolean;
  setOpen: (open: boolean) => void;
  handleSuccess: () => void;
  isOnPayroll?: boolean;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { company } = useCompany();

  const form = useForm<FormValues>({
    defaultValues: {
      firstName: employee?.hammrUser.firstName,
      lastName: employee?.hammrUser.lastName,
      phone: employee?.hammrUser.phone,
      workersCompCodeId: employee?.hammrUser.workersCompCode?.id.toString(),
      position: employee?.hammrUser.position,
      employmentType: employee?.primaryEarningRate
        ? getCompensationTypeFromHammrEarningRate(employee.primaryEarningRate)
        : 'Hourly',
      pay: parseFloat(employee?.primaryEarningRate?.amount).toFixed(2),
      email: employee?.hammrUser.email,
      gender: employee?.hammrUser.gender,
      ethnicity: employee?.hammrUser.ethnicity,
      veteranStatus: employee?.hammrUser.veteranStatus,
    },
    resolver: yupResolver(
      (
        generalInfoFormSchema.pick([
          'firstName',
          'lastName',
          'phone',
          'workersCompCodeId',
        ]) as ObjectSchema<GeneralInfoValues>
      )
        .concat(financialInfoFormSchema.pick(['pay']))
        .concat(employementContactInfoFormSchema.pick(['position']))
    ),
  });

  const onSubmit = async (data: FormValues) => {
    if (!company) {
      return;
    }
    setIsLoading(true);
    try {
      const updatedUser: Partial<MutateHammrUser> = {
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phone,
        workersCompCodeId: data.workersCompCodeId ? parseInt(data.workersCompCodeId) : undefined,
        position: data.position,
        email: data.email,
        gender: data.gender,
        ethnicity: data.ethnicity,
        veteranStatus: data.veteranStatus
      };

      if (data.employmentType === 'Hourly') {
        updatedUser.hourlyRate = parseFloat(data.pay);
      } else if (data.employmentType === 'Salaried') {
        updatedUser.salary = parseFloat(data.pay);
      }

      if (data.payEffectiveDate) {
        updatedUser.payEffectiveDate = dayjs(data.payEffectiveDate).startOf('day').valueOf();
      }

      await updateHammrUser(employee.hammrUser.id, updatedUser);

      handleSuccess();
      setOpen(false);
      toast({
        title: 'Success',
        description: 'Employee information successfully updated',
        toastVariant: 'success',
      });
    } catch (err) {
      handleFormError(err, form.setError);
      logError(err);
      toast({
        title: 'Error',
        description: (err as Error)?.message || 'An error occurred while updating the employee',
        toastVariant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Edit General Information" icon={<UserLine />}>
      <FormV2 onSubmit={form.handleSubmit(onSubmit)} onCancel={() => setOpen(false)} isLoading={isLoading}>
        <div className="space-y-4">
          <GeneralInfoForm
            form={form}
            showDOB={false}
            userExists={true}
            showAddress={false}
            showWorkersCompCode={true}
            showPhone={true}
          />
          <FinancialInfoForm form={form} isOnPayroll={false} />
        </div>
      </FormV2>
    </ModalV2>
  );
}
