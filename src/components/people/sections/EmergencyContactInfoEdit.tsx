import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { handleFormError, logError } from '@/utils/errorHandling';
import { HammrUser, UpdateUser } from '@/interfaces/user';
import { updateHammrUser } from '@/services/user';
import { yupResolver } from '@/utils/yupResolver';
import { useCompany } from '@/hooks/useCompany';
import { emergencyContactInfoFormSchema, EmergencyContactForm, EmergencyContactValues } from './EmergencyContactForm';
import PencilLine from '@/hammr-icons/PencilLine';

export default function EmergencyContactInfoEdit({
  user,
  open,
  setOpen,
  handleSuccess,
}: {
  user: HammrUser;
  open: boolean;
  setOpen: (open: boolean) => void;
  handleSuccess: () => void | Promise<void>;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { company } = useCompany();

  const defaultValues = useMemo(
    () => ({
      emergencyContactFirstName: user?.emergencyContactFirstName,
      emergencyContactLastName: user?.emergencyContactLastName,
      emergencyContactRelationship: user?.emergencyContactRelationship,
      emergencyContactPhone: user.emergencyContactPhone,
    }),
    [user]
  );

  const form = useForm<EmergencyContactValues>({
    defaultValues,
    resolver: yupResolver(emergencyContactInfoFormSchema),
  });

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form, open]);

  const onSubmit = async (data: EmergencyContactValues) => {
    if (!company) {
      return;
    }
    setIsLoading(true);
    try {
      const updatedUser: UpdateUser = {
        emergencyContactFirstName: data.emergencyContactFirstName,
        emergencyContactLastName: data.emergencyContactLastName,
        emergencyContactPhone: data.emergencyContactPhone,
        emergencyContactRelationship: data.emergencyContactRelationship,
        company: company.checkCompanyId,
        type: 'employee',
      };

      await updateHammrUser(user.id, updatedUser);

      await handleSuccess();
      setOpen(false);
      toast({
        title: 'Edited Emergency Contact Info',
        description: (
          <div>
            Successfully updated emergency contact info for{' '}
            <span className="font-medium">{`${user.firstName} ${user.lastName}`}</span>
          </div>
        ),
        toastVariant: 'success',
      });
    } catch (err) {
      handleFormError(err, form.setError);
      logError(err);
      toast({
        title: 'Error',
        description: (err as Error)?.message || 'An error occurred while updating emergency contact',
        toastVariant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Edit Emergency Contact" icon={<PencilLine />}>
      <FormV2
        submitText="Save"
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setOpen(false)}
        isLoading={isLoading}
      >
        <EmergencyContactForm form={form} />
      </FormV2>
    </ModalV2>
  );
}
