import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { logError } from '@/utils/errorHandling';
import BuildingLine from '@hammr-icons/BuildingLine';
import * as yup from 'yup';
import { yupResolver } from '@/utils/yupResolver';
import { createWorkplace } from '@/services/workplace';
import { TextField } from '@/components/elements/form/TextField';
import { FormMessage } from '@/hammr-ui/components/form';

interface WorkplaceFormData {
  address: string;
  city: string;
  state: string;
  postalCode: string;
}

const formSchema = yup.object().shape({
  address: yup.string().required('Please enter a street address'),
  city: yup.string().required('Please enter a city'),
  state: yup
    .string()
    .matches(/^[A-Za-z]{2}$/, 'Not a valid state abbreviation')
    .required('Please enter a state')
    .transform(value => value?.toUpperCase()),
  postalCode: yup
    .string()
    .matches(/^\d{5}(?:[-\s]\d{4})?$/, 'Not a valid postal code')
    .required('Please enter a postal code'),
});

export default function WorkplacesForm({
  open,
  setOpen,
  checkCompanyId,
  handleSuccess,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  handleSuccess?: () => void;
  checkCompanyId: string;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [addressError, setAddressError] = useState<string>();
  const { toast } = useToast();

  const form = useForm<WorkplaceFormData>({
    defaultValues: {
      address: '',
      city: '',
      state: '',
      postalCode: '',
    },
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    form.reset({});
    setAddressError(null);
  }, [form, open]);

  const setCheckSpecificErrorMessage = async (res) => {
    const error = (await res.json()).error;
    if (error?.type === 'address_invalid') {
      setAddressError(error.message);
    } else if (error?.type === 'state_not_supported') {
      form.setError('state', {
        type: 'manual',
        message: error.message,
      });
    } else if (error.input_errors) {
      if (error.input_errors[0].field === 'state') {
        form.setError('state', {
          type: 'manual',
          message: error.input_errors[0].message,
        });
      }
    }
  };

  const onSubmit = async (data: WorkplaceFormData) => {
    setIsLoading(true);
    setAddressError(undefined);
    try {
      const workplace = {
        company: checkCompanyId,
        address: {
          line1: data.address,
          city: data.city,
          state: data.state,
          postal_code: data.postalCode,
        },
      };

      const res = await createWorkplace(workplace);

      if (res.status === 400) {
        await setCheckSpecificErrorMessage(res);
      } else if (res.status === 201) {
        setOpen(false);
        handleSuccess?.();
      }
    } catch (err) {
      logError(err);
      toast({
        title: 'Error',
        description: err?.message || 'An error occurred while creating the workplace',
        toastVariant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Add New Workplace" icon={<BuildingLine />}>
      <FormV2
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setOpen(false)}
        isLoading={isLoading}
        submitText="Save"
      >
        <div className="flex flex-col gap-5">
          <TextField
            control={form.control}
            name="address"
            label="Street Address"
            placeholder="Enter street address"
            required
            error={form.formState.errors.address?.message}
          />

          <TextField
            control={form.control}
            name="city"
            label="City"
            placeholder="Enter city"
            required
            error={form.formState.errors.city?.message}
          />

          <div className="grid grid-cols-2 gap-4">
            <TextField
              control={form.control}
              name="state"
              label="State"
              placeholder="Enter state code"
              required
              error={form.formState.errors.state?.message}
            />
            <TextField
              control={form.control}
              name="postalCode"
              label="Postal Code"
              placeholder="Enter postal code"
              required
              error={form.formState.errors.postalCode?.message}
            />
          </div>

          {addressError && <FormMessage messageType="error">{addressError}</FormMessage>}
        </div>
      </FormV2>
    </ModalV2>
  );
}
