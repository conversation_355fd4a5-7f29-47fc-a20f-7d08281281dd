import { PropsWithChildren } from 'react';

interface DetailItemProps {
  title: string;
  rightContent?: React.ReactNode;
}

export function DetailItem({ title, rightContent, children }: PropsWithChildren<DetailItemProps>) {
  return (
    <div className="flex flex-col gap-1.5">
      <div className="flex justify-between">
        <div className="text-xs text-sub-600">{title}</div>
        {rightContent}
      </div>
      <div className="flex flex-row items-center gap-1 text-sm">{children}</div>
    </div>
  );
}
