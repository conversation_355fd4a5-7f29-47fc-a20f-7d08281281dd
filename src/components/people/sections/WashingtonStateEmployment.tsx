import { FieldErrors, Path, PathValue, useForm, UseFormReturn, useWatch } from 'react-hook-form';
import { SelectItem } from '@hammr-ui/components/select';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { TextField } from '@/components/elements/form/TextField';
import { useMutation, useQuery } from '@tanstack/react-query';
import { CheckCompanyDefinedAttribute, CheckCompanyDefinedAttributeValue } from '@/interfaces/check';
import { apiRequestCheck } from '@/utils/requestHelpers';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import * as yup from 'yup';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import { useToast } from '@hammr-ui/hooks/use-toast';
import { yupResolver } from '@hookform/resolvers/yup';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { updateHammrUser } from '@/services/user';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { FormV2 } from '@/components/elements/Form';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import UserLine from '@hammr-icons/UserLine';

interface WashingtonFieldsModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  userId: number;
  workersCompCodeId?: number;
  checkEmployeeId: string;
  onSuccess: () => void;
}

export const companyDefinedAttributesSchema = yup
  .object<CompanyDefinedAttributesForm>()
  .optional()
  .shape({
    wa_ltc_exemption: yup
      .string()
      .oneOf(['true', 'false'], 'Please select a valid option')
      .required('Please select whether the employee has a Washington LTC exemption'),
    wa_ltc_exemption_effective_start: yup.string().when('wa_ltc_exemption', {
      is: 'true',
      then: (schema) => schema.required('Please enter the effective start date for the LTC exemption'),
      otherwise: (schema) => schema.optional(),
    }),
    labor_and_industries_total_rate: yup
      .number()
      .typeError('Please enter a valid number')
      .moreThan(
        yup.ref('labor_and_industries_employee_rate'),
        'Total Hourly Rate must be greater than Employee Withholding Rate'
      )
      .required('Please enter the Washington L&I Total Hourly Rate'),
    labor_and_industries_employee_rate: yup
      .number()
      .typeError('Please enter a valid number')
      .lessThan(yup.ref('labor_and_industries_total_rate'), 'Total rate must be smaller than Total Hourly Rate')
      .required('Please enter the Washington L&I Hourly Employee Withholding Rate'),
    wa_corporate_officer: yup
      .string()
      .oneOf(['true', 'false'], 'Please select a valid option')
      .required('Please select the corporate officer status'),
    wa_soc_code: yup.string().when('wa_corporate_officer', {
      is: 'false',
      then: (schema) =>
        schema
          .required('Please enter the six-digit Standard Occupational Classification (SOC) code')
          .matches(/^\d{2}-\d{4}$/, 'Please enter a valid SOC code in the format XX-XXXX'),
      otherwise: (schema) => schema.optional(),
    }),
    wa_risk_class_code: yup
      .string()
      .test('six-digits', 'Risk class code must have 6 digits. Can include dashes.', (value) => {
        if (!value) return true; // Optional field
        return value.match(/\d/g)?.length === 6;
      })
      .optional(),
  });

// used to make error messages user friendly
const checkFieldsMappings = {
  wa_soc_code: 'SOC Code',
  wa_ltc_exemption: 'LTC Exemption',
  labor_and_industries_total_rate: 'L&I Total Rate',
  labor_and_industries_employee_rate: 'L&I Employee Rate',
  wa_corporate_officer: 'Corporate Officer',

  // The reason we named it "Workers Comp Code" is because setting a "Workers Comp Code"
  // actually sets this "wa_risk_class_code" internally.
  // In other words, the "Workers Comp Code" is synced with the "wa_risk_class_code".
  // Showing a message for "wa_risk_class_code" is a actually showing a message for "Workers Comp Code"
  wa_risk_class_code: 'Workers Comp Code',
};

export function WashingtonFieldsModal({
  open,
  setOpen,
  workersCompCodeId,
  userId,
  checkEmployeeId,
  onSuccess,
}: WashingtonFieldsModalProps) {
  const { toast } = useToast();

  const companyDefinedAttributes = useQuery<CheckCompanyDefinedAttribute[]>({
    queryKey: ['companyDefinedAttributes', checkEmployeeId],
    queryFn: () =>
      apiRequestCheck(`employees/${checkEmployeeId}/company_defined_attributes`).then(
        (response) => response.company_defined_attributes
      ),
    enabled: !!checkEmployeeId,
  });

  const formSchema = yup.object().shape({
    companyDefinedAttributes: yup.mixed<CompanyDefinedAttributesForm>().when('workersCompCode', () => {
      return companyDefinedAttributesSchema
        .pick(companyDefinedAttributes.data?.map((field) => field.name) as any)
        .required();
    }),
    workersCompCode: yup
      .string()
      .matches(/^-*([0-9]-*){6}$/, 'Must contain 6 digits for WA state employees (can have dashes)')
      .required('Workers Comp Code is required for WA state'),
  });

  const form = useForm({
    defaultValues: {
      workersCompCode: '',
      companyDefinedAttributes: {
        wa_soc_code: '',
        wa_ltc_exemption: '',
        labor_and_industries_total_rate: undefined,
        labor_and_industries_employee_rate: undefined,
        wa_corporate_officer: '',
      },
    },
    resolver: yupResolver(formSchema),
  });

  const { control, handleSubmit, reset } = form;

  const workersCompCodeValue = useWatch({ name: 'workersCompCode', control });

  const workersCompCodes = useQuery({
    queryKey: ['workersCompCodes'],
    queryFn: () => workersCompCodesService.get(),
    enabled: open,
  });

  const updateWorkplacesMutation = useMutation<
    any,
    Error,
    {
      workersCompCode: string;
      companyDefinedAttributes: CheckCompanyDefinedAttributeValue[];
    }
  >({
    mutationFn: async (data) => {
      await saveWashingtonFields(data.workersCompCode, checkEmployeeId, data.companyDefinedAttributes);

      await updateHammrUser(userId, {
        workersCompCodeId: workersCompCodes.data?.find((item) => item.code === data.workersCompCode)?.id,
      });

      onSuccess();
    },
    onSuccess: async () => {
      toast({
        title: 'Success',
        description: 'Washington State Employment Data successfully saved',
        toastVariant: 'success',
      });
      setOpen(false);
    },
  });

  useEffect(() => {
    if (!form.getFieldState('workersCompCode').isDirty && workersCompCodes.data) {
      const selectedWorkersComp = workersCompCodes.data.find((item) => item.id === workersCompCodeId);
      form.setValue('workersCompCode', selectedWorkersComp?.code);
    }
  }, [form, workersCompCodeId, workersCompCodeValue, workersCompCodes.data]);

  const onSubmit = (formData: {
    workersCompCode?: string;
    companyDefinedAttributes?: CompanyDefinedAttributesForm;
  }) => {
    const companyDefinedAttributes = getCompanyDefinedAttributesFormData(formData.companyDefinedAttributes);

    updateWorkplacesMutation.mutate({
      workersCompCode: formData.workersCompCode,
      companyDefinedAttributes,
    });
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        reset();
        setOpen(isOpen);
      }}
    >
      <DialogSurface>
        <DialogHeader title="Washington State Employment Data" showCloseButton icon={<UserLine />} />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setOpen(false)}
          isLoading={updateWorkplacesMutation.isPending}
          submitText="Save"
        >
          <div className="flex flex-col gap-6">
            <ControlledSelect
              name="workersCompCode"
              placeholder="Select a workers comp code"
              label="Workers Comp Code"
              control={form.control}
              required
              error={form.formState.errors.workersCompCode?.message}
            >
              {workersCompCodes.data?.map((item: WorkersCompCode) => (
                <SelectItem key={item.code} value={item.code}>{`${item.name} • ${item.code}`}</SelectItem>
              ))}
            </ControlledSelect>

            <WashingtonStateEmploymentFormFields form={form} employeeId={checkEmployeeId} />
          </div>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}

export interface CompanyDefinedAttributesForm {
  wa_ltc_exemption: string;
  wa_ltc_exemption_effective_start: string;
  labor_and_industries_total_rate: number;
  labor_and_industries_employee_rate: number;
  wa_soc_code: string;
  wa_corporate_officer: string;
}

type FormWithCompanyAttributes = { companyDefinedAttributes?: CompanyDefinedAttributesForm };

interface WashingtonStateEmploymentDataProps<T extends FormWithCompanyAttributes> {
  employeeId?: string;
  form: UseFormReturn<T>;
  preFillForm?: boolean;
}

export function WashingtonStateEmploymentFormFields<T extends FormWithCompanyAttributes>({
  employeeId,
  form,
}: WashingtonStateEmploymentDataProps<T>) {
  const checkCompanyDefinedAttributes = useQuery<CheckCompanyDefinedAttribute[]>({
    queryKey: ['companyDefinedAttributes', employeeId, 'formFields'],
    queryFn: () =>
      apiRequestCheck(`employees/${employeeId}/company_defined_attributes`).then(
        (response) => response.company_defined_attributes
      ),
    enabled: !!employeeId,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });

  const { setValue } = form;

  const LTCExemption = useWatch({
    name: 'companyDefinedAttributes.wa_ltc_exemption' as Path<T>,
    control: form.control,
  });
  const employeeRate = useWatch({
    name: 'companyDefinedAttributes.labor_and_industries_employee_rate' as Path<T>,
    control: form.control,
  });
  const totalRate = useWatch({
    name: 'companyDefinedAttributes.labor_and_industries_total_rate' as Path<T>,
    control: form.control,
  });
  const corporateOfficer = useWatch({
    name: 'companyDefinedAttributes.wa_corporate_officer' as Path<T>,
    control: form.control,
  });

  useEffect(() => {
    const fields = checkCompanyDefinedAttributes.data;

    const wa_ltc_exemption = fields?.find((item) => item.name === 'wa_ltc_exemption');
    const labor_and_industries_total_rate = fields?.find((item) => item.name === 'labor_and_industries_total_rate');
    const labor_and_industries_employee_rate = fields?.find(
      (item) => item.name === 'labor_and_industries_employee_rate'
    );
    const wa_soc_code = fields?.find((item) => item.name === 'wa_soc_code');
    const wa_corporate_officer = fields?.find((item) => item.name === 'wa_corporate_officer');

    setValue(
      'companyDefinedAttributes' as Path<T>,
      {
        wa_ltc_exemption: wa_ltc_exemption?.value,
        wa_ltc_exemption_effective_start: wa_ltc_exemption?.effective_start,
        labor_and_industries_total_rate: labor_and_industries_total_rate?.value ?? undefined,
        labor_and_industries_employee_rate: labor_and_industries_employee_rate?.value ?? undefined,
        wa_soc_code: wa_soc_code?.value ?? undefined,
        wa_corporate_officer: wa_corporate_officer?.value,
      } as PathValue<T, Path<T>>
    );
  }, [setValue, checkCompanyDefinedAttributes.isFetching, checkCompanyDefinedAttributes.data]);

  useEffect(() => {
    // when LTCExemption is "false" we don't need an effective_start date
    if (LTCExemption === 'false') {
      form.setValue('companyDefinedAttributes.wa_ltc_exemption_effective_start' as Path<T>, undefined);
    }
  }, [LTCExemption, form]);

  useEffect(() => {
    // Only trigger validation if the form is dirty and either rate field has been modified
    if (
      form.formState.isSubmitted &&
      (form.formState.dirtyFields.companyDefinedAttributes?.labor_and_industries_employee_rate ||
        form.formState.dirtyFields.companyDefinedAttributes?.labor_and_industries_total_rate)
    ) {
      // Trigger validation for both fields since they depend on each other
      form.trigger([
        'companyDefinedAttributes.labor_and_industries_employee_rate',
        'companyDefinedAttributes.labor_and_industries_total_rate',
      ] as Path<T>[]);
    }
  }, [employeeRate, totalRate, form]);

  const errors = form.formState.errors.companyDefinedAttributes as FieldErrors<CompanyDefinedAttributesForm>;
  return (
    <>
      {checkCompanyDefinedAttributes.isLoading ? (
        <div className="absolute left-0 top-0 z-10 flex h-full w-full bg-white-0/60">
          <div className="m-auto">
            <LoadingIndicator showText={false} />
          </div>
        </div>
      ) : undefined}

      {checkCompanyDefinedAttributes.data?.some((field) => field.name === 'wa_ltc_exemption') ? (
        <ControlledSelect
          label="Washington LTC Exemption"
          tooltip="This employee has provided an exemption approval letter from WA Cares and is exempt from this tax."
          name="companyDefinedAttributes.wa_ltc_exemption"
          control={form.control}
          required
          error={errors?.wa_ltc_exemption?.message as string}
        >
          <SelectItem value="true">Yes</SelectItem>
          <SelectItem value="false">No</SelectItem>
        </ControlledSelect>
      ) : undefined}

      {LTCExemption === 'true' ? (
        <ControlledDateInput
          name="companyDefinedAttributes.wa_ltc_exemption_effective_start"
          label="Effective start date for Washington LTC Exemption"
          control={form.control}
          required
        />
      ) : undefined}

      {checkCompanyDefinedAttributes.data?.some((field) => field.name === 'labor_and_industries_total_rate') ? (
        <div>
          <TextField
            label="L&I Total Hourly Rate"
            required
            name="companyDefinedAttributes.labor_and_industries_total_rate"
            control={form.control}
            placeholder="0.00"
            beforeContent="$"
            type="number"
            step="0.0001"
            error={errors?.labor_and_industries_total_rate?.message as string}
          />
          <div className="mt-1 text-xs text-sub-600">
            Hourly rate to the exact precision from the L&I rate notice, e.g. 1.1593
          </div>
        </div>
      ) : undefined}

      {checkCompanyDefinedAttributes.data?.some((field) => field.name === 'labor_and_industries_employee_rate') ? (
        <div>
          <TextField
            label="L&I Hourly Employee Withholding Rate"
            required
            name="companyDefinedAttributes.labor_and_industries_employee_rate"
            control={form.control}
            placeholder="0.00"
            beforeContent="$"
            type="number"
            step="0.0001"
            error={errors?.labor_and_industries_employee_rate?.message as string}
          />

          <div className="mt-1 text-xs text-sub-600">
            Hourly rate to the exact precision from the L&I rate notice, e.g. 1.1593
          </div>
        </div>
      ) : undefined}

      {checkCompanyDefinedAttributes.data?.some((field) => field.name === 'wa_corporate_officer') ? (
        <ControlledSelect
          label="Corporate officer status (WA)"
          tooltip="This employee is an owner or corporate officer of the business"
          required
          name="companyDefinedAttributes.wa_corporate_officer"
          control={form.control}
          error={errors?.wa_corporate_officer?.message as string}
        >
          <SelectItem value="true">Yes</SelectItem>
          <SelectItem value="false">No</SelectItem>
        </ControlledSelect>
      ) : undefined}

      {corporateOfficer === 'false' ? (
        <TextField
          label="Standard Occupational Classification (SOC) code"
          tooltip="Enter the six-digit code including the dash"
          required
          placeholder="Enter the six-digit code including the dash"
          name="companyDefinedAttributes.wa_soc_code"
          control={form.control}
          error={errors?.wa_soc_code?.message as string}
        />
      ) : undefined}
    </>
  );
}

export function getCompanyDefinedAttributesFormData(
  formData?: CompanyDefinedAttributesForm
): CheckCompanyDefinedAttributeValue[] {
  if (!formData) {
    return undefined;
  }
  const fields = [
    {
      name: 'wa_ltc_exemption',
      value: formData.wa_ltc_exemption,
      effective_start: formData.wa_ltc_exemption_effective_start
        ? dayjs(formData.wa_ltc_exemption_effective_start).format('YYYY-MM-DD')
        : undefined,
    },
    {
      name: 'labor_and_industries_total_rate',
      value: formData.labor_and_industries_total_rate,
    },
    {
      name: 'labor_and_industries_employee_rate',
      value: formData.labor_and_industries_employee_rate,
    },
    {
      name: 'wa_corporate_officer',
      value: formData.wa_corporate_officer,
    },
  ];

  if (formData.wa_corporate_officer === 'false') {
    fields.push({ name: 'wa_soc_code', value: formData.wa_soc_code });
  }

  return fields.filter((field) => field.value !== undefined);
}

export async function beautifyWashingtonStateErrorsFromCheck(error: any) {
  // beautify error messages
  if (error && 'message' in error) {
    error.message = Object.entries(checkFieldsMappings).reduce(
      (message, [field, name]) => (message as string).replace(field, `${name}`),
      error.message
    );
  }

  throw error;
}

export async function saveWashingtonFields(
  workersCompCode: string,
  checkEmployeeId: string,
  companyDefinedAttributes: CheckCompanyDefinedAttributeValue[] = [],
  isRiskClassCodeRequired = true
) {
  // The Check API has a limitation.
  // It doesn't accept the `wa_soc_code` at the same time with the `wa_corporate_officer` field.
  // A simple solution is to make the first call to Check WITHOUT the `wa_soc_code` field
  //   and then make a 2nd call WITH the `wa_soc_code` field.
  const corporateOfficer = companyDefinedAttributes.find((field) => field.name === 'wa_corporate_officer');
  const socCode = companyDefinedAttributes.find((field) => field.name === 'wa_soc_code');
  const fieldsWithoutSOCCode = companyDefinedAttributes.filter((field) => field.name !== 'wa_soc_code') ?? [];

  if (isRiskClassCodeRequired) {
    fieldsWithoutSOCCode.push({
      // in Hammr, the WorkersComp code is synced with the Check's `wa_risk_class_code` field
      name: 'wa_risk_class_code',
      value: workersCompCode,
    });
  }
  await apiRequestCheck(`employees/${checkEmployeeId}/company_defined_attributes`, {
    method: 'PATCH',
    body: {
      company_defined_attributes: fieldsWithoutSOCCode,
    },
  }).catch(beautifyWashingtonStateErrorsFromCheck);

  if (corporateOfficer?.value === 'false' && socCode) {
    await apiRequestCheck(`employees/${checkEmployeeId}/company_defined_attributes`, {
      method: 'PATCH',
      body: {
        company_defined_attributes: [socCode],
      },
    }).catch(beautifyWashingtonStateErrorsFromCheck);
  }
}
