import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { handleFormError, logError } from '@/utils/errorHandling';
import { HammrUser, UpdateUser } from '@/interfaces/user';
import { updateHammrUser } from '@/services/user';
import { yupResolver } from '@/utils/yupResolver';
import { useCompany } from '@/hooks/useCompany';
import {
  employementContactInfoFormSchema,
  EmployementContactForm,
  EmployementContactValues,
} from './EmployementInfoForm';
import PencilLine from '@/hammr-icons/PencilLine';
import { Employee } from '@/interfaces/employee';
import dayjs from 'dayjs';

export default function EmployementInfoEdit({
  user,
  open,
  checkEmployee,
  setOpen,
  handleSuccess,
}: {
  user: HammrUser;
  checkEmployee: Partial<Pick<Employee, 'start_date'>> | undefined;
  open: boolean;
  setOpen: (open: boolean) => void;
  handleSuccess: () => void | Promise<void>;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { company } = useCompany();

  const defaultValues = useMemo(
    () => ({
      startDate: checkEmployee?.start_date ? dayjs(checkEmployee.start_date).toDate() : null,
      employeeId: user?.employeeId,
      position: user?.position,
      role: user?.role,
      managerId: user?.managerId,
      departmentId: user?.department?.id,
    }),
    [user, checkEmployee]
  );

  const form = useForm<EmployementContactValues>({
    defaultValues,
    resolver: yupResolver(employementContactInfoFormSchema),
  });

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form, open]);

  const onSubmit = async (data: EmployementContactValues) => {
    if (!company) {
      return;
    }
    setIsLoading(true);
    try {
      const updatedUser: UpdateUser = {
        startDate: dayjs(data.startDate).format('YYYY-MM-DD'),
        employeeId: data.employeeId,
        position: data.position,
        role: data.role,
        managerId: data.managerId,
        company: company.checkCompanyId,
        type: 'employee',
        departmentId: data.departmentId,
      };

      await updateHammrUser(user.id, updatedUser);

      await handleSuccess();
      setOpen(false);
      toast({
        title: 'Edited Employement Info',
        description: (
          <div>
            Successfully updated employment info for{' '}
            <span className="font-medium">{`${user.firstName} ${user.lastName}`}</span>
          </div>
        ),
        toastVariant: 'success',
      });
    } catch (err) {
      handleFormError(err, form.setError);
      logError(err);
      toast({
        title: 'Error',
        description: (err as Error)?.message || 'An error occurred while updating emergency contact',
        toastVariant: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Edit Employment Information" icon={<PencilLine />}>
      <FormV2
        submitText="Save"
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setOpen(false)}
        isLoading={isLoading}
      >
        <EmployementContactForm form={form} workerClassification={user.workerClassification} />
      </FormV2>
    </ModalV2>
  );
}
