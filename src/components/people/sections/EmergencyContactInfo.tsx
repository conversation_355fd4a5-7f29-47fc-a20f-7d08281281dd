import PhoneLine from '@/hammr-icons/PhoneLine';
import Button from '@hammr-ui/components/button';
import { DetailItem } from './DetailItem';
import { EmergencyContactRelationship } from '@/interfaces/user';
import { EmergencyContactRelationshipOptions, getOptionLabel } from '@/options';

interface EmergencyContactInfoProps {
  title?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  relationship?: EmergencyContactRelationship;
  onEdit?: () => void;
  allowEdit?: boolean;
}

export function EmergencyContactInfo({
  title = 'Emergency Contact',
  firstName,
  lastName,
  phone,
  relationship,
  onEdit,
  allowEdit,
}: EmergencyContactInfoProps) {
  const fullName = [firstName, lastName].filter(Boolean).join(' ') || '-';

  return (
    <div className="flex flex-col gap-5 rounded-16 border border-soft-200 px-5 py-3 shadow-xs">
      <div className="flex flex-row justify-between">
        <div className="self-center">{title}</div>
        {allowEdit && (
          <Button onClick={onEdit} variant="link" color="primary">
            Edit
          </Button>
        )}
      </div>

      <DetailItem title="Full Name">{fullName}</DetailItem>
      <DetailItem title="Relationship">
        {getOptionLabel(relationship, EmergencyContactRelationshipOptions) || '-'}
      </DetailItem>
      <DetailItem title="Phone Number">
        {phone ? (
          <>
            <PhoneLine className="h-4 w-4 text-sub-600" />
            {phone}
          </>
        ) : (
          '-'
        )}
      </DetailItem>
    </div>
  );
}
