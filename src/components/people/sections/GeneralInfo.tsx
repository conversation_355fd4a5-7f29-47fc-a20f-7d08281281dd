import CalendarLine from '@/hammr-icons/CalendarLine';
import PhoneLine from '@/hammr-icons/PhoneLine';
import Button from '@hammr-ui/components/button';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import dayjs from 'dayjs';
import { DetailItem } from './DetailItem';
import { Ethnicity, Gender, VeteranStatus } from '@/interfaces/user';
import { EthnicityOptions, GenderOptions, getOptionLabel, VeteranStatusOptions } from '@/options';

interface GeneralInformationProps {
  title?: string;
  firstName: string;
  lastName: string;
  dob?: string;
  phone?: string;
  email?: string;
  gender?: Gender;
  ethnicity?: Ethnicity;
  veteranStatus?: VeteranStatus;
  workersCompCode?: WorkersCompCode;
  address?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postal_code: string;
  };
  ssn_last_four?: string;
  onEdit?: () => void;
  allowEdit?: boolean;
  isContractor?: boolean;
  isOnPayroll?: boolean;
}

export function GeneralInfo({
  title = 'Personal Information',
  firstName,
  lastName,
  dob,
  phone,
  email,
  gender,
  ethnicity,
  veteranStatus,
  address,
  workersCompCode,
  ssn_last_four,
  onEdit,
  allowEdit = false,
  isContractor = false,
  isOnPayroll,
}: GeneralInformationProps) {
  return (
    <div className="flex flex-col gap-5 rounded-16 border border-soft-200 px-5 py-3 shadow-xs">
      <div className="flex flex-row justify-between">
        <div className="self-center">{title}</div>
        {allowEdit && (
          <Button onClick={onEdit} variant="link" color="primary">
            Edit
          </Button>
        )}
      </div>

      <DetailItem title="Full Name">{`${firstName} ${lastName}`}</DetailItem>

      {!isContractor && isOnPayroll ? (
        <DetailItem title="Date of Birth">
          {dob ? (
            <>
              <CalendarLine className="h-4 w-4 text-sub-600" /> {dayjs(dob).format('MM/DD/YYYY')}
            </>
          ) : (
            '-'
          )}
        </DetailItem>
      ) : undefined}

      <DetailItem title="Phone Number">
        {phone ? (
          <>
            <PhoneLine className="h-4 w-4 text-sub-600" />
            {phone}
          </>
        ) : (
          '-'
        )}
      </DetailItem>

      <DetailItem title="Email Address">{email || '-'}</DetailItem>
      <DetailItem title="Gender">{getOptionLabel(gender, GenderOptions) || '-'}</DetailItem>
      <DetailItem title="Ethnicity">{getOptionLabel(ethnicity, EthnicityOptions) || '-'}</DetailItem>
      <DetailItem title="Veteran Status">{getOptionLabel(veteranStatus, VeteranStatusOptions) || '-'}</DetailItem>

      {isOnPayroll && (
        <DetailItem title="Home Address">
          <div className="flex flex-col">
            {address ? (
              <>
                <div className="text-sm">{address.line1}</div>
                {address.line2 && <div className="text-sm">{address.line2}</div>}
                <div className="text-sm">{`${address.city}, ${address.state}`}</div>
                <div className="text-sm">{address.postal_code}</div>
              </>
            ) : (
              '-'
            )}
          </div>
        </DetailItem>
      )}

      {!isContractor && isOnPayroll && (
        <DetailItem title="SSN">{ssn_last_four ? `XXX-XX-${ssn_last_four}` : '-'}</DetailItem>
      )}

      {!isContractor && (
        <DetailItem title="Workers Comp Code">
          {workersCompCode ? `${workersCompCode.name} (${workersCompCode.code})` : '-'}
        </DetailItem>
      )}
    </div>
  );
}
