import { Controller, Path, UseFormReturn } from 'react-hook-form';
import { TextField } from '@/components/elements/form/TextField';
import { FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import * as yup from 'yup';
import { PhoneInput, VALID_PHONE_NUMBER_REGEX } from '@/components/shared/PhoneInput';
import { EmergencyContactRelationship } from '@/interfaces/user';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { EmergencyContactRelationshipOptions } from '@/options';

export const emergencyContactInfoFormSchema = yup.object().shape({
  emergencyContactFirstName: yup.string().nullable().notRequired(),
  emergencyContactLastName: yup.string().nullable().notRequired(),
  emergencyContactRelationship: yup
    .mixed<EmergencyContactRelationship>()
    .oneOf(Object.values(EmergencyContactRelationship))
    .nullable()
    .notRequired(),
  emergencyContactPhone: yup
    .string()
    .matches(VALID_PHONE_NUMBER_REGEX, {
      message: 'Not a valid phone number',
      excludeEmptyString: true,
    })
    .nullable()
    .notRequired(),
});

export interface EmergencyContactValues {
  emergencyContactFirstName: string;
  emergencyContactLastName: string;
  emergencyContactRelationship: EmergencyContactRelationship;
  emergencyContactPhone: string | null;
}

export function EmergencyContactForm<T extends EmergencyContactValues>({ form }: { form: UseFormReturn<T> }) {
  return (
    <div className="flex flex-col gap-5">
      <div className="grid grid-cols-2 gap-4">
        <TextField
          control={form.control}
          name="emergencyContactFirstName"
          label="First Name"
          placeholder="Enter first name"
          error={form.formState.errors.emergencyContactFirstName?.message}
        />
        <TextField
          control={form.control}
          name="emergencyContactLastName"
          label="Last Name"
          placeholder="Enter last name"
          error={form.formState.errors.emergencyContactLastName?.message}
        />
      </div>
      <Controller
        control={form.control}
        name={'emergencyContactRelationship' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel>Relationship</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              items={EmergencyContactRelationshipOptions}
              value={field.value}
              onChange={field.onChange}
            />
            <FormMessage>{fieldState.error?.message as string}</FormMessage>
          </FormItem>
        )}
      />
      <Controller
        control={form.control}
        name={'emergencyContactPhone' as Path<T>}
        render={({ field, fieldState }) => (
          <FormItem error={!!fieldState.error}>
            <FormLabel className="mb-1">Phone number</FormLabel>
            <PhoneInput
              id="phone"
              name={field.name}
              value={field.value}
              onChange={field.onChange}
              placeholder="(*************"
              onBlur={field.onBlur}
            />
            {fieldState.error && <FormMessage className="mt-1">{fieldState.error?.message as string}</FormMessage>}
          </FormItem>
        )}
      />
    </div>
  );
}
