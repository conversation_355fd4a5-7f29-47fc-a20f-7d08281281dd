import { FC, useRef } from 'react';
import PencilLine from '@/hammr-icons/PencilLine';
import CompactButton from '@/hammr-ui/components/CompactButton';
import { useSignedUrl } from '@/hooks/useSignedUrl';
import { useToast } from '@/hooks/useToast';
import { HammrUser, UpdateUser } from '@/interfaces/user';
import { v4 as uuidv4 } from 'uuid';
import { useUploadFile } from '@/hooks/useUploadFile';
import { useCompany } from '@/hooks/useCompany';
import { updateHammrUser } from '@/services/user';
import { logError, showErrorToast } from '@/utils/errorHandling';
import Spinner from '@/hammr-ui/components/spinner';
import { Avatar, AvatarFallback, AvatarImage } from '@hammr-ui/components/RadixAvatar';
import { cn } from '@hammr-ui/lib/utils';

interface ProfileImageProps {
  user: HammrUser;
  onProfilePhotoChanged?: () => void;
}

export const ProfileImage: FC<ProfileImageProps> = ({ user, onProfilePhotoChanged }) => {
  const { addToast } = useToast();
  const { company } = useCompany();
  const { uploadAsync, isUploading } = useUploadFile('employee-profile-photos');
  const inputRef = useRef<HTMLInputElement>(null);

  async function uploadProfilePhoto(document: File) {
    try {
      const fileExtension = document.name.indexOf('.') ? document.name.split('.').pop() : '';

      const objectId = uuidv4() + (fileExtension ? '.' + fileExtension : '');

      await uploadAsync({ key: objectId, content: document });

      const updatedUser: UpdateUser = {
        profilePhotoObjectId: objectId,
        company: company.checkCompanyId,
        type: 'employee',
      };

      await updateHammrUser(user.id, updatedUser);
      onProfilePhotoChanged?.();

      addToast({
        title: 'Upload Successful',
        description: 'We have successfully uploaded profile photo',
        type: 'success',
      });
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to upload profile photo');
    }
  }

  return (
    <div className="relative flex size-14 items-center justify-center">
      <Profile user={user} className="size-14 text-xl" />
      <div className="absolute -bottom-0.5 -right-0.5 h-full w-full">
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full bg-background opacity-50">
            <Spinner className="text-foreground" />
          </div>
        )}
        <CompactButton
          className="absolute bottom-0 right-0 border-soft-200 bg-background"
          variant="outline"
          shape="round"
          onClick={() => inputRef.current?.click()}
          disabled={isUploading}
        >
          <PencilLine />
        </CompactButton>
        <input
          ref={inputRef}
          type="file"
          onChange={(e) => uploadProfilePhoto(e.target.files[0])}
          className="invisible w-0"
        />
      </div>
    </div>
  );
};

export function Profile({
  user,
  className,
}: {
  user: Pick<HammrUser, 'firstName' | 'lastName' | 'profilePhotoObjectId'>;
  className?: string;
}) {
  const [signedUrl] = useSignedUrl('employee-profile-photos', user?.profilePhotoObjectId);

  return (
    <Avatar className={cn('size-8', className)}>
      <AvatarImage src={signedUrl} alt={`${user?.firstName} ${user?.lastName}`} className="object-cover object-top" />
      <AvatarFallback className="font-medium text-strong-950">
        {user?.firstName[0]}
        {user?.lastName[0]}
      </AvatarFallback>
    </Avatar>
  );
}
