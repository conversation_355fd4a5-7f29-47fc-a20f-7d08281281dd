import { useForm } from 'react-hook-form';
import { ReactNode, useMemo, useState } from 'react';
import {
  GlAccountMappingSetting,
  IntegrationSupportedPlatform,
  IntegrationUserToken,
} from 'interfaces/integration-user-token';
import { addToast } from 'hooks/useToast';
import { useAuth } from 'hooks/useAuth';
import { useProjects } from 'hooks/data-fetching/useProjects';
import { Project } from 'interfaces/project';
import { FormItem, FormLabel } from '@/hammr-ui/components/form';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { cn } from '@/utils/cn';
import { Switch } from '@/components/ui/Switch';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import Button from '@/components/ui/button';
import { RiRefreshLine } from '@remixicon/react';
import TimesheetSyncOptionsDialog from './TimesheetSyncOptionsDialog';
import Spinner from '@/components/ui/spinner';
import dayjs from 'dayjs';

interface IntegrationDetailProps {
  integration: IntegrationSupportedPlatform;
  userToken?: IntegrationUserToken;
}

export const OBJECT_TYPES = {
  PAYROLL: 'PAYROLL',
  EMPLOYEES: 'EMPLOYEES',
  PROJECTS: 'PROJECTS',
  TIMESHEETS: 'TIMESHEETS',
  JOURNAL_ENTRIES: 'JOURNAL_ENTRIES',
} as const;

export const SYNC_TYPES = {
  PUSH: 'PUSH',
  PULL: 'PULL',
  MAP: 'MAP',
};

export const integrationNamesMapping = {
  QUICKBOOKS: 'QuickBooks Online',
  QUICKBOOKS_DESKTOP: 'QuickBooks Desktop',
  SAGE_INTACCT: 'Sage Intacct',
  WORKMANS_DASHBOARD: `Workman's Dashboard`,
};

interface IntegrationSetting {
  id: number;
  isEnabled: boolean;
  syncType: keyof typeof SYNC_TYPES;
  integrationUserTokenId: number;
  objectType: keyof typeof OBJECT_TYPES;
  metadata?: {
    entity?: string;
    entityName?: string;
    location?: string;
    locationName?: string;
  };
  lastSyncedAt: string; // ISO date string
  updatedAt: string; // ISO date string
  createdAt: string; // ISO date string
}

export interface GlAccountMappingSettingsResponse {
  glAccountMappingSettings: GlAccountMappingSetting;
  glAccountMappingSettingTypes: Record<
    string,
    {
      name: string;
      values: Array<{
        name: string;
        value: string | boolean;
      }>;
    }
  >;
}

interface UpdateGlAccountMappingSettingParams {
  setting: {
    [key: string]: string | number | boolean;
    integrationUserTokenId?: number;
  };
  id?: number;
}

export default function IntegrationDetailSettings({ integration, userToken }: IntegrationDetailProps) {
  const { user } = useAuth();
  const projects = useProjects(user.companyId) as Project[];
  const [initialAutoSyncValue, setInitialAutoSyncValue] = useState<string>('');
  const [showTimesheetSyncDialog, setShowTimesheetSyncDialog] = useState(false);

  const form = useForm({
    mode: 'all' as const,
    shouldUnregister: false,
  });

  // Query for GL Account Mapping Settings
  const glAccountMappingSettings = useQuery<GlAccountMappingSettingsResponse>({
    queryKey: ['glAccountMappingSettings', userToken?.id],
    queryFn: () => apiRequest(`gl-account-mapping-settings?integrationUserTokenId=${userToken?.id}`),
    enabled: !!userToken?.id,
  });

  const integrationObjectSettings = useQuery({
    queryKey: ['integration', integration.id, 'settings'],
    queryFn: () =>
      apiRequest<{
        settings: IntegrationSetting[];
      }>(`integrations/${integration.id}/settings`).then((response) => response.settings),
    staleTime: 0,
    refetchOnMount: true,
  });

  const updateSettings = useMutation({
    mutationFn: async ({
      isSyncUsed,
      options,
      message,
      ...formData
    }: {
      objectType: keyof typeof OBJECT_TYPES;
      isEnabled: boolean;
      syncType: keyof typeof SYNC_TYPES;
      isSyncUsed?: boolean;
      message?: string;
      options?: Record<string, any>;
    }) =>
      apiRequest(`integrations/${integration.id}/settings`, {
        method: 'POST',
        body: {
          ...formData,
          options,
        },
      }),
    onSuccess: async (_, { message }) => {
      addToast({
        title: 'Updated Setting',
        description: message ?? 'The setting has been successfully updated.',
        type: 'success',
      });
      // Force refetch both queries
      await Promise.all([integrationObjectSettings.refetch(), glAccountMappingSettings.refetch()]);
    },
    onError: () => {
      addToast({
        title: 'Failed to Update Setting',
        description: 'There was an error updating the setting. Please try again.',
        type: 'error',
      });
    },
  });

  // Mutation for updating GL Account Mapping Settings
  const updateGlAccountMappingSettingMutation = useMutation<unknown, Error, UpdateGlAccountMappingSettingParams>({
    mutationFn: async ({ setting, id }) => {
      if (id) {
        return apiRequest(`gl-account-mapping-settings/${id}`, {
          method: 'PATCH',
          body: setting,
        });
      } else {
        return apiRequest('gl-account-mapping-settings', {
          method: 'POST',
          body: {
            ...setting,
            platform: integration.id,
            integrationUserTokenId: userToken?.id,
          },
        });
      }
    },
    onSuccess: () => {
      addToast({
        title: 'Updated Integration',
        description: 'Your changes have been successfully saved.',
        type: 'success',
      });
      glAccountMappingSettings.refetch();
    },
    onError: () => {
      addToast({
        title: 'Integration Error',
        description: 'Unable to update settings.',
        type: 'error',
      });
      if (form.getValues('autoSync')) {
        form.setValue('autoSync', initialAutoSyncValue);
      }
    },
  });

  const onChange = async (name: string, value: string | number | boolean, settingFound: GlAccountMappingSetting) => {
    if (name === 'consolidateJournalEntryBy' && value === 'PROJECT') {
      // Force a fresh fetch of settings
      const { data: freshSettings } = await integrationObjectSettings.refetch();

      const projectSyncSetting = freshSettings?.find((item) => item.objectType === OBJECT_TYPES.PROJECTS);

      if (!projectSyncSetting?.isEnabled) {
        addToast({
          title: 'Not allowed',
          description: 'Please enable Projects syncing first',
          type: 'error',
        });
        return;
      }
    }

    updateGlAccountMappingSettingMutation.mutate({
      setting: {
        [name]: value,
        integrationUserTokenId: userToken?.id,
      },
      id: settingFound?.id,
    });
  };

  const integrationData = useMemo(() => {
    if (!glAccountMappingSettings.data) return [];

    const settingRow: GlAccountMappingSetting = glAccountMappingSettings.data.glAccountMappingSettings;
    const settingTypes = glAccountMappingSettings.data.glAccountMappingSettingTypes;
    const rowData: ReactNode[] = [];

    // iterate over settingTypes and create rowData
    Object.keys(settingTypes)
      .map((setting, index) => {
        const settingInfo = settingTypes[setting];
        const settingName = settingInfo.name;
        let settingValues = settingInfo.values;

        if (setting === 'autoSync' && integration.id === 'FOUNDATION') {
          return null;
        }

        // Filter out PROJECT option from consolidateJournalEntryBy and allow only specific integrations have it
        if (
          setting === 'consolidateJournalEntryBy' &&
          !['QUICKBOOKS', 'SAGE_INTACCT', 'QUICKBOOKS_DESKTOP'].includes(integration.id)
        ) {
          settingValues = settingValues.filter((value) => value.value !== 'PROJECT');
        }

        let defaultValue = '';

        if (setting === 'autoSync') {
          defaultValue = settingRow ? (settingRow[setting] ? 'true' : 'false') : '';
          setInitialAutoSyncValue(defaultValue);
        } else {
          defaultValue = settingRow ? settingRow[setting] : '';
        }

        rowData.push(
          <FormItem
            required
            className={cn({
              'mt-5': index !== 0 && !(integration.id === 'FOUNDATION' && setting === 'consolidateJournalEntryBy'),
            })}
          >
            <FormLabel>{settingName}</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              className="mt-1 w-full"
              items={settingValues.map((entry) => ({
                label: entry.name,
                value: String(entry.value),
              }))}
              value={defaultValue}
              onChange={(value) => onChange(setting, value, settingRow)}
            />
          </FormItem>
        );
      })
      .filter((row) => row !== null);

    return rowData;
  }, [glAccountMappingSettings.data, projects, integration.id]);

  if (!integrationData || integrationData.length === 0) {
    return null;
  }

  const projectsPullSyncSetting = integrationObjectSettings.data?.find(
    (item) => item.objectType === OBJECT_TYPES.PROJECTS && item.syncType === 'PULL'
  );

  const employeesSyncSetting = integrationObjectSettings.data?.find(
    (item) => item.objectType === OBJECT_TYPES.EMPLOYEES
  );

  const projectsPushSyncSetting = integrationObjectSettings.data?.find(
    (item) => item.objectType === OBJECT_TYPES.PROJECTS && item.syncType === 'PUSH'
  );

  const timesheetsSyncSetting = integrationObjectSettings.data?.find(
    (item) => item.objectType === OBJECT_TYPES.TIMESHEETS
  );

  const journalEntriesSyncSetting = integrationObjectSettings.data?.find(
    (item) => item.objectType === OBJECT_TYPES.JOURNAL_ENTRIES
  );

  return (
    <div className="flex max-w-[446px] flex-col gap-6">
      {integration.id !== 'WORKMANS_DASHBOARD' ? (
        <div className=" flex flex-col gap-4 rounded-xl border border-soft-200 p-4 shadow-xs">
          <div className="flex flex-row justify-between gap-3.5">
            <div>
              <h2 className="text-sm font-medium text-strong-950">Payroll</h2>
              <p className="mt-1 h-fit text-xs text-sub-600">
                Create journal entries in {integrationNamesMapping[integration.id]} after a payroll is processed and
                paid in Hammr.
              </p>
            </div>
          </div>
          <hr className="border-soft-200" />
          {integration.id === 'SAGE_INTACCT' ? (
            <div className="flex flex-row justify-between gap-3.5 pb-4 text-sm">
              <span>Intacct Entity</span>
              <span className="font-medium text-sub-600">
                {journalEntriesSyncSetting?.metadata?.entity
                  ? journalEntriesSyncSetting?.metadata?.entityName
                  : 'Top-level'}
              </span>
            </div>
          ) : undefined}
          {integrationData.map((row, index) => (
            <div key={index}>{row}</div>
          ))}
        </div>
      ) : undefined}

      {integration.id === 'QUICKBOOKS' ||
      integration.id === 'QUICKBOOKS_DESKTOP' ||
      integration.id === 'SAGE_INTACCT' ? (
        <div className="flex flex-col gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
          <div className="text-sm font-medium text-strong-950">Employees</div>
          <hr className="border-soft-200" />
          {integration.id === 'SAGE_INTACCT' ? (
            <div className="flex flex-row justify-between gap-3.5 text-sm">
              <span>Intacct Entity</span>
              <span className="font-medium text-sub-600">
                {employeesSyncSetting?.metadata?.entity ? employeesSyncSetting?.metadata?.entityName : 'Top-level'}
              </span>
            </div>
          ) : undefined}
          <div className="flex flex-row items-center justify-between gap-4">
            <div>
              <div className="mb-1 mt-2 text-sm">Hammr → {integrationNamesMapping[integration.id]}</div>
              <div className=" mb-2 text-xs text-sub-600">Syncs in real-time</div>
            </div>

            <Switch
              checked={employeesSyncSetting?.isEnabled}
              disabled={updateSettings.isPending && updateSettings.variables.objectType === OBJECT_TYPES.EMPLOYEES}
              onCheckedChange={(checked) => {
                if (!checked && timesheetsSyncSetting?.isEnabled) {
                  addToast({
                    title: 'Not allowed',
                    description: 'Employee sync cannot be disabled while Timesheets sync is enabled.',
                    type: 'error',
                  });
                  return;
                }
                updateSettings.mutate({
                  objectType: OBJECT_TYPES.EMPLOYEES,
                  isEnabled: checked,
                  syncType: 'PUSH',
                  message: checked ? 'Employee sync started. This typically takes 1-2 minutes to complete.' : undefined,
                });
              }}
            />
          </div>
        </div>
      ) : undefined}

      {integration.id === 'QUICKBOOKS' ||
      integration.id === 'QUICKBOOKS_DESKTOP' ||
      integration.id === 'SAGE_INTACCT' ||
      integration.id === 'WORKMANS_DASHBOARD' ? (
        <div className="flex flex-col gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
          <div className="text-sm font-medium text-strong-950">Projects</div>
          <hr className="border-soft-200" />
          {integration.id === 'SAGE_INTACCT' ? (
            <div className="flex flex-row justify-between gap-3.5 text-sm">
              <span>Intacct Entity</span>
              <span className="font-medium text-sub-600">
                {projectsPushSyncSetting?.metadata?.entity
                  ? projectsPushSyncSetting?.metadata?.entityName
                  : 'Top-level'}
              </span>
            </div>
          ) : undefined}
          {integration.id === 'SAGE_INTACCT' || integration.id === 'QUICKBOOKS_DESKTOP' ? (
            <>
              <div className="flex flex-row items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="mb-1 mt-2 text-sm">Hammr → {integrationNamesMapping[integration.id]} </div>
                  <div className="mb-2 text-xs text-sub-600">Syncs in real-time</div>
                </div>
                <Switch
                  checked={projectsPushSyncSetting?.isEnabled}
                  disabled={
                    updateSettings.isPending &&
                    updateSettings.variables.objectType === OBJECT_TYPES.PROJECTS &&
                    updateSettings.variables.syncType === 'PUSH'
                  }
                  onCheckedChange={(checked) => {
                    if (!checked && timesheetsSyncSetting?.isEnabled) {
                      addToast({
                        title: 'Not allowed',
                        description: 'Projects sync cannot be disabled while Timesheets sync is enabled.',
                        type: 'error',
                      });
                      return;
                    }

                    updateSettings.mutate({
                      objectType: OBJECT_TYPES.PROJECTS,
                      isEnabled: checked,
                      syncType: 'PUSH',
                      message: checked
                        ? 'Project sync started. This typically takes 1-2 minutes to complete.'
                        : 'Project sync turned off.',
                    });
                  }}
                />
              </div>
            </>
          ) : undefined}

          <div className="flex flex-row items-center justify-between gap-4">
            <div className="flex-1">
              <div className="mb-1 mt-2 text-sm">{integrationNamesMapping[integration.id]} → Hammr</div>
              <div className=" mb-2 text-xs text-sub-600">
                Syncs daily{' '}
                {projectsPullSyncSetting?.lastSyncedAt
                  ? `(last synced: ${dayjs(projectsPullSyncSetting?.lastSyncedAt).format('MMM D, h:mm A')})`
                  : ''}
              </div>
            </div>
            <Button
              beforeContent={<RiRefreshLine />}
              color="neutral"
              variant="stroke"
              size="small"
              disabled={!projectsPullSyncSetting?.isEnabled}
              loading={
                updateSettings.isPending &&
                updateSettings.variables.isSyncUsed &&
                updateSettings.variables.syncType === 'PULL' &&
                updateSettings.variables.objectType === OBJECT_TYPES.PROJECTS
              }
              onClick={() => {
                updateSettings.mutate({
                  objectType: OBJECT_TYPES.PROJECTS,
                  isEnabled: true,
                  syncType: 'PULL',
                  isSyncUsed: true,
                  message: 'Project sync started. This typically takes 1-2 minutes to complete.',
                });
              }}
            >
              Sync
            </Button>
            <Switch
              checked={projectsPullSyncSetting?.isEnabled}
              disabled={
                updateSettings.isPending &&
                updateSettings.variables.objectType === OBJECT_TYPES.PROJECTS &&
                updateSettings.variables.syncType === 'PULL'
              }
              onCheckedChange={(checked) => {
                if (!checked && timesheetsSyncSetting?.isEnabled) {
                  addToast({
                    title: 'Not allowed',
                    description: 'Projects sync cannot be disabled while Timesheets sync is enabled.',
                    type: 'error',
                  });
                  return;
                }

                updateSettings.mutate({
                  objectType: OBJECT_TYPES.PROJECTS,
                  isEnabled: checked,
                  syncType: 'PULL',
                  message: checked
                    ? 'Project sync started. This typically takes 1-2 minutes to complete.'
                    : 'Project sync turned off.',
                });
              }}
            />
          </div>
        </div>
      ) : undefined}

      {integration.id === 'QUICKBOOKS' ||
      integration.id === 'QUICKBOOKS_DESKTOP' ||
      integration.id === 'SAGE_INTACCT' ? (
        <div className="flex flex-col gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
          <div className="text-sm font-medium text-strong-950">Timesheets</div>
          <hr className="border-soft-200" />

          {integration.id === 'SAGE_INTACCT' ? (
            <div className="flex flex-row justify-between gap-3.5 text-sm">
              <span>Intacct Entity</span>
              <span className="font-medium text-sub-600">
                {timesheetsSyncSetting?.metadata?.entity ? timesheetsSyncSetting?.metadata?.entityName : 'Top-level'}
              </span>
            </div>
          ) : undefined}
          <div className="flex flex-row items-center justify-between gap-4">
            <div className="flex-1">
              <div className="mb-1 mt-2 text-sm">Hammr → {integrationNamesMapping[integration.id]}</div>
              <div className=" mb-2 text-xs text-sub-600">Syncs in real-time</div>
            </div>
            {updateSettings.isPending && updateSettings.variables.objectType === OBJECT_TYPES.TIMESHEETS ? (
              <Spinner className="h-5 w-5" />
            ) : undefined}
            <Switch
              checked={timesheetsSyncSetting?.isEnabled}
              disabled={updateSettings.isPending && updateSettings.variables.objectType === OBJECT_TYPES.TIMESHEETS}
              onCheckedChange={(checked) => {
                if (
                  checked &&
                  (!employeesSyncSetting?.isEnabled ||
                    !integrationObjectSettings.data?.find((item) => item.objectType === OBJECT_TYPES.PROJECTS)
                      ?.isEnabled)
                ) {
                  addToast({
                    title: 'Not allowed',
                    description: 'Please enable Employees and Projects syncing first',
                    type: 'error',
                  });
                  return;
                }

                if (checked) {
                  setShowTimesheetSyncDialog(true);
                } else {
                  updateSettings.mutate({
                    objectType: OBJECT_TYPES.TIMESHEETS,
                    isEnabled: false,
                    syncType: 'PUSH',
                  });
                }
              }}
            />
          </div>
        </div>
      ) : undefined}

      <TimesheetSyncOptionsDialog
        open={showTimesheetSyncDialog}
        onClose={() => setShowTimesheetSyncDialog(false)}
        onConfirm={(options) => {
          updateSettings.mutate({
            objectType: OBJECT_TYPES.TIMESHEETS,
            isEnabled: true,
            syncType: 'PUSH',
            message:
              options.all || options.startingFrom
                ? 'Timesheets sync started. This typically takes 1-2 minutes to complete.'
                : undefined,
            options,
          });
        }}
      />
    </div>
  );
}
