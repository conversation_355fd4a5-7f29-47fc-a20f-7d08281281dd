import { useAuth } from 'hooks/useAuth';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import { useRutterLink } from 'react-rutter-link';
import integrationUserTokenService from 'services/integration-user-token';
import { cn } from '@/utils/cn';
import { logError } from 'utils/errorHandling';
import { IntegrationSupportedPlatform, IntegrationUserToken } from 'interfaces/integration-user-token';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import router from 'next/router';
import Settings2Line from '@/hammr-icons/Settings2Line';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import { Switch } from '@/hammr-ui/components/Switch';
import Button from '@/hammr-ui/components/button';
import ToolsLine from '@/hammr-icons/ToolsLine';
import { addToast } from '@/hooks/useToast';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { apiRequest } from '@/utils/requestHelpers';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '../elements/Form';
import { TextField } from '@/components/elements/form/TextField';
import { useForm } from 'react-hook-form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';
import StepIndicator from '@/hammr-ui/components/StepIndicator';
import { Tooltip } from '@/components/ui/tooltip';
import * as yup from 'yup';
import { yupResolver } from 'utils/yupResolver';

interface SageIntacctConnectionData {
  companyId: string;
  userId: string;
  password: string;
}

interface SageIntacctObjectSettings {
  journal_entries: string;
  employees: string;
  projects: string;
  timesheets: string;
  employeeLocation?: string;
}

interface SageLocation {
  LOCATIONID: string;
  PARENTID?: string;
  NAME: string;
  PARENTNAME?: string;
}

const objectSettingsSchema = yup
  .object<SageIntacctObjectSettings>()
  .shape({
    journal_entries: yup.string().required('Journal Entries selection is required'),
    employees: yup.string().required('Employees selection is required'),
    projects: yup.string().required('Projects selection is required'),
    timesheets: yup.string().required('Timesheets selection is required'),
    employeeLocation: yup.string().when('employees', {
      is: 'top-level',
      then: (schema) => schema.required('Employee Location is required when Top-Level is selected'),
      otherwise: (schema) => schema.notRequired(),
    }),
  })
  .test(
    'entity-consistency',
    'Journal Entries and Timesheets must use the same entity as Employees when Employees is not Top-Level',
    function (values) {
      const { employees, journal_entries, timesheets } = values;

      // If employees is not top-level, journal_entries and timesheets must match employees
      if (employees && employees !== 'top-level') {
        if (journal_entries && journal_entries !== 'top-level' && journal_entries !== employees) {
          return this.createError({
            path: 'journal_entries',
            message: 'Journal Entries must use the same entity as Employees',
          });
        }
        if (timesheets && timesheets !== employees) {
          return this.createError({
            path: 'timesheets',
            message: 'Timesheets must use the same entity as Employees',
          });
        }
      }

      return true;
    }
  );

const tabs = [
  {
    name: 'General Settings',
    key: '/settings',
  },
  {
    name: 'Integrations',
    key: '/settings/integrations',
  },
] as const;

type IntegrationsOverviewTabsKey = (typeof tabs)[number]['key'];

const integrationsProviders = {
  CONDUCTOR: ConductorIntegrationProvider,
  SAGE_INTACCT: SageIntacctIntegrationProvider,
};

export default function IntegrationsOverview() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [selectedTab, setSelectedTab] = useState<IntegrationsOverviewTabsKey>('/settings/integrations');
  const [selectedUserToken, setSelectedUserToken] = useState<IntegrationUserToken>(null);
  const [openDialog, setOpenDialog] = useState(false);

  const userTokens = useQuery<IntegrationUserToken[]>({
    queryKey: ['userTokens'],
    queryFn: integrationUserTokenService.getHammrUserTokens,
  });

  const supportedPlatforms = useQuery({
    queryKey: ['supportedPlatforms'],
    queryFn: integrationUserTokenService.getSupportedPlatforms,
  });

  // some integrations like QUICKBOOKS_DESKTOP require an extra check
  // to see if the QBO Windows app is turned on or linked with us.
  const integrationsExtraChecks = {
    QUICKBOOKS: undefined,
    QUICKBOOKS_DESKTOP: () => apiRequest(`integrations/quickbooks_desktop/isEnabled`),
    SAGE_INTACCT: undefined,
    WORKMANS_DASHBOARD: undefined,
  };

  const integrationsExtraChecksQuery = useQuery({
    queryKey: ['integrationsExtraChecks', userTokens.data],
    queryFn: async () => {
      const entries = await Promise.all(
        Object.entries(integrationsExtraChecks).map(async ([key, fn]) => [key, await fn?.()])
      );

      return Object.fromEntries(entries);
    },
    enabled: userTokens.data?.some((token) => token.isEnabled && token.platform === 'QUICKBOOKS_DESKTOP') ?? false,
  });

  const onDisableIntegration = async () => {
    try {
      await integrationUserTokenService.deleteHammrUserToken(selectedUserToken.id);

      const integration = supportedPlatforms.data?.find((platform) => platform.id === selectedUserToken.platform);

      addToast({
        title: 'Disabled Integration',
        description: `Successfully disabled the integration ${integration.name}.`,
        type: 'success',
      });

      setOpenDialog(false);
      setSelectedUserToken(null);
      queryClient.invalidateQueries({ queryKey: ['userTokens'] });
      queryClient.invalidateQueries({ queryKey: ['supportedPlatforms'] });
    } catch (err) {
      logError(err);
      addToast({
        title: 'Integration Error',
        description: 'Unable to disable integration.',
        type: 'error',
      });
    }
  };

  if (!user || !user?.isCompanyAdmin || userTokens.isLoading || supportedPlatforms.isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!userTokens || !supportedPlatforms) return null;

  return (
    <>
      <PageHeader title="Settings" icon={<Settings2Line />} />

      <Tabs className="mx-8" value={selectedTab}>
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.key}
              value={tab.key}
              onClick={() => {
                router.push(tab.key);
              }}
            >
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="/settings/integrations" className="max-w-7xl">
          <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6 py-6">
            {supportedPlatforms.data?.map((platformInfo) => {
              const userToken = userTokens.data?.find((token) => token.platform === platformInfo.id && token.isEnabled);

              if (userToken) {
                const requiresExtraSetupCheck = integrationsExtraChecks[platformInfo.id];
                const checkingForExtraSetup = requiresExtraSetupCheck && integrationsExtraChecksQuery.isFetching;
                const extraSetupNeeded =
                  requiresExtraSetupCheck &&
                  !integrationsExtraChecksQuery.isFetching &&
                  integrationsExtraChecksQuery.data?.[platformInfo.id];

                return (
                  <div key={platformInfo.id} className={cn(`rounded-2xl border border-soft-200 p-5 shadow-xs`)}>
                    <div className="mb-2 flex justify-between">
                      <img src={platformInfo.logo} alt={platformInfo.name} className="h-[38px]" />
                      {userToken && (
                        <div className="flex flex-row gap-2">
                          {checkingForExtraSetup ? (
                            <Tooltip content="Connecting">
                              <span>
                                <LoadingIndicator size="xs" showText={false} />
                              </span>
                            </Tooltip>
                          ) : undefined}
                          <Switch
                            id={platformInfo.id}
                            checked={
                              userToken.isEnabled &&
                              (requiresExtraSetupCheck ? !integrationsExtraChecksQuery.data?.[platformInfo.id] : true)
                            }
                            disabled={checkingForExtraSetup || (requiresExtraSetupCheck && extraSetupNeeded)}
                            onCheckedChange={() => {
                              setSelectedUserToken(userToken);
                              setOpenDialog(true);
                            }}
                          />
                        </div>
                      )}
                    </div>
                    <div className="my-4">
                      <span className="text-strong-950">{platformInfo.name}</span>
                    </div>
                    <Button
                      type="button"
                      fullWidth
                      variant="outline"
                      color="neutral"
                      size="small"
                      disabled={checkingForExtraSetup}
                      onClick={() => {
                        if (extraSetupNeeded) {
                          return openURL(integrationsExtraChecksQuery.data[platformInfo.id]);
                        }

                        if (platformInfo.provider === 'RUTTER' || platformInfo.provider === 'CONDUCTOR' || platformInfo.provider === 'SAGE_INTACCT') {
                          router.push(`/settings/integrations/${platformInfo.urlId}`);
                        } else {
                          router.push(`/settings/integrations/manual/${platformInfo.urlId}`);
                        }
                      }}
                      beforeContent={<Settings2Line />}
                    >
                      {extraSetupNeeded ? 'Continue setup' : 'Manage'}
                    </Button>
                  </div>
                );
              }

              const Provider = integrationsProviders[platformInfo.provider] ?? RutterIntegrationProvider;
              return (
                <Provider key={platformInfo.id}>
                  {(setup) => (
                    <div className={cn(`rounded-2xl border border-soft-200 p-5 shadow-xs`)}>
                      <div className="mb-2 flex justify-between">
                        <img src={platformInfo.logo} alt={platformInfo.name} className="h-[38px]" />
                        <Switch
                          id={platformInfo.id}
                          checked={false}
                          onCheckedChange={() => {
                            setup(platformInfo);
                          }}
                        />
                      </div>
                      <div className="my-4">
                        <span className="text-strong-950">{platformInfo.name}</span>
                      </div>
                      <Button
                        type="button"
                        fullWidth
                        variant="outline"
                        color="neutral"
                        size="small"
                        onClick={() => {
                          setup(platformInfo);
                        }}
                        beforeContent={<ToolsLine />}
                      >
                        Set Up Integration
                      </Button>
                    </div>
                  )}
                </Provider>
              );
            })}
          </section>
        </TabsContent>
      </Tabs>

      {selectedUserToken && (
        <ConfirmDialog
          icon={<KeyIcon icon={<CloseCircleLine />} color="red" />}
          open={openDialog}
          setOpen={setOpenDialog}
          onConfirm={onDisableIntegration}
          title="Disable Integration"
          subtitle={
            <span>
              You are about to disable the integration <strong>{selectedUserToken.platform}</strong>. Do you want to
              proceed?
            </span>
          }
          confirmButton={{ color: 'error' }}
          confirmButtonText="Disable"
        />
      )}
    </>
  );
}

function openURL(url: string) {
  const v = window.outerHeight / 2 + window.screenY - 350;
  const w = window.outerWidth / 2 + window.screenX - 400;
  window.open(url, 'hammr-conductor', 'menubar=1, resizable=no, width=800, height=700, top=' + v + ', left=' + w);
}

function RutterIntegrationProvider({
  children,
}: {
  children: (setup: (platformInfo: IntegrationSupportedPlatform) => void) => ReactNode;
}) {
  const queryClient = useQueryClient();

  const config = {
    publicKey: process.env.NEXT_PUBLIC_RUTTER_PUBLIC_KEY,
    onSuccess: async (publicToken: string) => {
      try {
        // create access token in backend
        const response = await integrationUserTokenService.createHammrUserToken({
          publicToken,
          provider: 'RUTTER',
        });

        addToast({
          title: 'Enabled Integration',
          description: 'Successfully enabled the integration.',
          type: 'success',
        });

        if (response) {
          queryClient.invalidateQueries({ queryKey: ['userTokens'] });
          queryClient.invalidateQueries({ queryKey: ['supportedPlatforms'] });
        }
      } catch (error) {
        logError(error);
        addToast({
          title: 'Integration Error',
          description: 'Unable to enable integration.',
          type: 'error',
        });
      }
    },
  };

  const { open, ready, error } = useRutterLink(config);

  if (!ready || error)
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  return children((platformInfo: IntegrationSupportedPlatform) => {
    open({ platform: platformInfo.id });
  });
}

function ConductorIntegrationProvider({
  children,
}: {
  children: (setup: (platformInfo: IntegrationSupportedPlatform) => void) => ReactNode;
}) {
  const queryClient = useQueryClient();

  async function handleEnableIntegration({ id, provider }: IntegrationSupportedPlatform) {
    try {
      const response = await integrationUserTokenService.createHammrUserToken({
        provider,
        platform: id,
      });

      addToast({
        title: 'Enabled Integration',
        description: 'Successfully enabled the integration.',
        type: 'success',
      });

      if (response.url) {
        openURL(response.url);
      }
      if (response) {
        queryClient.invalidateQueries({ queryKey: ['userTokens'] });
        queryClient.invalidateQueries({ queryKey: ['supportedPlatforms'] });
      }
    } catch (error) {
      logError(error);
      addToast({
        title: 'Integration Error',
        description: 'Unable to enable integration.',
        type: 'error',
      });
    }
  }

  return children(handleEnableIntegration as (platformInfo: IntegrationSupportedPlatform) => void);
}

function SageIntacctIntegrationProvider({
  children,
}: {
  children: (setup: (platformInfo: IntegrationSupportedPlatform) => void) => ReactNode;
}) {
  const queryClient = useQueryClient();

  const [showSetupModal, setShowSetupModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [locations, setLocations] = useState<SageLocation[]>([]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<SageIntacctConnectionData>({
    defaultValues: {
      companyId: '',
      userId: '',
      password: '',
    },
  });

  const {
    control: objectControl,
    handleSubmit: handleObjectSubmit,
    formState: { errors: objectErrors },
    reset: resetObjectForm,
    watch: watchObjectForm,
    setValue: setObjectValue,
  } = useForm<SageIntacctObjectSettings>({
    resolver: yupResolver(objectSettingsSchema),
  });

  const selectedEntityForEmployees = watchObjectForm('employees');
  const selectedEntityForProjects = watchObjectForm('projects');
  const selectedEntityForTimesheets = watchObjectForm('timesheets');
  const selectedEntityForJournalEntries = watchObjectForm('journal_entries');

  const entities = locations.filter((location) => !location.PARENTID);
  const entityLocations = locations.filter((location) => location.PARENTID);

  const syncEntity = useCallback(
    (entityId: string) => {
      if (!entityId || entityId === 'top-level') return;

      setObjectValue('employees', entityId, { shouldValidate: true });
      setObjectValue('projects', entityId, { shouldValidate: true });
      setObjectValue('timesheets', entityId, { shouldValidate: true });
      setObjectValue('journal_entries', entityId, { shouldValidate: true });
    },
    [setObjectValue]
  );

  useEffect(() => {
    // Clear employeeLocation when employees selection changes from Top-Level
    if (selectedEntityForEmployees !== 'top-level') {
      setObjectValue('employeeLocation', undefined);
    }
  }, [selectedEntityForEmployees, setObjectValue]);

  useEffect(() => {
    syncEntity(selectedEntityForEmployees);
  }, [selectedEntityForEmployees, syncEntity]);
  useEffect(() => {
    syncEntity(selectedEntityForProjects);
  }, [selectedEntityForProjects, syncEntity]);
  useEffect(() => {
    syncEntity(selectedEntityForTimesheets);
  }, [selectedEntityForTimesheets, syncEntity]);
  useEffect(() => {
    syncEntity(selectedEntityForJournalEntries);
  }, [selectedEntityForJournalEntries, syncEntity]);

  const credentialsMutation = useMutation({
    mutationFn: async (data: SageIntacctConnectionData) => {
      return integrationUserTokenService.createHammrUserToken({
        provider: 'SAGE_INTACCT',
        platform: 'SAGE_INTACCT',
        metadata: {
          companyId: data.companyId,
          userId: data.userId,
          password: data.password,
        },
      });
    },
    onSuccess: (response) => {
      // Process locations if they exist in the response
      if (response.locations && Array.isArray(response.locations)) {
        setLocations(response.locations);
      }

      setCurrentStep(2);
    },
    onError: (error) => {
      logError(error);
      addToast({
        title: 'Integration Error',
        description: 'Unable to enable Sage Intacct integration.',
        type: 'error',
      });
    },
  });

  const objectSettingsMutation = useMutation({
    mutationFn: async (data: SageIntacctObjectSettings) => {
      const journalEntries = {
        objectType: 'JOURNAL_ENTRIES',
        metadata: {
          entity: data.journal_entries === 'top-level' ? undefined : data.journal_entries,
          entityName: locations.find((location) => location.LOCATIONID === data.journal_entries)?.NAME,
        },
      };

      const employeeMetadata: any = {
        entity: data.employees === 'top-level' ? undefined : data.employees,
        entityName: locations.find((location) => location.LOCATIONID === data.employees)?.NAME,
      };
      if (data.employees === 'top-level' && data.employeeLocation) {
        employeeMetadata.location = data.employeeLocation;
        employeeMetadata.locationName = locations.find(
          (location) => location.LOCATIONID === data.employeeLocation
        )?.NAME;
      }
      const employees = {
        objectType: 'EMPLOYEES',
        metadata: employeeMetadata,
      };

      const projects = {
        objectType: 'PROJECTS',
        metadata: {
          entity: data.projects === 'top-level' ? undefined : data.projects,
          entityName: locations.find((location) => location.LOCATIONID === data.projects)?.NAME,
        },
      };

      const timesheets = {
        objectType: 'TIMESHEETS',
        metadata: {
          entity: data.timesheets,
          entityName: locations.find((location) => location.LOCATIONID === data.timesheets)?.NAME,
        },
      };

      return apiRequest(`integrations/SAGE_INTACCT/finish-setup`, {
        method: 'POST',
        body: [journalEntries, employees, projects, timesheets],
      });
    },
    onSuccess: () => {
      addToast({
        title: 'Integration Complete',
        description: 'Successfully configured Sage Intacct integration.',
        type: 'success',
      });

      setShowSetupModal(false);
      reset();
      resetObjectForm();
      setCurrentStep(1);
      setLocations([]);
      queryClient.invalidateQueries({ queryKey: ['userTokens'] });
      queryClient.invalidateQueries({ queryKey: ['supportedPlatforms'] });

      router.push('/settings/integrations/sage-intacct');
    },
    onError: (error) => {
      logError(error);
      addToast({
        title: 'Configuration Error',
        description: 'Unable to configure object settings.',
        type: 'error',
      });
    },
  });

  return (
    <>
      {children(() => setShowSetupModal(true))}

      <ModalV2
        shouldCloseOnInteractOutside={false}
        open={showSetupModal}
        setOpen={(open) => {
          setShowSetupModal(open);
          if (!open) {
            reset();
            resetObjectForm();
            setCurrentStep(1);
            setLocations([]);
          }
        }}
        title="Connect Sage Intacct"
        icon={<ToolsLine />}
      >
        <StepIndicator currentStep={currentStep} steps={['Credentials', 'Setup Entity']} className="my-3 px-5" />

        {currentStep === 1 && (
          <FormV2
            onSubmit={handleSubmit((data) => credentialsMutation.mutate(data))}
            onCancel={() => {
              setShowSetupModal(false);
              reset();
              setCurrentStep(1);
              setLocations([]);
            }}
            isLoading={credentialsMutation.isPending}
            submitText="Continue"
          >
            <div className="space-y-4">
              <TextField
                name="companyId"
                label="Company ID"
                placeholder="Enter company ID"
                control={control}
                error={errors.companyId?.message}
                rules={{
                  required: 'Company ID is required',
                  minLength: {
                    value: 1,
                    message: 'Company ID cannot be empty',
                  },
                }}
                required
              />
              <TextField
                name="userId"
                label="User ID"
                placeholder="Enter user ID"
                control={control}
                error={errors.userId?.message}
                rules={{
                  required: 'User ID is required',
                  minLength: {
                    value: 1,
                    message: 'User ID cannot be empty',
                  },
                }}
                required
              />
              <TextField
                name="password"
                label="Password"
                type="password"
                placeholder="Enter password"
                control={control}
                error={errors.password?.message}
                rules={{
                  required: 'Password is required',
                  minLength: {
                    value: 1,
                    message: 'Password cannot be empty',
                  },
                }}
                required
              />
            </div>
          </FormV2>
        )}

        {currentStep === 2 && (
          <FormV2
            onSubmit={handleObjectSubmit((data) => objectSettingsMutation.mutate(data))}
            onCancel={() => {
              setCurrentStep(1);
            }}
            isLoading={objectSettingsMutation.isPending}
            submitText="Connect Sage Intacct"
            cancelText="Back"
          >
            <div className="space-y-4">
              <ControlledSelect
                name="journal_entries"
                label="Journal Entries"
                placeholder="Select Entity"
                control={objectControl}
                error={objectErrors.journal_entries?.message}
                required
              >
                <SelectItem value="top-level">Top-Level</SelectItem>
                {entities.map((entity) => (
                  <SelectItem key={entity.LOCATIONID} value={entity.LOCATIONID}>
                    {entity.NAME}
                  </SelectItem>
                ))}
              </ControlledSelect>

              <div className="flex gap-4">
                <div className="flex-1">
                  <ControlledSelect
                    name="employees"
                    label="Employees"
                    placeholder="Select Entity"
                    control={objectControl}
                    error={objectErrors.employees?.message}
                    required
                  >
                    <SelectItem value="top-level">Top-Level</SelectItem>
                    {entities.map((entity) => (
                      <SelectItem key={entity.LOCATIONID} value={entity.LOCATIONID}>
                        {entity.NAME}
                      </SelectItem>
                    ))}
                  </ControlledSelect>
                </div>

                {selectedEntityForEmployees === 'top-level' && (
                  <div className="flex-1">
                    <ControlledSelect
                      name="employeeLocation"
                      label="Location"
                      placeholder="Select Location"
                      control={objectControl}
                      error={objectErrors.employeeLocation?.message}
                      required
                    >
                      {entityLocations.map((location) => (
                        <SelectItem key={location.LOCATIONID} value={location.LOCATIONID}>
                          {location.NAME}
                        </SelectItem>
                      ))}
                    </ControlledSelect>
                  </div>
                )}
              </div>

              <ControlledSelect
                name="projects"
                label="Projects"
                placeholder="Select Entity"
                control={objectControl}
                error={objectErrors.projects?.message}
                required
              >
                <SelectItem value="top-level">Top-Level</SelectItem>
                {entities.map((entity) => (
                  <SelectItem key={entity.LOCATIONID} value={entity.LOCATIONID}>
                    {entity.NAME}
                  </SelectItem>
                ))}
              </ControlledSelect>

              <ControlledSelect
                name="timesheets"
                label="Timesheets"
                placeholder="Select Entity"
                control={objectControl}
                error={objectErrors.timesheets?.message}
                required
              >
                {entities.map((entity) => (
                  <SelectItem key={entity.LOCATIONID} value={entity.LOCATIONID}>
                    {entity.NAME}
                  </SelectItem>
                ))}
              </ControlledSelect>
            </div>
          </FormV2>
        )}
      </ModalV2>
    </>
  );
}
