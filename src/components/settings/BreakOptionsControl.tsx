import { useMemo } from 'react';
import { Checkbox } from '@hammr-ui/components/checkbox';

const breakOptionItems = [
  { label: 'No Breaks', value: 0 },
  { label: '15 mins', value: 15 },
  { label: '30 mins', value: 30 },
  { label: '60 mins', value: 60 },
];

interface BreakOptionsControlProps {
  value: number[];
  onChange?: (value: number[]) => void;
}

const BreakOptionsControl: React.FC<BreakOptionsControlProps> = ({ value, onChange }) => {
  const valueObj = useMemo(
    () =>
      value.reduce(
        (acc, curr) => {
          acc[curr] = true;
          return acc;
        },
        {} as Record<number, boolean>
      ),
    [value]
  );

  const handleSelect = (selectedValue: number) => {
    if (valueObj[selectedValue]) {
      onChange?.(value.filter((v) => v !== selectedValue));
    } else {
      onChange?.([...value, selectedValue].sort((a, b) => a - b));
    }
  };

  return (
    <div className="flex flex-col gap-3 rounded-2xl border border-soft-200 p-4 shadow-xs">
      {breakOptionItems.map((item) => (
        <label key={item.value} className="flex items-center gap-3">
          <Checkbox checked={!!valueObj[item.value]} onCheckedChange={() => handleSelect(item.value)} />
          <span className="text-strong-950">{item.label}</span>
        </label>
      ))}
    </div>
  );
};

export default BreakOptionsControl;
