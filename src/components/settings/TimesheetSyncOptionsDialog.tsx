import { useEffect } from 'react';
import { <PERSON><PERSON>, DialogBody, DialogFooter, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { FormControl, FormItem, FormLabel, FormMessage } from '@hammr-ui/components/form';
import { Button } from '@hammr-ui/components/button';
import { useDate } from 'hooks/useDate';
import { RadioGroup, RadioGroupItem } from '@hammr-ui/components/Radio';
import { Label } from '@hammr-ui/components/label';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from 'utils/yupResolver';
import DatePickerV2 from 'components/elements/form/ControlledDateInput';

interface TimesheetSyncOptionsDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (options: { all?: boolean; startingFrom?: string }) => void;
}

interface FormData {
  syncOption: string;
  startDate: Date | null;
}

const schema = yup.object().shape({
  syncOption: yup.string().required('Please select a sync option'),
  startDate: yup
    .date()
    .nullable()
    .when('syncOption', {
      is: 'startingFrom',
      then: (schema) => schema.required('Please select a date'),
      otherwise: (schema) => schema,
    }),
});

export default function TimesheetSyncOptionsDialog({ open, onClose, onConfirm }: TimesheetSyncOptionsDialogProps) {
  const { dayjs } = useDate();

  const {
    control,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    defaultValues: {
      syncOption: 'forward',
      startDate: null,
    },
    resolver: yupResolver(schema),
  });

  const selectedOption = watch('syncOption');

  useEffect(() => {
    if (open) {
      reset({
        syncOption: 'forward',
        startDate: null,
      });
    }
  }, [open, reset]);

  const onSubmit = (data: FormData) => {
    let options = {};

    switch (data.syncOption) {
      case 'all':
        options = { all: true };
        break;
      case 'startingFrom':
        options = { startingFrom: dayjs(data.startDate).format('YYYY-MM-DD') };
        break;
      case 'forward':
        options = {};
        break;
    }

    onConfirm(options);
    onClose();
  };

  // Disable dates from tomorrow onwards
  const disableFutureDates = (date: Date) => {
    return dayjs(date).isAfter(dayjs().endOf('day'));
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogSurface>
        <DialogHeader title="Timesheets" />
        <form onSubmit={handleSubmit(onSubmit)}>
          <DialogBody>
            <FormItem>
              <FormLabel className="mb-2">Initial timesheets sync</FormLabel>
              <FormControl>
                <Controller
                  name="syncOption"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup value={field.value} onValueChange={field.onChange}>
                      <div className="flex items-start gap-3">
                        <RadioGroupItem value="forward" id="forward" />
                        <div className="flex flex-col">
                          <Label htmlFor="forward" className="font-medium">
                            Sync only new timesheets going forward
                          </Label>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <RadioGroupItem value="all" id="all" />
                        <div className="flex flex-col">
                          <Label htmlFor="all" className="font-medium">
                            Sync all historical timesheets
                          </Label>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <RadioGroupItem value="startingFrom" id="startingFrom" />
                        <div className="flex flex-col">
                          <Label htmlFor="startingFrom" className="font-medium">
                            Sync timesheets starting from a specific date
                          </Label>
                        </div>
                      </div>
                    </RadioGroup>
                  )}
                />
              </FormControl>
              {errors.syncOption && <FormMessage>{errors.syncOption.message}</FormMessage>}
            </FormItem>

            {selectedOption === 'startingFrom' && (
              <DatePickerV2
                control={control}
                name="startDate"
                label="Sync timesheets starting from"
                required
                className="mt-4"
                dayPickerProps={{
                  disabled: disableFutureDates,
                  fromMonth: new Date(2000, 0),
                  toMonth: new Date(),
                }}
              />
            )}
          </DialogBody>
          <DialogFooter>
            <Button type="button" variant="stroke" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? <LoadingIndicator /> : 'Confirm'}
            </Button>
          </DialogFooter>
        </form>
      </DialogSurface>
    </Dialog>
  );
}
