import { useEffect, useMemo, useState } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import integrationUserTokenService from 'services/integration-user-token';
import {
  createGlAccountMapping,
  deleteDepartmentMappings,
  getDepartmentsWithCustomMappings,
  getGlAccountMappings,
  triggerIncrementalSync,
  updateGlAccountMapping,
} from 'services/gl-account-mappings';
import { addToast } from 'hooks/useToast';
import AlertFill from '@/hammr-icons/AlertFill';
import { useRouter } from 'next/router';
import Button from '@/hammr-ui/components/button';
import IntegrationDetailSettings, { GlAccountMappingSettingsResponse } from './IntegrationDetailSettings';
import { logError } from 'utils/errorHandling';
import IntegrationSyncHistory from './IntegrationSyncHistory';
import {
  GlAccountMapping,
  IntegrationSupportedPlatform,
  IntegrationUserToken,
} from 'interfaces/integration-user-token';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import Settings2Line from '@/hammr-icons/Settings2Line';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import { Badge } from '@/hammr-ui/components/badge';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { DepartmentsWithCustomMappings, GlAccount, GlAccountMappingResponse } from '@/interfaces/gl-account-mapping';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import CustomDepartmentMappingsModal from './CustomDepartmentMappingsModal';
import { apiRequest } from '@/utils/requestHelpers';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowsCircle from '@/hammr-icons/ArrowsCircle';
import { cn } from '@/utils/cn';
import DefaultMappingsTable from './DefaultMappingsTable';

interface IntegrationDetailProps {
  integration: IntegrationSupportedPlatform;
}

interface TableRow {
  category: string;
  integrationValue: React.ReactElement | null;
}

const tabs = [
  {
    name: 'Integration Settings',
    key: 'settings',
    isVisible: () => true,
  },
  {
    name: 'Payroll Account Mappings',
    key: 'account-mappings',
    isVisible: (integrationId: string) => integrationId !== 'WORKMANS_DASHBOARD',
  },
  {
    name: 'Payroll Sync History',
    key: 'sync-history',
    isVisible: (integrationId: string) => integrationId !== 'WORKMANS_DASHBOARD',
  },
] as const;

const defaultMappingTabs = [
  {
    name: 'Default Mapping',
    key: 'default-mapping',
  },
  {
    name: 'Custom Department Mapping',
    key: 'custom-department-mapping',
  },
] as const;

type DefaultMappingTab = (typeof defaultMappingTabs)[number]['key'];

type IntegrationTabsKey = (typeof tabs)[number]['key'];

const IntegrationDetail: React.FC<IntegrationDetailProps> = ({ integration }) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [openCustomDepartmentMapping, setOpenCustomDepartmentMapping] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<DepartmentsWithCustomMappings | null>(null);
  const [selectedTab, setSelectedTab] = useState<IntegrationTabsKey>(tabs[0].key);
  const [defaultMappingTab, setDefaultMappingTab] = useState<DefaultMappingTab>('default-mapping');
  const queryClient = useQueryClient();

  const userTokens = useQuery({
    queryKey: ['userTokens'],
    queryFn: integrationUserTokenService.getHammrUserTokens,
    enabled: router.isReady,
  });

  const userToken = userTokens.data?.find(
    (token: IntegrationUserToken) => token.platform === integration.id && token.isEnabled
  );

  // Query for GL Account Mapping Settings
  const glAccountMappingSettings = useQuery<GlAccountMappingSettingsResponse>({
    queryKey: ['glAccountMappingSettings', userToken?.id],
    queryFn: () => apiRequest(`gl-account-mapping-settings?integrationUserTokenId=${userToken?.id}`),
    enabled: !!userToken?.id,
  });

  const glAccounts = useQuery<GlAccount[]>({
    queryKey: ['glAccounts', userToken?.id],
    queryFn: () => apiRequest(`/integration-user-tokens/${userToken.id}/gl-accounts`),
    enabled: router.isReady && !!userToken,
  });

  const defaultGlAccountMappings = useQuery<GlAccountMappingResponse>({
    queryKey: ['defaultGlAccountMappings', integration.id, userToken?.id],
    queryFn: () => getGlAccountMappings(integration.id, userToken?.id),
    enabled: router.isReady && !!userToken,
  });

  const allCustomGlAccountMappings = useQuery<GlAccountMappingResponse>({
    queryKey: ['allCustomGlAccountMappings', integration.id, userToken?.id],
    queryFn: () => getGlAccountMappings(integration.id, userToken?.id, 'ALL_CUSTOM_MAPPINGS'),
    enabled: router.isReady && !!userToken,
  });

  const departmentsWithCustomMappings = useQuery<DepartmentsWithCustomMappings[]>({
    queryKey: ['departmentsWithCustomMappings', integration.id, userToken?.id],
    queryFn: () => getDepartmentsWithCustomMappings(integration.id, userToken?.id),
    enabled: router.isReady && !!userToken,
  });

  const columns = useMemo(
    (): ColDef<TableRow>[] => [
      {
        field: 'category',
        headerName: 'Hammr Payroll Category',
        sortable: true,
        cellRenderer: (params: ICellRendererParams<TableRow>) => params.value,
      },
      {
        field: 'integrationValue',
        headerName: `${integration.name} Account`,
        sortable: false,
        cellRenderer: (params: ICellRendererParams<TableRow>) => params.value,
      },
    ],
    [integration.name]
  );

  const departmentsWithCustomMappingsColumns = useMemo(
    (): ColDef[] => [
      { field: 'name', headerName: 'Department In Hammr', minWidth: 300 },
      {
        field: 'mappings',
        headerName: 'Mappings',
        minWidth: 300,
        cellRenderer: (params: ICellRendererParams<DepartmentsWithCustomMappings>) => {
          const hasCustomMappings = params.data.hasCustomMappings;
          const value = hasCustomMappings ? 'Custom Mapping' : 'Default Mapping';
          return (
            <div>
              <Select
                placeholder="Select an option"
                className="mt-1 w-full"
                value={value}
                onChange={(selectedValue: string) => {
                  if (selectedValue === 'Default Mapping' && hasCustomMappings) {
                    deleteDepartmentMappingsMutation.mutate(params.data.id);
                  } else if (selectedValue === 'Custom Mapping') {
                    setOpenCustomDepartmentMapping(true);
                    setSelectedDepartment(params.data);
                  }
                }}
              >
                <SelectItem value="Default Mapping">Default Mapping</SelectItem>
                <SelectItem value="Custom Mapping">Custom Mapping</SelectItem>
              </Select>
            </div>
          );
        },
      },
      {
        field: 'actions',
        headerName: '',
        cellRenderer: (params: ICellRendererParams<DepartmentsWithCustomMappings>) => {
          if (!params.data.hasCustomMappings) {
            return null;
          }

          return (
            <div className="flex justify-center">
              <LinkButton
                style="primary"
                onClick={() => {
                  handleDepartmentSelect(params.data);
                }}
              >
                View Accounts
              </LinkButton>
            </div>
          );
        },
      },
    ],
    [integration.id, userToken?.id, departmentsWithCustomMappings.data]
  );

  const onChange = async (category: string, accountId: string, glAccountMapping: GlAccountMapping | undefined) => {
    const account = glAccounts.data.find((account) => account.id === accountId);
    if (!account || !userToken) return;

    try {
      // Optimistically update the cache before making API call
      const updatedMapping = {
        payrollCategory: category,
        accountId: account.id,
        platformId: account.platformId,
        accountName: account.name,
        accountCategory: account.accountCategory,
        accountType: account.accountType,
        integrationUserTokenId: userToken.id,
        id: glAccountMapping?.id || `temp-${Date.now()}`,
      };
      queryClient.setQueryData(['defaultGlAccountMappings', integration.id, userToken?.id], (old: any) => {
        if (!old || !old.data) return old;
        const data = { ...old.data };
        if (glAccountMapping) {
          data.glAccountMappings = data.glAccountMappings.map((mapping: GlAccountMapping) =>
            mapping.payrollCategory === category ? { ...mapping, ...updatedMapping } : mapping
          );
        } else {
          data.glAccountMappings = [...data.glAccountMappings, updatedMapping];
        }
        return {
          ...old,
          data,
        };
      });
      if (glAccountMapping) {
        await updateGlAccountMapping(
          {
            payrollCategory: category,
            accountId: account.id,
            platformId: account.platformId,
            accountName: account.name,
            accountCategory: account.accountCategory,
            accountType: account.accountType,
            integrationUserTokenId: userToken.id,
          },
          glAccountMapping.id
        );
      } else {
        await createGlAccountMapping({
          payrollCategory: category,
          accountId: account.id,
          platformId: account.platformId,
          accountName: account.name,
          accountCategory: account.accountCategory,
          accountType: account.accountType,
          integrationUserTokenId: userToken.id,
        });
      }
      // defaultGlAccountMappings.refetch();
      addToast({
        title: 'Updated Integration',
        description: 'Your changes have been successfully saved.',
        type: 'success',
      });
    } catch (error) {
      queryClient.invalidateQueries({
        queryKey: ['defaultGlAccountMappings', integration.id, userToken?.id],
      });
      logError(error);
      addToast({
        title: 'Integration Error',
        description: 'Unable to update account mapping.',
        type: 'error',
      });
    }
  };

  const onDisableIntegration = async () => {
    if (!userToken) return;
    try {
      await integrationUserTokenService.deleteHammrUserToken(userToken.id);

      addToast({
        title: 'Disabled Integration',
        description: `Successfully disabled the integration ${integration.name}.`,
        type: 'success',
      });

      router.push('/settings/integrations');
    } catch (err) {
      logError(err);
      addToast({
        title: 'Integration Error',
        description: 'Unable to disable integration.',
        type: 'error',
      });
    }
  };

  useEffect(() => {
    if (router.query.tab) {
      const tabToSelect = tabs.find((tab) => router.query.tab === tab.key);
      if (tabToSelect) {
        setSelectedTab(tabToSelect.key);
      }
    }
  }, [router.query]);

  const showAccountsNotReady =
    defaultGlAccountMappings.data === null && defaultGlAccountMappings.isFetched && !defaultGlAccountMappings.isLoading;
  const showReEnableIntegration =
    !defaultGlAccountMappings.data && !defaultGlAccountMappings.isLoading && defaultGlAccountMappings.isError;

  const deleteDepartmentMappingsMutation = useMutation({
    mutationFn: (departmentId: number) => deleteDepartmentMappings(departmentId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['departmentsWithCustomMappings', integration.id, userToken?.id],
      });
      queryClient.invalidateQueries({
        queryKey: ['allCustomGlAccountMappings', integration.id, userToken?.id],
      });
      addToast({
        title: 'Success',
        description: 'Custom mappings deleted successfully.',
        type: 'success',
      });
    },
    onError: (error) => {
      logError(error);
      addToast({
        title: 'Error',
        description: 'Unable to delete custom mappings.',
        type: 'error',
      });
    },
  });

  const handleDepartmentSelect = (department: DepartmentsWithCustomMappings) => {
    setSelectedDepartment(department);
    setOpenCustomDepartmentMapping(true);
  };

  const triggerSyncMutation = useMutation({
    mutationFn: (variables: { tokenId: number; showGlobalToast: boolean }) => triggerIncrementalSync(variables.tokenId),
    onSuccess: async (data, variables) => {
      // Initial toast indicating the process has started
      if (variables.showGlobalToast) {
        addToast({
          title: 'Sync Process Started',
          description: 'Chart of accounts will be updated shortly. Please wait...',
          type: 'info',
        });
      }

      // TODO - why is this here @guilleeh?
      // Wait for 10 seconds
      await new Promise((resolve) => setTimeout(resolve, 10000));

      await queryClient.invalidateQueries({
        queryKey: ['defaultGlAccountMappings', integration.id, userToken?.id],
      });
      await queryClient.invalidateQueries({
        queryKey: ['allCustomGlAccountMappings', integration.id, userToken?.id],
      });
      await queryClient.invalidateQueries({
        queryKey: ['departmentsWithCustomMappings', integration.id, userToken?.id],
      });

      // Final toast after delay and invalidation
      if (variables.showGlobalToast) {
        addToast({
          title: 'Synced Chart of Accounts', // Your preferred title
          type: 'success',
        });
      }
    },
    onError: (error: any, variables) => {
      // Added 'variables'
      logError(error);
      if (variables.showGlobalToast) {
        // Conditionally show toast
        addToast({
          title: 'Sync Error',
          description:
            error?.response?.data?.error || error?.message || 'Failed to trigger account sync. Please try again.',
          type: 'error',
        });
      }
    },
  });

  const handleRefreshGlAccounts = async (showGlobalToast = true) => {
    if (!userToken) return;
    if (integration.id !== 'QUICKBOOKS_DESKTOP' && integration.id !== 'SAGE_INTACCT') {
      triggerSyncMutation.mutate({ tokenId: userToken.id, showGlobalToast });
    }
  };

  const combinedGlAccountsForSyncHistory = useMemo<GlAccountMapping[]>(() => {
    if (!userToken) {
      return [];
    }
    const enrichMapping = (mapping: any): GlAccountMapping => ({
      ...mapping,
      createdBy: userToken.createdBy,
      accountNumber: glAccounts.data?.find((acc) => acc.id === mapping.accountId)?.accountNumber || '',
      integrationUserTokenId: userToken.id,
      organizationId: userToken.organizationId,
    });

    const defaults = (defaultGlAccountMappings.data?.data.glAccountMappings || []).map(enrichMapping);
    const customs = (allCustomGlAccountMappings.data?.data.glAccountMappings || []).map(enrichMapping);

    const uniqueCustoms = customs.filter(
      (custom) => !defaults.some((def) => def.id === custom.id && def.payrollCategory === custom.payrollCategory)
    );

    return [...defaults, ...uniqueCustoms];
  }, [defaultGlAccountMappings.data, allCustomGlAccountMappings.data, userToken]);

  return (
    <>
      <PageHeader
        title={integration.name}
        subtitle={`Company ID: ${userToken?.storeUniqueId ? userToken.storeUniqueId : ''}`}
        icon={<Settings2Line />}
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Settings" onClick={() => router.push('/settings')} />
            <BreadcrumbItem text="Integrations" onClick={() => router.push('/settings/integrations')} />
            <BreadcrumbItem text={integration.name} active />
          </Breadcrumbs>
        }
        headerRight={
          <Button
            title="Disable"
            variant="outline"
            color="error"
            onClick={() => setOpen(true)}
            beforeContent={<CloseCircleLine />}
          >
            Disable
          </Button>
        }
      />

      {showAccountsNotReady ? (
        <div className="flex justify-center">
          <Badge color="orange" variant="lighter" size="medium" shape="square" className="rounded-lg p-6">
            <div className="flex items-center justify-between">
              <AlertFill className="text-warning-base" />
              <p className="text-md pl-2 font-medium text-strong-950">
                Syncing accounts, please try again after a 1-2 minutes...
              </p>
            </div>
          </Badge>
        </div>
      ) : showReEnableIntegration ? (
        <div className="flex justify-center">
          <Badge color="orange" variant="lighter" size="medium" shape="square" className="rounded-lg p-6">
            <div className="flex items-center justify-between">
              <AlertFill className="text-warning-base" />
              <p className="text-md pl-2 font-medium text-strong-950">
                Rutter connection not healthy. Please disable and re-enable.
              </p>
            </div>
          </Badge>
        </div>
      ) : defaultGlAccountMappings.isLoading ||
        allCustomGlAccountMappings.isLoading ||
        departmentsWithCustomMappings.isLoading ||
        userTokens.isLoading ? (
        <div className="flex h-full w-full items-center justify-center">
          <LoadingIndicator />
        </div>
      ) : (
        <Tabs className="mx-8" value={selectedTab} onValueChange={(value: IntegrationTabsKey) => setSelectedTab(value)}>
          <TabsList>
            {tabs
              .filter((tab) => tab.isVisible(integration.id))
              .map((tab) => (
                <TabsTrigger
                  key={tab.key}
                  value={tab.key}
                  onClick={() => {
                    const newQuery = { ...router.query, tab: tab.key };
                    router.replace(
                      {
                        pathname: router.pathname,
                        query: newQuery as Record<string, string>,
                      },
                      '',
                      { shallow: false }
                    );
                  }}
                >
                  {tab.name}
                </TabsTrigger>
              ))}
          </TabsList>

          <TabsContent value="settings">
            <IntegrationDetailSettings integration={integration} userToken={userToken} />
          </TabsContent>
          <TabsContent value="account-mappings" className="max-w-3xl">
            {/* only show if consolidation by department is enabled */}
            <div
              className={cn(
                'block w-full sm:flex',
                glAccountMappingSettings.data?.glAccountMappingSettings.consolidateJournalEntryBy === 'DEPARTMENT'
                  ? 'sm:justify-between'
                  : 'sm:justify-end'
              )}
            >
              {glAccountMappingSettings.data?.glAccountMappingSettings.consolidateJournalEntryBy === 'DEPARTMENT' && (
                <Tabs
                  value={defaultMappingTab}
                  onValueChange={(value) => setDefaultMappingTab(value as DefaultMappingTab)}
                  className="mb-6"
                >
                  <TabList>
                    {defaultMappingTabs.map((tab) => (
                      <TabItem key={tab.key} value={tab.key}>
                        {tab.name}
                      </TabItem>
                    ))}
                  </TabList>
                </Tabs>
              )}

              {/*
              Temporary code for checking if we should display this button or not
                until we figure out a more scalable solution
              */}
              {integration.id !== 'QUICKBOOKS_DESKTOP' ? (
                <Button
                  variant="outline"
                  color="neutral"
                  className="mb-6 "
                  beforeContent={
                    <ArrowsCircle
                      className={cn('fill-neutral-500', triggerSyncMutation.isPending ? 'animate-spin' : '')}
                    />
                  }
                  onClick={() => handleRefreshGlAccounts(true)}
                  disabled={triggerSyncMutation.isPending}
                >
                  Refresh Chart of Accounts
                </Button>
              ) : undefined}
            </div>
            <div>
              {defaultMappingTab === 'default-mapping' && (
                <DefaultMappingsTable
                  integration={integration}
                  userToken={userToken}
                  glAccountMappings={defaultGlAccountMappings.data}
                  glAccounts={glAccounts.data}
                  isLoading={defaultGlAccountMappings.isLoading || glAccounts.isLoading}
                  onChange={onChange}
                />
              )}
              {defaultMappingTab === 'custom-department-mapping' && (
                <UpdatedTable
                  colDefs={departmentsWithCustomMappingsColumns}
                  rowData={departmentsWithCustomMappings.data?.map((row) => ({ ...row, actions: null }))}
                />
              )}
            </div>
          </TabsContent>
          <TabsContent value="sync-history">
            <div className="max-w-7xl">
              <IntegrationSyncHistory
                integration={integration}
                integrationUserToken={userToken}
                glAccounts={combinedGlAccountsForSyncHistory}
                onRefreshGlAccounts={() => handleRefreshGlAccounts(false)}
              />
            </div>
          </TabsContent>
        </Tabs>
      )}

      {userToken && (
        <ConfirmDialog
          icon={<KeyIcon icon={<CloseCircleLine />} color="red" />}
          open={open}
          setOpen={setOpen}
          onConfirm={onDisableIntegration}
          title="Disable Integration"
          subtitle={
            <span>
              You are about to disable the integration <strong>{integration.name}</strong>. Do you want to proceed?
            </span>
          }
          confirmButton={{ color: 'error' }}
          confirmButtonText="Disable"
        />
      )}

      {openCustomDepartmentMapping && selectedDepartment && userToken && (
        <CustomDepartmentMappingsModal
          open={openCustomDepartmentMapping}
          setOpen={(modalOpen) => {
            setOpenCustomDepartmentMapping(modalOpen);
            if (!modalOpen) {
              setSelectedDepartment(null);
            }
          }}
          columns={columns}
          department={selectedDepartment}
          integration={integration}
        />
      )}
    </>
  );
};

export default IntegrationDetail;
