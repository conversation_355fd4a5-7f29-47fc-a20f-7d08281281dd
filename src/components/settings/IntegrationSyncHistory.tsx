import { useState, useEffect } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { useRouter } from 'next/router';
import { listPayrolls } from 'services/payroll';
import { checkRequestPaginated } from 'utils/requestHelpers';
import { capitalize } from 'utils/stringHelper';
import { formatLocaleUsa } from 'utils/dateHelper';
import { useDate } from 'hooks/useDate';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { StatusCell } from 'components/payroll/PayrollHistory';
import { useAuth } from 'hooks/useAuth';
import integrationUserTokenService from 'services/integration-user-token';
import { addToast } from 'hooks/useToast';
import { logError } from 'utils/errorHandling';
import Button from 'hammr-ui/components/button';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { getSyncHistory } from 'services/sync-history';
import { Tooltip } from '@hammr-ui/components/tooltip';
import {
  GlAccountMapping,
  IntegrationUserToken,
  PayrollHistoryItem,
  PreviewDetails,
  SyncHistory,
} from 'interfaces/integration-user-token';
import { IntegrationSupportedPlatform } from 'interfaces/integration-user-token';
import ArrowsCircle from '@/hammr-icons/ArrowsCircle';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import ErrorWarning from '@/hammr-icons/ErrorWarning';
import { cn } from '@/utils/cn';
import PreviewJournalEntriesModal from './PreviewJournalEntriesModal';
import { formatUSD } from '@/utils/format';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import { Payroll } from '@/interfaces/payroll';

dayjs.extend(advancedFormat);

const LoadingButton = ({
  integrationId,
  payrollId,
  callback,
  mode = 'create',
  syncHistoryId,
  integrationUserTokenId,
}: {
  integrationId: string;
  payrollId: string;
  callback: () => Promise<void>;
  mode?: 'create' | 'update';
  syncHistoryId?: string;
  integrationUserTokenId: number;
}) => {
  const [isCreatingJournalEntry, setIsCreatingJournalEntry] = useState(false);

  const handleCreateJournalEntry = async (payrollId: string) => {
    if (isCreatingJournalEntry) {
      return;
    }

    setIsCreatingJournalEntry(true);
    try {
      if (mode === 'create') {
        await integrationUserTokenService.createJournalEntry({
          platform: integrationId,
          payrollId,
          integrationUserTokenId,
        });
      } else {
        await integrationUserTokenService.updateJournalEntry({
          payrollId,
          platform: integrationId,
          syncHistoryId,
          integrationUserTokenId,
        });
      }

      await callback();

      addToast({
        title: 'Updated Integration',
        description: 'Journal entry created successfully.',
        type: 'success',
      });
    } catch (error) {
      logError(error);
      if (error?.message?.toLowerCase()?.includes('account mappings')) {
        addToast({
          title: 'Account Mappings not complete',
          description: error.message,
          type: 'error',
        });
      } else {
        addToast({
          title: 'Sync Error',
          description: error.message,
          type: 'error',
        });
      }
    } finally {
      setIsCreatingJournalEntry(false);
    }
  };

  return (
    <Button
      title="Sync"
      fullWidth={false}
      className="items-center py-0"
      onClick={() => handleCreateJournalEntry(payrollId)}
    >
      <div className="flex items-center gap-x-2">
        <ArrowsCircle
          height={24}
          width={24}
          className={cn('fill-white', isCreatingJournalEntry ? 'animate-spin' : '')}
        />
        Sync
      </div>
    </Button>
  );
};

interface IntegrationSyncHistoryProps {
  integration: IntegrationSupportedPlatform;
  integrationUserToken: IntegrationUserToken;
  glAccounts: GlAccountMapping[];
  onRefreshGlAccounts?: () => Promise<void>;
}

const IntegrationSyncHistory: React.FC<IntegrationSyncHistoryProps> = ({
  integration,
  integrationUserToken,
  glAccounts,
  onRefreshGlAccounts,
}) => {
  const router = useRouter();
  const { user } = useAuth();
  const [payrollHistory, setPayrollHistory] = useState<PayrollHistoryItem[]>([]);
  const [openPreviewModal, setOpenPreviewModal] = useState(false);
  const [previewDetails, setPreviewDetails] = useState<PreviewDetails>();
  const { formatUsa, dayjs } = useDate();

  const colDefs: ColDef<PayrollHistoryItem>[] = [
    {
      field: 'payPeriod',
      headerName: 'Pay period',
      minWidth: 250,
      cellRenderer: (params: ICellRendererParams<PayrollHistoryItem>) => {
        return `${formatUsa(params.data.periodStart)} - ${formatUsa(params.data.periodEnd)}`;
      },
    },
    {
      field: 'payDay',
      headerName: 'Payday',
      maxWidth: 200,
      cellRenderer: (params: ICellRendererParams<PayrollHistoryItem>) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      field: 'type',
      headerName: 'Type',
      valueFormatter: (params) => params.value.replace('_', '-'),
      minWidth: 120,
      maxWidth: 150,
    },
    {
      field: 'payrollTotal',
      headerName: 'Total Payroll',
      minWidth: 120,
      valueFormatter: (params) => {
        if (isNaN(params.value)) {
          return ' ';
        } else {
          return formatUSD.format(params.value);
        }
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      minWidth: 120,
      maxWidth: 170,
      cellRenderer: (params: ICellRendererParams<PayrollHistoryItem>) => {
        return <StatusCell status={params.value} />;
      },
    },
    {
      field: 'actions' as keyof PayrollHistoryItem,
      headerName: 'Synced On',
      minWidth: 200,
      cellRenderer: (params: ICellRendererParams<PayrollHistoryItem>) => {
        if (params.data.failedAt) {
          return (
            <div className="flex h-full items-center justify-start gap-x-4">
              <LoadingButton
                integrationUserTokenId={integrationUserToken.id}
                integrationId={integration.id}
                payrollId={params.data.id}
                syncHistoryId={params.data.syncHistoryId}
                callback={async () => {
                  await fetchPayrollHistoryAndSyncHistory();
                  params.refreshCell();
                }}
                mode="update"
              />
              <div>
                <Tooltip content="There was a problem auto-syncing this payroll. Please click 'Sync' to retry.">
                  <KeyIcon icon={<ErrorWarning />} color="yellow" />
                </Tooltip>
              </div>
            </div>
          );
        }

        if (params.data.syncedAt) {
          return formatUsa(params.data.syncedAt, { showTime: true, showTimezone: true });
        }

        if (params.data.status !== 'paid') {
          return (
            <div className="flex h-full items-center justify-start">
              <Tooltip content="Syncing will be available once this payroll is paid">
                <Button title="Sync" fullWidth={false} className="items-center py-0" disabled>
                  <div className="flex items-center gap-x-2">
                    <ArrowsCircle
                      height={24}
                      width={24}
                      className="fill-disabled-300"
                      fill="rgb(var(--disabled-300))"
                    />
                    Sync
                  </div>
                </Button>
              </Tooltip>
            </div>
          );
        }

        return (
          <div className="flex h-full items-center justify-start">
            <LoadingButton
              integrationUserTokenId={integrationUserToken.id}
              integrationId={integration.id}
              payrollId={params.data.id}
              callback={async () => {
                await fetchPayrollHistoryAndSyncHistory();
                params.refreshCell();
              }}
            />
          </div>
        );
      },
    },
    {
      field: 'preview' as keyof PayrollHistoryItem,
      headerName: '',
      minWidth: 50,
      maxWidth: 100,
      cellRenderer: (params: ICellRendererParams<PayrollHistoryItem>) => {
        if (params.data.status !== 'paid') {
          return null;
        }

        return (
          <LinkButton
            style="primary"
            size="medium"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();

              onRefreshGlAccounts();

              setPreviewDetails({
                payrollId: params.data.id,
                platform: integration.id,
                failedAt: params.data.failedAt,
                syncHistoryId: params.data.syncHistoryId,
                integrationUserTokenId: integrationUserToken.id,
              });
              setOpenPreviewModal(true);
            }}
          >
            Preview
          </LinkButton>
        );
      },
    },
  ];

  const mapPayrolls = (payrolls: Payroll[], syncHistory: SyncHistory[]): PayrollHistoryItem[] => {
    return payrolls
      .map((payroll) => {
        // filter out payrolls by draft, partially_paid, failed
        if (['draft', 'partially_paid', 'failed'].includes(payroll.status)) {
          return null;
        }

        return {
          periodStart: dayjs(payroll.period_start).valueOf(),
          periodEnd: dayjs(payroll.period_end).valueOf(),
          payPeriod: dayjs(payroll.period_start).valueOf(),
          payDay: dayjs(payroll.payday).valueOf(),
          type: capitalize(payroll.type),
          status: payroll.status,
          approved_at: payroll.approved_at ? dayjs(payroll.approved_at).valueOf() : 0,
          id: payroll.id,
          syncHistoryId: syncHistory.find((sync) => sync.payrollId === payroll.id)?.id,
          syncedAt: syncHistory.find((sync) => sync.payrollId === payroll.id)?.syncedAt,
          failedAt: syncHistory.find((sync) => sync.payrollId === payroll.id)?.failedAt,
          payrollTotal: parseFloat(payroll.totals?.employee_gross) + parseFloat(payroll.totals?.contractor_gross),
          managed: payroll.managed,
        };
      })
      .filter((payroll) => payroll !== null);
  };

  const fetchPayrollHistoryAndSyncHistory = async () => {
    const payrolls = await listPayrolls({
      companyId: user?.checkCompanyId,
    });

    const syncHistory = await getSyncHistory(integrationUserToken.id);

    // fetch the next payrolls to show up to 50 payrolls
    const allPayrolls = await checkRequestPaginated(payrolls, 2);

    const mappedPayrolls = mapPayrolls(allPayrolls, syncHistory);

    // remove non-managed payrolls - those are payrolls imported to Check outside Hammr
    const filteredPayrolls = mappedPayrolls.filter((payroll) => payroll?.managed);

    setPayrollHistory(filteredPayrolls);
  };

  useEffect(() => {
    fetchPayrollHistoryAndSyncHistory();
  }, [user?.checkCompanyId]);

  if (!payrollHistory) {
    return null;
  }

  return (
    <div className="max-w-6xl pb-20">
      <UpdatedTable<PayrollHistoryItem>
        colDefs={colDefs}
        getRowHeight={() => 60}
        rowData={payrollHistory}
        emptyRowsText="No payrolls found"
        gridOptions={{
          groupDisplayType: 'groupRows',
        }}
        enablePagination
        onRowClicked={(event) => {
          if (event.event.defaultPrevented) {
            return;
          }

          if (event.data.status === 'draft') {
            router.push(`/payroll/${event.data.id}`);
          } else {
            router.push(`/payroll/${event.data.id}/details`);
          }
        }}
      />
      {previewDetails && openPreviewModal && (
        <PreviewJournalEntriesModal
          open={openPreviewModal}
          setOpen={setOpenPreviewModal}
          payrollHistory={payrollHistory}
          previewDetails={previewDetails}
          setPreviewDetails={setPreviewDetails}
          glAccounts={glAccounts}
          callback={async () => {
            await fetchPayrollHistoryAndSyncHistory();
            setOpenPreviewModal(false);
            setPreviewDetails(undefined);
          }}
        />
      )}
    </div>
  );
};

export default IntegrationSyncHistory;
