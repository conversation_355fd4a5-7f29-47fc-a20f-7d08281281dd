import { Controller, useForm } from 'react-hook-form';
import { useState } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import {
  GlAccountMapping,
  IntegrationSupportedPlatform,
  IntegrationUserToken,
} from 'interfaces/integration-user-token';
import integrationUserTokenService from 'services/integration-user-token';
import { createGlAccountMapping, getGlAccountMappings, updateGlAccountMapping } from 'services/gl-account-mappings';
import { addToast } from 'hooks/useToast';
import { useRouter } from 'next/router';
import IntegrationDetailSettings from './IntegrationDetailSettings';
import { FOUNDATION_ACCOUNT_TYPES } from 'utils/constants';
import { capitalize } from 'lodash';
import { logError } from 'utils/errorHandling';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import Settings2Line from '@/hammr-icons/Settings2Line';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import Button from '@/hammr-ui/components/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { Input } from '@/hammr-ui/components/input';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import ManualIntegrationExport from './ManualIntegrationExport';
import { useQuery } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

interface ManualIntegrationDetailProps {
  integration: IntegrationSupportedPlatform;
}

interface PayrollCategoryType {
  id: string;
  name: string;
}

interface PayrollCategoryTypesMapping {
  [key: string]: string;
}

interface TableRowData {
  category: React.ReactNode;
  account: React.ReactNode;
  accountType: React.ReactNode;
  accountCostClass: React.ReactNode;
}

interface FormValues {
  [key: string]: string;
}

const tabs = [
  {
    name: 'Settings',
    key: 'settings',
    isVisible: () => true,
  },
  {
    name: 'Account Mappings',
    key: 'account-mappings',
    isVisible: (integrationId: string) => integrationId !== 'WORKMANS_DASHBOARD',
  },
  {
    name: 'Export',
    key: 'export',
    isVisible: (integrationId: string) => integrationId !== 'WORKMANS_DASHBOARD',
  },
] as const;

type IntegrationTabsKey = (typeof tabs)[number]['key'];

const ManualIntegrationDetail: React.FC<ManualIntegrationDetailProps> = ({ integration }) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState<IntegrationTabsKey>(tabs[0].key);

  const userTokens = useQuery({
    queryKey: ['userTokens'],
    queryFn: integrationUserTokenService.getHammrUserTokens,
    enabled: router.isReady,
  });

  const userToken = userTokens.data?.find(
    (token: IntegrationUserToken) => token.platform === integration.id && token.isEnabled
  );

  const glAccountMappings = useQuery({
    queryKey: ['glAccountMappings', integration.id, userToken?.id],
    queryFn: () => getGlAccountMappings(integration.id, userToken?.id),
    enabled: router.isReady && !!userToken,
  });

  const form = useForm<FormValues>({
    mode: 'all' as const,
    shouldUnregister: false,
  });

  const { control, getValues } = form;

  const columns: ColDef<TableRowData>[] = [
    {
      field: 'category' as keyof TableRowData,
      headerName: 'Hammr Payroll Category',
      sortable: true,
      cellRenderer: (params: ICellRendererParams<TableRowData>) => (
        <div className="flex justify-start">{params.value}</div>
      ),
    },
    {
      field: 'account' as keyof TableRowData,
      headerName: `${integration.name} Account`,
      sortable: false,
      cellRenderer: (params: ICellRendererParams<TableRowData>) => params.value,
    },
    {
      field: 'accountType' as keyof TableRowData,
      headerName: `${integration.name} Account Type`,
      sortable: false,
      cellRenderer: (params: ICellRendererParams<TableRowData>) => params.value,
    },
    {
      field: 'accountCostClass' as keyof TableRowData,
      headerName: `${integration.name} Cost Class`,
      sortable: false,
      cellRenderer: (params: ICellRendererParams<TableRowData>) => params.value,
    },
  ];

  const onSubmit = async () => {
    try {
      const formValues = getValues();

      const rowData = integrationData.map((row, index) => ({
        category: formValues[`category_${index}`],
        account: formValues[`categoryName_${index}`],
        accountType: formValues[`accountType_${index}`],
        accountCostClass: formValues[`costClass_${index}`],
      }));

      // 1. Remove rows that have an empty account.
      const filteredRowData = rowData.filter((row) => row.account !== '');

      // 2. Check for invalid account types and exit if found
      const invalidAccountType = filteredRowData.find((row) => row.accountType === 'default');

      if (invalidAccountType) {
        addToast({
          title: 'Update Integration Failed',
          description: `Account type is required for ${glAccountMappings.data?.data.payrollCategoryTypesMapping[invalidAccountType.category]}`,
          type: 'error',
        });
        return;
      }

      let madeChanges = false;
      const mappings = glAccountMappings.data?.data.glAccountMappings || [];

      // for each row, check if the category exists in glAccountMappings
      // if it does, update the mapping
      // if it doesn't, create a new mapping
      await Promise.all(
        filteredRowData.map(async (row) => {
          const glAccountMapping = mappings.find((mapping) => mapping.payrollCategory === row.category);

          if (glAccountMapping) {
            // check if we really need to update the mapping, by comparing values
            if (
              glAccountMapping.accountId === row.account &&
              glAccountMapping.accountType === row.accountType &&
              glAccountMapping.accountCostClass === row.accountCostClass
            ) {
              return;
            }
            madeChanges = true;
            await updateGlAccountMapping(
              {
                payrollCategory: row.category,
                accountId: row.account,
                accountName: row.account,
                accountCostClass: row.accountCostClass,
                accountType: row.accountType,
                integrationUserTokenId: userToken?.id,
              },
              glAccountMapping.id
            );
          } else {
            madeChanges = true;
            await createGlAccountMapping({
              payrollCategory: row.category,
              accountId: row.account,
              accountName: row.account,
              accountCostClass: row.accountCostClass,
              accountType: row.accountType,
              integrationUserTokenId: userToken?.id,
            });
          }
        })
      );

      if (madeChanges) {
        addToast({
          title: 'Updated Integration',
          description: 'Your changes have been successfully saved.',
          type: 'success',
        });
        glAccountMappings.refetch();
      }
    } catch (error) {
      logError(error);
      addToast({
        title: 'Integration Error',
        description: 'Unable to update account mappings.',
        type: 'error',
      });
    }
  };

  const onDisableIntegration = async () => {
    try {
      if (!userToken?.id) return;
      await integrationUserTokenService.deleteHammrUserToken(userToken.id);

      addToast({
        title: 'Disabled Integration',
        description: `Successfully disabled the integration ${integration.name}.`,
        type: 'success',
      });

      router.push('/settings/integrations');
    } catch (err) {
      logError(err);
      addToast({
        title: 'Integration Error',
        description: 'Unable to disable integration.',
        type: 'error',
      });
    }
  };

  const integrationData = glAccountMappings.data?.data
    ? glAccountMappings.data.data.payrollCategoryTypes.map(
        (category: PayrollCategoryType & { isHeader?: boolean }, index: number) => {
          if (category.isHeader) {
            return {
              category: <p className="text-lg font-medium text-strong-950">{category.name}</p>,
              account: null,
              accountType: null,
              accountCostClass: null,
            };
          }

          const categoryName = glAccountMappings.data.data.payrollCategoryTypesMapping[category.id];
          const glAccountMapping = glAccountMappings.data.data.glAccountMappings.find(
            (mapping: GlAccountMapping) => mapping.payrollCategory === category.id
          );

          return {
            category: (
              <div className="flex !justify-start">
                <span>{categoryName}</span>
                <Controller
                  name={`category_${index}`}
                  control={control}
                  defaultValue={category.id}
                  render={(field) => <input hidden readOnly {...field} />}
                />
              </div>
            ),
            account: (
              <Controller
                name={`categoryName_${index}`}
                control={control}
                defaultValue={glAccountMapping?.accountId || ''}
                render={({ field }) => <Input {...field} />}
              />
            ),
            accountType: (
              <Controller
                name={`accountType_${index}`}
                control={control}
                defaultValue={glAccountMapping?.accountType || 'default'}
                render={({ field }) => (
                  <DropdownPicker
                    placeholder="Select an option"
                    className="mt-1 w-full"
                    items={FOUNDATION_ACCOUNT_TYPES.map((entry) => ({
                      label: capitalize(entry),
                      value: entry,
                    }))}
                    value={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
            ),
            accountCostClass: (
              <Controller
                name={`costClass_${index}`}
                control={control}
                defaultValue={glAccountMapping?.accountCostClass || ''}
                render={({ field }) => <Input {...field} />}
              />
            ),
          };
        }
      )
    : [];

  if (userTokens.isLoading || glAccountMappings.isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!integrationData || integrationData.length === 0) {
    return null;
  }

  return (
    <>
      <PageHeader
        title={integration.name}
        icon={<Settings2Line />}
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Settings" onClick={() => router.push('/settings')} />
            <BreadcrumbItem text="Integrations" onClick={() => router.push('/settings/integrations')} />
            <BreadcrumbItem text={integration.name} active />
          </Breadcrumbs>
        }
        headerRight={
          <Button
            title="Disable"
            variant="outline"
            color="error"
            onClick={() => setOpen(true)}
            beforeContent={<CloseCircleLine />}
          >
            Disable
          </Button>
        }
      />
      <Tabs className="mx-8" value={selectedTab} onValueChange={(value: IntegrationTabsKey) => setSelectedTab(value)}>
        <TabsList>
          {tabs
            .filter((tab) => tab.isVisible(integration.id))
            .map((tab) => (
              <TabsTrigger
                key={tab.key}
                value={tab.key}
                onClick={() => {
                  const newQuery = { ...router.query, tab: tab.key };
                  router.replace(
                    {
                      pathname: router.pathname,
                      query: newQuery as Record<string, string>,
                    },
                    '',
                    { shallow: false }
                  );
                }}
              >
                {tab.name}
              </TabsTrigger>
            ))}
        </TabsList>

        <TabsContent value="settings">
          <IntegrationDetailSettings integration={integration} userToken={userToken} />
        </TabsContent>
        <TabsContent value="account-mappings">
          <div className="max-w-7xl">
            <UpdatedTable
              colDefs={columns}
              rowData={integrationData}
              getRowHeight={() => 80}
              gridOptions={{
                suppressRowHoverHighlight: true,
                suppressCellFocus: true,
                gridId: 'account-mappings-table',
              }}
            />
            <br />
            <div className="flex justify-end">
              <Button onClick={onSubmit}>Save</Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="export">
          <div className="max-w-7xl">
            <ManualIntegrationExport integration={integration} integrationUserToken={userToken} />
          </div>
        </TabsContent>
      </Tabs>

      {userToken && (
        <ConfirmDialog
          icon={<KeyIcon icon={<CloseCircleLine />} color="red" />}
          open={open}
          setOpen={setOpen}
          onConfirm={onDisableIntegration}
          title="Disable Integration"
          subtitle={
            <span>
              You are about to disable the integration <strong>{integration.name}</strong>. Do you want to proceed?
            </span>
          }
          confirmButton={{ color: 'error' }}
          confirmButtonText="Disable"
        />
      )}
    </>
  );
};

export default ManualIntegrationDetail;
