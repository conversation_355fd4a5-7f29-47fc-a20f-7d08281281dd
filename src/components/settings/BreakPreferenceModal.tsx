import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { <PERSON>alog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { FormV2 } from 'components/elements/Form';
import { FormItem, FormLabel, FormControl } from '@hammr-ui/components/form';
import { Switch } from '@hammr-ui/components/Switch';
import { TimeInput } from '@/hammr-ui/components/time-input';
import { DurationPicker } from '@hammr-ui/components/duration-picker';
import * as yup from 'yup';
import { yupResolver } from 'utils/yupResolver';
import { useToast } from 'hooks/useToast';
import { useCompany } from 'hooks/useCompany';
import { logError, showErrorToast } from 'utils/errorHandling';
import timeTrackingSettings from '../../services/time-tracking-settings';
import { Company } from 'interfaces/company';
import BreakOptionsControl from './BreakOptionsControl';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import Settings2Line from '@hammr-icons/Settings2Line';
import { RadioGroup, RadioGroupItem } from '@hammr-ui/components/Radio';
import { Label } from '@hammr-ui/components/label';

dayjs.extend(utc);

interface FormData {
  areRealtimeBreaksEnabled: boolean;
  breakOptions: number[];
  areRealtimeBreakRemindersEnabled: boolean;
  realtimeBreakStartReminderAt: Date | null;
  realtimeBreakEndReminderAfter: number;
}

const formSchema = yup.object().shape({
  areRealtimeBreaksEnabled: yup.boolean(),
  breakOptions: yup.array().of(yup.number()),
  areRealtimeBreakRemindersEnabled: yup.boolean().when('areRealtimeBreaksEnabled', {
    is: (value: boolean) => value,
    then: () => yup.boolean(),
    otherwise: () => yup.boolean().notRequired(),
  }),
  realtimeBreakStartReminderAt: yup
    .date()
    .nullable()
    .when('areRealtimeBreaksEnabled', {
      is: true,
      then: (schema) => schema.required('Please select a time'),
      otherwise: (schema) => schema.notRequired(),
    }),
  realtimeBreakEndReminderAfter: yup.number().when('areRealtimeBreaksEnabled', {
    is: (value: boolean) => value,
    then: () => yup.number(),
    otherwise: () => yup.number().notRequired(),
  }),
});

interface BreakPreferenceModalProps {
  open: boolean;
  onClose: () => void;
  company: Company;
  onSuccess: () => void;
}

const BreakPreference: React.FC<BreakPreferenceModalProps> = ({ open, onClose, onSuccess, company }) => {
  const { addToast } = useToast();
  const { getCompany } = useCompany();

  const {
    control,
    watch,
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      areRealtimeBreaksEnabled: false,
      breakOptions: [],
      areRealtimeBreakRemindersEnabled: false,
      realtimeBreakStartReminderAt: null,
      realtimeBreakEndReminderAfter: 0,
    },
    shouldUnregister: false,
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    if (!open) return;

    reset({
      areRealtimeBreaksEnabled: company?.timeTrackingSettings.areRealtimeBreaksEnabled ?? false,
      breakOptions: company?.timeTrackingSettings.breakOptions ?? [],
      areRealtimeBreakRemindersEnabled: company?.timeTrackingSettings.areRealtimeBreakRemindersEnabled ?? false,
      realtimeBreakStartReminderAt: company?.timeTrackingSettings.realtimeBreakStartReminderAt
        ? dayjs.utc('1970-01-01T' + company.timeTrackingSettings.realtimeBreakStartReminderAt).toDate()
        : null,
      realtimeBreakEndReminderAfter: company?.timeTrackingSettings.realtimeBreakEndReminderAfter ?? 0,
    });
  }, [reset, open, company]);

  const areRealtimeBreaksEnabled = watch('areRealtimeBreaksEnabled');
  const areRealtimeBreakRemindersEnabled = watch('areRealtimeBreakRemindersEnabled');

  const onSubmit = async (data: FormData) => {
    try {
      const formattedTime = data.realtimeBreakStartReminderAt
        ? dayjs.utc(data.realtimeBreakStartReminderAt).format('HH:mm:ss') + '+00'
        : null;

      await timeTrackingSettings.update({
        ...data,
        realtimeBreakStartReminderAt: formattedTime,
      });

      await getCompany(company.id);

      addToast({
        title: 'Updated Break Preference',
        description: 'We have successfully updated the break preference.',
        type: 'success',
      });

      onSuccess();
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Unable to update break preference');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogSurface>
        <DialogHeader title="Break Preference" icon={<Settings2Line className="text-sub-600" />} />
        <FormV2 onSubmit={handleSubmit(onSubmit)} onCancel={onClose} isLoading={isSubmitting} submitText="Save">
          <FormItem>
            <FormControl>
              <Controller
                control={control}
                name="areRealtimeBreaksEnabled"
                render={({ field }) => (
                  <RadioGroup
                    value={field.value ? 'REALTIME' : 'CLOCK_OUT'}
                    onValueChange={(value) => field.onChange(value === 'REALTIME')}
                  >
                    <div className="flex items-start gap-4">
                      <RadioGroupItem value="CLOCK_OUT" id="clock_out" />
                      <div className="flex flex-col">
                        <Label htmlFor="clock_out" className="font-medium">
                          Clock-Out Breaks
                        </Label>
                        <p className="mt-2 text-sub-600">
                          When clocking out, employees can select how long their break was from a list of 15, 30, or 60
                          minutes.
                        </p>
                        {!areRealtimeBreaksEnabled && (
                          <FormItem className="mt-4">
                            <FormLabel>Select The Breaks To Enable</FormLabel>
                            <FormControl>
                              <Controller
                                control={control}
                                name="breakOptions"
                                render={({ field }) => (
                                  <BreakOptionsControl value={field.value} onChange={field.onChange} />
                                )}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      </div>
                    </div>

                    <div className="mt-6 flex items-start gap-4">
                      <RadioGroupItem value="REALTIME" id="realtime" />
                      <div className="flex flex-col">
                        <Label htmlFor="realtime" className="font-medium">
                          Real-Time Breaks
                        </Label>
                        <p className="mt-2 text-sub-600">Employees can start and stop a break while on the clock.</p>
                        {areRealtimeBreaksEnabled && (
                          <FormItem className="mt-4 gap-y-3 rounded-2xl border border-soft-200 p-4 shadow-xs">
                            <div className="flex items-center justify-between">
                              <FormLabel>Remind Employees To Start & End Breaks</FormLabel>
                              <FormControl>
                                <Controller
                                  control={control}
                                  name="areRealtimeBreakRemindersEnabled"
                                  render={({ field }) => (
                                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                                  )}
                                />
                              </FormControl>
                            </div>
                            {areRealtimeBreakRemindersEnabled && (
                              <>
                                <div className="flex items-center justify-between">
                                  <FormLabel>Remind to Start Break At</FormLabel>
                                  <FormControl className="w-32">
                                    <Controller
                                      control={control}
                                      name="realtimeBreakStartReminderAt"
                                      render={({ field }) => (
                                        <div className="w-32">
                                          <TimeInput value={field.value} onChange={(date) => field.onChange(date)} />
                                        </div>
                                      )}
                                    />
                                  </FormControl>
                                </div>

                                <div className="flex items-center justify-between">
                                  <FormLabel>Remind to End Break After</FormLabel>
                                  <FormControl className="w-48">
                                    <Controller
                                      control={control}
                                      name="realtimeBreakEndReminderAfter"
                                      render={({ field }) => (
                                        <div className="w-24">
                                          <DurationPicker
                                            value={field.value}
                                            onChange={field.onChange}
                                            minHour={0}
                                            maxHour={23}
                                          />
                                        </div>
                                      )}
                                    />
                                  </FormControl>
                                </div>
                              </>
                            )}
                          </FormItem>
                        )}
                      </div>
                    </div>
                  </RadioGroup>
                )}
              />
            </FormControl>
          </FormItem>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default BreakPreference;
