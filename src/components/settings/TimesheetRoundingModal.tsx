import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from 'utils/yupResolver';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import { DropdownPicker } from '@hammr-ui/components/Dropdown';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { FormV2 } from 'components/elements/Form';
import { FormItem, FormLabel, FormControl, FormMessage } from '@hammr-ui/components/form';
import { Switch } from '@hammr-ui/components/Switch';
import { Company } from 'interfaces/company';
import { useCompany } from 'hooks/useCompany';
import Settings2Line from '@/hammr-icons/Settings2Line';
import Alert from '@/hammr-ui/components/Alert';
import { TimesheetRoundingInterval, TimesheetRoundingType } from '@/interfaces/time-tracking-settings';
import timeTrackingSettings from '../../services/time-tracking-settings';

interface FormData {
  timesheetRoundingEnabled: boolean;
  timesheetRoundingType: TimesheetRoundingType;
  timesheetRoundingInterval: TimesheetRoundingInterval;
}

interface RoundingInfo {
  title: string;
  items: string[];
}

const infoMapping: Record<string, RoundingInfo> = {
  [`${TimesheetRoundingType.RoundDown}-${TimesheetRoundingInterval.Five}`]: {
    title: 'Round Down 5 minutes means',
    items: ['Rounds down to the nearest 5 minute interval', '8:04 am becomes 8:00 am'],
  },
  [`${TimesheetRoundingType.RoundDown}-${TimesheetRoundingInterval.Ten}`]: {
    title: 'Round Down 10 minutes means',
    items: ['Rounds down to the nearest 10 minute interval', '8:07 am becomes 8:00 am'],
  },
  [`${TimesheetRoundingType.RoundDown}-${TimesheetRoundingInterval.Fifteen}`]: {
    title: 'Round Down 15 minutes means',
    items: ['Rounds down to the nearest 15 minute interval', '8:12 am becomes 8:00 am'],
  },
  [`${TimesheetRoundingType.RoundUp}-${TimesheetRoundingInterval.Five}`]: {
    title: 'Round Up 5 minutes means',
    items: ['Rounds up to the nearest 5 minute interval', '8:04 am becomes 8:05 am'],
  },
  [`${TimesheetRoundingType.RoundUp}-${TimesheetRoundingInterval.Ten}`]: {
    title: 'Round Up 10 minutes means',
    items: ['Rounds up to the nearest 10 minute interval', '8:07 am becomes 8:10 am'],
  },
  [`${TimesheetRoundingType.RoundUp}-${TimesheetRoundingInterval.Fifteen}`]: {
    title: 'Round Up 15 minutes means)',
    items: ['Rounds up to the nearest 15 minute interval', '8:12 am becomes 8:15 am'],
  },
  [`${TimesheetRoundingType.Nearest}-${TimesheetRoundingInterval.Five}`]: {
    title: 'Nearest 5 minutes means',
    items: [
      'Rounds up or down depending on the interval that is closer',
      '8:02 am becomes 8:00 am',
      '8:03 am becomes 8:05 am',
    ],
  },
  [`${TimesheetRoundingType.Nearest}-${TimesheetRoundingInterval.Ten}`]: {
    title: 'Nearest 10 minutes means',
    items: [
      'Rounds up or down depending on the interval that is closer',
      '8:04 am becomes 8:00 am',
      '8:06 am becomes 8:10 am',
    ],
  },
  [`${TimesheetRoundingType.Nearest}-${TimesheetRoundingInterval.Fifteen}`]: {
    title: 'Nearest 15 minutes means',
    items: [
      'Rounds up or down depending on the interval that is closer',
      '8:04 am becomes 8:00 am',
      '8:08 am becomes 8:15 am',
    ],
  },
  [`${TimesheetRoundingType.EmployeeFriendly}-${TimesheetRoundingInterval.Five}`]: {
    title: 'Employee Friendly means',
    items: [
      'Clock In rounds down: 8:03 am becomes 8:00 am',
      'Clock Out rounds up: 4:56 pm becomes 5:00 pm',
    ],
  },
  [`${TimesheetRoundingType.EmployeeFriendly}-${TimesheetRoundingInterval.Ten}`]: {
    title: 'Employee Friendly means',
    items: [
      'Clock In rounds down: 8:07 am becomes 8:00 am',
      'Clock Out rounds up: 4:52 pm becomes 5:00 pm',
    ],
  },
  [`${TimesheetRoundingType.EmployeeFriendly}-${TimesheetRoundingInterval.Fifteen}`]: {
    title: 'Employee Friendly means',
    items: [
      'Clock In rounds down: 8:12 am becomes 8:00 am',
      'Clock Out rounds up: 4:48 pm becomes 5:00 pm',
    ],
  },
};

const formSchema = yup.object().shape({
  timesheetRoundingEnabled: yup.boolean(),
  timesheetRoundingType: yup
    .string()
    .nullable()
    .oneOf(Object.values(TimesheetRoundingType))
    .when('timesheetRoundingEnabled', {
      is: true,
      then: () => yup.string().required('Type is required'),
    }),
  timesheetRoundingInterval: yup
    .string()
    .nullable()
    .oneOf(Object.values(TimesheetRoundingInterval))
    .when('timesheetRoundingEnabled', {
      is: true,
      then: () => yup.string().required('Interval is required'),
    }),
});

interface TimesheetRoundingModalProps {
  open: boolean;
  onClose: () => void;
  company: Company;
  onSuccess: () => void;
}

const TimesheetRoundingModal: React.FC<TimesheetRoundingModalProps> = ({ open, onClose, onSuccess, company }) => {
  const { addToast } = useToast();
  const { getCompany } = useCompany();

  const {
    control,
    watch,
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      timesheetRoundingEnabled: false,
      timesheetRoundingType: null,
      timesheetRoundingInterval: null,
    },
    shouldUnregister: false,
    resolver: yupResolver(formSchema),
  });

  useEffect(() => {
    if (!open) return;

    reset({
      timesheetRoundingEnabled: company?.timeTrackingSettings?.timesheetRoundingEnabled ?? false,
      timesheetRoundingType: company?.timeTrackingSettings?.timesheetRoundingType ?? null,
      timesheetRoundingInterval: company?.timeTrackingSettings?.timesheetRoundingInterval ?? null,
    });
  }, [reset, open, company]);

  const enabled = watch('timesheetRoundingEnabled');
  const type = watch('timesheetRoundingType');
  const interval = watch('timesheetRoundingInterval');

  const onSubmit = async (data: FormData) => {
    try {
      await timeTrackingSettings.update(data);
      await getCompany(company.id);

      addToast({
        title: 'Updated Timesheet Rounding Settings',
        description: 'We have successfully updated the overtime settings.',
        type: 'success',
      });

      onSuccess();
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Unable to update timesheet rounding settings');
    }
  };

  const roundingInfo = infoMapping[`${type}-${interval}`];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogSurface>
        <DialogHeader title="Timesheet Rounding" icon={<Settings2Line className="text-sub-600" />} />
        <FormV2 onSubmit={handleSubmit(onSubmit)} onCancel={onClose} isLoading={isSubmitting} submitText="Save">
          <div className="flex flex-col gap-5">
            <Controller
              control={control}
              name="timesheetRoundingEnabled"
              render={({ field, fieldState }) => (
                <FormItem error={!!fieldState.error} className="flex-row justify-between">
                  <FormLabel>Timesheet Rounding Enabled</FormLabel>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />
            {enabled && (
              <>
                <Controller
                  control={control}
                  name="timesheetRoundingType"
                  render={({ field, fieldState }) => (
                    <FormItem error={!!fieldState.error}>
                      <FormLabel>Type</FormLabel>
                      <FormControl>
                        <DropdownPicker
                          placeholder="Please select Type"
                          items={[
                            { value: TimesheetRoundingType.RoundDown, label: 'Round Down' },
                            { value: TimesheetRoundingType.RoundUp, label: 'Round Up' },
                            { value: TimesheetRoundingType.Nearest, label: 'Nearest' },
                            { value: TimesheetRoundingType.EmployeeFriendly, label: 'Employee Friendly' },
                          ]}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage>{fieldState.error?.message}</FormMessage>
                    </FormItem>
                  )}
                />
                <Controller
                  control={control}
                  name="timesheetRoundingInterval"
                  render={({ field, fieldState }) => (
                    <FormItem error={!!fieldState.error}>
                      <FormLabel>Interval</FormLabel>
                      <FormControl>
                        <DropdownPicker
                          placeholder="Please select Interval"
                          items={[
                            { value: TimesheetRoundingInterval.Five, label: '5 Minutes' },
                            { value: TimesheetRoundingInterval.Ten, label: '10 Minutes' },
                            { value: TimesheetRoundingInterval.Fifteen, label: '15 Minutes' },
                          ]}
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage>{fieldState.error?.message}</FormMessage>
                    </FormItem>
                  )}
                />
                {!!roundingInfo && (
                  <Alert>
                    <span>
                      <div className="text-strong-950">{roundingInfo.title}</div>
                      <ul className="list-inside list-disc">
                        {roundingInfo.items.map((item) => (
                          <li key={item}>{item}</li>
                        ))}
                      </ul>
                    </span>
                  </Alert>
                )}
              </>
            )}
          </div>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default TimesheetRoundingModal;
