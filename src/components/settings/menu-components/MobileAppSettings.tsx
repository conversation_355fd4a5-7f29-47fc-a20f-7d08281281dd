import { useCallback, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Switch } from '@/hammr-ui/components/Switch';
import { useToast } from 'hooks/useToast';
import { useCompany } from 'hooks/useCompany';
import { debounce } from 'lodash';
import moment from 'moment';
import { useMutation } from '@tanstack/react-query';
import timeTrackingSettings from '@/services/time-tracking-settings';
import { useAuth } from '@/hooks/useAuth';
import ControlledSwitch from '@/components/elements/form/ControlledSwitch';
import { TimeInput } from '@hammr-ui/components/time-input';

function toDate(timeString: string | null): Date | null {
  if (!timeString) return null;
  const date = moment.utc(timeString, 'HH:mm:ssZ').toDate();
  return date;
}

function toTimeString(date: Date | null): string | null {
  if (!date) return null;
  return moment.utc(date).format('HH:mm:ss+00');
}

export default function MobileAppSettings() {
  const { company } = useCompany();
  const { user, updateUser } = useAuth();
  const { addToast } = useToast();

  const defaultValues = useMemo(
    () => ({
      isClockInReminderEnabled: user?.isClockInReminderEnabled ?? false,
      clockInReminderAt: user?.clockInReminderAt ?? null,
      isClockOutReminderEnabled: user?.isClockOutReminderEnabled ?? false,
      clockOutReminderAt: user?.clockOutReminderAt ?? null,
      allowWorkersToTrackTime: company?.timeTrackingSettings.allowWorkersToTrackTime ?? false,
      allowWorkersToAddTimesheets: company?.timeTrackingSettings.allowWorkersToAddEditTime,
      allowForemanToCreateProjects: company?.timeTrackingSettings.allowForemanToCreateProjects ?? false,
      locationBreadcrumbing: company?.timeTrackingSettings.locationBreadcrumbingEnabled,
      isInjuryReportRequired: company?.timeTrackingSettings.isInjuryReportRequired,
      isClockinClockoutPhotosEnabled: company?.timeTrackingSettings.isClockinClockoutPhotosEnabled ?? false,
    }),
    [
      company?.timeTrackingSettings.allowWorkersToTrackTime,
      company?.timeTrackingSettings.allowWorkersToAddEditTime,
      company?.timeTrackingSettings.allowForemanToCreateProjects,
      company?.timeTrackingSettings.locationBreadcrumbingEnabled,
      company?.timeTrackingSettings.isInjuryReportRequired,
      company?.timeTrackingSettings.isClockinClockoutPhotosEnabled,
      user?.clockInReminderAt,
      user?.clockOutReminderAt,
      user?.isClockInReminderEnabled,
      user?.isClockOutReminderEnabled,
    ]
  );

  const form = useForm({
    defaultValues,
  });

  const isClockInReminderEnabled = form.watch('isClockInReminderEnabled');
  const isClockOutReminderEnabled = form.watch('isClockOutReminderEnabled');

  useEffect(() => {
    form.reset(defaultValues);
  }, [user, form, defaultValues]);

  const updateSettings = useMutation({
    mutationFn: async ({ setting, value }: { setting: string; value: any }) => {
      const currentValues = form.getValues();
      const updatedValues = { ...currentValues, [setting]: value };
      if (
        ['isClockInReminderEnabled', 'clockInReminderAt', 'isClockOutReminderEnabled', 'clockOutReminderAt'].includes(
          setting
        )
      ) {
        const userUpdate = {
          isClockInReminderEnabled: updatedValues.isClockInReminderEnabled,
          clockInReminderAt: updatedValues.clockInReminderAt,
          isClockOutReminderEnabled: updatedValues.isClockOutReminderEnabled,
          clockOutReminderAt: updatedValues.clockOutReminderAt,
        };
        await updateUser(user.uid, userUpdate);
      } else if (setting === 'isInjuryReportRequired') {
        // injuryReporRequired is technically not part of timeTrackingSettings but it's the place
        // where it makes the most sense to put it.
        await timeTrackingSettings.update({
          isInjuryReportRequired: updatedValues.isInjuryReportRequired,
        });
      } else if (setting === 'isClockinClockoutPhotosEnabled') {
        await timeTrackingSettings.update({
          isClockinClockoutPhotosEnabled: updatedValues.isClockinClockoutPhotosEnabled,
        });
      } else {
        await timeTrackingSettings.update({
          allowWorkersToTrackTime: updatedValues.allowWorkersToTrackTime,
          allowWorkersToAddEditTime: updatedValues.allowWorkersToAddTimesheets,
          allowForemanToCreateProjects: updatedValues.allowForemanToCreateProjects,
          locationBreadcrumbingEnabled: updatedValues.locationBreadcrumbing,
        });
      }

      form.reset({
        ...form.getValues(),
        [setting]: value,
      });
    },
    onSuccess: () => {
      addToast({
        title: 'Updated Setting',
        description: 'The setting has been successfully updated.',
        type: 'success',
      });
    },
  });

  const debouncedUpdateClockInReminder = useCallback(
    debounce((value: string) => {
      updateSettings.mutate({ setting: 'clockInReminderAt', value: value });
    }, 1200),
    [updateSettings]
  );

  const debouncedUpdateClockOutReminder = useCallback(
    debounce((value: string) => {
      updateSettings.mutate({ setting: 'clockOutReminderAt', value: value });
    }, 1200),
    [updateSettings]
  );

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col items-stretch justify-between gap-4 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div className="flex flex-row justify-between gap-3.5">
          <div>
            <h2 className="text-sm font-medium text-strong-950">Clock-In Reminder</h2>
            <p className="mt-1 h-fit text-xs text-sub-600">Notification sent to remind the employee to clock in.</p>
          </div>
          <Controller
            control={form.control}
            name="isClockInReminderEnabled"
            render={({ field: { value, onChange } }) => (
              <Switch
                checked={value}
                onCheckedChange={(checked) => {
                  onChange(checked);
                  updateSettings.mutate({ setting: 'isClockInReminderEnabled', value: checked });
                }}
              />
            )}
          />
        </div>
        {isClockInReminderEnabled ? (
          <div className="flex flex-row items-center justify-between gap-3.5">
            <h2 className="text-sm font-medium text-strong-950">Remind me at</h2>

            <Controller
              control={form.control}
              name="clockInReminderAt"
              render={({ field: { value, onChange } }) => (
                <TimeInput
                  value={toDate(value)}
                  onChange={(date: Date | null) => {
                    const timeString = toTimeString(date);
                    onChange(timeString);
                    debouncedUpdateClockInReminder(timeString);
                  }}
                />
              )}
            />
          </div>
        ) : undefined}
      </div>

      <div className="flex flex-col items-stretch justify-between gap-4 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div className="flex flex-row justify-between gap-3.5">
          <div>
            <h2 className="text-sm font-medium text-strong-950">Clock-Out Reminder</h2>
            <p className="mt-1 h-fit text-xs text-sub-600">Notification sent to remind the employee to clock out.</p>
          </div>
          <Controller
            control={form.control}
            name="isClockOutReminderEnabled"
            render={({ field: { value, onChange } }) => (
              <Switch
                checked={value}
                onCheckedChange={(checked) => {
                  onChange(checked);
                  updateSettings.mutate({ setting: 'isClockOutReminderEnabled', value: checked });
                }}
              />
            )}
          />
        </div>
        {isClockOutReminderEnabled ? (
          <div className="flex flex-row items-center justify-between gap-3.5">
            <h2 className="text-sm font-medium text-strong-950">Remind me at</h2>

            <Controller
              control={form.control}
              name="clockOutReminderAt"
              render={({ field: { value, onChange } }) => (
                <TimeInput
                  value={toDate(value)}
                  onChange={(date: Date | null) => {
                    const timeString = toTimeString(date);
                    onChange(timeString);
                    debouncedUpdateClockOutReminder(timeString);
                  }}
                />
              )}
            />
          </div>
        ) : undefined}
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Location Breadcrumbing</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Automatically track the location of your employee while they are clocked in.
          </p>
        </div>
        <Controller
          control={form.control}
          name="locationBreadcrumbing"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'locationBreadcrumbingEnabled', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Injury Reporting</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Require employees to report if they were injured or not when clocking out of a timesheet.
          </p>
        </div>
        <ControlledSwitch
          control={form.control}
          name="isInjuryReportRequired"
          onChange={(value) => {
            updateSettings.mutate({ setting: 'isInjuryReportRequired', value: value });
          }}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Clock In/Out Photos</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Require employees to upload photos when clocking in and out of a timesheet
          </p>
        </div>
        <ControlledSwitch
          control={form.control}
          name="isClockinClockoutPhotosEnabled"
          onChange={(value) => {
            updateSettings.mutate({ setting: 'isClockinClockoutPhotosEnabled', value: value });
          }}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Allow Workers To Track Time</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Determines if workers can use time-tracking on the mobile app to clock in and out.
          </p>
        </div>
        <Controller
          control={form.control}
          name="allowWorkersToTrackTime"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'allowWorkersToTrackTime', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Allow Workers To Add/Edit Timesheets</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Employees will be able to manually add or edit timesheets in the mobile app.
          </p>
        </div>
        <Controller
          control={form.control}
          name="allowWorkersToAddTimesheets"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'allowWorkersToAddEditTime', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Allow Foreman To Create Projects</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Foremen will be able to create new projects in the mobile app.
          </p>
        </div>
        <Controller
          control={form.control}
          name="allowForemanToCreateProjects"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'allowForemanToCreateProjects', value: checked });
              }}
            />
          )}
        />
      </div>
    </div>
  );
}
