import { TimeInput } from '@/hammr-ui/components/time-input';
import { useCompany } from '@/hooks/useCompany';
import { addToast } from '@/hooks/useToast';
import { apiRequest } from '@/utils/requestHelpers';
import { useMutation } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { Controller, useForm } from 'react-hook-form';

function toDate(timeString: string | null): Date | null {
  if (!timeString) return null;
  const date = dayjs(timeString, 'HH:mm:ss').toDate();
  return date;
}

function toTimeString(date: Date | null): string | null {
  if (!date) return null;
  return dayjs(date).format('HH:mm:ss');
}

export default function SchedulingSettings() {
  const { company, getCompany } = useCompany();

  const form = useForm({
    defaultValues: {
      defaultNotificationTime: company?.schedulingSettings.defaultNotificationTime,
    },
  });

  const updateSettings = useMutation({
    async mutationFn({ setting, value }: { setting: string; value: any }) {
      await apiRequest('scheduling-settings', {
        method: 'PATCH',
        body: { ...form.getValues(), [setting]: value },
      });

      form.reset({
        ...form.getValues(),
        [setting]: value,
      });
    },
    onSuccess() {
      addToast({
        title: 'Updated Setting',
        description: 'The setting has been successfully updated.',
        type: 'success',
      });
      // updates the company object across the app state
      getCompany(company?.id);
    },
    onError() {
      addToast({
        title: 'Failed to Update Setting',
        description: 'There was an error updating the setting. Please try again.',
        type: 'error',
      });
    },
  });

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Default Notification Time</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Default notification time pre-filled when creating schedules.
          </p>
        </div>
        <Controller
          control={form.control}
          name="defaultNotificationTime"
          render={({ field }) => (
            <TimeInput
              value={field.value ? toDate(field.value) : toDate('08:00:00')}
              onChange={(date: Date | null) => {
                const timeString = toTimeString(date);
                field.onChange(timeString);
                updateSettings.mutate({ setting: 'defaultNotificationTime', value: timeString });
              }}
            />
          )}
        />
      </div>
    </div>
  );
}
