import { useCallback } from 'react';
import { Switch } from '@/components/ui/Switch';
import { FormControl, FormItem, FormMessage } from '@/components/ui/form';
import { DurationPicker } from '@/components/ui/duration-picker';
import { WEEK_DAYS, WEEK_DAYS_SHORT } from '@/interfaces/payschedule';
import { Badge } from '@/hammr-ui/components/badge';

// Utility functions for day name conversion
export const useOvertimeDayConversion = () => {
  // Convert short day name to full day name
  const getFullDayName = useCallback((shortDay: (typeof WEEK_DAYS_SHORT)[number]) => {
    const index = WEEK_DAYS_SHORT.indexOf(shortDay);
    return WEEK_DAYS[index];
  }, []);

  // Convert full day name to short day name
  const getShortDayName = useCallback((fullDay: (typeof WEEK_DAYS)[number]) => {
    const index = WEEK_DAYS.indexOf(fullDay);
    return WEEK_DAYS_SHORT[index];
  }, []);

  // Convert array of short days to full days
  const convertToFullDays = useCallback(
    (shortDays: string[]) => {
      return shortDays
        .filter((day) => WEEK_DAYS_SHORT.includes(day as (typeof WEEK_DAYS_SHORT)[number]))
        .map((day) => getFullDayName(day as (typeof WEEK_DAYS_SHORT)[number]));
    },
    [getFullDayName]
  );

  // Convert array of full days to short days
  const convertToShortDays = useCallback(
    (fullDays: string[]) => {
      return fullDays
        .filter((day) => WEEK_DAYS.includes(day as (typeof WEEK_DAYS)[number]))
        .map((day) => getShortDayName(day as (typeof WEEK_DAYS)[number]));
    },
    [getShortDayName]
  );

  return { getFullDayName, getShortDayName, convertToFullDays, convertToShortDays };
};

// DaySelector component for both overtime and double overtime day selection
export interface DaySelectorProps {
  title: string;
  description: string;
  selectedDays: string[];
  onDayToggle: (day: string) => void;
  isDisabled?: boolean;
}

export const DaySelector = ({
  title,
  description,
  selectedDays,
  onDayToggle,
  isDisabled = false,
}: DaySelectorProps) => {
  const isDaySelected = (day: string) => selectedDays?.includes(day) || false;

  return (
    <div className="mt-4 flex flex-col">
      <div className="text-sm font-medium text-strong-950">{title}</div>
      <p className="mt-1 h-fit text-xs text-sub-600">{description}</p>

      <div className="mt-4 flex flex-row items-center justify-between">
        {WEEK_DAYS_SHORT.map((day) => (
          <Badge
            size="large"
            shape="square"
            color="gray"
            variant={isDaySelected(day) ? 'outline' : 'lighter'}
            className={`px-3.5 capitalize ${isDisabled ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
            key={day}
            onClick={isDisabled ? undefined : () => onDayToggle(day)}
          >
            <span className={`font-medium ${isDaySelected(day) ? 'text-strong-950' : 'text-sub-600'}`}>
              {day.toLowerCase()}
            </span>
          </Badge>
        ))}
      </div>
    </div>
  );
};

// Reusable OvertimeSection component
export interface OvertimeSectionProps {
  title: string;
  settingEnabled: boolean;
  onSettingEnabledChange: (checked: boolean) => void;
  thresholdValue: number;
  onThresholdChange: (value: number) => void;
  maxHour: number;
  componentIsDisabled?: boolean;
  thresholdError?: string;
  children?: React.ReactNode;
}

export const OvertimeSection = ({
  title,
  settingEnabled,
  onSettingEnabledChange,
  thresholdValue,
  onThresholdChange,
  thresholdError,
  maxHour,
  children,
  componentIsDisabled,
}: OvertimeSectionProps) => {
  return (
    <div className="flex flex-col gap-4 rounded-2xl border border-soft-200 p-4 shadow-xs">
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium text-strong-950">{title}</div>
        <Switch checked={settingEnabled} onCheckedChange={onSettingEnabledChange} disabled={componentIsDisabled} />
      </div>
      {settingEnabled && (
        <div>
          <FormItem error={!!thresholdError} className="flex flex-row items-center justify-between">
            <div className="text-sm font-medium text-strong-950">Calculate after:</div>
            <FormControl className="flex">
              <div className="w-24">
                <DurationPicker
                  value={thresholdValue}
                  onChange={onThresholdChange}
                  maxHour={maxHour}
                  disabled={componentIsDisabled}
                />
              </div>
            </FormControl>
            <FormMessage>{thresholdError}</FormMessage>
          </FormItem>

          {children && (
            <>
              <hr className="mt-4 border-soft-200" />
              {children}
            </>
          )}
        </div>
      )}
    </div>
  );
};

// Generic day toggle handler creator
export const createDayToggleHandler = (
  dailyOvertimeEnabled: boolean,
  dailyDoubleOvertimeEnabled: boolean,
  setValue: (name: string, value: any, options?: object) => void,
  formValues: any,
  updateFn: (values: any) => void
) => {
  return (day: string, primaryDays: string[], primaryField: 'overtimeDays' | 'doubleOvertimeDays') => {
    // return early if the relevant setting is disabled
    const isPrimaryOvertimeDays = primaryField === 'overtimeDays';
    const isPrimaryEnabled = isPrimaryOvertimeDays ? dailyOvertimeEnabled : dailyDoubleOvertimeEnabled;

    if (!isPrimaryEnabled) {
      console.log(`Cannot toggle days when ${primaryField} is disabled`);
      return;
    }

    const currentPrimaryDays = [...primaryDays];
    const primaryDayIndex = currentPrimaryDays.indexOf(day);

    // Simple toggle behavior - toggle the day in the primary array
    if (primaryDayIndex === -1) {
      // Add day if not already selected
      currentPrimaryDays.push(day);
    } else {
      // Remove day if already selected
      currentPrimaryDays.splice(primaryDayIndex, 1);
    }

    // Update form value immediately for UI feedback
    setValue(primaryField, currentPrimaryDays, { shouldDirty: true });

    // Prepare updated values
    const updatedValues = { ...formValues };
    updatedValues[primaryField] = currentPrimaryDays;

    // Call the update function with the updated values
    updateFn(updatedValues);
  };
};
