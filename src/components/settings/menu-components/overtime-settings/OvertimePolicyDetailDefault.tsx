import { useEffect, useCallback, useMemo } from 'react';
import { useToast } from 'hooks/useToast';
import { Controller, useForm } from 'react-hook-form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { WEEK_DAYS } from '@/interfaces/payschedule';
import { yupResolver } from '@/utils/yupResolver';
import * as yup from 'yup';
import { DropdownPicker } from '@/components/ui/Dropdown';
import { capitalize, debounce } from 'lodash';
import overtimeSettings from '@/services/overtime-settings';
import { logError, showErrorToast } from '@/utils/errorHandling';
import { OvertimeSettings } from '@/interfaces/overtime-settings';
import { DaySelector, OvertimeSection, useOvertimeDayConversion, createDayToggleHandler } from './OvertimeComponents';

// Form values interface
interface OvertimeFormValues {
  weeklyOvertimeEnabled: boolean;
  dailyOvertimeEnabled: boolean;
  dailyOvertimeThreshold: number;
  weeklyOvertimeThreshold: number;
  weekStartDay: string;
  overtimeDays: string[];
  doubleOvertimeDays: string[];
  dailyDoubleOvertimeEnabled: boolean;
  dailyDoubleOvertimeThreshold: number;
  overtimeDistribution: 'WEIGHTED' | 'SEQUENTIAL';
}

const formSchema = yup.object().shape({
  weeklyOvertimeEnabled: yup.boolean(),
  dailyOvertimeEnabled: yup.boolean(),
  dailyOvertimeThreshold: yup.number(),
  weeklyOvertimeThreshold: yup.number(),
  weekStartDay: yup.string(),
  overtimeDays: yup.array().of(yup.string()),
  doubleOvertimeDays: yup.array().of(yup.string()),
  dailyDoubleOvertimeEnabled: yup.boolean(),
  dailyDoubleOvertimeThreshold: yup.number(),
  overtimeDistribution: yup.string().oneOf(['SEQUENTIAL', 'WEIGHTED']),
});

export default function OvertimePolicyDetailDefault({ overtimeSetting }: { overtimeSetting: OvertimeSettings }) {
  const { addToast } = useToast();
  const { convertToFullDays, convertToShortDays } = useOvertimeDayConversion();

  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useForm<OvertimeFormValues>({
    defaultValues: {
      weeklyOvertimeEnabled: overtimeSetting.weeklyOvertimeEnabled,
      dailyOvertimeEnabled: overtimeSetting.dailyOvertimeEnabled,
      dailyOvertimeThreshold: overtimeSetting.dailyOvertimeThreshold,
      weeklyOvertimeThreshold: overtimeSetting.weeklyOvertimeThreshold,
      weekStartDay: overtimeSetting.weekStartDay ?? WEEK_DAYS[1],
      // Convert full day names from API to short day names for UI
      overtimeDays: convertToShortDays(overtimeSetting.overtimeDays || []),
      doubleOvertimeDays: convertToShortDays(overtimeSetting.doubleOvertimeDays || []),
      dailyDoubleOvertimeEnabled: overtimeSetting.dailyDoubleOvertimeEnabled,
      dailyDoubleOvertimeThreshold: overtimeSetting.dailyDoubleOvertimeThreshold,
      overtimeDistribution: overtimeSetting.overtimeDistribution || 'SEQUENTIAL',
    },
    shouldUnregister: false,
    resolver: yupResolver(formSchema),
  });

  const weeklyOvertimeEnabled = watch('weeklyOvertimeEnabled');
  const dailyOvertimeEnabled = watch('dailyOvertimeEnabled');
  const dailyDoubleOvertimeEnabled = watch('dailyDoubleOvertimeEnabled');
  const overtimeDays = watch('overtimeDays') || [];
  const doubleOvertimeDays = watch('doubleOvertimeDays') || [];

  const formValues = watch();

  const updateOvertimeSettings = useCallback(
    async (values: OvertimeFormValues) => {
      try {
        if (
          (values.weeklyOvertimeEnabled && !values.weeklyOvertimeThreshold) ||
          (values.dailyOvertimeEnabled && !values.dailyOvertimeThreshold) ||
          (values.dailyDoubleOvertimeEnabled && !values.dailyDoubleOvertimeThreshold)
        ) {
          // don't update if an overtime is enabled but doesn't have a threshold set
          return;
        }

        // Convert short day names to full day names for the API
        const apiValues = {
          ...values,
          overtimeDays: convertToFullDays(values.overtimeDays || []),
          doubleOvertimeDays: convertToFullDays(values.doubleOvertimeDays || []),
        };

        await overtimeSettings.update(overtimeSetting.id, apiValues);

        addToast({
          title: 'Updated Overtime Settings',
          description: 'We have successfully updated the overtime settings.',
          type: 'success',
        });
      } catch (error) {
        logError(error);
        showErrorToast(error, 'Unable to update overtime settings');
      }
    },
    [addToast, convertToFullDays, overtimeSetting.id]
  );

  // Create a debounced version of updateOvertimeSettings for day toggles
  const debouncedUpdateOvertimeSettings = useMemo(() => {
    // Store latest values
    let pendingValues: OvertimeFormValues | null = null;

    // Function to execute the update with the latest values
    const executeUpdate = () => {
      if (pendingValues) {
        updateOvertimeSettings(pendingValues);
        pendingValues = null;
      }
    };

    // Create the debounced executor
    const debouncedExecute = debounce(executeUpdate, 500);

    // Return a function that stores the values and schedules execution
    return (values: OvertimeFormValues) => {
      pendingValues = values;
      debouncedExecute();
    };
  }, [updateOvertimeSettings]);

  // Create generic toggle handler for day selection
  const handleDayToggle = useCallback(
    createDayToggleHandler(
      dailyOvertimeEnabled,
      dailyDoubleOvertimeEnabled,
      setValue,
      formValues,
      debouncedUpdateOvertimeSettings
    ),
    [dailyOvertimeEnabled, dailyDoubleOvertimeEnabled, setValue, formValues, debouncedUpdateOvertimeSettings]
  );

  const handleOvertimeDayToggle = useCallback(
    (day: string) => {
      handleDayToggle(day, overtimeDays, 'overtimeDays');
    },
    [handleDayToggle, overtimeDays]
  );

  const handleDoubleOvertimeDayToggle = useCallback(
    (day: string) => {
      handleDayToggle(day, doubleOvertimeDays, 'doubleOvertimeDays');
    },
    [handleDayToggle, doubleOvertimeDays]
  );

  useEffect(() => {
    const { unsubscribe } = watch(async (values) => {
      // Skip this effect for overtimeDays changes as they're handled by the debounced function
      if (JSON.stringify(values.overtimeDays) !== JSON.stringify(formValues.overtimeDays)) {
        return;
      }

      updateOvertimeSettings(values as OvertimeFormValues);
    });
    return () => unsubscribe();
  }, [addToast, overtimeSetting.id, formValues, watch, updateOvertimeSettings]);

  return (
    <form className="mb-6 flex flex-col gap-6">
      <div className="rounded-2xl border border-soft-200 p-4 shadow-xs">
        <FormItem error={!!errors.weekStartDay} className="flex flex-row items-center  justify-between">
          <FormLabel>Week Start Day</FormLabel>
          <FormControl>
            <Controller
              control={control}
              name="weekStartDay"
              render={({ field }) => (
                <DropdownPicker
                  className="h-9 max-w-36"
                  items={WEEK_DAYS.map((day) => ({
                    value: day,
                    label: capitalize(day),
                  }))}
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="Select week start day"
                  fullWidth
                />
              )}
            />
          </FormControl>
          <FormMessage>{errors.weekStartDay?.message}</FormMessage>
        </FormItem>
      </div>

      <OvertimeSection
        title="Weekly Overtime"
        settingEnabled={weeklyOvertimeEnabled}
        onSettingEnabledChange={(checked) => setValue('weeklyOvertimeEnabled', checked)}
        thresholdValue={watch('weeklyOvertimeThreshold')}
        onThresholdChange={(value) => setValue('weeklyOvertimeThreshold', value)}
        thresholdError={errors.weeklyOvertimeThreshold?.message}
        maxHour={167}
      />

      <OvertimeSection
        title="Daily Overtime"
        settingEnabled={dailyOvertimeEnabled}
        onSettingEnabledChange={(checked) => {
          setValue('dailyOvertimeEnabled', checked);
          // Clear overtime days when setting is disabled
          if (!checked && overtimeDays.length > 0) {
            setValue('overtimeDays', []);
            updateOvertimeSettings({
              ...formValues,
              dailyOvertimeEnabled: checked,
              overtimeDays: [],
            });
          }
        }}
        thresholdValue={watch('dailyOvertimeThreshold')}
        onThresholdChange={(value) => setValue('dailyOvertimeThreshold', value)}
        thresholdError={errors.dailyOvertimeThreshold?.message}
        maxHour={23}
      >
        <DaySelector
          title="Overtime days"
          description="All hours worked on these days will be counted as overtime."
          selectedDays={overtimeDays}
          onDayToggle={handleOvertimeDayToggle}
          isDisabled={!dailyOvertimeEnabled}
        />
      </OvertimeSection>

      <OvertimeSection
        title="Daily Double Overtime"
        settingEnabled={dailyDoubleOvertimeEnabled}
        onSettingEnabledChange={(checked) => {
          setValue('dailyDoubleOvertimeEnabled', checked);
          // Clear double overtime days when setting is disabled
          if (!checked && doubleOvertimeDays.length > 0) {
            setValue('doubleOvertimeDays', []);
            updateOvertimeSettings({
              ...formValues,
              dailyDoubleOvertimeEnabled: checked,
              doubleOvertimeDays: [],
            });
          }
        }}
        thresholdValue={watch('dailyDoubleOvertimeThreshold')}
        onThresholdChange={(value) => setValue('dailyDoubleOvertimeThreshold', value)}
        thresholdError={errors.dailyDoubleOvertimeThreshold?.message}
        maxHour={23}
      >
        <DaySelector
          title="Double Overtime days"
          description="All hours worked on these days will be counted as double overtime."
          selectedDays={doubleOvertimeDays}
          onDayToggle={handleDoubleOvertimeDayToggle}
          isDisabled={!dailyDoubleOvertimeEnabled}
        />
      </OvertimeSection>
    </form>
  );
}
