import { useEffect, useCallback, useMemo, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@/utils/yupResolver';
import * as yup from 'yup';
import { debounce } from 'lodash';
import overtimeSettings from '@/services/overtime-settings';
import { logError, showErrorToast } from '@/utils/errorHandling';
import { OvertimeSettings } from '@/interfaces/overtime-settings';
import { Input } from '@/hammr-ui/components/input';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { useQueryClient } from '@tanstack/react-query';
import { DaySelector, OvertimeSection, useOvertimeDayConversion, createDayToggleHandler } from './OvertimeComponents';

// Form values interface
interface OvertimeFormValues {
  weeklyOvertimeEnabled: boolean;
  dailyOvertimeEnabled: boolean;
  dailyOvertimeThreshold: number;
  weeklyOvertimeThreshold: number;
  overtimeDays: string[];
  doubleOvertimeDays: string[];
  dailyDoubleOvertimeEnabled: boolean;
  dailyDoubleOvertimeThreshold: number;
  name: string;
  description: string;
}

const formSchema = yup.object().shape({
  weeklyOvertimeEnabled: yup.boolean(),
  dailyOvertimeEnabled: yup.boolean(),
  dailyOvertimeThreshold: yup.number(),
  weeklyOvertimeThreshold: yup.number(),
  overtimeDays: yup.array().of(yup.string()),
  doubleOvertimeDays: yup.array().of(yup.string()),
  dailyDoubleOvertimeEnabled: yup.boolean(),
  dailyDoubleOvertimeThreshold: yup.number(),
  name: yup.string().required('Name is required'),
  description: yup.string(),
});

export default function OvertimePolicyDetailCustom({ overtimeSetting }: { overtimeSetting: OvertimeSettings }) {
  const { addToast } = useToast();
  const queryClient = useQueryClient();
  const { convertToFullDays, convertToShortDays } = useOvertimeDayConversion();

  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useForm<OvertimeFormValues>({
    defaultValues: {
      weeklyOvertimeEnabled: overtimeSetting.weeklyOvertimeEnabled,
      dailyOvertimeEnabled: overtimeSetting.dailyOvertimeEnabled,
      dailyOvertimeThreshold: overtimeSetting.dailyOvertimeThreshold,
      weeklyOvertimeThreshold: overtimeSetting.weeklyOvertimeThreshold,
      overtimeDays: convertToShortDays(overtimeSetting.overtimeDays || []),
      doubleOvertimeDays: convertToShortDays(overtimeSetting.doubleOvertimeDays || []),
      dailyDoubleOvertimeEnabled: overtimeSetting.dailyDoubleOvertimeEnabled,
      dailyDoubleOvertimeThreshold: overtimeSetting.dailyDoubleOvertimeThreshold,
      name: overtimeSetting.name,
      description: overtimeSetting.description || '',
    },
    shouldUnregister: false,
    resolver: yupResolver(formSchema),
  });

  const weeklyOvertimeEnabled = watch('weeklyOvertimeEnabled');
  const dailyOvertimeEnabled = watch('dailyOvertimeEnabled');
  const dailyDoubleOvertimeEnabled = watch('dailyDoubleOvertimeEnabled');
  const overtimeDays = watch('overtimeDays') || [];
  const doubleOvertimeDays = watch('doubleOvertimeDays') || [];

  const formValues = watch();

  const updateOvertimeSettings = useCallback(
    async (values: OvertimeFormValues) => {
      try {
        if (
          (values.weeklyOvertimeEnabled && !values.weeklyOvertimeThreshold) ||
          (values.dailyOvertimeEnabled && !values.dailyOvertimeThreshold) ||
          (values.dailyDoubleOvertimeEnabled && !values.dailyDoubleOvertimeThreshold)
        ) {
          // don't update if an overtime is enabled but doesn't have a threshold set
          return;
        }

        // Convert short day names to full day names for the API
        const apiValues = {
          ...values,
          overtimeDays: convertToFullDays(values.overtimeDays || []),
          doubleOvertimeDays: convertToFullDays(values.doubleOvertimeDays || []),
        };

        await overtimeSettings.update(overtimeSetting.id, apiValues);

        queryClient.invalidateQueries({ queryKey: ['overtimeSettings', String(overtimeSetting.id)] });

        addToast({
          title: 'Updated Overtime Settings',
          description: 'We have successfully updated the overtime settings.',
          type: 'success',
        });
      } catch (error) {
        logError(error);
        showErrorToast(error, 'Unable to update overtime settings');
      }
    },
    [addToast, convertToFullDays, overtimeSetting.id, queryClient]
  );

  // Create a debounced version of updateOvertimeSettings for day toggles
  const debouncedUpdateOvertimeSettings = useMemo(() => {
    // Store latest values
    let pendingValues: OvertimeFormValues | null = null;

    // Function to execute the update with the latest values
    const executeUpdate = () => {
      if (pendingValues) {
        updateOvertimeSettings(pendingValues);
        pendingValues = null;
      }
    };

    // Create the debounced executor
    const debouncedExecute = debounce(executeUpdate, 500);

    // Return a function that stores the values and schedules execution
    return (values: OvertimeFormValues) => {
      pendingValues = values;
      debouncedExecute();
    };
  }, [updateOvertimeSettings]);

  // Clear overtime days when their respective settings are disabled
  useEffect(() => {
    if (!dailyOvertimeEnabled && overtimeDays.length > 0) {
      setValue('overtimeDays', []);
      updateOvertimeSettings({
        ...formValues,
        overtimeDays: [],
      });
    }
  }, [dailyOvertimeEnabled, overtimeDays.length, setValue, formValues, updateOvertimeSettings]);

  useEffect(() => {
    if (!dailyDoubleOvertimeEnabled && doubleOvertimeDays.length > 0) {
      setValue('doubleOvertimeDays', []);
      updateOvertimeSettings({
        ...formValues,
        doubleOvertimeDays: [],
      });
    }
  }, [dailyDoubleOvertimeEnabled, doubleOvertimeDays.length, setValue, formValues, updateOvertimeSettings]);

  const isDisabled = !overtimeSetting.isActive;

  // track accumulated changes that need API updates
  const [pendingChanges, setPendingChanges] = useState<Partial<OvertimeFormValues>>({});

  // handle accumulated field changes
  // essentially the timer for keystrokes wasn't resetting initially - this is a workaround
  useEffect(() => {
    if (Object.keys(pendingChanges).length === 0) return;

    const timer = setTimeout(() => {
      updateOvertimeSettings({ ...formValues, ...pendingChanges });
      setPendingChanges({});
    }, 1000);

    return () => clearTimeout(timer);
  }, [pendingChanges, formValues, updateOvertimeSettings]);

  useEffect(() => {
    // set up the form change listener only once
    const subscription = watch((values, { name: fieldName }) => {
      // skip if the change is for overtimeDays or doubleOvertimeDays as they're handled elsewhere
      if (fieldName === 'overtimeDays' || fieldName === 'doubleOvertimeDays') {
        return;
      }

      // accumulate name and description changes
      if (fieldName === 'name' || fieldName === 'description') {
        setPendingChanges((prev) => ({
          ...prev,
          [fieldName]: values[fieldName],
        }));
      } else if (fieldName) {
        // only update immediately for other specific field changes
        updateOvertimeSettings(values as OvertimeFormValues);
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, updateOvertimeSettings]);

  // create generic toggle handler for day selection - do not use debounced update here
  const handleDayToggle = useCallback(
    createDayToggleHandler(
      dailyOvertimeEnabled,
      dailyDoubleOvertimeEnabled,
      setValue,
      formValues,
      debouncedUpdateOvertimeSettings
    ),
    [dailyOvertimeEnabled, dailyDoubleOvertimeEnabled, setValue, formValues, debouncedUpdateOvertimeSettings]
  );

  const handleOvertimeDayToggle = useCallback(
    (day: string) => {
      handleDayToggle(day, overtimeDays, 'overtimeDays');
    },
    [handleDayToggle, overtimeDays]
  );

  const handleDoubleOvertimeDayToggle = useCallback(
    (day: string) => {
      handleDayToggle(day, doubleOvertimeDays, 'doubleOvertimeDays');
    },
    [handleDayToggle, doubleOvertimeDays]
  );

  return (
    <form className="mb-6 flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <div className="text-sm font-medium text-strong-950">Overtime Policy Name</div>
        <Controller
          control={control}
          name="name"
          render={({ field }) => (
            <Input
              {...field}
              disabled={isDisabled}
              className={isDisabled ? 'bg-weak-50 text-disabled-300' : ''}
              placeholder="Enter policy name"
            />
          )}
        />
      </div>

      <div className="flex flex-col gap-2">
        <div className="text-sm font-medium text-strong-950">Description</div>
        <Controller
          control={control}
          name="description"
          render={({ field }) => (
            <Textarea
              {...field}
              disabled={isDisabled}
              className={isDisabled ? 'bg-weak-50 text-disabled-300' : ''}
              placeholder="Enter policy description"
              hideCounter
            />
          )}
        />
      </div>

      <OvertimeSection
        title="Weekly Overtime"
        settingEnabled={weeklyOvertimeEnabled}
        onSettingEnabledChange={(checked) => setValue('weeklyOvertimeEnabled', checked)}
        thresholdValue={watch('weeklyOvertimeThreshold')}
        onThresholdChange={(value) => setValue('weeklyOvertimeThreshold', value)}
        thresholdError={errors.weeklyOvertimeThreshold?.message}
        maxHour={167}
        componentIsDisabled={isDisabled}
      />

      <OvertimeSection
        title="Daily Overtime"
        settingEnabled={dailyOvertimeEnabled}
        onSettingEnabledChange={(checked) => {
          setValue('dailyOvertimeEnabled', checked);
          // Clear overtime days when setting is disabled
          if (!checked && overtimeDays.length > 0) {
            setValue('overtimeDays', []);
            updateOvertimeSettings({
              ...formValues,
              dailyOvertimeEnabled: checked,
              overtimeDays: [],
            });
          }
        }}
        thresholdValue={watch('dailyOvertimeThreshold')}
        onThresholdChange={(value) => setValue('dailyOvertimeThreshold', value)}
        thresholdError={errors.dailyOvertimeThreshold?.message}
        maxHour={23}
        componentIsDisabled={isDisabled}
      >
        <DaySelector
          title="Overtime days"
          description="All hours worked on these days will be counted as overtime."
          selectedDays={overtimeDays}
          onDayToggle={handleOvertimeDayToggle}
          isDisabled={isDisabled || !dailyOvertimeEnabled}
        />
      </OvertimeSection>

      <OvertimeSection
        title="Daily Double Overtime"
        settingEnabled={dailyDoubleOvertimeEnabled}
        onSettingEnabledChange={(checked) => {
          setValue('dailyDoubleOvertimeEnabled', checked);
          // Clear double overtime days when setting is disabled
          if (!checked && doubleOvertimeDays.length > 0) {
            setValue('doubleOvertimeDays', []);
            updateOvertimeSettings({
              ...formValues,
              dailyDoubleOvertimeEnabled: checked,
              doubleOvertimeDays: [],
            });
          }
        }}
        thresholdValue={watch('dailyDoubleOvertimeThreshold')}
        onThresholdChange={(value) => setValue('dailyDoubleOvertimeThreshold', value)}
        thresholdError={errors.dailyDoubleOvertimeThreshold?.message}
        maxHour={23}
        componentIsDisabled={isDisabled}
      >
        <DaySelector
          title="Double Overtime days"
          description="All hours worked on these days will be counted as double overtime."
          selectedDays={doubleOvertimeDays}
          onDayToggle={handleDoubleOvertimeDayToggle}
          isDisabled={isDisabled || !dailyDoubleOvertimeEnabled}
        />
      </OvertimeSection>
    </form>
  );
}
