import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ModalV2 } from '@/components/elements/ModalV2';
import { TextField } from '@/components/elements/form/TextField';
import { Button } from '@/hammr-ui/components/button';
import { <PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/hammr-ui/components/dialog';
import { Textarea } from '@/hammr-ui/components/Textarea';
import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { RiTimeLine } from '@remixicon/react';
import { FormV2 } from '@/components/elements/Form';

interface CreatePolicyModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onCreatePolicy: (name: string, description: string) => Promise<void>;
}

interface FormValues {
  name: string;
  description: string;
}

export const CreatePolicyModal = ({ open, setOpen, onCreatePolicy }: CreatePolicyModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      await onCreatePolicy(data.name, data.description);
      reset();
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Create Overtime Policy" icon={<RiTimeLine />}>
      <FormV2 onSubmit={handleSubmit(onSubmit)} submitText="Create" onCancel={() => setOpen(false)}>
        <div className="flex flex-col gap-5">
          <TextField
            control={control}
            name="name"
            label="Policy Name"
            placeholder="Enter policy name"
            required
            rules={{ required: 'Policy name is required' }}
            error={errors.name?.message}
          />

          <FormItem>
            <FormLabel>Description</FormLabel>
            <FormControl>
              <Textarea placeholder="Enter policy description" hideCounter {...control.register('description')} />
            </FormControl>
          </FormItem>
        </div>
      </FormV2>
    </ModalV2>
  );
};
