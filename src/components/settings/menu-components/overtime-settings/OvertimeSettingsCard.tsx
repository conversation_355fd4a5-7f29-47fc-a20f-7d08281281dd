import React from 'react';
import Settings2Line from '@/hammr-icons/Settings2Line';
import { Button } from '@/hammr-ui/components/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/hammr-ui/components/card';
import { Badge } from '@/hammr-ui/components/badge';
import dayjs from 'dayjs';

interface OvertimeSettingsCardProps {
  title: string;
  createdAt: string;
  description: string;
  onManage: () => void;
  archived?: boolean;
}

const OvertimeSettingsCard: React.FC<OvertimeSettingsCardProps> = ({
  title,
  createdAt,
  description,
  onManage,
  archived,
}) => {
  return (
    <Card className="w-[446px]">
      <CardHeader className="space-y-0 p-4 pb-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium">{title}</h3>
            {archived && (
              <Badge color="gray" size="medium" variant="lighter">
                ARCHIVED
              </Badge>
            )}
          </div>
          <span className="text-xs text-sub-600">{dayjs(createdAt).format('MM/DD/YYYY')}</span>
        </div>
      </CardHeader>

      <CardContent className="px-4 pb-3.5 pt-2">
        <p className="text-xs text-sub-600">{description}</p>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <Button
          variant="outline"
          color="neutral"
          size="small"
          className="w-full justify-center"
          onClick={onManage}
          beforeContent={<Settings2Line className="size-4" />}
        >
          Manage
        </Button>
      </CardFooter>
    </Card>
  );
};

export default OvertimeSettingsCard;
