import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';

import { TabContent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import OvertimeSettingsCard from './OvertimeSettingsCard';
import { default as overtimeSettingsService, CreateOvertimeSettingsPayload } from '@/services/overtime-settings';
import AddLine from '@/hammr-icons/AddLine';
import { Button } from '@hammr-ui/components/button';
import { useToast } from 'hooks/useToast';
import { CreatePolicyModal } from './CreatePolicyModal';
import { useCompany } from 'hooks/useCompany';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Label } from '@hammr-ui/components/label';

type OvertimeStatus = 'active' | 'archived';

const OvertimeSettings = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<OvertimeStatus>('active');
  const { data } = useQuery({
    queryKey: ['overtimeSettings'],
    queryFn: async () => {
      const data = await overtimeSettingsService.getAll();
      return data;
    },
  });
  const overtimeSettings = data;
  const { addToast } = useToast();
  const { company, getCompany } = useCompany();

  const [overtimeDistribution, setOvertimeDistribution] = useState<'SEQUENTIAL' | 'WEIGHTED' | 'MINIMUM_RATE'>(
    'SEQUENTIAL'
  );

  const [defaultSetting, ...otherSettings] = overtimeSettings?.sort((setting) => (setting.isDefault ? -1 : 1)) ?? [];

  const activeSettings = otherSettings?.filter((setting) => setting.isActive);
  const archivedSettings = otherSettings?.filter((setting) => !setting.isActive);

  // Update state when company data is loaded
  useEffect(() => {
    if (company?.overtimeSettings?.overtimeDistribution !== undefined) {
      setOvertimeDistribution(company.overtimeSettings.overtimeDistribution);
    }
  }, [company?.overtimeSettings?.overtimeDistribution]);

  // State for create policy modal
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Set the active tab based on URL param or default to 'active'
  useEffect(() => {
    const overtimeParam = router.query.overtime as OvertimeStatus;
    if (overtimeParam && (overtimeParam === 'active' || overtimeParam === 'archived')) {
      setActiveTab(overtimeParam);
    } else if (!router.query.overtime) {
      // Set default URL param if not present
      router.push(
        {
          pathname: router.pathname,
          query: { ...router.query, overtime: 'active' },
        },
        undefined,
        { shallow: true }
      );
    }
  }, [router.query.overtime, router.pathname, router]);

  // Update URL when tab changes
  const handleTabChange = (value: OvertimeStatus) => {
    setActiveTab(value);
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, overtime: value },
      },
      undefined,
      { shallow: true }
    );
  };

  // Handle opening the create policy modal
  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };

  // Handle creating a new policy
  const handleCreatePolicy = async (name: string, description: string) => {
    try {
      // Create a new overtime policy with default values
      const defaultPolicy: CreateOvertimeSettingsPayload = {
        name: name.trim(),
        description: description.trim(),
        weeklyOvertimeEnabled: true,
        dailyOvertimeEnabled: true,
        dailyDoubleOvertimeEnabled: true,
        overtimeDays: ['SATURDAY', 'SUNDAY'],
        doubleOvertimeDays: [],
      };

      // Create new policy using the service
      const newPolicy = await overtimeSettingsService.create(defaultPolicy);

      // Close modal
      setIsCreateModalOpen(false);

      // Navigate to the new policy detail page
      router.push(`/settings/overtime/${newPolicy.id}`);

      addToast({
        title: 'Created Overtime Policy',
        description: 'New overtime policy has been created successfully.',
        type: 'success',
      });
    } catch (error) {
      console.error('Failed to create overtime policy:', error);
      addToast({
        title: 'Error',
        description: 'Failed to create new overtime policy. Please try again.',
        type: 'error',
      });
      throw error; // Re-throw to let the modal handle the error state
    }
  };

  // Handle navigating to an existing policy
  const handleManageSettings = (settingId: string) => {
    // Navigate to the policy detail page
    router.push(`/settings/overtime/${settingId}`);
  };

  // handle toggling overtime distribution
  const handleOvertimeDistributionChange = async (value: 'SEQUENTIAL' | 'WEIGHTED' | 'MINIMUM_RATE') => {
    const previousValue = overtimeDistribution;
    setOvertimeDistribution(value);

    try {
      await overtimeSettingsService.update(defaultSetting.id, {
        overtimeDistribution: value,
      });

      // call getCompany as overtimeSettings (default) needs to be refetched due to dist update
      await getCompany(company.id);

      addToast({
        title: 'Overtime Settings Updated',
        description: `Overtime calculation has been updated to ${value === 'SEQUENTIAL' ? 'Sequential' : value === 'WEIGHTED' ? 'Weighted Average' : 'Minimum Rate'}.`,
        type: 'success',
      });
    } catch (error) {
      console.error('Failed to update overtime distribution:', error);
      addToast({
        title: 'Error',
        description: 'Failed to update overtime distribution. Please try again.',
        type: 'error',
      });

      // revert the UI state on error
      setOvertimeDistribution(previousValue);
    }
  };

  return (
    <>
      <div className="w-full max-w-md">
        <div className="mb-6 rounded-12 border border-soft-200 p-4 shadow-xs">
          <div className="flex flex-col justify-between">
            <div className="flex gap-2">
              <div className="text-sm font-medium text-strong-950">Overtime Calculation</div>
            </div>
            <div className="flex items-center gap-4">
              <RadioGroup value={overtimeDistribution} onValueChange={handleOvertimeDistributionChange}>
                <div className="mt-4 flex items-start gap-4">
                  <RadioGroupItem value="SEQUENTIAL" id="sequential" />
                  <div className="flex flex-col">
                    <Label htmlFor="sequential" className="font-medium">
                      Sequential Overtime
                    </Label>
                    <p className="mt-2 text-sub-600">
                      Overtime pay is calculated using the overtime rate on hours that exceed the regular threshold.
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="mt-4 flex items-start gap-4">
                    <RadioGroupItem value="WEIGHTED" id="weighted" />
                    <div className="flex flex-col">
                      <Label htmlFor="weighted" className="font-medium">
                        Weighted Average Overtime
                      </Label>
                      <p className="mt-2 text-sub-600">
                        Overtime pay is calculated based on the average pay rate across all hours worked, not just
                        overtime hours.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-start gap-4">
                  <div className="mt-4 flex items-start gap-4">
                    <RadioGroupItem value="MINIMUM_RATE" id="minimum-rate" />
                    <div className="flex flex-col">
                      <Label htmlFor="minimum-rate" className="font-medium">
                        Minimum Rate Overtime
                      </Label>
                      <p className="mt-2 text-sub-600">
                        Overtime pay is calculated based on the pay rate of hours with the minimum hourly rate.
                      </p>
                    </div>
                  </div>
                </div>
              </RadioGroup>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabList className="w-full">
            <TabItem className="w-full" value="active">
              Active
            </TabItem>
            <TabItem className="w-full" value="archived">
              Archived
            </TabItem>
          </TabList>

          <TabContent value="active">
            <div className="mt-6 flex flex-col gap-6">
              <>
                {defaultSetting && (
                  <OvertimeSettingsCard
                    key={defaultSetting.id}
                    title={'Default Overtime Policy'}
                    createdAt={defaultSetting.createdAt}
                    description={"Default OT policy for all projects that don't require specific adjustments."}
                    onManage={() => handleManageSettings(String(defaultSetting.id))}
                  />
                )}
                {activeSettings?.map((setting) => (
                  <OvertimeSettingsCard
                    key={setting.id}
                    title={setting.name}
                    createdAt={setting.createdAt}
                    description={setting.description}
                    onManage={() => handleManageSettings(String(setting.id))}
                  />
                ))}
              </>

              <div>
                <Button onClick={handleOpenCreateModal} beforeContent={<AddLine />} fullWidth>
                  Create Overtime Policy
                </Button>
                <p className="mt-3 text-xs text-sub-600">
                  Create custom Overtime Policies to be used on projects where you don&apos;t want to apply the Default
                  Overtime Policy.
                </p>
              </div>
            </div>
          </TabContent>

          <TabContent value="archived">
            <div className="mt-6 flex flex-col gap-6">
              {archivedSettings?.length === 0 ? (
                <p className="py-4 text-center text-sub-600">No archived overtime settings found.</p>
              ) : (
                <>
                  {archivedSettings?.map((setting) => (
                    <OvertimeSettingsCard
                      key={setting.id}
                      title={setting.name}
                      createdAt={setting.createdAt}
                      description={setting.description}
                      onManage={() => handleManageSettings(String(setting.id))}
                      archived={true}
                    />
                  ))}
                </>
              )}
            </div>
          </TabContent>
        </Tabs>
      </div>

      {/* Create Policy Modal */}
      <CreatePolicyModal open={isCreateModalOpen} setOpen={setIsCreateModalOpen} onCreatePolicy={handleCreatePolicy} />
    </>
  );
};

export default OvertimeSettings;
