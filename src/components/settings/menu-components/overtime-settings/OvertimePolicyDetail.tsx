import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Breadcrumbs, BreadcrumbItem } from '@/hammr-ui/components/Breadcrumbs';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { Badge } from '@/hammr-ui/components/badge';
import Settings2Line from '@/hammr-icons/Settings2Line';
import OvertimePolicyDetailDefault from './OvertimePolicyDetailDefault';
import OvertimePolicyDetailCustom from './OvertimePolicyDetailCustom';
import { OvertimeSettings } from '@/interfaces/overtime-settings';
import overtimeSettings from '@/services/overtime-settings';
import { Button } from '@/hammr-ui/components/button';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import { useToast } from 'hooks/useToast';
import ArrowGoBackLine from '@/hammr-icons/ArrowGoBackLine';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

const OvertimePolicyDetail: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const [policy, setPolicy] = useState<OvertimeSettings | null>(null);
  const { addToast } = useToast();
  const queryClient = useQueryClient();

  const handleArchiveToggle = async (policyId: number, isActive: boolean) => {
    try {
      if (isActive) {
        // Archive the policy
        await overtimeSettings.archive(policyId);
        addToast({
          title: 'Policy Archived',
          description: 'The overtime policy has been archived successfully.',
          type: 'success',
        });
      } else {
        // Unarchive the policy
        await overtimeSettings.unarchive(policyId);
        addToast({
          title: 'Policy Unarchived',
          description: 'The overtime policy has been unarchived successfully.',
          type: 'success',
        });
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['overtimeSettings'] });
    } catch (error) {
      addToast({
        title: isActive ? 'Archive Failed' : 'Unarchive Failed',
        description: `Unable to ${isActive ? 'archive' : 'unarchive'} the overtime policy. Please try again.`,
        type: 'error',
      });
    }
  };

  // Fetch policy data based on ID
  const { data: policyData, isLoading } = useQuery({
    queryKey: ['overtimeSettings', id],
    queryFn: async () => {
      const idInt = parseInt(id as string);

      if (isNaN(idInt)) {
        return null;
      }

      const data = await overtimeSettings.get(idInt);
      return data;
    },
    enabled: !!id,
  });

  useEffect(() => {
    if (policyData) {
      setPolicy(policyData);
    }
  }, [policyData]);

  const handleBreadcrumbClick = (path: string) => {
    router.push(path);
  };

  if (isLoading) {
    return (
      <div className="mt-1/5 flex items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!policy) {
    return <div className="p-8">Error fetching policy</div>;
  }

  return (
    <div className="flex flex-col">
      <PageHeader
        icon={<Settings2Line />}
        title={
          <div className="flex items-center gap-2">
            {policy.isDefault ? 'Default Overtime Policy' : policy.name}
            <Badge className="uppercase" color={policy.isActive ? 'green' : 'gray'} variant="lighter">
              {policy.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>
        }
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Settings" onClick={() => handleBreadcrumbClick('/settings')} />
            <BreadcrumbItem text="Overtime Settings" onClick={() => handleBreadcrumbClick('/settings?menu=overtime')} />
            <BreadcrumbItem text={policy.isDefault ? 'Default Overtime Policy' : policy.name} active />
          </Breadcrumbs>
        }
        headerRight={
          policy.isDefault ? undefined : (
            <Button
              onClick={() => handleArchiveToggle(policy.id, policy.isActive)}
              variant="outline"
              color="neutral"
              beforeContent={policy.isActive ? <ArchiveLine /> : <ArrowGoBackLine />}
            >
              {policy.isActive ? 'Archive' : 'Unarchive'}
            </Button>
          )
        }
      />

      <div className="mx-8 w-full max-w-md">
        {policy.isDefault ? (
          <OvertimePolicyDetailDefault overtimeSetting={policy} />
        ) : (
          <OvertimePolicyDetailCustom overtimeSetting={policy} />
        )}
      </div>
    </div>
  );
};

export default OvertimePolicyDetail;
