import { useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { RiArrowRightSLine } from '@remixicon/react';
import Button from '@/hammr-ui/components/button';
import { Switch } from '@/hammr-ui/components/Switch';
import { useToast } from 'hooks/useToast';
import { useCompany } from 'hooks/useCompany';
import TimesheetRoundingModal from '../TimesheetRoundingModal';
import BreakPreferenceModal from '../BreakPreferenceModal';
import { TimesheetRoundingType } from '@/interfaces/time-tracking-settings';
import timeTrackingSettings from '@/services/time-tracking-settings';
import { useMutation } from '@tanstack/react-query';
import { Input } from '@/hammr-ui/components/input';
import { TimeInput } from '@/hammr-ui/components/time-input';
import dayjs from 'dayjs';

function toDate(timeString: string | null): Date | null {
  if (!timeString) return null;
  const date = dayjs(timeString, 'HH:mm:ss').toDate();
  return date;
}

function toTimeString(date: Date | null): string | null {
  if (!date) return null;
  return dayjs(date).format('HH:mm:ss');
}

export default function TimeTrackingSettings() {
  const { company, getCompany } = useCompany();
  const { addToast } = useToast();
  const [showTimesheetRoundingModal, setShowTimesheetRoundingModal] = useState(false);
  const [showBreakPreferenceModal, setShowBreakPreferenceModal] = useState(false);

  const form = useForm<{
    useDecimalHours: boolean;
    isCostCodeRequired: boolean;
    isDriveTimeEnabled: boolean;
    driveTimeRate: number;
    defaultClockInTime: string | null;
    defaultClockOutTime: string | null;
  }>({
    defaultValues: {
      useDecimalHours: company?.timeTrackingSettings?.useDecimalHours || false,
      isCostCodeRequired: company?.timeTrackingSettings?.isCostCodeRequired || false,
      isDriveTimeEnabled: company?.timeTrackingSettings.isDriveTimeEnabled || false,
      driveTimeRate: company?.timeTrackingSettings.driveTimeRate || 0,
      defaultClockInTime: company?.timeTrackingSettings.defaultClockInTime || null,
      defaultClockOutTime: company?.timeTrackingSettings.defaultClockOutTime || null,
    },
  });

  const updateSettings = useMutation({
    mutationFn: async ({ setting, value }: { setting: string; value: any }) => {
      await timeTrackingSettings.update({ ...form.getValues(), [setting]: value });

      form.reset({
        ...form.getValues(),
        [setting]: value,
      });
    },
    onSuccess: () => {
      addToast({
        title: 'Updated Setting',
        description: 'The setting has been successfully updated.',
        type: 'success',
      });
      // updates the company object across the app state
      getCompany(company?.id);
    },
    onError: () => {
      addToast({
        title: 'Failed to Update Setting',
        description: 'There was an error updating the setting. Please try again.',
        type: 'error',
      });
    },
  });

  const timesheetRoundingText = useMemo(() => {
    if (
      !company?.timeTrackingSettings?.timesheetRoundingEnabled ||
      !company?.timeTrackingSettings?.timesheetRoundingType
    ) {
      return 'Not set';
    }

    switch (company?.timeTrackingSettings.timesheetRoundingType) {
      case TimesheetRoundingType.RoundDown:
        return 'Round Down';
      case TimesheetRoundingType.RoundUp:
        return 'Round Up';
      case TimesheetRoundingType.Nearest:
        return 'Nearest';
      case TimesheetRoundingType.EmployeeFriendly:
        return 'Employee Friendly';
      default:
        return 'Not set';
    }
  }, [company?.timeTrackingSettings?.timesheetRoundingEnabled, company?.timeTrackingSettings?.timesheetRoundingType]);

  const breakPreferenceText = useMemo(() => {
    if (company?.timeTrackingSettings?.areRealtimeBreaksEnabled) {
      return 'Real-time';
    }
    return 'Clock-out';
  }, [company?.timeTrackingSettings?.areRealtimeBreaksEnabled]);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Timesheets Rounding</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Automatically round clock-in and clock-out times from Hammr’s time-tracking app and configure your preferred
            rounding method.
          </p>
        </div>
        <Button
          variant="outline"
          color="neutral"
          className="flex items-center gap-2 font-medium text-sub-600"
          afterContent={<RiArrowRightSLine className="font-medium text-sub-600" />}
          onClick={() => setShowTimesheetRoundingModal(true)}
        >
          <span>{timesheetRoundingText}</span>
        </Button>
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Break Preferences</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">Configure how your employees can log breaks.</p>
        </div>
        <Button
          variant="outline"
          color="neutral"
          className="flex items-center gap-2 font-medium text-sub-600"
          afterContent={<RiArrowRightSLine className="font-medium text-sub-600" />}
          onClick={() => setShowBreakPreferenceModal(true)}
        >
          <span>{breakPreferenceText}</span>
        </Button>
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Decimal Hours</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            E.g: show 2 hours 30 minutes as 2.50 instead of 2:30 in reports.
          </p>
        </div>
        <Controller
          control={form.control}
          name="useDecimalHours"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'useDecimalHours', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Default Clock-In Time</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Default clock-in time pre-filled when creating manual timesheets.
          </p>
        </div>
        <Controller
          control={form.control}
          name="defaultClockInTime"
          render={({ field }) => (
            <TimeInput
              value={field.value ? toDate(field.value) : toDate('08:00:00')}
              onChange={(date: Date | null) => {
                const timeString = toTimeString(date);
                field.onChange(timeString);
                updateSettings.mutate({ setting: 'defaultClockInTime', value: timeString });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Default Clock-Out Time</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Default clock-out time pre-filled when creating manual timesheets.
          </p>
        </div>
        <Controller
          control={form.control}
          name="defaultClockOutTime"
          render={({ field }) => (
            <TimeInput
              value={field.value ? toDate(field.value) : toDate('16:00:00')}
              onChange={(date: Date | null) => {
                const timeString = toTimeString(date);
                field.onChange(timeString);
                updateSettings.mutate({ setting: 'defaultClockOutTime', value: timeString });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Required Cost Code</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Configure whether Cost Code is required when users create a timesheet or clock in via the mobile app.
          </p>
        </div>
        <Controller
          control={form.control}
          name="isCostCodeRequired"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'isCostCodeRequired', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="rounded-xl border border-soft-200 p-4 shadow-xs">
        <div className="flex items-center justify-between gap-3.5 ">
          <div>
            <h2 className="text-sm font-medium text-strong-950">Drive Time</h2>
            <p className="mt-1 h-fit text-xs text-sub-600">
              Configure whether admins and employees can set drive time hours on timesheets.
            </p>
          </div>
          <Controller
            control={form.control}
            name="isDriveTimeEnabled"
            render={({ field }) => (
              <Switch
                checked={field.value}
                onCheckedChange={(checked) => {
                  field.onChange(checked);
                  updateSettings.mutate({ setting: 'isDriveTimeEnabled', value: checked });
                }}
              />
            )}
          />
        </div>
        {form.watch('isDriveTimeEnabled') && (
          <div className="mt-4 flex items-center justify-between">
            <h2 className="text-sm font-medium text-strong-950">Drive Time Rate</h2>
            <Controller
              control={form.control}
              name="driveTimeRate"
              render={({ field }) => (
                <Input
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.value);
                    if (!e.target.value) return;
                    updateSettings.mutate({ setting: 'driveTimeRate', value: e.target.value });
                  }}
                  className="w-[150px]"
                  beforeContent="$"
                  type="number"
                />
              )}
            />
          </div>
        )}
      </div>

      <TimesheetRoundingModal
        open={showTimesheetRoundingModal}
        onClose={() => setShowTimesheetRoundingModal(false)}
        company={company}
        onSuccess={() => {
          setShowTimesheetRoundingModal(false);
          if (company?.id) {
            getCompany(company.id);
          }
          addToast({
            title: 'Updated Timesheet Rounding',
            description: 'Timesheet rounding settings have been successfully updated.',
            type: 'success',
          });
        }}
      />

      <BreakPreferenceModal
        open={showBreakPreferenceModal}
        onClose={() => setShowBreakPreferenceModal(false)}
        company={company}
        onSuccess={() => {
          setShowBreakPreferenceModal(false);
          if (company?.id) {
            getCompany(company.id);
          }
          addToast({
            title: 'Updated Break Preferences',
            description: 'Break preference settings have been successfully updated.',
            type: 'success',
          });
        }}
      />
    </div>
  );
}
