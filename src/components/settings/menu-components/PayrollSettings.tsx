import { useMemo, useState } from 'react';
import { RiArrowRightSLine } from '@remixicon/react';
import Button from '@/hammr-ui/components/button';
import { useCompany } from 'hooks/useCompany';
import { useToast } from 'hooks/useToast';
import EditPaySchedule from '@/components/company/EditPaySchedule';

interface PayFrequencyOption {
  value: string;
  label: string;
}

export default function PayrollSettings() {
  const { company, getCompany } = useCompany();
  const { addToast } = useToast();
  const [showEditPayScheduleModal, setShowEditPayScheduleModal] = useState(false);

  const payFrequencyOptions: PayFrequencyOption[] = useMemo(
    () => [
      { value: 'weekly', label: 'Weekly' },
      { value: 'biweekly', label: 'Bi-Weekly' },
      { value: 'semimonthly', label: 'Semi-Monthly' },
      { value: 'monthly', label: 'Monthly' },
    ],
    []
  );

  const payFrequencyLabel = useMemo(() => {
    const payFrequency = company?.paySchedule?.payFrequency || 'weekly';
    return payFrequencyOptions.find((option) => option.value === payFrequency)?.label || 'Weekly';
  }, [company?.paySchedule?.payFrequency, payFrequencyOptions]);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Pay Period</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">Frequency at which you pay employees.</p>
        </div>
        <Button
          variant="outline"
          color="neutral"
          className="flex items-center gap-2 font-medium text-sub-600"
          afterContent={<RiArrowRightSLine className="font-medium text-sub-600" />}
          onClick={() => setShowEditPayScheduleModal(true)}
        >
          <span>{payFrequencyLabel}</span>
        </Button>
      </div>

      <EditPaySchedule
        open={showEditPayScheduleModal}
        onClose={() => setShowEditPayScheduleModal(false)}
        paySchedule={company.paySchedule}
        handleSuccess={() => {
          setShowEditPayScheduleModal(false);
          if (company?.id) {
            getCompany(company.id);
          }
          addToast({
            title: 'Updated Pay Schedule',
            description: 'Your pay schedule has been updated.',
            type: 'success',
          });
        }}
      />
    </div>
  );
}
