import { Controller, useForm } from 'react-hook-form';
import { Switch } from '@/hammr-ui/components/Switch';
import { useCompany } from 'hooks/useCompany';
import { useToast } from 'hooks/useToast';
import prevailingWageSettings from '@/services/prevailing-wage-settings';
import { useMutation } from '@tanstack/react-query';

export default function PrevailingWageSettings() {
  const { company } = useCompany();
  const { addToast } = useToast();

  const form = useForm({
    defaultValues: {
      allowCustomPrevailingWagePerEmployee:
        company?.prevailingWageSettings?.allowCustomPrevailingWagePerEmployee || false,
      overridePwIfBelowRegularRate: company?.prevailingWageSettings?.overridePwIfBelowRegularRate || false,
      calculateOvertimeOnCashFringe: company?.prevailingWageSettings?.calculateOvertimeOnCashFringe || false,
    },
  });

  const updateSettings = useMutation({
    mutationFn: async ({ setting, value }: { setting: string; value: any }) => {
      await prevailingWageSettings.update({ ...form.getValues(), [setting]: value });

      form.reset({
        ...form.getValues(),
        [setting]: value,
      });
    },
    onSuccess: () => {
      addToast({
        title: 'Updated Prevailing Wage Setting',
        description: 'Prevailing wage setting has been updated successfully.',
        type: 'success',
      });
    },
    onError: () => {
      addToast({
        title: 'Failed to Update Setting',
        description: 'There was an error updating the prevailing wage setting. Please try again.',
        type: 'error',
      });
    },
  });

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Enable Custom Prevailing Wage Per Employee</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Allow specific employees to have different prevailing wage rates than the company default.
          </p>
        </div>
        <Controller
          control={form.control}
          name="allowCustomPrevailingWagePerEmployee"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'allowCustomPrevailingWagePerEmployee', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Override Prevailing Wage If Below Regular Rate</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            If an employee{"'"}s prevailing wage rate is below their regular rate, use the regular rate.
          </p>
        </div>
        <Controller
          control={form.control}
          name="overridePwIfBelowRegularRate"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'overridePwIfBelowRegularRate', value: checked });
              }}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between gap-3.5 rounded-xl border border-soft-200 p-4 shadow-xs">
        <div>
          <h2 className="text-sm font-medium text-strong-950">Calculate Overtime on Cash Fringe</h2>
          <p className="mt-1 h-fit text-xs text-sub-600">
            Include cash fringe benefits when calculating overtime pay rates.
          </p>
        </div>
        <Controller
          control={form.control}
          name="calculateOvertimeOnCashFringe"
          render={({ field: { value, onChange } }) => (
            <Switch
              checked={value}
              onCheckedChange={(checked) => {
                onChange(checked);
                updateSettings.mutate({ setting: 'calculateOvertimeOnCashFringe', value: checked });
              }}
            />
          )}
        />
      </div>
    </div>
  );
}
