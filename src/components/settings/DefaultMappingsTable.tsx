import React, { useMemo, memo } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { ColDef, ICellRendererParams } from '@ag-grid-community/core';
import { GlAccount, GlAccountMappingResponse } from '@/interfaces/gl-account-mapping';
import {
  GlAccountMapping,
  IntegrationSupportedPlatform,
  IntegrationUserToken,
} from 'interfaces/integration-user-token';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Combobox } from '@/hammr-ui/components/combobox';

// Redefine MemoizedCombobox here
interface MemoizedComboboxProps {
  value: string;
  onChange: (value: string) => void;
  items: { label: string; value: string }[];
  placeholder: string;
  className?: string;
}

const MemoizedCombobox = memo<MemoizedComboboxProps>(
  ({ value, onChange, items, placeholder, className }) => (
    <Combobox placeholder={placeholder} className={className} items={items} value={value} onChange={onChange} />
  ),
  (prevProps, nextProps) => {
    return (
      prevProps.value === nextProps.value &&
      prevProps.items === nextProps.items &&
      prevProps.placeholder === nextProps.placeholder &&
      prevProps.className === nextProps.className
    );
  }
);

interface TableRow {
  category: string;
  integrationValue: React.ReactElement | null;
}

interface DefaultMappingsTableProps {
  integration: IntegrationSupportedPlatform;
  userToken: IntegrationUserToken | undefined;
  glAccountMappings: GlAccountMappingResponse | undefined;
  isLoading: boolean;
  glAccounts: GlAccount[];
  onChange: (category: string, accountId: string, glAccountMapping: GlAccountMapping | undefined) => void;
}

const DefaultMappingsTable: React.FC<DefaultMappingsTableProps> = ({
  integration,
  userToken,
  glAccountMappings,
  glAccounts,
  isLoading,
  onChange,
}) => {
  const columns = useMemo(
    (): ColDef<TableRow>[] => [
      {
        field: 'category',
        headerName: 'Hammr Payroll Category',
        sortable: true,
        cellRenderer: (params: ICellRendererParams<TableRow>) => params.value,
      },
      {
        field: 'integrationValue',
        headerName: `${integration.name} Account`,
        sortable: false,
        cellRenderer: (params: ICellRendererParams<TableRow>) => params.value,
      },
    ],
    [integration.name]
  );

  const memoizedGlAccountItems = useMemo(() => {
    if (!glAccounts) {
      return [];
    }

    return glAccounts
      .map((entry) => ({
        label: `${entry.name} ${entry.accountNumber ? `(${entry.accountNumber})` : ''}`,
        value: entry.id.toString(),
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [glAccounts]);

  const integrationData = glAccountMappings?.data
    ? glAccountMappings.data.payrollCategoryTypes.map((category: { id: string; name: string; isHeader: boolean }) => {
        if (category.isHeader) {
          return {
            category: category.name,
            integrationValue: null,
          };
        }

        const categoryName = glAccountMappings.data.payrollCategoryTypesMapping[category.id];
        const glAccountMapping = glAccountMappings.data.glAccountMappings.find(
          (mapping) => mapping.payrollCategory === category.id
        ) as unknown as GlAccountMapping | undefined;

        return {
          category: categoryName,
          integrationValue: (
            <MemoizedCombobox
              placeholder="Select an option"
              className="mt-1 w-full"
              items={memoizedGlAccountItems}
              value={glAccountMapping ? glAccountMapping.accountId.toString() : ''}
              onChange={(value: string) => {
                if (userToken) {
                  onChange(
                    category.id,
                    value,
                    glAccountMapping
                      ? {
                          ...glAccountMapping,
                          createdBy:
                            userToken && (userToken as IntegrationUserToken).createdBy
                              ? Number((userToken as IntegrationUserToken).createdBy)
                              : undefined,
                          integrationUserTokenId: userToken.id,
                          organizationId:
                            userToken && (userToken as IntegrationUserToken).organizationId
                              ? (userToken as IntegrationUserToken).organizationId
                              : undefined,
                        }
                      : undefined
                  );
                }
              }}
            />
          ),
        };
      })
    : [];

  return (
    <UpdatedTable
      colDefs={columns}
      isLoading={isLoading}
      rowData={integrationData}
      getRowHeight={(params) => {
        if (params.data && params.data.integrationValue === null) {
          return 40;
        }
        return 60;
      }}
      tableProps={{
        rowClass: '!border-b-0',
        getRowClass: (params) => {
          if (params.data && params.data.integrationValue === null) {
            return '!bg-weak-50 !border-b !border-gray-200 !font-medium !text-neutral-400 uppercase text-xs rounded-lg';
          }
          return '!border-b-0';
        },
      }}
    />
  );
};

export default DefaultMappingsTable;
