import { UpdatedTable } from '../shared/UpdatedTable';
import { <PERSON><PERSON>, Di<PERSON>Footer, Di<PERSON>Header, DialogSurface } from '@/hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import integrationUserTokenService from 'services/integration-user-token';
import React from 'react';
import { useForm } from 'react-hook-form';
import { getGlAccountMappings, saveDepartmentMappings } from '@/services/gl-account-mappings';
import { IntegrationSupportedPlatform, IntegrationUserToken } from '@/interfaces/integration-user-token';
import { ColDef } from '@ag-grid-community/core';
import { addToast } from '@/hooks/useToast';
import { logError } from '@/utils/errorHandling';
import { DepartmentsWithCustomMappings, GlAccount, GlAccountMappingResponse } from '@/interfaces/gl-account-mapping';
import { RiEyeLine } from '@remixicon/react';
import { Combobox } from '@/hammr-ui/components/combobox';
import { apiRequest } from '@/utils/requestHelpers';

interface TableRow {
  category: string;
  integrationValue: React.ReactElement | null;
}

interface CustomDepartmentMappingsModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  columns: ColDef<TableRow>[];
  department: DepartmentsWithCustomMappings | null;
  integration: IntegrationSupportedPlatform;
}

interface FormValues {
  [key: string]: string;
}

export default function CustomDepartmentMappingsModal({
  open,
  setOpen,
  department,
  columns,
  integration,
}: CustomDepartmentMappingsModalProps) {
  const queryClient = useQueryClient();
  const userTokens = useQuery({
    queryKey: ['userTokens'],
    queryFn: integrationUserTokenService.getHammrUserTokens,
  });

  const userToken = userTokens.data?.find(
    (token: IntegrationUserToken) => token.platform === integration.id && token.isEnabled
  );

  const glAccounts = useQuery<GlAccount[]>({
    queryKey: ['glAccounts', userToken?.id],
    queryFn: () => apiRequest(`/integration-user-tokens/${userToken.id}/gl-accounts`),
    enabled: !!userToken,
  });

  const glAccountMappings = useQuery<GlAccountMappingResponse>({
    queryKey: ['glAccountMappings', integration.id, userToken?.id, department?.id, department?.hasCustomMappings],
    queryFn: () =>
      getGlAccountMappings(integration.id, userToken?.id, department?.hasCustomMappings ? department.id : undefined),
    enabled: !!userToken,
    refetchOnWindowFocus: false,
  });

  const payrollCategoryTypes = glAccountMappings.data?.data?.payrollCategoryTypes || [];
  const payrollCategoryTypesMapping = glAccountMappings.data?.data?.payrollCategoryTypesMapping || {};
  const glAccountMappingsArr = glAccountMappings.data?.data?.glAccountMappings || [];

  // Initialize default values for the form
  const defaultValues = payrollCategoryTypes.reduce((acc, category) => {
    if (!category.isHeader) {
      const glAccountMapping = glAccountMappingsArr.find((mapping) => mapping.payrollCategory === category.id);
      acc[`${category.id}`] = glAccountMapping ? glAccountMapping.accountId.toString() : '';
    }
    return acc;
  }, {} as FormValues);

  const { handleSubmit, setValue, watch } = useForm<FormValues>({
    defaultValues,
  });

  const integrationData = payrollCategoryTypes.map((category) => {
    if (category.isHeader) {
      return {
        category: category.name,
        integrationValue: null,
      };
    }
    const categoryName = payrollCategoryTypesMapping[category.id];
    const glAccountMapping = glAccountMappingsArr.find((mapping) => mapping.payrollCategory === category.id);
    const initialValue = glAccountMapping ? glAccountMapping.accountId.toString() : '';
    const currentValue = watch(`${category.id}`);

    return {
      category: categoryName,
      integrationValue: (
        <Combobox
          placeholder="Select an option"
          className="mt-1 w-full"
          items={
            glAccounts.data
              ?.toSorted((a, b) => a.name.localeCompare(b.name))
              .map((entry) => ({
                label: `${entry.name} ${entry.accountNumber ? `(${entry.accountNumber})` : ''}`,
                value: entry.id.toString(),
              })) ?? []
          }
          value={currentValue || initialValue}
          onChange={(value: string) => {
            // Only update if we have a value
            if (value) {
              setValue(`${category.id}`, value, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
            }
          }}
        />
      ),
    };
  });

  const saveDepartmentMappingsMutation = useMutation({
    mutationFn: async (
      mappings: Record<
        string,
        {
          accountId: string;
          accountName: string;
          accountType: string;
          accountCategory: string;
          platformId: string;
        }
      >
    ) => {
      return saveDepartmentMappings(department?.id, {
        mappings,
        integrationUserTokenId: userToken?.id,
      });
    },
    onSuccess: () => {
      // Just invalidate the departments list to update hasCustomMappings
      queryClient.invalidateQueries({ queryKey: ['departmentsWithCustomMappings', integration.id, userToken?.id] });
      glAccountMappings.refetch();
      setOpen(false);
      addToast({
        title: 'Updated Department Mapping',
        description: 'Your changes have been successfully saved.',
        type: 'success',
      });
    },
    onError: (error) => {
      logError(error);
      addToast({
        title: 'Mapping Error',
        description: 'Unable to update department mapping.',
        type: 'error',
      });
    },
  });

  const onSubmit = (data: FormValues) => {
    // Ensure we include all values, even if they haven't changed
    const allValues = payrollCategoryTypes.reduce(
      (acc, category) => {
        if (!category.isHeader) {
          const glAccountMapping = glAccountMappingsArr.find((mapping) => mapping.payrollCategory === category.id);
          const formValue = data[category.id];
          const initialValue = glAccountMapping ? glAccountMapping.accountId.toString() : '';

          // Use form value if it exists and is not empty, otherwise use initial value
          const accountId = formValue || initialValue;

          if (accountId) {
            // Find the account details from glAccounts
            const account = glAccounts.data.find((acc) => acc.id === accountId);
            if (account) {
              acc[category.id] = {
                accountId,
                accountName: account.name,
                accountType: account.accountType,
                accountCategory: account.accountCategory,
                platformId: account.platformId,
              };
            }
          }
        }
        return acc;
      },
      {} as Record<
        string,
        {
          accountId: string;
          accountName: string;
          accountType: string;
          accountCategory: string;
          platformId: string;
        }
      >
    );

    saveDepartmentMappingsMutation.mutate(allValues);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface size="6xl" className="max-h-[90vh] w-full">
        <DialogHeader icon={<RiEyeLine />} title={`${department?.name} Custom Mapping`} />
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="p-4" style={{ height: 'calc(90vh - 160px)' }}>
            <UpdatedTable
              colDefs={columns}
              isLoading={glAccountMappings.isLoading || glAccounts.isFetching}
              rowData={integrationData}
              getRowHeight={(params) => {
                if (params.data && params.data.integrationValue === null) {
                  return 40;
                }
                return 60;
              }}
              tableProps={{
                domLayout: 'normal',
                rowClass: '!border-b-0',
                getRowClass: (params) => {
                  if (params.data && params.data.integrationValue === null) {
                    return '!bg-weak-50 !border-b !border-gray-200 !font-medium !text-neutral-400 uppercase text-xs rounded-lg';
                  }
                  return '!border-b-0';
                },
              }}
              gridOptions={{
                headerHeight: 40,
                suppressScrollOnNewData: true,
                suppressHorizontalScroll: false,
                domLayout: 'normal',
                suppressModelUpdateAfterUpdateTransaction: true,
              }}
              enablePagination={false}
              parentContainerClassName="h-full"
              fitToWindow
            />
          </div>
          <DialogFooter>
            <Button
              fullWidth={false}
              className="items-center py-0"
              onClick={() => setOpen(false)}
              variant="outline"
              color="neutral"
              type="button"
            >
              Cancel
            </Button>
            <Button
              fullWidth={false}
              className="items-center py-0"
              type="submit"
              loading={saveDepartmentMappingsMutation.isPending}
            >
              Save
            </Button>
          </DialogFooter>
        </form>
      </DialogSurface>
    </Dialog>
  );
}
