import { UpdatedTable } from '../shared/UpdatedTable';
import { Di<PERSON>Footer, DialogSurface, DialogHeader, Dialog } from '@/hammr-ui/components/dialog';
import Button from '@/hammr-ui/components/button';
import ArrowsCircle from '@/hammr-icons/ArrowsCircle';
import { useMutation } from '@tanstack/react-query';
import integrationUserTokenService from 'services/integration-user-token';
import { JournalEntryDisplayItem, JournalEntryLineItem } from '@/interfaces/journal-entry';
import { useEffect, useRef, useState } from 'react';
import { formatUSD } from '@/utils/format';
import React from 'react';
import { formatLocaleUsa } from 'utils/dateHelper';
import { PreviewDetails } from '@/interfaces/integration-user-token';
import { ColDef, GridApi, GridReadyEvent } from '@ag-grid-community/core';
import { addToast } from '@/hooks/useToast';
import { logError } from '@/utils/errorHandling';
import { GlAccountMapping, PayrollHistoryItem } from '@/interfaces/integration-user-token';
import { RiEyeLine } from '@remixicon/react';

interface PreviewJournalEntriesModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  payrollHistory: PayrollHistoryItem[];
  previewDetails: PreviewDetails;
  setPreviewDetails: (details: PreviewDetails | null) => void;
  glAccounts: GlAccountMapping[];
  callback: () => Promise<void>;
}

const calculateTotals = (entries: JournalEntryDisplayItem[]) => {
  const totalDebit = Number(entries.reduce((sum, item) => sum + (item.debit || 0), 0).toFixed(2));
  const totalCredit = Number(entries.reduce((sum, item) => sum + (item.credit || 0), 0).toFixed(2));
  return { totalDebit, totalCredit };
};

export default function PreviewJournalEntriesModal({
  open,
  setOpen,
  payrollHistory,
  previewDetails,
  setPreviewDetails,
  glAccounts,
  callback,
}: PreviewJournalEntriesModalProps) {
  const [journalEntries, setJournalEntries] = useState<JournalEntryDisplayItem[]>([]);
  const [gridApi, setGridApi] = useState<GridApi<JournalEntryDisplayItem>>();
  const [rawLineItems, setRawLineItems] = useState<JournalEntryLineItem[] | null>(null);

  const currentPayroll = payrollHistory?.find((payroll) => payroll.id === previewDetails?.payrollId);
  const modalTitle = currentPayroll
    ? `Journal Entry Preview • ${formatLocaleUsa(new Date(currentPayroll.periodStart))} - ${formatLocaleUsa(new Date(currentPayroll.periodEnd))}`
    : 'Journal Entry Preview';

  const { mutate: createJournalEntry, isPending: isCreatingJournalEntry } = useMutation({
    mutationFn: async (payrollId: string) => {
      if (previewDetails.syncHistoryId) {
        return integrationUserTokenService.updateJournalEntry({
          platform: previewDetails.platform,
          payrollId,
          syncHistoryId: previewDetails.syncHistoryId,
          integrationUserTokenId: previewDetails.integrationUserTokenId,
        });
      } else {
        return integrationUserTokenService.createJournalEntry({
          payrollId,
          platform: previewDetails.platform,
          integrationUserTokenId: previewDetails.integrationUserTokenId,
        });
      }
    },
    onSuccess: async () => {
      await callback();
      addToast({
        title: 'Updated Integration',
        description: 'Journal entry created successfully.',
        type: 'success',
      });
    },
    onError: (error) => {
      logError(error);
      if (error?.message?.toLowerCase()?.includes('account mappings')) {
        addToast({
          title: 'Account Mappings not complete',
          description: error.message,
          type: 'error',
        });
      } else {
        addToast({
          title: 'Sync Error',
          description: error.message,
          type: 'error',
        });
      }
    },
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (details: PreviewDetails) =>
      integrationUserTokenService.previewJournalEntry({
        platform: details.platform,
        payrollId: details.payrollId,
        integrationUserTokenId: details.integrationUserTokenId,
      }),
    onSuccess: (data) => {
      setRawLineItems(data?.journal_entry?.line_items || []);
    },
    onError: (error) => {
      logError(error);
      if (error?.message?.toLowerCase()?.includes('account mappings')) {
        addToast({
          title: 'Account Mappings not complete',
          description: error.message,
          type: 'error',
        });
      } else {
        addToast({
          title: 'Sync Error',
          description: error.message,
          type: 'error',
        });
      }
    },
  });

  useEffect(() => {
    if (open && previewDetails) {
      setRawLineItems(null);
      mutate(previewDetails);
    } else if (!open) {
      setJournalEntries([]);
      setRawLineItems(null);
    }
  }, [open, previewDetails, mutate]);

  useEffect(() => {
    if (!rawLineItems) {
      setJournalEntries([]);
      return;
    }

    const formattedEntries = rawLineItems.map((item: JournalEntryLineItem) => {
      const numericTotalAmount = Number(item.total_amount);
      const amount = Math.abs(numericTotalAmount);

      const debitValue = numericTotalAmount > 0 ? amount : null;
      const creditValue = numericTotalAmount < 0 ? amount : null;

      const account = glAccounts?.find((acc) => acc.accountId === item.account_id);

      return {
        ...item,
        account_name: account ? account.accountName : item.account_id,
        debit: debitValue,
        credit: creditValue,
        total_amount: isNaN(numericTotalAmount) ? 0 : numericTotalAmount,
        account_id: item.account_id,
      };
    });
    setJournalEntries(formattedEntries);
  }, [rawLineItems, glAccounts]);

  const handleGridReady = (params: GridReadyEvent<JournalEntryDisplayItem>) => {
    setGridApi(params.api);
  };

  useEffect(() => {
    if (gridApi && journalEntries.length > 0) {
      const { totalDebit, totalCredit } = calculateTotals(journalEntries);

      const totalRow: JournalEntryDisplayItem = {
        description: 'TOTAL',
        account_name: '',
        debit: totalDebit,
        credit: totalCredit,
        account_id: '',
        total_amount: totalDebit + totalCredit,
      };
      gridApi.setGridOption('pinnedBottomRowData', [totalRow]);
    }
  }, [gridApi, journalEntries]);

  const journalEntryColDefs: ColDef<JournalEntryDisplayItem>[] = [
    {
      field: 'description',
      headerName: 'Description',
      minWidth: 400,
      flex: 1,
    },
    {
      field: 'account_name',
      headerName: 'Account Name',
      minWidth: 150,
      valueGetter: (params) => {
        const glAccount = glAccounts.find((acc) => acc.accountId === params.data.account_id);
        return `${params.data.account_name} ${glAccount?.accountNumber ? `(${glAccount.accountNumber})` : ''}`;
      },
    },
    {
      field: 'debit',
      headerName: 'Debit',
      maxWidth: 150,
      valueFormatter: (params) => (!params.value && params.value !== 0 ? '' : formatUSD.format(params.value)),
    },
    {
      field: 'credit',
      headerName: 'Credit',
      maxWidth: 150,
      valueFormatter: (params) => (!params.value && params.value !== 0 ? '' : formatUSD.format(params.value)),
    },
  ];

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (!open) setPreviewDetails(null);
      }}
    >
      <DialogSurface size="6xl" className="max-h-[90vh] w-full">
        <DialogHeader icon={<RiEyeLine />} title={modalTitle} />
        <div className="p-4" style={{ height: 'calc(90vh - 160px)' }}>
          <UpdatedTable
            fitToWindow
            onGridReady={handleGridReady}
            colDefs={journalEntryColDefs}
            rowData={journalEntries}
            isLoading={isPending}
            emptyRowsText={isPending ? 'Loading journal entries...' : 'No journal entries found'}
            getRowHeight={(params) => (params.node?.rowPinned === 'bottom' ? 48 : 52)}
            tableProps={{ domLayout: 'normal' }}
            gridOptions={{
              headerHeight: 40,
              suppressScrollOnNewData: true,
              suppressHorizontalScroll: false,
              domLayout: 'normal',
            }}
            enablePagination={false}
            parentContainerClassName="h-full"
          />
        </div>
        <DialogFooter>
          <Button
            title="Sync"
            fullWidth={false}
            className="items-center py-0"
            onClick={() => createJournalEntry(previewDetails.payrollId)}
          >
            <div className="flex items-center gap-x-2">
              <ArrowsCircle
                height={24}
                width={24}
                className={isCreatingJournalEntry ? 'animate-spin fill-white' : 'fill-white'}
              />
              Sync
            </div>
          </Button>
        </DialogFooter>
      </DialogSurface>
    </Dialog>
  );
}
