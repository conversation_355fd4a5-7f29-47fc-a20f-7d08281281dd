import { useState, useEffect } from 'react';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { useRouter } from 'next/router';
import { listPayrolls } from 'services/payroll';
import { checkRequestPaginated } from 'utils/requestHelpers';
import { capitalize } from 'utils/stringHelper';
import { formatLocaleUsa } from 'utils/dateHelper';
import { useDate } from 'hooks/useDate';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { StatusCell } from 'components/payroll/PayrollHistory';
import { useAuth } from 'hooks/useAuth';
import integrationUserTokenService from 'services/integration-user-token';
import { addToast } from 'hooks/useToast';
import { logError } from 'utils/errorHandling';
import Button from 'hammr-ui/components/button';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { getSyncHistory } from 'services/sync-history';
import { Tooltip } from '@hammr-ui/components/tooltip';
import { IntegrationUserToken, PayrollRow } from 'interfaces/integration-user-token';
import { IntegrationSupportedPlatform } from 'interfaces/integration-user-token';
import ArrowsCircle from '@/hammr-icons/ArrowsCircle';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import ErrorWarning from '@/hammr-icons/ErrorWarning';
import { cn } from '@/utils/cn';
import DownloadCloud2Line from '@/hammr-icons/DownloadCloud2Line';
import { Payroll } from '@/interfaces/payroll';

const LoadingButton = ({
  integrationId,
  payrollId,
  callback,
}: {
  integrationId: string;
  payrollId: string;
  callback: () => Promise<void>;
}) => {
  const [isCreatingJournalEntry, setIsCreatingJournalEntry] = useState(false);

  const handleDownload = async (payrollId: string) => {
    if (isCreatingJournalEntry) {
      return;
    }

    setIsCreatingJournalEntry(true);
    try {
      await integrationUserTokenService.exportPayrollJournalEntryToCsv({
        payrollId,
      });

      await callback();

      addToast({
        title: 'Downloaded Journal Entry Export',
        description: 'Journal entry export downloaded successfully.',
        type: 'success',
      });
    } catch (error) {
      logError(error);
      addToast({
        title: 'Download Failed',
        description: error.message,
        type: 'error',
      });
    } finally {
      setIsCreatingJournalEntry(false);
    }
  };

  return (
    <Button title="Download" fullWidth={false} className="items-center py-0" onClick={() => handleDownload(payrollId)}>
      <div className="flex items-center gap-x-2">
        <DownloadCloud2Line height={24} width={24} className="fill-white" />
        Download
      </div>
    </Button>
  );
};

interface ManualIntegrationExportProps {
  integration: IntegrationSupportedPlatform;
  integrationUserToken: IntegrationUserToken;
}

const ManualIntegrationExport: React.FC<ManualIntegrationExportProps> = ({ integration, integrationUserToken }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [payrollHistory, setPayrollHistory] = useState<PayrollRow[]>([]);
  const { formatUsa, dayjs } = useDate();

  const callback = async () => {
    await fetchPayrollHistoryAndSyncHistory();
  };

  const colDefs: ColDef[] = [
    {
      field: 'payPeriod',
      headerName: 'Pay period',
      minWidth: 250,
      cellRenderer: (params: ValueFormatterParams) => {
        return `${formatUsa(params.data.periodStart)} - ${formatUsa(params.data.periodEnd)}`;
      },
    },
    {
      field: 'payDay',
      headerName: 'Payday',
      maxWidth: 200,
      cellRenderer: (params: ValueFormatterParams) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      field: 'type',
      headerName: 'Type',
      valueFormatter: (params: ValueFormatterParams) => params.value.replace('_', '-'),
      minWidth: 120,
      maxWidth: 150,
    },
    {
      field: 'status',
      headerName: 'Status',
      minWidth: 120,
      maxWidth: 170,
      cellRenderer: (params: ValueFormatterParams) => {
        return <StatusCell status={params.value} />;
      },
    },
    {
      field: 'approved_at',
      headerName: 'Approved at',
      minWidth: 250,
      cellRenderer: (params: ValueFormatterParams) => {
        return params.value ? formatUsa(params.value, { showTime: true, showTimezone: true }) : '';
      },
    },
    {
      field: 'actions',
      headerName: 'Download',
      minWidth: 250,
      cellRenderer: (params: ValueFormatterParams) => {
        return (
          <div className="flex h-full items-center justify-start">
            <LoadingButton integrationId={integration.id} payrollId={params.data.id} callback={callback} />
          </div>
        );
      },
    },
  ];

  const mapPayrolls = (payrolls: Payroll[]): PayrollRow[] => {
    return payrolls
      .map((payroll) => {
        // filter out payrolls by draft, partially_paid, failed
        if (['draft', 'partially_paid', 'failed'].includes(payroll.status)) {
          return null;
        }

        return {
          periodStart: dayjs(payroll.period_start).valueOf(),
          periodEnd: dayjs(payroll.period_end).valueOf(),
          payPeriod: dayjs(payroll.period_start).valueOf(),
          payDay: dayjs(payroll.payday).valueOf(),
          type: capitalize(payroll.type),
          status: payroll.status,
          approved_at: payroll.approved_at ? dayjs(payroll.approved_at).valueOf() : 0,
          id: payroll.id,
        };
      })
      .filter((payroll) => payroll !== null);
  };

  const fetchPayrollHistoryAndSyncHistory = async () => {
    const payrolls = await listPayrolls({
      companyId: user?.checkCompanyId,
    });

    const allPayrolls = await checkRequestPaginated(payrolls, 2);

    const mappedPayrolls = mapPayrolls(allPayrolls);
    setPayrollHistory(mappedPayrolls);
  };

  useEffect(() => {
    fetchPayrollHistoryAndSyncHistory();
  }, [user?.checkCompanyId]);

  if (!payrollHistory) {
    return null;
  }

  return (
    <div className="pb-20">
      <UpdatedTable
        colDefs={colDefs}
        getRowHeight={() => 60}
        rowData={payrollHistory}
        emptyRowsText="No payrolls found"
        gridOptions={{
          groupDisplayType: 'groupRows',
        }}
        enablePagination
        onRowClicked={(event) => {
          if (event.data.status === 'draft') {
            router.push(`/payroll/${event.data.id}`);
          } else {
            router.push(`/payroll/${event.data.id}/details`);
          }
        }}
      />
    </div>
  );
};

export default ManualIntegrationExport;
