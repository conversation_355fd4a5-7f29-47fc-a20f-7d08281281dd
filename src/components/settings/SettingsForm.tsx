import { useEffect, useState } from 'react';
import {
  RiArrowRightSLine,
  RiFlashlightLine,
  RiSmartphoneLine,
  RiTable2,
  RiTimeLine,
  RiTimerLine,
} from '@remixicon/react';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import * as TabMenuVertical from '@/components/ui/tab-menu-vertical';
import { useCompany } from 'hooks/useCompany';
import { useAuth } from 'hooks/useAuth';
import TimeTrackingSettings from './menu-components/TimeTrackingSettings';
import MobileAppSettings from './menu-components/MobileAppSettings';
import OvertimeSettings from './menu-components/overtime-settings/OvertimeSettings';
import PayrollSettings from './menu-components/PayrollSettings';
import PrevailingWageSettings from './menu-components/PrevailingWageSettings';
import { PageHeader } from '@hammr-ui/components/PageHeader';
import Settings2Line from '@hammr-icons/Settings2Line';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@hammr-ui/components/FlatTabs';
import { useRouter } from 'next/router';
import CalendarLine from '@/hammr-icons/CalendarLine';
import SchedulingSettings from './menu-components/SchedulingSettings';

export default function SettingsForm() {
  const router = useRouter();
  const { company } = useCompany();

  const tabs = [
    {
      name: 'General Settings',
      key: '/settings',
    },
    ...(company?.isPayrollEnabled ? [{ name: 'Integrations', key: '/settings/integrations' }] : []),
  ] as const;

  type SettingsFormTabsKey = (typeof tabs)[number]['key'];

  const { user, getUser } = useAuth();

  // TODO:
  //  we need to properly use layouts so we don't duplicate the tabs code between "General settings"
  //  and "Integrations" pages
  const [selectedTab, setSelectedTab] = useState<SettingsFormTabsKey>('/settings');

  useEffect(() => {
    getUser(user?.uid);
  }, [getUser, user?.uid]);

  // Get the active menu item from query parameters or use default
  const activeMenuItem = (router.query.menu as string) || 'time-tracking';

  // Combined menu items and their corresponding components
  const menuConfig = {
    'time-tracking': {
      name: 'Time Tracking',
      icon: RiTimerLine,
      component: TimeTrackingSettings,
    },
    'mobile-app': {
      name: 'Mobile App',
      icon: RiSmartphoneLine,
      component: MobileAppSettings,
    },
    overtime: {
      name: 'Overtime',
      icon: RiTimeLine,
      component: OvertimeSettings,
    },
    payroll: {
      name: 'Payroll',
      icon: RiFlashlightLine,
      component: PayrollSettings,
    },
    'prevailing-wage': {
      name: 'Prevailing Wage',
      icon: RiTable2,
      component: PrevailingWageSettings,
    },
    scheduling: {
      name: 'Scheduling',
      icon: CalendarLine,
      component: SchedulingSettings,
    },
  };

  // Handle menu item click by updating the URL query parameter
  const handleMenuItemClick = (menuItem: string) => {
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, menu: menuItem },
      },
      undefined,
      { shallow: true }
    );
  };

  const CurrentComponent =
    menuConfig[activeMenuItem as keyof typeof menuConfig]?.component || menuConfig['time-tracking'].component;

  return (
    <>
      <PageHeader title="Settings" icon={<Settings2Line />} />

      <Tabs className="mx-8" value={selectedTab}>
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.key}
              value={tab.key}
              onClick={() => {
                router.push(tab.key);
              }}
            >
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="/settings" className="flex max-w-screen-md flex-col gap-6">
          {company && user ? (
            <div className="flex flex-row items-start gap-16">
              <TabMenuVertical.Root
                defaultValue="Profile Settings"
                type="card"
                className="min-w-64"
                value={activeMenuItem}
                onValueChange={handleMenuItemClick}
              >
                <h4 className="mb-2 px-2 py-1 text-xs font-medium uppercase text-soft-400">Select Menu</h4>
                <TabMenuVertical.List>
                  {Object.entries(menuConfig).map(([id, item]) => {
                    // const isActive = activeMenuItem === id;
                    const Icon = item.icon;
                    return (
                      <TabMenuVertical.Trigger key={id} value={id}>
                        <TabMenuVertical.Icon as={Icon} />
                        {item.name}
                        <TabMenuVertical.ArrowIcon as={RiArrowRightSLine} />
                      </TabMenuVertical.Trigger>
                    );
                  })}
                </TabMenuVertical.List>
              </TabMenuVertical.Root>

              <div className="flex-1">
                <CurrentComponent />
              </div>
            </div>
          ) : (
            <div className="mt-8 flex justify-center">
              <LoadingIndicator />
            </div>
          )}
        </TabsContent>
      </Tabs>
    </>
  );
}
