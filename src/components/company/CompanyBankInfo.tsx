import { useEffect, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { generateCompanyPaymentSetupLink, getCheckCompany } from 'services/company';
import { getBankAccount } from 'services/bank-account';
import { capitalize } from 'utils/stringHelper';
import importScript from 'hooks/importScript';
import EditPaymentSetup from '../shared/EditPaymentSetup';
import SetSignerTitleModal from './SetSignerTitleModal';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import Button from '@/hammr-ui/components/button';
import { useMutation, useQuery } from '@tanstack/react-query';
import { logError } from '@/utils/errorHandling';

const CompanyBankInfo: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [paymentSetupLink, setPaymentSetupLink] = useState(null);
  const [signerTitle, setSignerTitle] = useState(null);
  const [email, setEmail] = useState(null);
  const [signerTitleModalOpen, setSignerTitleModalOpen] = useState(false);

  importScript('https://cdn.checkhq.com/component-initialize.js');

  const company = useQuery({
    queryKey: ['company', user?.checkCompanyId],
    queryFn: () => getCheckCompany(user.checkCompanyId),
    enabled: !!user?.checkCompanyId,
  });

  const bankAccount = useQuery({
    queryKey: ['bankAccount', company.data?.bank_accounts[0]],
    queryFn: () => getBankAccount(company.data?.bank_accounts[0]),
    enabled: !!company.data?.bank_accounts[0],
  });

  useEffect(() => {
    if (company.error) {
      logError(company.error);
      toast({
        title: 'Error',
        description: 'Failed to load bank information',
        toastVariant: 'error',
      });
    }
  }, [company.error, toast]);

  useEffect(() => {
    if (bankAccount.error) {
      logError(bankAccount.error);
      toast({
        title: 'Error',
        description: 'Failed to load bank account information',
        toastVariant: 'error',
      });
    }
  }, [bankAccount.error, toast]);

  const updatePaymentSetup = useMutation({
    mutationFn: ({ signerTitle, email }: { signerTitle?: string; email?: string }) =>
      generateCompanyPaymentSetupLink(
        user?.checkCompanyId,
        user?.name,
        signerTitle || user.signer_title,
        email || user?.email
      ),
    onError: (error) => {
      logError(error);
      toast({
        title: 'Error',
        description: 'Failed to generate payment setup link',
        toastVariant: 'error',
      });
    },
  });

  const createCompanyPaymentSetupLink = async (userSignerTitle?: string, userEmail?: string) => {
    const result = await updatePaymentSetup.mutateAsync({ signerTitle: userSignerTitle, email: userEmail });
    setPaymentSetupLink(result.url);
  };

  const handlePaymentUpdate = async () => {
    if (!user.signer_title) {
      setSignerTitleModalOpen(true);
    } else {
      createCompanyPaymentSetupLink();
    }
  };

  useEffect(() => {
    if (signerTitle) {
      setSignerTitleModalOpen(false);
      createCompanyPaymentSetupLink(signerTitle, email);
    }
  }, [signerTitle, email]);

  if (!user) return null;

  const isLoading = company.isLoading || bankAccount.isLoading;
  const noBankAccountFound = company.data?.bank_accounts.length === 0;

  if (isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (noBankAccountFound) {
    return (
      <div className="w-full">
        <div className="border-stroke-soft-200 rounded-16 border p-6 shadow-md">
          <div className="mb-6">
            <h2 className="text-base font-medium text-strong-950">Bank Account</h2>
            <p className="mt-1.5 text-sm text-sub-600">No bank account found.</p>
          </div>
        </div>
      </div>
    );
  }

  if (bankAccount.data && company.data) {
    return (
      <>
        <section className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs">
          <div className="flex items-center justify-between">
            <h2 className="font-medium text-strong-950">Bank Account</h2>
            <Button color="primary" onClick={handlePaymentUpdate} loading={isLoading} variant="lighter">
              Update Payment Method
            </Button>
          </div>

          <div className="mt-5 flex flex-col  space-y-5">
            <div className="flex flex-col gap-1.5">
              <div className="text-xs text-sub-600">Institution</div>
              <div className="text-sm text-strong-950">
                {bankAccount.data?.plaid_bank_account ? (
                  <>
                    {bankAccount.data?.plaid_bank_account.institution_name} -{' '}
                    {bankAccount.data?.plaid_bank_account.mask}
                  </>
                ) : (
                  '-'
                )}
                {bankAccount.data?.raw_bank_account?.institution_name && (
                  <>{bankAccount.data?.raw_bank_account.institution_name}</>
                )}
              </div>
            </div>

            <div className="flex flex-col gap-1.5">
              <div className="text-xs text-sub-600">Account Type</div>
              <div className="text-sm text-strong-950">
                {bankAccount.data?.plaid_bank_account && (
                  <>{capitalize(bankAccount.data?.plaid_bank_account.subtype)}</>
                )}
                {bankAccount.data?.raw_bank_account && <>{capitalize(bankAccount.data?.raw_bank_account.subtype)}</>}
              </div>
            </div>

            <div className="flex flex-col gap-1.5">
              <div className="text-xs text-sub-600">Status</div>
              <div className="text-sm text-strong-950">
                {bankAccount.data?.plaid_bank_account?.verified ? 'Verified' : 'Not Verified'}
              </div>
            </div>
          </div>
        </section>

        {paymentSetupLink && (
          <EditPaymentSetup
            componentLink={paymentSetupLink}
            handleSuccess={() => {
              // Refresh logic
            }}
            handleCancel={() => {
              setPaymentSetupLink(null);
              // setLoading(false);
            }}
          />
        )}
        {signerTitleModalOpen && (
          <SetSignerTitleModal
            isOpen={signerTitleModalOpen}
            setIsOpen={setSignerTitleModalOpen}
            handleComplete={(signerTitle, email) => {
              setSignerTitle(signerTitle);
              setEmail(email);
            }}
          />
        )}
      </>
    );
  }

  return null;
};

export default CompanyBankInfo;
