import { useForm } from 'react-hook-form';
import { useAuth } from 'hooks/useAuth';
import { <PERSON><PERSON>, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormV2 } from '../elements/Form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import EditLine from '@/hammr-icons/EditLine';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { logError } from '@/utils/errorHandling';
import { useEffect } from 'react';

interface Props {
  handleComplete: (signerTitle: string, email: string) => void;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const SetSignerTitleModal: React.FC<Props> = ({ handleComplete, isOpen, setIsOpen }) => {
  const { user, updateUser } = useAuth();
  const { toast } = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      signerTitle: user.signer_title,
      email: user.email,
    },
  });
  const updateUserMutation = useMutation({
    mutationFn: (data: { signerTitle: string; email: string }) =>
      updateUser(user.uid, { signerTitle: data.signerTitle, email: data.email }),
    onSuccess: (_, variables) => {
      handleComplete(variables.signerTitle, variables.email);
    },
  });

  useEffect(() => {
    if (updateUserMutation.error) {
      logError(updateUserMutation.error);
      toast({
        title: 'Error',
        description: 'Failed to update user information',
        toastVariant: 'error',
      });
    }
  }, [updateUserMutation.error, toast]);

  const onSubmit = async (data: any) => {
    await updateUserMutation.mutateAsync({ signerTitle: data.signerTitle, email: data.email });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<EditLine className="text-sub-600" />} title="Set Your Signer Title" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={updateUserMutation.isPending}
          submitText="Save"
        >
          <FormItem required error={!!errors['signerTitle']}>
            <FormLabel>Signer Title</FormLabel>
            <FormControl>
              <Input
                placeholder="Enter signer title"
                {...register('signerTitle', {
                  required: 'Please enter a signer title',
                })}
              />
            </FormControl>
            <FormMessage>{errors['signerTitle']?.message as string}</FormMessage>
            <FormMessage>{'The title should be "Officer", "Manager", etc.'}</FormMessage>
          </FormItem>

          {!user?.email && (
            <FormItem required error={!!errors['email']} className="mt-5">
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter an email"
                  {...register('email', {
                    required: 'Please enter an email',
                  })}
                />
              </FormControl>
              <FormMessage>{errors['email']?.message as string}</FormMessage>
            </FormItem>
          )}
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default SetSignerTitleModal;
