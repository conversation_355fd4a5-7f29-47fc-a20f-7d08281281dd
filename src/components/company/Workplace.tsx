import { useState, useEffect } from 'react';
import { createWorkplace, listWorkplaces } from 'services/workplace';
import { useAuth } from 'hooks/useAuth';
import WorkplaceList from 'components/company/WorkplaceList';
import AddNewWorkplace from 'components/company/AddNewWorkplace';
import UpdateWorkplace from 'components/company/UpdateWorkplace';
import Spinner from 'components/icons/Spinner';
import { Workplace as WorkplaceInterface, WorkplaceForm } from 'interfaces/workplace';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';

const Workplace: React.FC<{
  center?: boolean;
  handleCreateWorkplace?: any;
}> = ({ center, handleCreateWorkplace }) => {
  const { user } = useAuth();
  const { addToast } = useToast();
  const [workplaces, setWorkplaces] = useState(null);
  const [addWorkplaceView, setAddWorkplaceView] = useState(false);
  const [updateWorkplaceView, setUpdateWorkplaceView] = useState(false);
  const [workplaceToUpdate, setWorkplaceToUpdate] = useState(null);
  const [loading, setLoading] = useState(false);

  const refresh = () => {
    listWorkplaces(user.checkCompanyId)
      .then((res) => {
        setWorkplaces(res);
        if (handleCreateWorkplace && res.length > 0) {
          handleCreateWorkplace();
        }
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  useEffect(() => {
    if (user.checkCompanyId && !workplaces) {
      refresh();
    }
  }, [user.checkCompanyId, workplaces]);

  if (!user) return null;

  const handleCancel = () => {
    setAddWorkplaceView(false);
    setUpdateWorkplaceView(false);
    listWorkplaces(user?.checkCompanyId)
      .then((res) => {
        setLoading(false);
        setWorkplaces(res);
        if (handleCreateWorkplace && res.length > 0) {
          handleCreateWorkplace();
        }
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  const handleAddNewWorkplace = () => {
    setAddWorkplaceView(true);
  };

  // const handleUpdateWorkplace = (workplace) => {
  //   setWorkplaceToUpdate(workplace);
  //   setUpdateWorkplaceView(true);
  // };

  const handleUseCompanyAddress = () => {
    if (!loading) {
      setLoading(true);
      const companyAddress = JSON.parse(localStorage.getItem('companyOnboardAddress'));
      const workplace: WorkplaceForm = {
        company: user?.checkCompanyId,
        address: companyAddress,
      };
      createWorkplace(workplace)
        .then(async (res) => {
          if (res.status == 400) {
            setLoading(false);
          } else if (res.status == 201) {
            addToast({
              title: 'Added Workplace',
              description: 'We have successfully added your workplace.',
              type: 'success',
            });
            handleCancel();
          }
        })
        .catch((err) => {
          logError(err);
          showErrorToast(err);
        });
    }
  };

  if (addWorkplaceView && user) {
    return (
      <div className={`sm:w-full ${center && `mx-auto`}`}>
        <div>
          <div className="display-4 text-2xl">Add a workplace</div>
          <div className="mt-5">
            <p>Fill out the form below to add a new workplace.</p>
          </div>
          <AddNewWorkplace
            checkCompanyId={user?.checkCompanyId}
            handleCancel={handleCancel}
            handleCreateWorkplace={handleCreateWorkplace}
          />
        </div>
      </div>
    );
  } else if (updateWorkplaceView && user) {
    return (
      <div className={`sm:w-full ${center && `mx-auto`}`}>
        <div>
          <div className="display-4">Update workplace</div>
          <div className="mt-5">
            <p>Change any values below to update your workplace.</p>
          </div>
          <UpdateWorkplace
            checkCompanyId={user?.checkCompanyId}
            currentWorkplaceData={workplaceToUpdate}
            handleCancel={handleCancel}
          />
        </div>
      </div>
    );
  } else {
    return (
      <div className={`sm:w-full ${center && `mx-auto`}`}>
        <div>
          <div>
            <div className="card-header">Your workplaces</div>
            <div className="mt-5">
              <p>
                In order to accurately calculate tax for employees, we need to keep track of your company&apos;s
                physical work locations.
              </p>
            </div>
          </div>
          {workplaces && user ? (
            <div>
              {workplaces.length > 0 ? (
                <div>
                  <WorkplaceList workplaces={workplaces} />
                  <div
                    onClick={handleAddNewWorkplace}
                    className="mt-5 cursor-pointer text-center text-sm text-hammr-orange-500 hover:underline"
                  >
                    Add another workplace
                  </div>
                </div>
              ) : loading ? (
                <div className="mt-10 max-h-64 border-b border-t pt-3">
                  <Spinner width="40" fill="blue" className="mx-auto my-6 animate-spin" />
                </div>
              ) : (
                <div className="mt-10 flex flex-col">
                  <div
                    onClick={handleAddNewWorkplace}
                    className="cursor-pointer text-center text-sm text-hammr-orange-500 hover:underline"
                  >
                    Create your first workplace
                  </div>
                  {localStorage.getItem('companyOnboardAddress') && (
                    <div>
                      <div className="my-1 text-center text-sm">or</div>
                      <div
                        onClick={handleUseCompanyAddress}
                        className="mt cursor-pointer text-center text-sm text-hammr-orange-500 hover:underline"
                      >
                        Use company address
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="mt-10 max-h-64 border-b border-t pt-3">
              <Spinner width="40" fill="blue" className="mx-auto my-6 animate-spin" />
            </div>
          )}
        </div>
      </div>
    );
  }
};

export default Workplace;
