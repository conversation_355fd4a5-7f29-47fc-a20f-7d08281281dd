interface Props {
  workplaces: any;
}

const WorkplaceList: React.FC<Props> = ({ workplaces }) => {
  return (
    <div>
      <div className="pt-3 max-h-64 border-t border-b mt-10">
        <div className="pb-5 overflow-auto max-h-52 px-5">
          {workplaces.map((workplace, index) => (
            <div key={index} className="flex text-lg my-2 h-14 rounded">
              <p className="inline-block my-auto flex-1">
                {workplace.address.line1}
                <br />
                {workplace.address.city}, {workplace.address.state},{' '}
                {workplace.address.postal_code}
              </p>
              <div className="flex-none" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WorkplaceList;
