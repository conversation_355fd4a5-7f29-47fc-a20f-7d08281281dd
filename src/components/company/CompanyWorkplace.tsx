import { useState, useEffect } from 'react';
import { useAuth } from 'hooks/useAuth';
import WorkplacesForm from '../people/sections/WorkplacesForm';
import { listWorkplaces } from 'services/workplace';
import { logError } from 'utils/errorHandling';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import Button from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import { useQuery, useMutation } from '@tanstack/react-query';

const CompanyWorkplace: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const workplaces = useQuery({
    queryKey: ['workplaces', user?.checkCompanyId],
    queryFn: () => listWorkplaces(user.checkCompanyId),
    enabled: !!user?.checkCompanyId,
  });

  useEffect(() => {
    if (workplaces.error) {
      logError(workplaces.error);
      toast({
        title: 'Error',
        description: 'Failed to load workplaces',
        toastVariant: 'error',
      });
    }
  }, [workplaces.error, toast]);

  const handleWorkplaceAdded = () => {
    workplaces.refetch();
  };

  if (!user) return null;

  if (workplaces.isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  return (
    <>
      <section className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs">
        <hgroup>
          <h2 className="font-medium text-strong-950">Your Workplaces</h2>
          <p className="mt-1 text-sm text-sub-600">
            In order to accurately calculate tax for employees, we need to keep track of your company&apos;s physical
            work locations.
          </p>
        </hgroup>

        {workplaces.data?.map((workplace, index) => (
          <address key={index} className="mt-5 text-sm not-italic text-strong-950">
            <div>{workplace.address.line1}</div>
            <div>
              {workplace.address.city}, {workplace.address.state}, {workplace.address.postal_code}
            </div>
          </address>
        ))}

        <Button className="mt-5" variant="outline" onClick={() => setIsFormOpen(true)} beforeContent={<AddLine />}>
          Add Workplace
        </Button>
      </section>

      <WorkplacesForm
        open={isFormOpen}
        setOpen={setIsFormOpen}
        handleSuccess={handleWorkplaceAdded}
        checkCompanyId={user.checkCompanyId}
      />
    </>
  );
};

export default CompanyWorkplace;
