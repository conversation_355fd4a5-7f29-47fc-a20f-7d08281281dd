import CompanyBasicInfo from 'components/company/CompanyBasicInfo';
import CompanyBankInfo from 'components/company/CompanyBankInfo';
import { useAuth } from 'hooks/useAuth';
import { useEffect, useState } from 'react';
import { getCheckCompany } from 'services/company';
import TaxSetup from './TaxSetup';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Tabs, TabsList, TabsTrigger, TabsContent } from 'hammr-ui/components/FlatTabs';
import CompanyWorkplace from './CompanyWorkplace';

type Tabs = 'basic-info' | 'workplace' | 'bank-account' | 'tax-setup';

const Details: React.FC = () => {
  const tabs = [
    { key: 'basic-info', name: 'General Information' },
    { key: 'workplace', name: 'Workplace' },
    { key: 'bank-account', name: 'Bank Account' },
    { key: 'tax-setup', name: 'Tax Setup' },
  ];

  const { user } = useAuth();
  const [selectedTab, setSelectedTab] = useState<Tabs>(null);
  const [company, setCompany] = useState(null);

  const refresh = () => {
    getCheckCompany(user.checkCompanyId)
      .then(async (res) => {
        setCompany(res);
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  if (!user) return null;

  if (!selectedTab) {
    setSelectedTab('basic-info');
  }

  useEffect(() => {
    if (user.checkCompanyId && !company) {
      refresh();
    }
  }, [user.checkCompanyId]);

  return (
    <Tabs className="mx-8" value={selectedTab} onValueChange={(value: Tabs) => setSelectedTab(value)}>
      <TabsList>
        {tabs.map((tab) => (
          <TabsTrigger key={tab.key} value={tab.key}>
            {tab.name}
          </TabsTrigger>
        ))}
      </TabsList>
      <TabsContent value="basic-info">
        <CompanyBasicInfo />
      </TabsContent>
      <TabsContent value="workplace">
        <CompanyWorkplace />
      </TabsContent>
      <TabsContent value="bank-account">
        <CompanyBankInfo />
      </TabsContent>
      <TabsContent value="tax-setup">
        <TaxSetup />
      </TabsContent>
    </Tabs>
  );
};

export default Details;
