import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { WorkplaceForm, Workplace } from 'interfaces/workplace';
import { createWorkplace } from 'services/workplace';
import { useAuth } from 'hooks/useAuth';
import Button from 'components/elements/Button';
import { logError, showErrorToast } from 'utils/errorHandling';

interface WorkplaceData {
  address: string;
  city: string;
  state: string;
  postalCode: string;
  phone: string;
}

/**
 * @deprecated . use WorkplacesForm.tsx
 * @param handleCancel
 * @param checkCompanyId
 * @param handleCreateWorkplace
 * @constructor
 */
const AddNewWorkplace: React.FC<{
  handleCancel: any;
  checkCompanyId: string;
  handleCreateWorkplace?: any;
}> = ({ handleCancel, checkCompanyId, handleCreateWorkplace }) => {
  const { user } = useAuth();
  const {
    register,
    formState: { errors },
    handleSubmit,
    setError,
  } = useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { addToast } = useToast();

  const setCheckSpecificErrorMessage = async (res) => {
    const jsonRes = await res.json();
    if (jsonRes.error?.type == 'address_invalid') {
      setError('address', {
        type: 'manual',
        message: jsonRes.error.message,
      });
      setError('postalCode', {
        type: 'manual',
        message: jsonRes.error.message,
      });
      setError('state', {
        type: 'manual',
        message: jsonRes.error.message,
      });
      setError('city', {
        type: 'manual',
        message: jsonRes.error.message,
      });
    } else if (jsonRes.error?.type == 'state_not_supported') {
      setError('state', {
        type: 'manual',
        message: jsonRes.error.message,
      });
    } else if (jsonRes.error.input_errors) {
      if (jsonRes.error.input_errors[0].field == 'state') {
        setError('state', {
          type: 'manual',
          message: jsonRes.error.input_errors[0].message,
        });
      }
    }
  };

  const onSubmit = (data: WorkplaceData) => {
    setIsLoading(true);
    const workplace: WorkplaceForm = {
      company: checkCompanyId,
      address: {
        line1: data.address,
        city: data.city,
        state: data.state,
        postal_code: data.postalCode,
      },
    };
    createWorkplace(workplace)
      .then(async (res) => {
        if (res.status == 400) {
          setCheckSpecificErrorMessage(res).then(() => {
            setIsLoading(false);
          });
        } else if (res.status == 201) {
          addToast({
            title: 'Added workplace',
            description: 'We have successfully added your workplace.',
            type: 'success',
          });
          handleCancel();
          if (handleCreateWorkplace) {
            handleCreateWorkplace();
          }
        }
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  return (
    <div className="mt-5 rounded-md">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="border-t py-4">
          <div>
            <input
              id="address"
              className="input-padding focus:shadow-outline-blue block w-full appearance-none rounded-md border border-gray-300 bg-gray-50 placeholder-gray-400 shadow-sm transition duration-150 ease-in-out focus:border-blue-300 focus:outline-none sm:text-sm sm:leading-5"
              type="text"
              {...register('address', {
                required: 'Please enter a street address',
              })}
              placeholder="Street address"
            />
            {errors.address && <div className="mt-2 text-xs text-red-600">{errors.address.message as string}</div>}
          </div>
          <div className="mt-4">
            <input
              id="city"
              className="input-padding focus:shadow-outline-blue block w-full appearance-none rounded-md border border-gray-300 bg-gray-50 placeholder-gray-400 shadow-sm transition duration-150 ease-in-out focus:border-blue-300 focus:outline-none sm:text-sm sm:leading-5"
              type="text"
              {...register('city', {
                required: 'Please enter a city',
              })}
              placeholder="City"
            />
            {errors.city && <div className="mt-2 text-xs text-red-600">{errors.city.message as string}</div>}
          </div>
          <div className="mt-4 flex">
            <div className="flex-1 pr-2">
              <input
                id="state"
                className="input-padding focus:shadow-outline-blue block w-full appearance-none rounded-md border border-gray-300 bg-gray-50 placeholder-gray-400 shadow-sm transition duration-150 ease-in-out focus:border-blue-300 focus:outline-none sm:text-sm sm:leading-5"
                type="text"
                {...register('state', {
                  required: 'Please enter a state',
                  pattern: {
                    value:
                      /^(?:A[KLRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY]|a[klrz]|c[aot]|d[ce]|fl|ga|hi|i[adln]|k[sy]|la|m[adeinost]|n[cdehjmvy]|o[hkr]|pa|ri|s[cd]|t[nx]|ut|v[at]|w[aivy])*$/,
                    message: 'Not a valid state',
                  },
                })}
                placeholder="State"
              />
              {errors.state && <div className="mt-2 text-xs text-red-600">{errors.state.message as string}</div>}
            </div>
            <div className="flex-1 pl-2">
              <input
                id="postalCode"
                className="input-padding focus:shadow-outline-blue block w-full appearance-none rounded-md border border-gray-300 bg-gray-50 placeholder-gray-400 shadow-sm transition duration-150 ease-in-out focus:border-blue-300 focus:outline-none sm:text-sm sm:leading-5"
                type="text"
                {...register('postalCode', {
                  required: 'Please enter a postal code',
                  pattern: {
                    value: /^\d{5}(?:[-\s]\d{4})?$/,
                    message: 'Not a valid postal code',
                  },
                })}
                placeholder="Postal code"
              />
              {errors.postalCode && (
                <div className="mt-2 text-xs text-red-600">{errors.postalCode.message as string}</div>
              )}
            </div>
          </div>

          <div className="mt-4">
            <span className="text-center">
              <Button title="Add workplace" type="submit" isLoading={isLoading} fullWidth={true} center={true} />
            </span>
            <div onClick={handleCancel} className="mt-4 cursor-pointer text-center text-sm hover:underline">
              Cancel
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AddNewWorkplace;
