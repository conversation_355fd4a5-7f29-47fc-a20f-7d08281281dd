import { useState, useEffect } from 'react';
import { useAuth } from 'hooks/useAuth';
import { getCheckCompany, generateTaxSetupComponentLink } from 'services/company';
import importScript from 'hooks/importScript';
import EditTaxSetup from '../shared/EditTaxSetup';
import SetSignerTitleModal from './SetSignerTitleModal';
import { logError } from 'utils/errorHandling';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import Button from '@/hammr-ui/components/button';
import { useQuery, useMutation } from '@tanstack/react-query';

const TaxSetup: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [companyTaxSetupLink, setCompanyTaxSetupLink] = useState(null);
  const [signerTitle, setSignerTitle] = useState(null);
  const [email, setEmail] = useState(null);
  const [signerTitleModalOpen, setSignerTitleModalOpen] = useState(false);

  importScript('https://cdn.checkhq.com/component-initialize.js');

  const company = useQuery({
    queryKey: ['company', user?.checkCompanyId],
    queryFn: () => getCheckCompany(user.checkCompanyId),
    enabled: !!user?.checkCompanyId,
  });

  useEffect(() => {
    if (company.error) {
      logError(company.error);
      toast({
        title: 'Error',
        description: 'Failed to load tax information',
        toastVariant: 'error',
      });
    }
  }, [company.error, toast]);

  const updateTaxSetup = useMutation({
    mutationFn: ({ signerTitle, email }: { signerTitle?: string; email?: string }) =>
      generateTaxSetupComponentLink(
        user?.checkCompanyId,
        user?.name,
        signerTitle || user.signer_title,
        email || user?.email
      ),
    onError: (error) => {
      logError(error);
      toast({
        title: 'Error',
        description: 'Failed to generate tax setup link',
        toastVariant: 'error',
      });
    },
  });

  const handleTaxSetup = async () => {
    if (!user.signer_title) {
      setSignerTitleModalOpen(true);
    } else {
      createCompanyTaxSetupLink();
    }
  };

  const createCompanyTaxSetupLink = async (userSignerTitle?: string, userEmail?: string) => {
    const result = await updateTaxSetup.mutateAsync({ signerTitle: userSignerTitle, email: userEmail });
    setCompanyTaxSetupLink(result.url);
  };

  useEffect(() => {
    if (signerTitle) {
      setSignerTitleModalOpen(false);
      createCompanyTaxSetupLink(signerTitle, email);
    }
  }, [signerTitle, email]);

  if (!user) return null;

  if (company.isLoading) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (company.data) {
    return (
      <>
        <section className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs">
          <div className="flex items-center justify-between">
            <h2 className="font-medium text-strong-950">Tax Setup</h2>
            <Button color="primary" onClick={handleTaxSetup} loading={company.isLoading} variant="lighter">
              Update Tax Setup
            </Button>
          </div>
          <p className="mt-5 h-fit text-sm text-sub-600">
            Configure your company&apos;s tax settings and compliance information.
          </p>
        </section>

        {companyTaxSetupLink && (
          <EditTaxSetup
            componentLink={companyTaxSetupLink}
            handleSuccess={() => {}}
            handleCancel={() => {
              setCompanyTaxSetupLink(null);
            }}
          />
        )}
        {signerTitleModalOpen && (
          <SetSignerTitleModal
            isOpen={signerTitleModalOpen}
            setIsOpen={setSignerTitleModalOpen}
            handleComplete={(signerTitle, email) => {
              setSignerTitle(signerTitle);
              setEmail(email);
            }}
          />
        )}
      </>
    );
  }

  return null;
};

export default TaxSetup;
