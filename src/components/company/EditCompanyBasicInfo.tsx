import { useEffect, useMemo, useState } from 'react';
import Spinner from 'components/icons/Spinner';
import { Controller, useForm } from 'react-hook-form';
import { useCompany } from 'hooks/useCompany';
import { useToast } from 'hooks/useToast';
import { CheckCompany, UpdateHammrOrganization } from 'interfaces/company';
import { updateHammrOrganization } from 'services/company';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Briefcase4Line from '@/hammr-icons/Briefcase4Line';
import { FormV2 } from '../elements/Form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';

const EditCompanyBasicInfo: React.FC<{
  checkCompanyId: string;
  company: CheckCompany;
  handleSuccess: any;
  editCompanyBasicInfo: boolean;
  setEditCompanyBasicInfo: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ company, handleSuccess, editCompanyBasicInfo, setEditCompanyBasicInfo }) => {
  const defaultValues = useMemo(
    () => ({
      legalName: company?.legal_name,
      tradeName: company?.trade_name,
      address: company?.address.line1,
      city: company?.address.city,
      state: company?.address.state,
      postalCode: company?.address.postal_code,
      phone: company?.phone,
    }),
    [company]
  );

  const {
    register,
    control,
    formState: { errors },
    handleSubmit,
    reset,
    setError,
  } = useForm({
    defaultValues,
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const { company: hammrOrg } = useCompany();
  const { addToast } = useToast();

  useEffect(() => {
    reset(defaultValues);
  }, [open, reset, defaultValues]);
  const onSubmit = async (data: any) => {
    setIsProcessing(true);

    const updatedHammrOrg: UpdateHammrOrganization = {
      name: data.legalName,
      tradeName: data.tradeName,
      phone: data.phone,
      address1: data.address,
      city: data.city,
      state: data.state,
      postalCode: data.postalCode,
    };

    try {
      await updateHammrOrganization(hammrOrg.id, updatedHammrOrg);

      handleSuccess();
      addToast({
        title: 'Updated Company',
        description: 'We have successfully updated your company.',
        type: 'success',
      });
    } catch (err) {
      logError(err);
      showErrorToast(err);
    } finally {
      setIsProcessing(false);
    }
  };

  if (company) {
    return (
      <Dialog open={editCompanyBasicInfo} onOpenChange={setEditCompanyBasicInfo}>
        <DialogSurface>
          <DialogHeader icon={<Briefcase4Line className="text-sub-600" />} title="Edit General Information" />
          <FormV2
            onSubmit={handleSubmit(onSubmit)}
            onCancel={() => setEditCompanyBasicInfo(false)}
            isLoading={isProcessing}
            submitText="Save"
          >
            <FormItem required error={!!errors['tradeName']}>
              <FormLabel>Trade Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter trade name"
                  {...register('tradeName', {
                    required: 'Please enter a trade name',
                  })}
                />
              </FormControl>
              <FormMessage>{errors['tradeName']?.message as string}</FormMessage>
            </FormItem>

            <FormItem required className="mt-5">
              <FormLabel>Legal Name</FormLabel>
              <FormControl>
                <Input disabled placeholder="Enter legal name" {...register('legalName')} />
              </FormControl>
            </FormItem>

            <FormItem
              required
              className="mt-5"
              error={!!errors.address || !!errors.city || !!errors.state || !!errors.postalCode}
            >
              <FormLabel>Home Address</FormLabel>
              <div className="grid grid-cols-2 gap-2">
                <fieldset className="col-span-2">
                  <Input
                    placeholder="Street Address"
                    {...register('address', { required: 'Please enter a street address.' })}
                  />
                  <FormMessage>{errors['address']?.message as string}</FormMessage>
                </fieldset>

                <fieldset className="col-span-2">
                  <Input placeholder="City" {...register('city', { required: 'Please enter a city.' })} />
                  <FormMessage>{errors['city']?.message as string}</FormMessage>
                </fieldset>

                <fieldset>
                  <Input
                    placeholder="State Code"
                    {...register('state', {
                      required: 'Please enter a state',
                      pattern: {
                        value:
                          /^(?:A[KLRZ]|C[AOT]|D[CE]|FL|GA|HI|I[ADLN]|K[SY]|LA|M[ADEINOST]|N[CDEHJMVY]|O[HKR]|PA|RI|S[CD]|T[NX]|UT|V[AT]|W[AIVY]|a[klrz]|c[aot]|d[ce]|fl|ga|hi|i[adln]|k[sy]|la|m[adeinost]|n[cdehjmvy]|o[hkr]|pa|ri|s[cd]|t[nx]|ut|v[at]|w[aivy])*$/,
                        message: 'Not a valid state',
                      },
                    })}
                  />
                  <FormMessage>{errors['state']?.message as string}</FormMessage>
                </fieldset>

                <fieldset>
                  <Input
                    placeholder="Postal Code"
                    {...register('postalCode', {
                      required: 'Please enter a postal code.',
                      pattern: { value: /^\d{5}(?:[-\s]\d{4})?$/, message: 'Not a valid postal code' },
                    })}
                  />
                  <FormMessage>{errors['postalCode']?.message as string}</FormMessage>
                </fieldset>
              </div>
            </FormItem>

            <Controller
              control={control}
              name="phone"
              render={({ field }) => (
                <FormItem required error={!!errors['phone']} className="mt-5">
                  <FormLabel>Phone number</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter phone number"
                      {...register('phone', {
                        required: 'Please enter a phone number',
                      })}
                    />
                  </FormControl>
                  <FormMessage>{errors['phone']?.message as string}</FormMessage>
                </FormItem>
              )}
            />
          </FormV2>
        </DialogSurface>
      </Dialog>
    );
  } else {
    return (
      <div>
        <div className="mt-1/5 w-full">
          <div className="mx-auto">
            <Spinner width="40" fill="blue" className="mx-auto my-10 animate-spin" />
          </div>
        </div>
      </div>
    );
  }
};

export default EditCompanyBasicInfo;
