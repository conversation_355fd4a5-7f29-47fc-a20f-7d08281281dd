import { useState, useEffect } from 'react';
import { useAuth } from 'hooks/useAuth';
import { getCheckCompany } from 'services/company';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import EditCompanyBasicInfo from './EditCompanyBasicInfo';
import { formatPhoneNumber } from 'utils/format';
import { logError, showErrorToast } from 'utils/errorHandling';
import Button from '@hammr-ui/components/button';

const CompanyBasicInfo: React.FC = () => {
  const { user } = useAuth();
  const [company, setCompany] = useState(null);
  const [editCompanyBasicInfo, setEditCompanyBasicInfo] = useState(false);

  const refresh = () => {
    getCheckCompany(user.checkCompanyId)
      .then((res) => {
        setCompany(res);
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  const handleCompanyBasicInfoSave = () => {
    setEditCompanyBasicInfo(false);
    refresh();
  };

  useEffect(() => {
    if (user.checkCompanyId && !company) {
      refresh();
    }
  }, [user.checkCompanyId]);

  if (!company) {
    return (
      <div className="mt-1/5 flex flex-col items-center">
        <LoadingIndicator />
      </div>
    );
  }

  return (
    <section className="flex max-w-[446px] flex-col gap-5 rounded-2xl border border-soft-200 p-5 shadow-xs">
      <div className="flex items-center justify-between">
        <h2 className="font-medium text-strong-950">General Information</h2>
        <Button onClick={() => setEditCompanyBasicInfo(true)} variant="link" color="primary">
          Edit
        </Button>
      </div>

      <EditCompanyBasicInfo
        editCompanyBasicInfo={editCompanyBasicInfo}
        setEditCompanyBasicInfo={setEditCompanyBasicInfo}
        checkCompanyId={user.checkCompanyId}
        company={company}
        handleSuccess={handleCompanyBasicInfoSave}
      />

      {company?.trade_name && (
        <div className="flex flex-col gap-1.5">
          <div className="text-xs text-sub-600">Trade Name</div>
          <div className="text-sm text-strong-950">{company.trade_name}</div>
        </div>
      )}

      <div className="flex flex-col gap-1.5">
        <div className="text-xs text-sub-600">Legal Name</div>
        <div className="text-sm text-strong-950">{company.legal_name}</div>
      </div>

      <div className="flex flex-col gap-1.5">
        <div className="text-xs text-sub-600">Company Address</div>
        <div className="flex flex-col gap-0.5">
          <div className="text-sm text-strong-950">{company.address.line1}</div>
          {company.address.line2 && <div className="text-sm text-strong-950">{company.address.line2}</div>}
          <div className="text-sm text-strong-950">{`${company.address.city}, ${company.address.state}`}</div>
          <div className="text-sm text-strong-950">{company.address.postal_code}</div>
        </div>
      </div>

      <div className="flex flex-col gap-1.5">
        <div className="text-xs text-sub-600">Phone Number</div>
        <div className="flex flex-row gap-1 text-sm text-strong-950">
          {company.phone ? <>{formatPhoneNumber(company.phone)}</> : '-'}
        </div>
      </div>
    </section>
  );
};

export default CompanyBasicInfo;
