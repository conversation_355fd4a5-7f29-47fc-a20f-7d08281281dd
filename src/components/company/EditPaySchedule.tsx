import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from 'utils/yupResolver';
import { useToast } from 'hooks/useToast';
import dayjs from 'dayjs';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import { FormV2 } from 'components/elements/Form';
import { FormItem, FormLabel, FormControl, FormMessage } from '@hammr-ui/components/form';
import { DateInput } from '@/hammr-ui/components/date-input';
import { PaySchedule, payFrequencyOptions } from 'interfaces/payschedule';
import { apiRequest } from 'utils/requestHelpers';
import Settings2Line from '@/hammr-icons/Settings2Line';
import ControlledSelect from '../elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';

interface FormData {
  payFrequency: string;
  firstPayday: Date | null;
  secondPayday?: Date | null;
  firstPeriodEnd: Date | null;
}

const validationSchema = yup.object().shape({
  payFrequency: yup.string().required('Pay frequency is required'),
  firstPayday: yup.date().nullable().required('First Pay Day is required').typeError('First Pay Day is required'),
  firstPeriodEnd: yup
    .date()
    .nullable()
    .required('First Period End is required')
    .typeError('First Period End is required'),
  secondPayday: yup
    .date()
    .nullable()
    .when('payFrequency', {
      is: 'semimonthly',
      then: (schema) =>
        schema
          .required('Second Pay Day is required')
          .test('different-days', 'First and second pay days must be different', function (value) {
            const { firstPayday } = this.parent;
            if (!value || !firstPayday) return true;
            return !dayjs(value).isSame(dayjs(firstPayday), 'day');
          }),
    }),
});

interface EditPayScheduleProps {
  open: boolean;
  onClose: () => void;
  paySchedule?: PaySchedule;
  handleSuccess: () => void;
}

const EditPaySchedule: React.FC<EditPayScheduleProps> = ({ open, onClose, paySchedule, handleSuccess }) => {
  const { addToast } = useToast();

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting },
    watch,
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      payFrequency: '',
      firstPayday: null,
      secondPayday: null,
      firstPeriodEnd: null,
    },
  });

  useEffect(() => {
    if (open) {
      reset({
        payFrequency: paySchedule?.payFrequency || '',
        firstPayday: paySchedule?.firstPayday ? dayjs(paySchedule.firstPayday).toDate() : null,
        secondPayday: paySchedule?.secondPayday ? dayjs(paySchedule.secondPayday).toDate() : null,
        firstPeriodEnd: paySchedule?.firstPeriodEnd ? dayjs(paySchedule.firstPeriodEnd).toDate() : null,
      });
    }
  }, [open, paySchedule, reset]);

  const payFrequency = watch('payFrequency');

  const onSubmit = async (data: FormData) => {
    try {
      const body = {
        payFrequency: data.payFrequency,
        firstPayday: data.firstPayday ? dayjs(data.firstPayday).format('YYYY-MM-DD') : null,
        firstPeriodEnd: data.firstPeriodEnd ? dayjs(data.firstPeriodEnd).format('YYYY-MM-DD') : null,
        secondPayday: null,
      };

      if (data.payFrequency === 'semimonthly') {
        body.secondPayday = data.secondPayday ? dayjs(data.secondPayday).format('YYYY-MM-DD') : null;
      } else {
        delete body.secondPayday;
      }

      await apiRequest('pay-schedule', {
        method: 'POST',
        body,
      });

      addToast({
        title: 'Updated Pay Schedule',
        description: 'We have successfully updated the pay schedule.',
        type: 'success',
      });

      handleSuccess();
    } catch (error) {
      logError(error);
      showErrorToast(error, 'Failed to update pay schedule');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogSurface>
        <DialogHeader title="Pay Period" icon={<Settings2Line className="text-sub-600" />} />
        <FormV2 onSubmit={handleSubmit(onSubmit)} onCancel={onClose} isLoading={isSubmitting} submitText="Save">
          <ControlledSelect
            control={control}
            name="payFrequency"
            label="Pay Frequency"
            className="w-full"
            error={errors.payFrequency?.message}
            placeholder="Select pay frequency"
            onChange={(value) => {
              if (value !== 'semimonthly') {
                setValue('secondPayday', null);
              }
            }}
            required
          >
            {payFrequencyOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {`${option.name} (${option.description})`}
              </SelectItem>
            ))}
          </ControlledSelect>

          <div className="mt-5">
            <FormLabel className="text-md mb-4 font-medium text-gray-800"></FormLabel>

            <FormItem error={!!errors.firstPeriodEnd} className="mt-4" required>
              <FormLabel>First Period End</FormLabel>
              <FormControl>
                <Controller
                  control={control}
                  name="firstPeriodEnd"
                  render={({ field }) => (
                    <DateInput
                      value={field.value}
                      onChange={(date) => {
                        field.onChange(date);
                      }}
                    />
                  )}
                />
              </FormControl>
              <FormMessage>{errors.firstPeriodEnd?.message}</FormMessage>
            </FormItem>

            <FormItem error={!!errors.firstPayday} className="mt-4" required>
              <FormLabel>First Pay Day</FormLabel>
              <FormControl>
                <Controller
                  control={control}
                  name="firstPayday"
                  render={({ field }) => (
                    <DateInput
                      value={field.value}
                      onChange={(date) => {
                        field.onChange(date);
                      }}
                    />
                  )}
                />
              </FormControl>
              <FormMessage>{errors.firstPayday?.message}</FormMessage>
            </FormItem>

            {payFrequency === 'semimonthly' && (
              <FormItem error={!!errors.secondPayday} className="mt-4" required>
                <FormLabel>Second Pay Day</FormLabel>
                <FormControl>
                  <Controller
                    control={control}
                    name="secondPayday"
                    render={({ field }) => (
                      <DateInput
                        value={field.value}
                        onChange={(date) => {
                          field.onChange(date);
                        }}
                      />
                    )}
                  />
                </FormControl>
                <FormMessage>{errors.secondPayday?.message}</FormMessage>
              </FormItem>
            )}
          </div>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default EditPaySchedule;
