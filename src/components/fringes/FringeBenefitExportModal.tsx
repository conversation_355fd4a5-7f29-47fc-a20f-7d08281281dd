import { useToast } from 'hooks/useToast';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { logError, showErrorToast } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { useProjects } from 'hooks/data-fetching/useProjects';
import { Project } from 'interfaces/project';
import { useAuth } from 'hooks/useAuth';
import { generateFringeStatementPdf } from 'utils/reports';
import { getFringeBenefitsReport } from 'services/fringes';
import dayjs from 'dayjs';
import { useCompany } from 'hooks/useCompany';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import ExportLine from '@/hammr-icons/ExportLine';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { DateInput } from '@/hammr-ui/components/date-input';
import { Input } from '@/hammr-ui/components/input';
import { Combobox } from '@/hammr-ui/components/combobox';

interface FringeBenefitExportModalProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  wageTableId: number;
}

const FringeBenefitExportForm = ({ control, errors, register, projects }) => {
  return (
    <>
      <FormItem required error={!!errors['projectId']}>
        <FormLabel>Project</FormLabel>
        <FormControl>
          <Controller
            rules={{ required: 'Please select a classification' }}
            control={control}
            name="projectId"
            render={({ field }) => (
              <Combobox
                aria-invalid={!!errors['projectId']}
                placeholder="Select an option"
                className="mt-1 w-full"
                items={projects.map((project) => {
                  return {
                    label: project.name + (project.projectNumber ? ` (${project.projectNumber})` : ''),
                    value: project.id.toString(),
                  };
                })}
                {...field}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['classificationId']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['weekEnding']} className="mt-5">
        <FormLabel>Week Ending</FormLabel>
        <Controller
          name="weekEnding"
          control={control}
          rules={{ required: 'Please select a week ending' }}
          render={({ field }) => (
            <FormControl>
              <DateInput
                dayPickerProps={{ disabled: { after: new Date() } }}
                {...field}
                value={field.value ? field.value : null}
              />
            </FormControl>
          )}
        />
        <FormMessage>{errors['weekEnding']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['signatoryName']} className="mt-5">
        <FormLabel>Signatory Name</FormLabel>
        <FormControl>
          <Input
            placeholder="Enter signatory name"
            {...register('signatoryName', {
              required: 'Please enter a signatory name',
            })}
          />
        </FormControl>
        <FormMessage>{errors['signatoryName']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['signatoryTitle']} className="mt-5">
        <FormLabel>Signatory Title</FormLabel>
        <FormControl>
          <Input
            placeholder="Enter signatory title"
            {...register('signatoryTitle', {
              required: 'Please enter a signatory title',
            })}
          />
        </FormControl>
        <FormMessage>{errors['signatoryTitle']?.message}</FormMessage>
      </FormItem>
    </>
  );
};

const FringeBenefitExportModal = ({ isOpen, setIsOpen, wageTableId }: FringeBenefitExportModalProps) => {
  const { addToast } = useToast();
  const {
    formState: { errors },
    handleSubmit,
    control,
    register,
    reset,
  } = useForm();
  const { user } = useAuth();
  const { company } = useCompany();
  const projects = useProjects(user.companyId, { wageTableId }) as Project[];

  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  const getFringeBenefits = async ({ wageTableId, weekEnding, projectId }) => {
    const data = await getFringeBenefitsReport({
      wageTableId,
      weekEnding,
      projectId,
    });
    return data;
  };

  const onSubmit = async (data) => {
    setIsProcessing(true);
    const selectedProject = projects.find((project) => project.id === Number(data.projectId));

    try {
      const dayWithTimezone = dayjs(data.weekEnding).tz(company.timezone);
      const weekEndingValue = dayWithTimezone.endOf('day').valueOf();

      const fringeBenefitsResponse = await getFringeBenefits({
        wageTableId,
        weekEnding: weekEndingValue,
        projectId: data.projectId,
      });
      if (fringeBenefitsResponse.wageTableData.length === 0) {
        throw Error('No employee data for the selected project and date.');
      }

      generateFringeStatementPdf({
        fileName: `${dayjs(data.weekEnding).format('YYYY-MM-DD')}_fringe-benefits-statement.pdf`,
        project: selectedProject,
        fringeBenefitsResponse,
        weekEnding: weekEndingValue,
        signatoryName: data.signatoryName,
        signatoryTitle: data.signatoryTitle,
        orgTimezone: company.timezone,
      });

      addToast({
        title: 'Exported Fringe Benefits Statement',
        description: 'Successfully exported the Fringe Benefits Statement.',
        type: 'success',
      });

      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to generate fringe statement');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<ExportLine className="text-sub-600" />} title="Export Fringe Benefits Statement" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Generate"
        >
          <FringeBenefitExportForm
            {...{
              control,
              errors,
              register,
            }}
            projects={projects}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default FringeBenefitExportModal;
