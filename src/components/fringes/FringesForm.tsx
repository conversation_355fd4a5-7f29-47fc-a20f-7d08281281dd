import Alert from '@/hammr-ui/components/Alert';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { FringeBenefitClassificationObject, FringesCategories } from 'interfaces/fringes';
import { Controller } from 'react-hook-form';
import { TextField } from '../elements/form/TextField';
import ControlledDateInput from '../elements/form/ControlledDateInput';

const FringesForm = ({
  control,
  classifications,
  errors,
  register,
  fringe,
  type = 'create',
  defaultValues = {},
  fringeBenefitName,
}: {
  type?: 'create' | 'update-name' | 'update-amount';
  control;
  classifications;
  errors;
  register;
  fringe?: FringeBenefitClassificationObject;
  defaultValues?;
  fringeBenefitName?: string;
}) => {
  if (type === 'create') {
    return <CreateFringesForm {...{ control, classifications, errors, register, defaultValues }} />;
  } else if (type === 'update-amount') {
    return (
      <UpdateAmountFringesForm
        {...{
          control,
          fringeBenefitName,
          classification: {
            name: fringe.classification.name,
            id: fringe.classification.id,
            fringePay: fringe.amount,
          },
          errors,
          register,
          defaultValues,
          fringe,
        }}
      />
    );
  } else if (type === 'update-name') {
    return (
      <UpdateNameFringesForm
        {...{
          control,
          classification: {
            name: fringe.fringeBenefit.name,
            id: fringe.fringeBenefit.id,
          },
          errors,
          register,
          defaultValues,
          fringe,
        }}
      />
    );
  }
};

const UpdateNameFringesForm = ({ errors, register, classification, control, fringe }) => {
  const FRINGE_CATEGORY_OPTIONS: { label: string; value: FringesCategories }[] = [
    {
      label: 'Health and Welfare',
      value: 'HEALTH_AND_WELFARE',
    },
    {
      label: 'Pension',
      value: 'PENSION',
    },
    {
      label: 'Training',
      value: 'TRAINING_FUND_CONTRIBUTION',
    },
    {
      label: 'Vacation',
      value: 'VACATION',
    },
    {
      label: 'Other',
      value: 'OTHER',
    },
  ];

  return (
    <div className="flex flex-col gap-y-4">
      <section>
        <TextField
          label="Fringe name"
          type="text"
          className="w-full"
          error={Boolean(errors.name)}
          name="name"
          control={control}
          required
        />
      </section>

      <section>
        <FormItem required error={!!errors['category']}>
          <FormLabel>Category</FormLabel>
          <FormControl>
            <Controller
              rules={{ required: 'Please select a classification' }}
              control={control}
              name="category"
              render={({ field }) => (
                <DropdownPicker
                  aria-invalid={!!errors['category']}
                  placeholder="Select an option"
                  className="mt-1 w-full"
                  items={FRINGE_CATEGORY_OPTIONS.map((options) => {
                    return {
                      label: options.label,
                      value: options.value,
                    };
                  })}
                  {...field}
                />
              )}
            />
          </FormControl>
          <FormMessage>{errors['category']?.message}</FormMessage>
        </FormItem>
      </section>
    </div>
  );
};

const UpdateAmountFringesForm = ({ errors, register, classification, fringeBenefitName, control, fringe }) => {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="text-xs text-sub-600">Fringe Benefit</div>
          <div className="mt-2 text-sm">{fringeBenefitName}</div>
        </div>

        <div>
          <div className="text-xs text-sub-600">Classification</div>
          <div className="mt-2 text-sm">{classification.name}</div>
        </div>
      </div>

      {/* Divider */}
      <hr className="border-soft-200" />

      {/* Amount field */}
      <FormItem required error={!!errors['amount']}>
        <FormLabel>Amount</FormLabel>
        <FormControl>
          <Input
            type="number"
            step="any"
            beforeContent="$"
            placeholder="0.00"
            {...register('amount', {
              required: 'Please enter an amount',
              min: { value: 0, message: 'Enter a value 0 or above ' },
            })}
          />
        </FormControl>
        <FormMessage>{errors['amount']?.message}</FormMessage>
      </FormItem>

      {/* Effective Date field */}
      <ControlledDateInput
        rules={{ required: 'Please enter an effective date' }}
        required
        control={control}
        name="effectiveDate"
        label="Effective Date"
      />
    </div>
  );
};

const CreateFringesForm = ({ errors, register, control, classifications }) => {
  const FRINGE_CATEGORY_OPTIONS: { label: string; value: FringesCategories }[] = [
    {
      label: 'Health and Welfare',
      value: 'HEALTH_AND_WELFARE',
    },
    {
      label: 'Pension',
      value: 'PENSION',
    },
    {
      label: 'Training',
      value: 'TRAINING_FUND_CONTRIBUTION',
    },
    {
      label: 'Vacation',
      value: 'VACATION',
    },
    {
      label: 'Other',
      value: 'OTHER',
    },
  ];

  return (
    <>
      <FormItem required error={!!errors['name']}>
        <FormLabel>Fringe Name</FormLabel>
        <FormControl>
          <Input
            placeholder="Enter fringe name"
            {...register('name', {
              required: 'Please enter a fringe name',
            })}
          />
        </FormControl>
        <FormMessage>{errors['name']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['classificationId']} className="mt-5">
        <FormLabel>Category</FormLabel>
        <FormControl>
          <Controller
            rules={{ required: 'Please select a classification' }}
            control={control}
            name="category"
            render={({ field }) => (
              <DropdownPicker
                aria-invalid={!!errors['category']}
                placeholder="Select an option"
                className="mt-1 w-full"
                items={FRINGE_CATEGORY_OPTIONS.map((options) => {
                  return {
                    label: options.label,
                    value: options.value,
                  };
                })}
                {...field}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['category']?.message}</FormMessage>
      </FormItem>

      <ControlledDateInput required control={control} name="effectiveDate" label="Effective Date" className="mt-5" />

      {classifications.length > 0 && (
        <>
          <hr className="mt-5 border-soft-200" />

          <legend className="mt-5 font-medium text-strong-950">Fringe Amounts</legend>

          <div className="mt-5 flex flex-col gap-y-5">
            {classifications.map((classification) => {
              return (
                <FormItem required error={!!errors[classification.id]} key={classification.id}>
                  <FormLabel>{classification.name}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="any"
                      beforeContent="$"
                      placeholder="0.00"
                      {...register(String(classification.id), {
                        required: 'Please enter a value of 0 or above',
                        min: { value: 0, message: 'Enter a value 0 or above ' },
                      })}
                    />
                  </FormControl>
                  <FormMessage>{errors[classification.id]?.message}</FormMessage>
                </FormItem>
              );
            })}
          </div>
        </>
      )}
    </>
  );
};

export default FringesForm;
