import { ColDef, ICellRendererParams, RowClickedEvent, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Tooltip } from '@hammr-ui/components/tooltip';
import UpdateFringeModal from './UpdateFringeModal';
import FringeBenefitHistoryModal from './FringeBenefitHistoryModal';
import { FringeBenefitClassificationObject } from 'interfaces/fringes';
import { formatLocaleUsa, isItemActive } from 'utils/dateHelper';
import CompactButton from '@/hammr-ui/components/CompactButton';
import PencilLine from '@/hammr-icons/PencilLine';
import HistoryLine from '@/hammr-icons/HistoryLine';

const FringesTable = (props: {
  wageTableId: number;
  classifications;
  fringes: FringeBenefitClassificationObject[];
  fetchFringes;
}) => {
  const { classifications, fringes, fetchFringes } = props;
  const [selectedFringesRow, setSelectedFringesRow] = useState<FringeBenefitClassificationObject>(undefined);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [updateType, setUpdateType] = useState<'update-amount' | 'update-name'>(undefined);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const gridApi = useRef(null);

  const mappedFringesData = useMemo(() => {
    const allFringeClassificationNames = new Map<string, number>();

    fringes.forEach((fringe) => {
      if (!allFringeClassificationNames.has(fringe.fringeBenefit.name)) {
        allFringeClassificationNames.set(fringe.fringeBenefit.name, 1);
      } else {
        allFringeClassificationNames.set(
          fringe.fringeBenefit.name,
          allFringeClassificationNames.get(fringe.fringeBenefit.name) + 1
        );
      }
    });

    const uniqueFringesData = Array.from(allFringeClassificationNames.entries()).map(([name, count]) => {
      // if only one FringeBenefitClassification then look it up and return it
      if (count === 1) {
        return fringes.find((fringe) => fringe.fringeBenefit.name === name);
      }

      const uniqueClassifications = new Set<string>();
      fringes.forEach((fringe) => {
        if (fringe.fringeBenefit.name === name) {
          uniqueClassifications.add(fringe.classification.name);
        }
      });

      const uniqueClassificationsArray = Array.from(uniqueClassifications);

      return uniqueClassificationsArray.map((classificationName) => {
        // if more than one FringeBenefitClassification then return the first one
        const activeFringe = fringes.find((fringe) => {
          const startDate = fringe?.startDate;
          const endDate = fringe?.endDate;
          return (
            fringe.fringeBenefit.name === name &&
            fringe.classification.name === classificationName &&
            isItemActive(
              startDate ? new Date(startDate).toString() : null,
              endDate ? new Date(endDate).toString() : null
            )
          );
        });

        if (activeFringe) {
          return activeFringe;
        }

        // if no active FringeBenefitClassification then return the one with the earliest start date
        return fringes
          .filter((fringe) => fringe.fringeBenefit.name === name && fringe.classification.name === classificationName)
          .sort((a, b) => {
            const aStartDate = a.startDate ? a.startDate : null;
            const bStartDate = b.startDate ? b.startDate : null;
            return aStartDate - bStartDate;
          })[0];
      });
    });

    const uniqueFringesDataFlat = uniqueFringesData.flat();

    return uniqueFringesDataFlat.map((fringe) => {
      const startDate = fringe.startDate ? fringe.startDate : null;
      const endDate = fringe.endDate ? fringe.endDate : null;

      return {
        ...fringe,
        classificationName: fringe.classification ? fringe.classification.name : '',
        amount: fringe.amount,
        startDate: startDate,
        endDate: endDate,
        name: fringe.fringeBenefit.name,
        id: fringe.id,
        benefitId: fringe.fringeBenefitId,
      };
    });
  }, [fringes]);

  const wageTableWithoutClassification = mappedFringesData.some((fringe) => !fringe.classificationName);

  useEffect(() => {
    fetchFringes();
  }, []);

  const getFringe = (id) => {
    return mappedFringesData.find((fringe) => fringe.fringeBenefitId === id);
  };

  const colDefs: ColDef[] = [
    {
      headerName: 'Fringe Name',
      field: 'name',
      initialSort: 'asc',
      rowGroup: true,
      minWidth: 200,
      cellRenderer: (params: ValueFormatterParams) => {
        if (params.node.parent.displayed) {
          return null;
        }
        return params.value;
      },
    },
    {
      headerName: 'Classification',
      minWidth: 250,
      field: 'classificationName',
      sort: 'asc',
      sortable: false,
      hide: wageTableWithoutClassification,
    },
    {
      headerName: 'Amount',
      field: 'amount',
      sortable: false,
      hide: wageTableWithoutClassification,
      cellRenderer: (params: ValueFormatterParams) => {
        return <div>${params.value}/hour</div>;
      },
    },
    {
      headerName: 'Start',
      field: 'startDate',
      sortable: false,
      cellRenderer: (params: ValueFormatterParams) => {
        if (!params.value) return null;
        return formatLocaleUsa(params.value as Date);
      },
      cellClass: 'text-sub-600',
      hide: wageTableWithoutClassification,
    },
    {
      headerName: '',
      field: 'actions',
      cellStyle: { overflow: 'visible' },
      maxWidth: 150,
      sortable: false,
      pinned: 'right',
      hide: wageTableWithoutClassification,
      cellRenderer: (params: ValueFormatterParams) => {
        return (
          <div className="flex h-full items-center justify-end gap-1 p-1">
            <Tooltip content="History">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedFringesRow(params.data);
                  setIsHistoryModalOpen(true);
                }}
              >
                <HistoryLine />
              </CompactButton>
            </Tooltip>
            <Tooltip content="Edit">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedFringesRow(params.data);
                  setIsUpdateModalOpen(true);
                  setUpdateType('update-amount');
                }}
              >
                <PencilLine />
              </CompactButton>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const groupRenderer = (params: ICellRendererParams) => {
    return (
      <div className="flex items-center justify-between">
        <div>{params.value}</div>
        <Tooltip content="Edit">
          <CompactButton size="large">
            <PencilLine />
          </CompactButton>
        </Tooltip>
      </div>
    );
  };

  const onRowClicked = (params: RowClickedEvent) => {
    if (params.node.group) {
      const fringeId = params.node.allLeafChildren[0].data.fringeBenefitId;
      const fringe = getFringe(parseInt(fringeId));
      setSelectedFringesRow(fringe);
      setIsUpdateModalOpen(true);
      setUpdateType('update-name');
      return;
    }

    setSelectedFringesRow(params.data);
    setIsUpdateModalOpen(true);
    setUpdateType('update-amount');
  };

  return (
    <div className="max-w-6xl">
      <UpdatedTable
        key={wageTableWithoutClassification ? 'wageTableWithoutClassification' : 'wageTableWithClassification'}
        colDefs={colDefs}
        rowData={mappedFringesData}
        hideSidebar={true}
        emptyRowsText={`No project fringes created. Click the "Create Project Fringe" button to get started.`}
        showRowGroupPanel={false}
        parentRef={gridApi}
        groupRenderer={groupRenderer}
        onRowClicked={wageTableWithoutClassification ? undefined : onRowClicked}
        gridOptions={{
          groupRemoveSingleChildren: wageTableWithoutClassification ? true : false,
        }}
      />
      <UpdateFringeModal
        isOpen={isUpdateModalOpen}
        setIsOpen={setIsUpdateModalOpen}
        fringe={selectedFringesRow}
        classifications={classifications}
        callback={fetchFringes}
        type={updateType}
      />
      <FringeBenefitHistoryModal
        open={isHistoryModalOpen}
        setOpen={setIsHistoryModalOpen}
        fringe={selectedFringesRow}
        allFringes={fringes}
      />
    </div>
  );
};

export default FringesTable;
