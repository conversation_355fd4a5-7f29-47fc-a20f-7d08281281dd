import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { FringeBenefitClassificationObject } from 'interfaces/fringes';
import { Classification } from 'interfaces/classifications';
import FringesForm from './FringesForm';
import { updateClassificationFringe, updateFringe } from 'services/fringes';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import PencilLine from '@/hammr-icons/PencilLine';
import dayjs from 'dayjs';

interface UpdateFringeModalProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  callback: () => void;
  fringe: FringeBenefitClassificationObject;
  classifications: Classification;
  type: 'update-amount' | 'update-name';
}

export default function UpdateFringeModal({
  isOpen,
  setIsOpen,
  callback,
  fringe,
  classifications,
  type,
}: UpdateFringeModalProps) {
  const { addToast } = useToast();
  const {
    formState: { errors },
    handleSubmit,
    control,
    register,
    reset,
  } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!isOpen || !fringe) return;

    reset({
      name: fringe.fringeBenefit.name,
      amount: null,
      category: fringe.fringeBenefit.category,
      effectiveDate: null,
    });
  }, [isOpen]);

  const onSubmit = async (data) => {
    let endpointRequest: any = () => 0;
    if (type === 'update-amount') {
      endpointRequest = () =>
        updateClassificationFringe(fringe.id, {
          amount: parseFloat(data.amount).toFixed(2),
          startDate: dayjs(data.effectiveDate).format('YYYY-MM-DD'),
        });
    } else {
      endpointRequest = () =>
        updateFringe(fringe.fringeBenefitId, {
          name: data.name,
          category: data.category,
        });
    }
    setIsProcessing(true);

    try {
      await endpointRequest();

      addToast({
        title: 'Updated Fringe',
        description: `We have successfully updated your fringe`,
        type: 'success',
      });

      callback();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update fringe');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<PencilLine className="text-sub-600" />} title="Update Fringe Benefit" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Save"
        >
          <FringesForm
            fringeBenefitName={fringe.fringeBenefit.name}
            {...{
              control,
              classifications,
              errors,
              register,
              fringe,
              type,
            }}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
