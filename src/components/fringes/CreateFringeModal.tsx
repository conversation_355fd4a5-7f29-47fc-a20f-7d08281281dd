import { FormV2 } from 'components/elements/Form';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import { createFringe } from 'services/fringes';
import FringesForm from './FringesForm';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Table2Line from '@/hammr-icons/Table2Line';
import dayjs from 'dayjs';

const CreateFringeModal = ({ isOpen, setIsOpen, callback, classifications, wageTableId }) => {
  const { addToast } = useToast();
  const {
    formState: { errors },
    handleSubmit,
    control,
    register,
    reset,
  } = useForm();
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen]);

  const onSubmit = async (data) => {
    const { name, category, effectiveDate, ...amounts } = data;

    setIsProcessing(true);

    try {
      const fringeBenefits = Object.keys(amounts).map((classificationId) => {
        let amount = amounts[classificationId];

        if (typeof amount !== 'string' || amount === '') {
          amount = 0;
        }

        return {
          classificationId: parseInt(classificationId),
          amount: parseFloat(amount).toFixed(2),
          startDate: dayjs(effectiveDate).format('YYYY-MM-DD'),
        };
      });

      const fringe = {
        name,
        category,
        wageTableId,
        fringeBenefitClassifications: fringeBenefits,
      };

      await createFringe(fringe);

      addToast({
        title: 'Created Fringe Benefit',
        description: (
          <>
            Successfully created the project fringe <strong>{fringe.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create project fringe');
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setIsProcessing(false);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Create Fringe Benefit" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Create"
        >
          <FringesForm
            {...{
              control,
              classifications,
              errors,
              register,
            }}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default CreateFringeModal;
