import { FringeBenefitClassificationObject } from 'interfaces/fringes';
import { Fragment, useMemo } from 'react';
import dayjs from 'dayjs';
import { ModalV2 } from '@/components/elements/ModalV2';
import { useCompany } from '@/hooks/useCompany';
import { RiHistoryLine } from '@remixicon/react';

interface FringeBenefitHistoryModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  fringe: FringeBenefitClassificationObject | undefined;
  allFringes: FringeBenefitClassificationObject[];
}

const FringeBenefitHistoryModal = ({ open, setOpen, fringe, allFringes }: FringeBenefitHistoryModalProps) => {
  const { company } = useCompany();
  const fringeHistory = useMemo(() => {
    if (!fringe) return [];

    return allFringes
      .filter((f) => f.fringeBenefitId === fringe.fringeBenefitId && f.classificationId === fringe.classificationId)
      .toSorted((a, b) => {
        if (!a.endDate && !b.endDate) {
          return 0;
        }
        if (!a.endDate) {
          return -1;
        }
        if (!b.endDate) {
          return 1;
        }

        return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
      });
  }, [fringe, allFringes]);

  const formatDateRange = (startDate: number, endDate: number, isActive: boolean) => {
    const start = startDate ? dayjs(startDate).format('MMM D, YYYY') : '';
    const endWord = endDate
      ? dayjs(endDate).format('MMM D, YYYY')
      : dayjs(startDate).isAfter(dayjs())
        ? 'Onward'
        : 'Today';

    if (start === endWord) {
      return start;
    }

    return `${start} - ${endWord}`;
  };

  if (!fringe) return null;

  const fringeBenefitName = fringe.fringeBenefit.name;
  const classificationName = fringe.classification.name;

  return (
    <ModalV2
      open={open}
      setOpen={setOpen}
      title="Fringe Benefit History"
      icon={<RiHistoryLine className="text-primary h-5 w-5" />}
    >
      <div className="p-5 pb-0">
        <div className="flex items-start justify-between">
          <div>
            <div className="text-xs text-sub-600">Fringe Benefit</div>
            <div className="mt-1 text-sm text-strong-950">{fringeBenefitName}</div>
          </div>
          <div className="mr-10 flex flex-col items-start">
            <div className="text-xs text-sub-600">Classification</div>
            <div className="mt-1 text-sm text-strong-950">{classificationName}</div>
          </div>
        </div>
      </div>

      <div className="mt-5 border-t border-soft-200"></div>

      <div className="space-y-4 px-5">
        <div>
          {fringeHistory.map((historyItem, index) => {
            const areDatesBetweenToday =
              dayjs(historyItem.startDate).isAfter(dayjs.tz(new Date(), company?.timezone)) &&
              dayjs(historyItem.endDate).isBefore(dayjs.tz(new Date(), company?.timezone));
            const isActive = !historyItem.endDate || areDatesBetweenToday;

            return (
              <Fragment key={`${historyItem.id}-${index}`}>
                {index > 0 && <div className="border-t border-soft-200"></div>}
                <div key={`${historyItem.id}-${index}`} className="py-5">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-10">
                      <div>
                        <div className="text-sm font-medium text-strong-950">${historyItem.amount}/hour</div>
                        <div className="mt-0.5 text-xs text-sub-600">Amount</div>
                      </div>
                    </div>

                    <div className="text-sm text-strong-950">
                      {formatDateRange(historyItem.startDate, historyItem.endDate, isActive)}
                    </div>
                  </div>
                </div>
              </Fragment>
            );
          })}
        </div>
      </div>
    </ModalV2>
  );
};

export default FringeBenefitHistoryModal;
