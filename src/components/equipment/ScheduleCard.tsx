import Button from '@/hammr-ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import { Tabs, TabItem, TabList, TabContent } from '@/hammr-ui/components/tabs';
import { RiAddLine } from '@remixicon/react';
import Link from 'next/link';
import { useMemo, useState } from 'react';
import { MappedScheduleEvent } from '@/interfaces/equipment';
import ScheduleList from './ScheduleList';
import EmptyStateScheduleMeeting from '@/hammr-icons/EmptyStateScheduleMeeting';

export default function ScheduleCard({
  mappedSchedules = [],
  equipmentId,
}: {
  mappedSchedules?: MappedScheduleEvent[];
  equipmentId: string;
}) {
  const [activeTab, setActiveTab] = useState('upcoming');

  const now = new Date();

  const upcomingSchedules = useMemo(
    () => mappedSchedules?.filter((schedule) => new Date(schedule.startTime) > now) || [],
    [mappedSchedules, now]
  );

  const pastSchedules = useMemo(
    () => mappedSchedules?.filter((schedule) => new Date(schedule.startTime) <= now) || [],
    [mappedSchedules, now]
  );

  return (
    <Card className="flex flex-col">
      <CardHeader className="p-4">
        <CardTitle className="flex items-center justify-between">
          <div>Schedule</div>
          <Link href={`/scheduling?modal=add&equipmentId=${equipmentId}`}>
            <Button size="x-small" variant="outline" color="neutral" beforeContent={<RiAddLine />}>
              Add Event
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex grow justify-center p-4 pt-0">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabList className="grid w-full grid-cols-2">
            <TabItem value="upcoming">Upcoming</TabItem>
            <TabItem value="past">Past</TabItem>
          </TabList>
          <TabContent value="upcoming">
            {upcomingSchedules.length > 0 ? (
              <ScheduleList schedules={upcomingSchedules} />
            ) : (
              <div className="mt-5 flex flex-col items-center gap-5">
                <EmptyStateScheduleMeeting />
                <p className="h-fit text-center text-sm text-soft-400">
                  There is no upcoming schedule event for this equipment.
                </p>
              </div>
            )}
          </TabContent>
          <TabContent value="past">
            {pastSchedules.length > 0 ? (
              <ScheduleList schedules={pastSchedules} />
            ) : (
              <div className="mt-5 flex flex-col items-center gap-5">
                <EmptyStateScheduleMeeting />
                <p className="h-fit text-center text-sm text-soft-400">
                  There is no past schedule event for this equipment.
                </p>
              </div>
            )}
          </TabContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
