import { useEffect, useRef, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import { formatUSD } from '@/utils/format';
import Item from 'components/shared/DetailsItem';
import { <PERSON><PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import EmptyStateSpendingSummary from '@/hammr-icons/EmptyStateSpendingSummary';
import { UpdateEquipmentModal } from './UpdateEquipmentModal';
import { Equipment } from '@/interfaces/equipment';
import { useQuery } from '@tanstack/react-query';
import equipmentCategories from '@/services/equipment-categories';
import { AgGridReact } from '@ag-grid-community/react';
import { useRouter } from 'next/router';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import { apiRequest } from '@/utils/requestHelpers';
import { UserTimesheet } from '@/interfaces/timesheet';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import EmptyStateFinanceBanking from '@/hammr-icons/EmptyStateFinanceBanking';
import { BarChart } from '../charts';
import ScheduleCard from './ScheduleCard';
import Timesheets from '../timesheets/Timesheets';
import { useTimesheetListStore } from '@/components/timesheets/store';
import { cn } from '@/hammr-ui/lib/cn';
import EquipmentTotalUsageBreakdown from './EquipmentTotalUsageBreakdown';

interface Props {
  equipment: Equipment;
  showEditModal: boolean;
  setShowEditModal: (value: boolean) => void;
  activeTab?: string;
  setActiveTab?: (value: string) => void;
  isExportingRef?: React.MutableRefObject<boolean>;
  showTimesheetModal?: boolean;
  setShowTimesheetModal?: (value: boolean) => void;
  gridRef?: React.MutableRefObject<AgGridReact>;
}

export default function EquipmentDetails({
  equipment,
  showEditModal,
  setShowEditModal,
  activeTab: externalActiveTab,
  setActiveTab: externalSetActiveTab,
  isExportingRef,
  showTimesheetModal,
  setShowTimesheetModal,
  gridRef,
}: Props) {
  const categories = useQuery({
    queryKey: ['equipment', 'equipment-categories'],
    queryFn: () => equipmentCategories.getAll(),
  });

  const router = useRouter();

  // Use external tab state if provided, otherwise use local state
  const [localActiveTab, setLocalActiveTab] = useState<string>((router.query.tab as string) || 'general');
  const activeTab = externalActiveTab || localActiveTab;
  const setActiveTab = externalSetActiveTab || setLocalActiveTab;

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: value },
      },
      undefined,
      { shallow: true }
    );
  };

  const mappedSchedules = equipment?.equipmentScheduleEvents?.map((equipmentScheduleEvent) => ({
    ...equipmentScheduleEvent.scheduleEvent,
    equipmentId: equipmentScheduleEvent.equipmentId,
    projectName: equipmentScheduleEvent.scheduleEvent.project?.name,
    projectAddress: equipmentScheduleEvent.scheduleEvent.project?.address,
  }));

  return (
    <Tabs className="flex flex-grow flex-col" value={activeTab} onValueChange={handleTabChange}>
      <TabsList>
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="timesheets">Timesheets</TabsTrigger>
      </TabsList>

      <TabsContent
        value="general"
        className="max-w-6xl grid-cols-1 gap-6 data-[state=active]:grid xl:grid-cols-[352px_1fr]"
      >
        <GeneralInformationCard equipment={equipment} setShowEditModal={setShowEditModal} />
        <WeeklyUsageHistoryCard equipmentId={equipment.id} />
        <ScheduleCard mappedSchedules={mappedSchedules} equipmentId={equipment.id.toString()} />
        <EquipmentTotalUsageBreakdown equipment={equipment} />
      </TabsContent>

      <TabsContent value="timesheets" className="data-[state=active]:flex data-[state=active]:grow">
        <TimesheetsTabContent
          equipment={equipment}
          isExportingRef={isExportingRef}
          showTimesheetModal={showTimesheetModal}
          setShowTimesheetModal={setShowTimesheetModal}
          gridRef={gridRef}
        />
      </TabsContent>

      {equipment && (
        <UpdateEquipmentModal
          open={showEditModal}
          setOpen={setShowEditModal}
          equipment={equipment}
          categories={
            categories.data
              ? categories.data.map((category) => ({
                  label: category.name,
                  value: category.name,
                }))
              : []
          }
        />
      )}
    </Tabs>
  );
}

function GeneralInformationCard({ equipment, setShowEditModal }: Omit<Props, 'showEditModal'>) {
  return (
    <Card className="pb-5">
      <CardHeader>
        <CardTitle>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div>General Information</div>
            </div>
            <LinkButton size="medium" style="primary" onClick={() => setShowEditModal(true)}>
              Edit
            </LinkButton>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="px-5 py-0">
        <div className="space-y-4">
          <Item title="Equipment">{equipment?.name}</Item>
          <Item title="Category" className="capitalize">
            {equipment?.category.name}
          </Item>
          <Item title="Year">{equipment?.year}</Item>
          <Item title="Hourly Cost">
            {equipment?.hourlyCost ? formatUSD.format(Number(equipment.hourlyCost)) : '—'}
          </Item>
        </div>
      </CardContent>
    </Card>
  );
}

const options = [
  { label: 'Last 3 Months', days: 90 },
  { label: 'Last 6 Months', days: 180 },
  { label: 'Last 12 Months', days: 365 },
];

function WeeklyUsageHistoryCard({ equipmentId }: { equipmentId: number }) {
  const [chartSize, setChartSize] = useState(options[0]);

  const timesheetsQuery = useQuery({
    queryKey: ['timesheets', chartSize.days],
    async queryFn() {
      const res = await apiRequest<{ timesheets: UserTimesheet[] }>('timesheets', { urlParams: { equipmentId } });

      // Process timesheets into weekly buckets
      const timesheets = res.timesheets;
      if (!timesheets || timesheets.length === 0) return [];

      // Get the current date and find the start of the current week (Monday)
      const now = new Date();
      const currentWeekStart = new Date(now);
      currentWeekStart.setDate(now.getDate() - now.getDay() + (now.getDay() === 0 ? -6 : 1)); // Adjust to Monday
      currentWeekStart.setHours(0, 0, 0, 0);

      // Create weekly buckets starting from the current week and going backward
      const weeklyData = [];
      const weekCount = Math.ceil(chartSize.days / 7);

      for (let i = 0; i < weekCount; i++) {
        // Calculate the start and end of this week
        const weekStart = new Date(currentWeekStart);
        weekStart.setDate(currentWeekStart.getDate() - 7 * i);

        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);

        // Filter timesheets for this week
        const weeklyTimesheets = timesheets.filter((timesheet) => {
          const timesheetDate = new Date(timesheet.clockIn);
          return timesheetDate >= weekStart && timesheetDate <= weekEnd;
        });

        // Calculate total amount for this week
        const totalAmount = weeklyTimesheets.reduce((sum, timesheet) => {
          if (timesheet.equipment && timesheet.equipment.hourlyCost) {
            const hours = timesheet.totalMinutes / 60;
            return sum + timesheet.equipment.hourlyCost * hours;
          }
          return sum;
        }, 0);

        // Format date range as the period label
        const formattedStartDate = weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        const formattedEndDate = weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        const periodLabel = `${formattedStartDate} - ${formattedEndDate}`;

        weeklyData.push({
          period: periodLabel,
          amount: totalAmount,
          weekStart,
          weekEnd,
        });
      }

      // Reverse the order so oldest week appears first and current week appears last
      return weeklyData.reverse();
    },
  });

  return (
    <Card className="flex flex-col">
      <CardHeader className="p-4">
        <CardTitle className="flex items-center justify-between">
          Weekly Usage History
          <Select
            value={chartSize.days.toString()}
            onChange={(value) => setChartSize(options.find((o) => o.days === Number(value)))}
            className="h-[32px] w-fit text-sub-600"
            disabled={timesheetsQuery.isPending}
          >
            {options.map((size) => (
              <SelectItem key={size.days} value={size.days.toString()}>
                {size.label}
              </SelectItem>
            ))}
          </Select>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex grow items-center justify-center gap-2 p-4 pt-0">
        {timesheetsQuery.isPending ? (
          <LoadingIndicator />
        ) : timesheetsQuery.isError ? (
          <p className="text-error-base">An error occured when fetching the usage history.</p>
        ) : timesheetsQuery.data.length === 0 ? (
          <div className="flex flex-col items-center gap-5">
            <EmptyStateFinanceBanking className="size-[108px]" />
            <p className="h-fit text-sm text-soft-400">You do not have any usage data yet.</p>
          </div>
        ) : (
          <BarChart mappedChartData={timesheetsQuery.data} />
        )}
      </CardContent>
    </Card>
  );
}

interface TimesheetsTabContentProps {
  equipment: Equipment;
  isExportingRef?: React.MutableRefObject<boolean>;
  showTimesheetModal?: boolean;
  setShowTimesheetModal?: (value: boolean) => void;
  gridRef?: React.MutableRefObject<AgGridReact>;
}

function TimesheetsTabContent({
  equipment,
  isExportingRef: externalExportingRef,
  showTimesheetModal: externalShowTimesheetModal,
  setShowTimesheetModal: externalSetShowTimesheetModal,
  gridRef: externalGridRef,
}: TimesheetsTabContentProps) {
  // Use external refs and state if provided, otherwise use local ones
  const localExportingRef = useRef<boolean>(false);
  const [localShowTimesheetModal, setLocalShowTimesheetModal] = useState(false);
  const localGridRef = useRef<AgGridReact>(null);

  const isExportingRef = externalExportingRef || localExportingRef;
  const showTimesheetModal =
    externalShowTimesheetModal !== undefined ? externalShowTimesheetModal : localShowTimesheetModal;
  const setShowTimesheetModal = externalSetShowTimesheetModal || setLocalShowTimesheetModal;
  const gridRef = externalGridRef || localGridRef;

  useEffect(() => {
    useTimesheetListStore.setState((state) => ({
      ...state,
      selectedEmployeeIds: [],
      selectedEquipmentIds: [equipment.id],
    }));
  }, [equipment.id]);

  return (
    <>
      <div
        className={cn('flex flex-grow flex-col items-center justify-center gap-4 text-center', {
          hidden: equipment.timesheets.length > 0,
        })}
      >
        <EmptyStateSpendingSummary />
        <p className="text-sm text-soft-400">This equipment doesn&apos;t have any associated timesheets yet.</p>
      </div>
      <div className={cn('flex flex-grow', { hidden: equipment.timesheets.length === 0 })}>
        <Timesheets
          isExportingRef={isExportingRef}
          showTimesheetModal={showTimesheetModal}
          setShowTimesheetModal={setShowTimesheetModal}
          gridRef={gridRef}
          selectedEquipmentId={equipment.id}
        />
      </div>
    </>
  );
}
