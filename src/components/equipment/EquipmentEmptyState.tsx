import { Button } from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import EmptyStateSpendingSummary from '@/hammr-icons/EmptyStateSpendingSummary';

type EquipmentEmptyStateProps = {
  isArchived: boolean;
  onAddEquipment: () => void;
};

const EquipmentEmptyState = ({ isArchived, onAddEquipment }: EquipmentEmptyStateProps) => {
  return (
    <div className="mt-15 flex flex-col items-center justify-center py-10">
      <EmptyStateSpendingSummary className="mb-4" />
      <p className="mb-4 text-center text-sm text-soft-400">
        {isArchived ? (
          'There are no archived equipments yet.'
        ) : (
          <>
            There is no equipment tracked yet.
            <br />
            Click the button below to add one.
          </>
        )}
      </p>
      {!isArchived && (
        <Button beforeContent={<AddLine />} onClick={onAddEquipment}>
          Add Equipment
        </Button>
      )}
    </div>
  );
};

export default EquipmentEmptyState;
