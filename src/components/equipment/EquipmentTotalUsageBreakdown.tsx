import { <PERSON>, CardTitle, <PERSON>Header, CardContent } from '@/hammr-ui/components/card';
import EmptyStateFinanceBanking from '@/hammr-icons/EmptyStateFinanceBanking';
import { useQuery } from '@tanstack/react-query';
import { getTimesheet } from '@/services/timesheet';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { UpdatedTable } from '../shared/UpdatedTable';
import { ColDef } from '@ag-grid-community/core';
import { formatMinutesToHoursWorked } from '@/utils/format';
import { amountFormatterCellRenderer } from '../payroll/utils';
import { DatePeriodSelectorImproved } from '../timesheets/DatePeriodSelectorImproved';
import { useState } from 'react';
import { Equipment } from '@/interfaces/equipment';

const EquipmentTotalUsageBreakdown = ({ equipment }: { equipment: Equipment }) => {
  const equipmentId = equipment.id;
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isAllTime, setIsAllTime] = useState<boolean>(true);

  const allTimeEndDate = new Date();
  const allTimeStartDate = new Date(equipment.createdAt);

  const { data, isPending } = useQuery({
    queryKey: ['equipment-usage-data', equipmentId, startDate, endDate, isAllTime],
    queryFn: () => {
      const params: Record<string, any> = {
        equipmentId: equipmentId.toString(),
      };

      // Only add date parameters if not in All Time mode
      if (!isAllTime && startDate && endDate) {
        params.from = startDate.getTime();
        params.to = endDate.getTime();
      }

      return getTimesheet(params);
    },
  });

  // project - cost - hours
  const columns: ColDef[] = [
    {
      headerName: 'Project',
      field: 'project',
      rowGroup: true,
      cellRenderer: (params) => {
        if (params.value === undefined) {
          return params.node.key;
        }
        return params.value;
      },
      initialSort: 'asc',
    },
    {
      headerName: 'Cost',
      field: 'cost',
      valueFormatter: (params) => amountFormatterCellRenderer('cost', params, 'currency'),
    },
    {
      headerName: 'Hours',
      field: 'minutes',
      valueFormatter: (params) => amountFormatterCellRenderer('minutes', params, 'number'),
      cellRenderer: (params) => {
        return formatMinutesToHoursWorked(params.valueFormatted);
      },
    },
  ];

  const mappedRowData = data?.timesheets.map((timesheet) => {
    const totalHours = timesheet.totalMinutes / 60;
    const totalCost = parseFloat((totalHours * parseFloat(equipment.hourlyCost)).toFixed(2));

    return {
      project: timesheet.project.name,
      cost: totalCost,
      minutes: timesheet.totalMinutes,
    };
  });

  return (
    <Card className="pb-5">
      <CardHeader>
        <CardTitle>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div>Total Usage Breakdown</div>
            </div>
            <div className="flex items-center">
              <DatePeriodSelectorImproved
                onDateChange={(start, end) => {
                  // Check if both dates are undefined, which indicates All Time selection
                  const allTimeSelected = !start && !end;
                  setIsAllTime(allTimeSelected);
                  setStartDate(start);
                  setEndDate(end);
                }}
                showAllTimeOption={true}
                allTimeStartDate={allTimeStartDate}
                allTimeEndDate={allTimeEndDate}
              />
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex items-center justify-center px-5 py-0">
        {isPending ? (
          <div className="mt-10">
            <LoadingIndicator />
          </div>
        ) : data.timesheets.length === 0 ? (
          <div className="flex flex-col items-center justify-center text-center">
            <div className="text-weak-400 mb-4 mt-10">
              <EmptyStateFinanceBanking />
            </div>
            <p className="text-sm text-soft-400">You do not have any usage data yet.</p>
          </div>
        ) : (
          <UpdatedTable
            colDefs={columns}
            rowData={mappedRowData}
            isLoading={isPending}
            gridOptions={{ groupDisplayType: 'custom', groupDefaultExpanded: 0 }}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default EquipmentTotalUsageBreakdown;
