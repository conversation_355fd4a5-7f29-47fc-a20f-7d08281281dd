import { RiCalendarScheduleLine, RiMapPinLine } from '@remixicon/react';
import dayjs from 'dayjs';
import { MappedScheduleEvent } from '@/interfaces/equipment';

const ScheduleItem = ({ schedule }: { schedule: MappedScheduleEvent }) => {
  return (
    <div className="mb-3 border-b border-soft-200 px-4 py-3 last:mb-0 last:border-b-0">
      <div className="flex">
        {/* First column: Icon */}
        <div className="mr-3 flex-shrink-0 pt-0.5">
          <RiCalendarScheduleLine size={20} color="rgb(var(--raw-soft-400))" />
        </div>

        {/* Second column: Schedule details */}
        <div className="flex flex-grow flex-col">
          {/* Row 1: Date range */}
          <div className="text-strong-900 text-sm font-medium">{getScheduleTitle(schedule)}</div>

          {/* Row 2: Project name */}
          {schedule.projectName && <div className="mt-1 text-sm text-sub-600">{schedule.projectName}</div>}

          {/* Row 3: Project location */}
          {schedule.projectAddress && (
            <div className="mt-1 flex items-center text-xs text-soft-400">
              <RiMapPinLine className="mr-1 inline-block" size={16} />
              <span>{schedule.projectAddress}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const ScheduleList = ({ schedules }: { schedules: MappedScheduleEvent[] }) => {
  if (!schedules || schedules.length === 0) {
    return <div className="text-soft-500 text-center text-sm">No schedule events found</div>;
  }

  return (
    <div className="mt-3 max-h-[300px] overflow-y-auto px-1 py-2">
      {schedules.map((schedule) => (
        <ScheduleItem key={schedule.id} schedule={schedule} />
      ))}
    </div>
  );
};

// if the start and end date are on the same calendar day -> May 02, 2025
// if the start and end date are on different calendar days -> May 02 - 03, 2025
const getScheduleTitle = (schedule: MappedScheduleEvent) => {
  const startDate = dayjs(schedule.startTime);
  const endDate = dayjs(schedule.endTime);

  if (startDate.isSame(endDate, 'day')) {
    return startDate.format('MMM D, YYYY');
  }

  return `${startDate.format('MMM D')} - ${endDate.format('D, YYYY')}`;
};

export default ScheduleList;
