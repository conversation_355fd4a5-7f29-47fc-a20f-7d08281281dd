import { useForm } from 'react-hook-form';
import { RiToolsFill } from '@remixicon/react';

import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { TextField } from '@/components/elements/form/TextField';
import ControlledCombobox from '../elements/form/ControlledCombobox';
import ControlledSelect from '../elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { equipmentService } from '@/services/equipment';
import { CreateEquipmentPayload } from '@/interfaces/equipment';
import { addToast } from '@/hooks/useToast';
import { useEffect } from 'react';

type AddEquipmentFormData = {
  name: string;
  categoryName: string;
  year: string;
  hourlyCost: string;
};

interface AddEquipmentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  categories: { label: string; value: string }[];
}

const MIN_EQUIPMENT_YEAR = 1950;

export const AddEquipmentModal = ({ open, setOpen, categories }: AddEquipmentModalProps) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AddEquipmentFormData>({
    defaultValues: {
      name: '',
      categoryName: '',
      year: '',
      hourlyCost: '',
    },
  });
  useEffect(() => {
    if (open) {
      reset();
    }
  }, [open, reset]);

  const queryClient = useQueryClient();

  const addEquipmentMutation = useMutation({
    mutationFn(data: AddEquipmentFormData) {
      // Create payload that matches the API expectations
      const payload: CreateEquipmentPayload = {
        name: data.name,
        categoryName: data.categoryName,
        year: data.year,
        hourlyCost: data.hourlyCost,
      };

      // Call the equipment service to create the equipment
      return equipmentService.create(payload);
    },
    onSuccess(newEquipment) {
      queryClient.invalidateQueries({ queryKey: ['equipment'] });
      setOpen(false);
      addToast({
        title: 'Added Equipment',
        description: (
          <>
            Successfully added the equipment <strong>{newEquipment.name}</strong>.
          </>
        ),
        type: 'success',
      });
    },
  });

  return (
    <ModalV2
      open={open}
      setOpen={setOpen}
      title="Add Equipment"
      icon={<RiToolsFill className="text-primary h-5 w-5" />}
    >
      <FormV2
        onSubmit={handleSubmit((formData) => addEquipmentMutation.mutate(formData))}
        onCancel={() => setOpen(false)}
        isLoading={addEquipmentMutation.isPending}
        submitText="Add Equipment"
      >
        <div className="space-y-4">
          <TextField
            name="name"
            label="Equipment Name"
            control={control}
            error={errors.name?.message}
            rules={{ required: 'Equipment name is required' }}
            required
            placeholder="Enter equipment name"
          />

          <ControlledCombobox
            name="categoryName"
            label="Category"
            control={control}
            error={errors.categoryName?.message}
            rules={{ required: 'Category is required' }}
            required
            items={categories}
            itemClassName={'capitalize'}
            className="capitalize"
            enableTextAsFirstOption
            searchPlaceholder="Search or create a new category"
          />

          <ControlledSelect
            name="year"
            label="Year"
            control={control}
            error={errors.year?.message}
            rules={{ required: 'Year is required' }}
            required
          >
            {Array.from({ length: new Date().getFullYear() - MIN_EQUIPMENT_YEAR + 1 }, (_, i) => (
              <SelectItem key={new Date().getFullYear() - i} value={String(new Date().getFullYear() - i)}>
                {new Date().getFullYear() - i}
              </SelectItem>
            ))}
          </ControlledSelect>

          <TextField
            name="hourlyCost"
            label="Hourly Cost"
            control={control}
            error={errors.hourlyCost?.message}
            placeholder="0.00"
            type="number"
            step="0.01"
            min="0"
            required
          />
        </div>
      </FormV2>
    </ModalV2>
  );
};
