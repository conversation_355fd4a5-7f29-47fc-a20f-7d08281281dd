import { FC, useState } from 'react';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef } from '@ag-grid-community/core';
import { formatUSD } from '@/utils/format';
import { Equipment } from '@/interfaces/equipment';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import ArrowGoBackLine from '@/hammr-icons/ArrowGoBackLine';
import { equipmentService } from '@/services/equipment';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog/ConfirmDialog';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { useRouter } from 'next/router';
import { Badge } from '@/hammr-ui/components/badge';
import dayjs from 'dayjs';

interface EquipmentTableProps {
  status: 'in-use' | 'archived';
  equipment: Equipment[];
  refetchEquipment?: () => Promise<any>;
}

interface EquipmentRow {
  id: number;
  name: string;
  category: string;
  year: string;
  hourlyCost: string;
  lastUsedBy: string;
  lastUsedProject: string;
  lastUsedOn: string;
}

const EquipmentTable: FC<EquipmentTableProps> = ({ status, equipment, refetchEquipment }) => {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedEquipment, setSelectedEquipment] = useState<{ id: number; name: string } | null>(null);
  const router = useRouter();

  // Transform equipment data into row format
  const rowData: EquipmentRow[] = equipment?.map((item: Equipment) => ({
    id: item.id,
    name: item.name,
    category: item.category.name,
    year: item.year,
    hourlyCost: item.hourlyCost,
    lastUsedBy: item.lastUsedBy,
    lastUsedProject: item.lastUsedProject,
    lastUsedOn: item.lastUsedOn,
  }));

  const handleActionClick = (equipmentId: number, equipmentName: string) => {
    setSelectedEquipment({ id: equipmentId, name: equipmentName });
    setConfirmDialogOpen(true);
  };

  const handleConfirmAction = async () => {
    if (!selectedEquipment) return;

    try {
      if (status === 'in-use') {
        // Archive equipment
        await equipmentService.archive(selectedEquipment.id);
      } else {
        // Restore equipment
        await equipmentService.restore(selectedEquipment.id);
      }

      // Refresh equipment data
      if (refetchEquipment) {
        await refetchEquipment();
      }
    } catch (error) {
      console.error(`Error ${status === 'in-use' ? 'archiving' : 'restoring'} equipment:`, error);
    }
  };

  // Table column definitions
  const colDefs: ColDef[] = [
    {
      field: 'name',
      headerName: 'Equipment',
      minWidth: 150,
      flex: 1,
      sortable: true,
      initialSort: 'asc',
    },
    {
      field: 'category',
      headerName: 'Category',
      minWidth: 120,
      flex: 1,
      sortable: true,
      cellClass: 'capitalize',
      cellRenderer: (params) => {
        return (
          <Badge color="gray" variant="lighter" className="w-fit">
            {params.value}
          </Badge>
        );
      },
    },
    {
      field: 'year',
      headerName: 'Year',
      minWidth: 80,
      sortable: true,
      cellRenderer: (params) => {
        return <div className="text-sub-600">{params.value}</div>;
      },
    },
    {
      field: 'hourlyCost',
      headerName: 'Hourly Cost',
      minWidth: 120,
      sortable: true,
      cellRenderer: (params) => {
        if (!params.value || params.value === '—') return '—';
        return formatUSD.format(Number(params.value));
      },
    },
    {
      field: 'lastUsedBy',
      headerName: 'Last Used By',
      minWidth: 150,
      flex: 1,
      sortable: true,
      cellRenderer: (params) => {
        return <div className="font-medium text-strong-950">{params.value}</div>;
      },
    },
    {
      field: 'lastUsedProject',
      headerName: 'Last Project',
      minWidth: 150,
      flex: 1,
      sortable: true,
      cellRenderer: (params) => {
        return <div className="text-sub-600">{params.value}</div>;
      },
    },
    {
      field: 'lastUsedOn',
      headerName: 'Last Used On',
      minWidth: 120,
      sortable: true,
      cellRenderer: (params) => {
        return params.value ? dayjs(params.value).format('MM/DD/YYYY') : '';
      },
    },
    {
      field: 'actions',
      headerName: '',
      maxWidth: 56,
      resizable: false,
      suppressMovable: true,
      cellRenderer: (params) => {
        return (
          <LinkButton
            size="small"
            onClick={(e) => {
              // e.stopPropagation();
              handleActionClick(params.data.id, params.data.name);
            }}
            className="flex items-center justify-center"
          >
            <Tooltip content={status === 'in-use' ? 'Archive' : 'Restore'}>
              {status === 'in-use' ? <ArchiveLine /> : <ArrowGoBackLine />}
            </Tooltip>
          </LinkButton>
        );
      },
    },
  ];

  return (
    <>
      <UpdatedTable
        colDefs={colDefs}
        rowData={rowData}
        enablePagination
        emptyRowsText={`No ${status === 'in-use' ? 'active' : 'archived'} equipment found`}
        onRowClicked={(params) => {
          if (params.node.field === 'actions') return;

          // Navigate to equipment detail page when row is clicked
          router.push(`/equipment/${params.data.id}`);
        }}
        defaultColDef={{
          resizable: true,
        }}
        fitToWindow
      />

      <ConfirmDialog
        open={confirmDialogOpen}
        setOpen={setConfirmDialogOpen}
        onConfirm={handleConfirmAction}
        title={`${status === 'in-use' ? 'Archive' : 'Restore'} Equipment`}
        subtitle={
          selectedEquipment
            ? `Are you sure you want to ${status === 'in-use' ? 'archive' : 'restore'} "${selectedEquipment.name}"?`
            : ''
        }
        confirmButtonText={status === 'in-use' ? 'Archive' : 'Restore'}
        confirmButton={{ color: status === 'in-use' ? 'error' : 'primary' }}
      />
    </>
  );
};

export default EquipmentTable;
