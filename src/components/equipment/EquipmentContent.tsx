import EquipmentTable from '@/components/equipment/EquipmentTable';
import EquipmentMap from '@/components/equipment/EquipmentMap';
import { Equipment } from '@/interfaces/equipment';

type EquipmentContentProps = {
  activeTab: 'in-use' | 'archived';
  viewMode: string;
  equipment: Equipment[];
  refetchEquipment?: () => Promise<any>;
};

const EquipmentContent = ({ activeTab, viewMode, equipment, refetchEquipment }: EquipmentContentProps) => {
  const status = activeTab === 'in-use' ? 'in-use' : 'archived';

  return viewMode === 'table' ? (
    <EquipmentTable status={status} equipment={equipment} refetchEquipment={refetchEquipment} />
  ) : (
    <EquipmentMap status={status} equipment={equipment} />
  );
};

export default EquipmentContent;
