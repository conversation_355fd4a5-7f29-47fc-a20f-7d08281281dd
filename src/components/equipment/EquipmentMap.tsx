import { useMemo } from 'react';
import { Equipment } from '@/interfaces/equipment';
import dayjs from 'dayjs';
import MapComponent, { Location } from '@/hammr-ui/components/Map';
import { RiCalendarLine, RiFolderOpenLine, RiMapPinLine, RiUserLine } from '@remixicon/react';

interface Props {
  status: 'in-use' | 'archived';
  equipment: Equipment[];
}

export default function EquipmentMap({ status, equipment }: Props) {
  const locations = useMemo(() => {
    const locations: Location[] = [];

    equipment.forEach((singleEquipment) => {
      const mostRecentTimesheet = singleEquipment.timesheets.toSorted((a, b) => {
        const aClockIn = dayjs(a.clockIn).valueOf();
        const bClockIn = dayjs(b.clockIn).valueOf();
        if (aClockIn > bClockIn) return -1;
        else if (aClockIn < bClockIn) return 1;
        else return 0;
      })[0];

      if (!mostRecentTimesheet) return;

      const clockInLocationOfMostRecentTimesheet: any = mostRecentTimesheet.userLocations?.find(
        (userLocation) => userLocation.locationEvent === 'CLOCK_IN'
      );

      if (clockInLocationOfMostRecentTimesheet) {
        locations.push({
          lng: clockInLocationOfMostRecentTimesheet.locationCoordinates.coordinates[0],
          lat: clockInLocationOfMostRecentTimesheet.locationCoordinates.coordinates[1],
          markerInfo: {
            title: singleEquipment.name,
            description: (
              <div className="flex flex-col gap-1 text-strong-950">
                <p className="flex h-fit gap-1 text-xs text-sub-600">
                  <RiUserLine className="size-4" /> {mostRecentTimesheet.user?.firstName}{' '}
                  {mostRecentTimesheet.user?.lastName}
                </p>
                <p className="flex h-fit gap-1 text-xs text-sub-600">
                  <RiCalendarLine className="size-4" /> {dayjs(singleEquipment.lastUsedOn).format('MM/DD/YYYY, h:mm A')}
                </p>
                <p className="flex h-fit gap-1 text-xs text-sub-600">
                  <RiFolderOpenLine className="size-4" /> {mostRecentTimesheet.project?.name}
                </p>
                {mostRecentTimesheet.project?.address && (
                  <p className="flex h-fit gap-1 text-xs text-sub-600">
                    <RiMapPinLine className="size-4" /> {mostRecentTimesheet.project?.address}
                  </p>
                )}
              </div>
            ),
          },
        });
      }
    });

    return locations;
  }, [equipment]);

  return <MapComponent locations={locations} className="h-full rounded-10" />;
}
