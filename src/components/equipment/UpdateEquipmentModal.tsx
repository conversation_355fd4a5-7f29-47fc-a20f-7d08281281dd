import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { RiToolsFill } from '@remixicon/react';

import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { TextField } from '@/components/elements/form/TextField';
import ControlledCombobox from '../elements/form/ControlledCombobox';
import ControlledSelect from '../elements/form/ControlledSelect';
import { SelectItem } from '@/hammr-ui/components/select';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { equipmentService } from '@/services/equipment';
import { addToast } from '@/hooks/useToast';
import { Equipment } from '@/interfaces/equipment';

export type UpdateEquipmentFormData = {
  name: string;
  categoryName: string;
  year: string;
  hourlyCost: string;
};

interface UpdateEquipmentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  categories: { label: string; value: string }[];
  equipment: Equipment;
}

const MIN_EQUIPMENT_YEAR = 1950;

export const UpdateEquipmentModal = ({ open, setOpen, categories, equipment }: UpdateEquipmentModalProps) => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<UpdateEquipmentFormData>({
    defaultValues: {
      name: equipment?.name || '',
      categoryName: equipment?.category?.name || '',
      year: String(equipment?.year) || '',
      hourlyCost: String(equipment?.hourlyCost) || '',
    },
  });

  const queryClient = useQueryClient();

  const equipmentMutation = useMutation({
    mutationFn(data: UpdateEquipmentFormData) {
      return equipmentService.update(equipment.id, {
        name: data.name,
        categoryName: data.categoryName,
        year: data.year,
        hourlyCost: data.hourlyCost,
      });
    },
    onSuccess(newEquipment) {
      queryClient.invalidateQueries({ queryKey: ['equipment'] });
      setOpen(false);
      addToast({
        title: 'Edited Equipment',
        description: (
          <>
            Successfully edited the equipment <strong>{newEquipment.name}</strong>.
          </>
        ),
        type: 'success',
      });
    },
  });

  // Reset form with equipment data when it changes or modal opens
  useEffect(() => {
    if (equipment && open) {
      reset({
        name: equipment.name || '',
        categoryName: equipment.category?.name || '',
        year: String(equipment.year) || '',
        hourlyCost: String(equipment.hourlyCost) || '',
      });
    }
  }, [equipment, open, reset]);

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <ModalV2
      open={open}
      setOpen={setOpen}
      title="Edit Equipment"
      icon={<RiToolsFill className="text-primary h-5 w-5" />}
    >
      <FormV2
        onSubmit={handleSubmit((formData) => equipmentMutation.mutate(formData))}
        onCancel={handleCancel}
        isLoading={equipmentMutation.isPending}
        submitText="Save Changes"
      >
        <div className="space-y-4">
          <TextField
            name="name"
            label="Equipment Name"
            control={control}
            error={errors.name?.message}
            rules={{ required: 'Equipment name is required' }}
            required
          />

          <ControlledCombobox
            name="categoryName"
            label="Category"
            control={control}
            error={errors.categoryName?.message}
            rules={{ required: 'Category is required' }}
            required
            items={categories}
            className={'capitalize'}
            itemClassName={'capitalize'}
            enableTextAsFirstOption
          />

          <ControlledSelect
            name="year"
            label="Year"
            control={control}
            error={errors.year?.message}
            rules={{ required: 'Year is required' }}
            required
          >
            {Array.from({ length: new Date().getFullYear() - MIN_EQUIPMENT_YEAR + 1 }, (_, i) => (
              <SelectItem key={new Date().getFullYear() - i} value={String(new Date().getFullYear() - i)}>
                {new Date().getFullYear() - i}
              </SelectItem>
            ))}
          </ControlledSelect>

          <TextField
            name="hourlyCost"
            label="Hourly Cost"
            control={control}
            error={errors.hourlyCost?.message}
            placeholder="0.00"
            type="number"
            step="0.01"
            min="0"
            required
          />
        </div>
      </FormV2>
    </ModalV2>
  );
};
