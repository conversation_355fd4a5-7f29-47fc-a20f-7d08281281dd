import ListSettingsLine from '@/hammr-icons/ListSettingsLine';
import { <PERSON>alog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormV2 } from '../elements/Form';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { useEffect } from 'react';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';
import { Controller, useForm } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { useToast } from '@/hooks/useToast';
import { AccrualMethod, TimeOffPolicy } from '@/interfaces/timeoff';
import dayjs from 'dayjs';

interface Props {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  alreadyEnrolledEmployeeIds: number[];
  policy: TimeOffPolicy;
  onSuccess: () => void;
}

interface FormData {
  userIds: number[];
  startingBalance: string;
}

export default function EnrollEmployeesModal({
  isOpen,
  setIsOpen,
  alreadyEnrolledEmployeeIds,
  policy,
  onSuccess,
}: Props) {
  const { addToast } = useToast();
  const {
    register,
    formState: { errors },
    control,
    handleSubmit,
    reset,
  } = useForm<FormData>({
    defaultValues: {
      userIds: [],
      startingBalance: '',
    },
  });

  const enrollMutation = useMutation({
    mutationFn: async (data: FormData) => {
      await apiRequest(`time-off-policies/${policy.id}/users`, {
        method: 'POST',
        body: {
          userIds: data.userIds,
          startingBalance: Number(data.startingBalance),
        },
      });
    },
    onSuccess: () => {
      addToast({
        type: 'success',
        title: 'Enrolled Employees',
        description: (
          <>
            Successfully enrolled employees to the Time Off policy{' '}
            <strong className="font-medium">{policy.name}</strong>.
          </>
        ),
      });
      setIsOpen(false);
      onSuccess();
    },
  });

  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  const accrualResetDate = policy.accrualResetDate
    ? dayjs(policy.accrualResetDate, 'MM-DD').isAfter(dayjs())
      ? dayjs(policy.accrualResetDate, 'MM-DD').format('MMM D, YYYY')
      : dayjs(policy.accrualResetDate, 'MM-DD').add(1, 'year').format('MMM D, YYYY')
    : 'the work anniversary of each employee';

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<ListSettingsLine className="text-sub-600" />} title="Enroll Employees" />
        <FormV2
          onSubmit={handleSubmit((data) => enrollMutation.mutate(data))}
          onCancel={() => setIsOpen(false)}
          isLoading={enrollMutation.isPending}
          submitText="Enroll"
        >
          <Controller
            name="userIds"
            control={control}
            rules={{ required: 'Please select at least one employee' }}
            render={({ field }) => (
              <FormItem error={!!errors.userIds}>
                <FormLabel>
                  Employees{' '}
                  {!!field.value.length && <span className="ml-px text-sub-600">({field.value.length} selected)</span>}
                </FormLabel>
                <FormControl>
                  <WorkerCrewSelect
                    selectedWorkerIds={field.value}
                    onChange={field.onChange}
                    error={!!errors.userIds}
                    queryFilters={{ workerClassification: 'EMPLOYEE' }}
                    disabledWorkers={alreadyEnrolledEmployeeIds.map((id) => ({
                      workerId: id,
                      selected: true,
                      reason: '',
                    }))}
                  />
                </FormControl>
                <FormMessage messageType="error">{errors.userIds?.message}</FormMessage>
              </FormItem>
            )}
          />
          {policy.isLimited ? (
            <FormItem required error={!!errors.startingBalance} className="mt-5">
              <FormLabel>Starting Balance</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.01"
                  placeholder="Enter number"
                  {...register('startingBalance', {
                    required: 'Please enter starting balance',
                    min: { value: 0, message: 'Enter a value 0 or above' },
                  })}
                  afterContent="hours"
                />
              </FormControl>
              <span className="text-xs text-sub-600">
                {policy.accrualMethod === AccrualMethod.FIXED ? (
                  <>
                    Enter time-off you would like to grant these employees in this cycle. They will accrue{' '}
                    {policy.accrualHoursRate} hours when the policy resets on {accrualResetDate}.
                  </>
                ) : (
                  <>
                    If employees have previously earned time off balance, enter those hours here to carry them forward.
                    They will start accruing additional time-off starting today based on the accrual rate of this
                    policy.
                  </>
                )}
              </span>
              <FormMessage>{errors.startingBalance?.message as string}</FormMessage>
            </FormItem>
          ) : undefined}
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
