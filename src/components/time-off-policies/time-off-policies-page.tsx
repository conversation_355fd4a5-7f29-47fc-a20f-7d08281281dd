import Layout from '@/components/dashboard/Layout';
import CreateTimeOffPolicyModal from '@/components/time-off-policies/CreateTimeOffPolicyModal';
import AddLine from '@/hammr-icons/AddLine';
import EmptyStateTimeOff from '@/hammr-icons/EmptyStateTimeOff';
import ListSettingsLine from '@/hammr-icons/ListSettingsLine';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import StatusBadge from '@/hammr-ui/components/StatusBadge';
import Button from '@/hammr-ui/components/button';
import { TabContent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { apiRequest } from '@/utils/requestHelpers';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { TimeOffPolicy, TimeOffPolicyType } from '@/interfaces/timeoff';
import { Badge } from '@/hammr-ui/components/badge';
import { policyTypeColors } from '@/components/time-off-requests/TimeOffCalendar';
import { ButtonGroup, ButtonGroupItem } from '@/hammr-ui/components/ButtonGroup';
import { RiCalendar2Line, RiListSettingsLine } from '@remixicon/react';

interface Props {
  timeOffTab: 'requests' | 'policies';
  setTimeOffTab: (tab: 'requests' | 'policies') => void;
}

export default function TimeOffPolicies({ timeOffTab, setTimeOffTab }: Props) {
  const router = useRouter();
  const [showTimeOffPolicyModal, setShowTimeOffPolicyModal] = useState(false);

  const policies = useQuery<TimeOffPolicy[]>({
    queryKey: ['time-off-policies', 'active', 'archived'],
    queryFn: () =>
      apiRequest<{
        timeOffPolicies: TimeOffPolicy[];
      }>('time-off-policies?includeArchived=true').then((response) => response.timeOffPolicies),
  });

  const data = {
    active: policies.data?.filter((policy) => !policy.endDate),
    archived: policies.data?.filter((policy) => policy.endDate),
  };

  return (
    <Layout noPadding>
      <PageHeader
        title="Policies"
        icon={<ListSettingsLine />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowTimeOffPolicyModal(true)}>
            Create Time Off Policy
          </Button>
        }
      />

      {policies.isPending ? (
        <div className="flex h-full items-center justify-center">
          <LoadingIndicator text="Loading Policies..." />
        </div>
      ) : policies.isError ? (
        <div className="flex h-full items-center justify-center text-sm text-error-base">
          An error occured when fetching the policies.
        </div>
      ) : data.active.length === 0 && data.archived.length === 0 ? (
        <section className="flex flex-col items-center pt-[120px]">
          <EmptyStateTimeOff />
          <p className="mt-5 h-fit text-center text-sm text-soft-400">
            There is no Time Off Policy yet.
            <br />
            Click the button below to add one.
          </p>

          <Button beforeContent={<AddLine />} onClick={() => setShowTimeOffPolicyModal(true)} className="mt-5">
            Create Time Off Policy
          </Button>
        </section>
      ) : (
        <Tabs defaultValue="active" className="px-8 py-6">
          <menu className="flex items-center justify-between">
            <TabList>
              <TabItem value="active">Active</TabItem>
              <TabItem value="archived">Archived</TabItem>
            </TabList>
            <ButtonGroup>
              <ButtonGroupItem
                active={timeOffTab === 'requests'}
                beforeContent={<RiCalendar2Line />}
                onClick={() => setTimeOffTab('requests')}
              >
                Requests
              </ButtonGroupItem>
              <ButtonGroupItem
                active={timeOffTab === 'policies'}
                beforeContent={<RiListSettingsLine />}
                onClick={() => setTimeOffTab('policies')}
              >
                Policies
              </ButtonGroupItem>
            </ButtonGroup>
          </menu>
          {(['active', 'archived'] as const).map((value) => (
            <TabContent value={value} key={value}>
              {data[value].length === 0 ? (
                <section className="flex flex-col items-center pt-[120px]">
                  <EmptyStateTimeOff />
                  <p className="mt-5 h-fit text-center text-sm text-soft-400">
                    There is no {value} Time Off Policy yet.
                    {value === 'active' && (
                      <>
                        <br />
                        Click the button below to add one.
                      </>
                    )}
                  </p>

                  {value === 'active' && (
                    <Button
                      beforeContent={<AddLine />}
                      onClick={() => setShowTimeOffPolicyModal(true)}
                      className="mt-5"
                    >
                      Create Time Off Policy
                    </Button>
                  )}
                </section>
              ) : (
                <section className="mt-6 grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6">
                  {data[value].map((policy) => (
                    <article
                      key={policy.id}
                      className="flex cursor-pointer flex-col items-start justify-between rounded-16 border border-soft-200 bg-white-0 p-5 shadow-xs hover:bg-weak-50"
                      onClick={() => {
                        router.push(`/time-off-policies/${policy.id}`);
                      }}
                    >
                      <header className="w-full">
                        <hgroup className="flex items-center justify-between">
                          <h2 className="text-sm font-medium text-strong-950">{policy.name}</h2>
                          {value === 'active' ? (
                            <StatusBadge>Active</StatusBadge>
                          ) : (
                            <StatusBadge status="disabled">Inactive</StatusBadge>
                          )}
                        </hgroup>
                        <Badge
                          className="mt-3"
                          style={{
                            backgroundColor: policyTypeColors[policy.type].bg,
                            color: policyTypeColors[policy.type].text,
                          }}
                        >
                          {policy.type}
                        </Badge>
                      </header>
                      <footer className="mt-9 flex w-full items-center justify-between">
                        <LinkButton
                          size="medium"
                          style="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/time-off-policies/${policy.id}?tab=enrolled`);
                          }}
                        >
                          {policy.users.length} Enrolled
                        </LinkButton>
                        <span className="text-xs text-sub-600">
                          {policy.type === TimeOffPolicyType.UNPAID ? 'Unpaid' : 'Paid'}
                        </span>
                      </footer>
                    </article>
                  ))}
                </section>
              )}
            </TabContent>
          ))}
        </Tabs>
      )}

      <CreateTimeOffPolicyModal open={showTimeOffPolicyModal} setOpen={setShowTimeOffPolicyModal} />
    </Layout>
  );
}
