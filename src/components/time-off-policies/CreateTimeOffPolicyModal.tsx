import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { FormV2 } from '../elements/Form';
import { Dispatch, FormEvent, SetStateAction, useEffect, useState } from 'react';
import { FormItem, FormLabel } from '@/hammr-ui/components/form';
import { DropdownPicker } from '@/hammr-ui/components/Dropdown';
import ListSettingsLine from '@/hammr-icons/ListSettingsLine';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import SunLine from '@/hammr-icons/SunLine';
import MedicineBottleLine from '@/hammr-icons/MedicineBottleLine';
import CalendarEventLine from '@/hammr-icons/CalendarEventLine';
import { useRouter } from 'next/router';
import { TimeOffPolicyType } from '@/interfaces/timeoff';

interface Props {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

export default function CreateTimeOffPolicyModal({ open, setOpen }: Props) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [policyType, setPolicyType] = useState<TimeOffPolicyType>();
  const router = useRouter();

  async function handleSubmit(e: FormEvent) {
    e.preventDefault();
    setIsProcessing(true);
    router.push(`/time-off-policies/create?policyType=${policyType}`);
  }

  useEffect(() => {
    if (!open) {
      setPolicyType(undefined);
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogSurface>
        <DialogHeader icon={<ListSettingsLine className="text-sub-600" />} title="Create Time Off Policy" />
        <FormV2 onSubmit={handleSubmit} onCancel={() => setOpen(false)} isLoading={isProcessing} submitText="Continue">
          <FormItem>
            <FormLabel>Policy Type</FormLabel>
            <DropdownPicker
              placeholder="Select an option"
              className="mt-1 w-full"
              items={Object.values(TimeOffPolicyType).map((type) => {
                return {
                  label: type.replaceAll('_', ' '),
                  value: type,
                };
              })}
              value={policyType}
              onChange={(value) => {
                setPolicyType(value as TimeOffPolicyType);
              }}
            />
          </FormItem>

          <div className="relative mt-5 flex items-center">
            <p className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white-0 px-2.5 text-2xs font-medium uppercase text-soft-400">
              OR start from A template
            </p>
            <hr className="w-full border-soft-200" />
          </div>

          <button
            className={`mt-5 flex gap-3.5 rounded-12 border p-4 ${policyType === 'PTO' ? 'border-primary-base' : 'border-soft-200'}`}
            type="button"
            onClick={() => setPolicyType(TimeOffPolicyType.PTO)}
          >
            <KeyIcon icon={<SunLine />} />
            <span className="text-left text-xs text-sub-600">
              <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">Paid Time Off</dfn>
              Flexible days for employees to use for vacations or personal needs.
            </span>
          </button>
          <button
            className={`mt-5 flex gap-3.5 rounded-12 border p-4 ${policyType === 'SICK' ? 'border-primary-base' : 'border-soft-200'}`}
            type="button"
            onClick={() => setPolicyType(TimeOffPolicyType.SICK)}
          >
            <KeyIcon icon={<MedicineBottleLine />} />
            <span className="text-left text-xs text-sub-600">
              <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">Sick Leave</dfn>
              Dedicated days for employees to recover from illness or for medical care.
            </span>
          </button>
          <button
            className={`mt-5 flex gap-3.5 rounded-12 border p-4 ${policyType === 'UNPAID' ? 'border-primary-base' : 'border-soft-200'}`}
            type="button"
            onClick={() => setPolicyType(TimeOffPolicyType.UNPAID)}
          >
            <KeyIcon icon={<CalendarEventLine />} />
            <span className="text-left text-xs text-sub-600">
              <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">Unpaid Leave</dfn>
              Time off without pay for personal or other non-work-related reasons.
            </span>
          </button>
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
}
