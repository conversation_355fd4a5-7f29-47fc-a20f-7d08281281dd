import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import { Switch } from '@/hammr-ui/components/Switch';
import Button from '@/hammr-ui/components/button';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import { Label } from '@/hammr-ui/components/label';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import { AccrualMethod, TimeOffPolicy, TimeOffPolicyType } from '@/interfaces/timeoff';
import { getNumberOfDaysInMonth } from '@/utils/dateHelper';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import dayjs from 'dayjs';
import { useCompany } from '@/hooks/useCompany';

export const months = [
  { name: 'January', value: '01' },
  { name: 'February', value: '02' },
  { name: 'March', value: '03' },
  { name: 'April', value: '04' },
  { name: 'May', value: '05' },
  { name: 'June', value: '06' },
  { name: 'July', value: '07' },
  { name: 'August', value: '08' },
  { name: 'September', value: '09' },
  { name: 'October', value: '10' },
  { name: 'November', value: '11' },
  { name: 'December', value: '12' },
];

function getDaysInMonth(month: number) {
  return Array.from({ length: getNumberOfDaysInMonth(month) }, (_, i) => String(i + 1).padStart(2, '0'));
}

const defaultValues = {
  name: '',
  type: null,
  isLimited: null,
  accrualMethod: AccrualMethod.ACCRUED,
  accrualHoursRate: null,
  accrualResetDate: null,
  accrualHoursInterval: null,
  accrualLimit: null,
  carryoverLimit: '',
  addNewEmployeesAutomatically: false,
};

interface Props {
  onSubmit: (data: typeof defaultValues) => void;
  onCancel: () => void;
  isSubmitting: boolean;
  policy?: TimeOffPolicy;
}

export default function PolicyForm({ onSubmit, onCancel, policy, isSubmitting }: Props) {
  const isEditing = !!policy;

  const router = useRouter();
  const policyType = router.query.policyType;

  const {
    register,
    control,
    formState: { errors },
    setValue,
    handleSubmit,
    watch,
  } = useForm({
    defaultValues: {
      ...(policy
        ? {
            name: policy.name,
            type: policy.type,
            isLimited: String(policy.isLimited),
            accrualMethod: policy.accrualMethod,
            accrualHoursRate: policy.accrualHoursRate,
            accrualHoursInterval: policy.accrualHoursInterval,
            accrualLimit: policy.accrualLimit,
            accrualResetDate: policy.accrualResetDate,
            carryoverLimit: String(policy.carryoverLimit),
            addNewEmployeesAutomatically: policy.addNewEmployeesAutomatically,
          }
        : defaultValues),
    },
  });

  useEffect(() => {
    if (policyType) {
      setValue('type', policyType as TimeOffPolicyType);
    }
  }, [policyType, setValue]);

  const isLimited = watch('isLimited');
  const selectedAccrualMethod = watch('accrualMethod');
  const isUnpaid = watch('type') === 'UNPAID';

  const { company } = useCompany();

  return (
    <>
      <form
        id="policy-form"
        className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs"
        onSubmit={handleSubmit((data) => {
          onSubmit({ ...data, carryoverLimit: data.carryoverLimit === '' ? undefined : data.carryoverLimit });
        })}
      >
        <fieldset>
          <legend className="font-medium text-strong-950">General Information</legend>

          <FormItem required error={!!errors['name']} className="mt-5">
            <FormLabel>Policy Name</FormLabel>
            <FormControl>
              <Input
                placeholder="Enter policy name"
                {...register('name', {
                  required: 'Please enter policy name',
                })}
              />
            </FormControl>
            <FormMessage>{errors['name']?.message}</FormMessage>
          </FormItem>

          <ControlledSelect
            error={errors['type']?.message}
            label="Policy Type"
            required
            rules={{ required: 'Please select a policy type' }}
            control={control}
            name="type"
            className="mt-5 w-full"
          >
            {Object.values(TimeOffPolicyType).map((type) => (
              <SelectItem key={type} value={type}>
                {type.replaceAll('_', ' ')}
              </SelectItem>
            ))}
          </ControlledSelect>

          {!isUnpaid && (
            <FormItem required error={!!errors['isLimited']} className="mt-5">
              <FormLabel className="normal-case">
                Is there a limit to the number of hours employees can take off in this policy?
              </FormLabel>
              <FormControl>
                <Controller
                  rules={{ required: 'Please select an option' }}
                  control={control}
                  name="isLimited"
                  render={({ field }) => (
                    <RadioGroup
                      value={field.value}
                      onValueChange={field.onChange}
                      className="!mt-3 flex flex-col gap-3"
                      disabled={isEditing}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="true" id="yes" />
                        <Label className="font-normal" htmlFor="yes">
                          Yes, there’s a limit
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="false" id="no" />
                        <Label className="font-normal" htmlFor="no">
                          No, it’s unlimited
                        </Label>
                      </div>
                    </RadioGroup>
                  )}
                />
              </FormControl>
              <FormMessage>{errors['isLimited']?.message as string}</FormMessage>
            </FormItem>
          )}
        </fieldset>

        {isLimited === 'true' && !isUnpaid && (
          <>
            <hr className="mt-5 border-soft-200" />

            <fieldset className="mt-5">
              <legend className="font-medium text-strong-950">Accrual Details</legend>

              <FormItem required error={!!errors['accrualMethod']} className="mt-5">
                <FormLabel>Accrual Method</FormLabel>
                <FormControl>
                  <Controller
                    rules={{ required: 'Please select an option' }}
                    control={control}
                    name="accrualMethod"
                    render={({ field }) => (
                      <RadioGroup
                        value={field.value}
                        onValueChange={field.onChange}
                        className="!mt-3 flex flex-col gap-3"
                      >
                        <div className="flex gap-2">
                          <RadioGroupItem value={AccrualMethod.FIXED} id="fixed" />
                          <Label className="text-xs font-normal text-sub-600" htmlFor="fixed">
                            <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">All at once</dfn>
                            Employees receive the full time-off balance upfront at the start of the accrual period.
                          </Label>
                        </div>
                        <div className="flex gap-2">
                          <RadioGroupItem value={AccrualMethod.ACCRUED} id="accrued" />
                          <Label className="text-xs font-normal text-sub-600" htmlFor="accrued">
                            <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">
                              Accrued over pay periods
                            </dfn>
                            Time-off is earned gradually with each pay period.
                          </Label>
                        </div>
                        <div className="flex gap-2">
                          <RadioGroupItem value={AccrualMethod.HOURS_WORKED} id="hours_worked" />
                          <Label className="text-xs font-normal text-sub-600" htmlFor="hours_worked">
                            <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">
                              Accrued based on hours worked
                            </dfn>
                            Time-off is earned proportional to the number of hours an employee works.
                          </Label>
                        </div>
                      </RadioGroup>
                    )}
                  />
                </FormControl>
                <FormMessage>{errors['accrualMethod']?.message as string}</FormMessage>
              </FormItem>

              {selectedAccrualMethod !== AccrualMethod.HOURS_WORKED ? (
                <FormItem required error={!!errors['accrualHoursRate']} className="mt-5">
                  <FormLabel>Total Hours Per Year</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter number"
                      {...register('accrualHoursRate', {
                        required: 'Please enter accrual rate',
                      })}
                      afterContent="hours"
                    />
                  </FormControl>
                  <span className="text-xs text-sub-600">Number of hours that will accrue in an year.</span>
                  <FormMessage>{errors['accrualHoursRate']?.message as string}</FormMessage>
                </FormItem>
              ) : (
                <FormItem
                  required
                  error={!!errors['accrualHoursRate'] ?? !!errors['accrualHoursInterval']}
                  className="mt-5"
                >
                  <FormLabel>Accrual Rate</FormLabel>
                  <div className="flex flex-row items-center gap-4 whitespace-nowrap">
                    <FormControl>
                      <Input
                        type="number"
                        {...register('accrualHoursRate', {
                          required: 'Please enter accrual hours rate',
                        })}
                        afterContent="hours"
                      />
                    </FormControl>
                    earned per
                    <FormControl>
                      <Input
                        type="number"
                        {...register('accrualHoursInterval', {
                          required: 'Please enter accrual hours interval',
                        })}
                        afterContent="hours"
                      />
                    </FormControl>
                    worked.
                  </div>
                  <span className="text-xs text-sub-600">
                    The number of time-off hours employees will earn based on number of hours worked.
                  </span>
                  <FormMessage className="text-error-base">
                    {(errors['accrualHoursRate']?.message as string) ??
                      (errors['accrualHoursInterval']?.message as string)}
                  </FormMessage>
                </FormItem>
              )}

              <FormItem required error={!!errors['accrualResetDate']} className="mt-5">
                <FormLabel>Accrual Period Reset</FormLabel>
                <span className="text-xs text-sub-600">
                  On this date each year, the accrual period resets. Accrual starts from zero, plus any unused time that
                  carries over from the previous year.
                </span>
                <FormControl>
                  <Controller
                    control={control}
                    name="accrualResetDate"
                    render={({ field }) =>
                      company.isPayrollEnabled ? (
                        <RadioGroup
                          value={field.value}
                          onValueChange={(value) => {
                            if (value === '') {
                              field.onChange('01-01');
                              return;
                            }
                            field.onChange(null);
                          }}
                          className="!mt-3 flex flex-col gap-3"
                        >
                          <div className="flex gap-2">
                            <RadioGroupItem value={null} id="start_date" />
                            <Label className="text-xs font-normal text-sub-600" htmlFor="start_date">
                              <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">
                                On each employee’s start date anniversary
                              </dfn>
                              The reset date will be different for each employee.
                            </Label>
                          </div>
                          <div className="flex gap-2">
                            <RadioGroupItem value={field.value ? field.value : ''} id="custom_date" />
                            <Label className="text-xs font-normal text-sub-600" htmlFor="custom_date">
                              <dfn className="mb-1 block text-sm font-medium not-italic text-strong-950">
                                On the same date for all employees
                              </dfn>
                              The reset date will be the same for all employees.
                              {field.value && (
                                <div className="mt-3 flex gap-3">
                                  <Select
                                    onChange={(value) => {
                                      if (value === '') return;

                                      const day = field.value?.split('-')[1];
                                      const date = dayjs(`${value}-${day}`);

                                      // this fixes the case where the user switches to a month where 31st doesn't exist
                                      const actualDay = date.date().toString().padStart(2, '0');
                                      field.onChange({
                                        target: { value: `${value}-${actualDay}` },
                                      });
                                    }}
                                    className="h-9 w-fit"
                                    value={field.value?.split('-')[0]}
                                  >
                                    {months.map((month) => {
                                      return (
                                        <SelectItem key={month.value} value={month.value}>
                                          {month.name}
                                        </SelectItem>
                                      );
                                    })}
                                  </Select>
                                  <Select
                                    onChange={(value) => {
                                      if (value === '') return;

                                      const month = field.value?.split('-')[0];
                                      field.onChange({ target: { value: `${month}-${value}` } });
                                    }}
                                    className="h-9 w-fit"
                                    value={field.value?.split('-')[1]}
                                  >
                                    {getDaysInMonth(+field.value?.split('-')[0]).map((day) => {
                                      return (
                                        <SelectItem key={day} value={String(day)}>
                                          {day}
                                        </SelectItem>
                                      );
                                    })}
                                  </Select>
                                </div>
                              )}
                            </Label>
                          </div>
                        </RadioGroup>
                      ) : (
                        <div className="mt-3 flex gap-3">
                          <Select
                            onChange={(value) => {
                              if (value === '') return;

                              const day = field.value?.split('-')[1];
                              const date = dayjs(`${value}-${day}`);

                              // this fixes the case where the user switches to a month where 31st doesn't exist
                              const actualDay = date.date().toString().padStart(2, '0');
                              field.onChange({
                                target: { value: `${value}-${actualDay}` },
                              });
                            }}
                            className="h-9 w-fit"
                            value={field.value?.split('-')[0]}
                          >
                            {months.map((month) => {
                              return (
                                <SelectItem key={month.value} value={month.value}>
                                  {month.name}
                                </SelectItem>
                              );
                            })}
                          </Select>
                          <Select
                            onChange={(value) => {
                              if (value === '') return;

                              const month = field.value?.split('-')[0];
                              field.onChange({ target: { value: `${month}-${value}` } });
                            }}
                            className="h-9 w-fit"
                            value={field.value?.split('-')[1]}
                          >
                            {getDaysInMonth(+field.value?.split('-')[0]).map((day) => {
                              return (
                                <SelectItem key={day} value={String(day)}>
                                  {day}
                                </SelectItem>
                              );
                            })}
                          </Select>
                        </div>
                      )
                    }
                  />
                </FormControl>
                <FormMessage>{errors['accrualResetDate']?.message as string}</FormMessage>
              </FormItem>

              <FormItem required error={!!errors['accrualLimit']} className="mt-5">
                <FormLabel>Accrual limit</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter number"
                    {...register('accrualLimit', {
                      required: 'Please enter accrual limit',
                    })}
                    afterContent="hours"
                  />
                </FormControl>
                <span className="text-xs text-sub-600">
                  Employees will stop accruing hours once they reach this limit.
                </span>
                <FormMessage>{errors['accrualLimit']?.message as string}</FormMessage>
              </FormItem>

              <FormItem error={!!errors['carryoverLimit']} className="mt-5">
                <FormLabel>Carryover</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter number"
                    {...register('carryoverLimit')}
                    afterContent="hours"
                  />
                </FormControl>
                <span className="text-xs text-sub-600">
                  Number of hours that will carry over from previous year to current year.
                </span>
                <FormMessage>{errors['carryoverLimit']?.message as string}</FormMessage>
              </FormItem>
            </fieldset>
          </>
        )}

        {(isLimited !== null || isUnpaid) && (
          <>
            <hr className="mt-5 border-soft-200" />

            <FormItem className="mt-5 flex-row items-center justify-between">
              <FormLabel htmlFor="addNewEmployeesAutomatically">Add New Employees Automatically</FormLabel>
              <FormControl>
                <Controller
                  name="addNewEmployeesAutomatically"
                  control={control}
                  render={({ field }) => (
                    <Switch id="addNewEmployeesAutomatically" checked={field.value} onCheckedChange={field.onChange} />
                  )}
                />
              </FormControl>
            </FormItem>
          </>
        )}
      </form>

      <fieldset className="mt-4 max-w-[446px] space-x-3 text-right">
        <Button variant="outline" color="neutral" size="small" type="button" onClick={onCancel} disabled={isSubmitting}>
          Cancel
        </Button>
        <Button size="small" form="policy-form" loading={isSubmitting}>
          Save
        </Button>
      </fieldset>
    </>
  );
}
