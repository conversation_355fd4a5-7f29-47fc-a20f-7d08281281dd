import { useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import { Button } from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import { useQuery } from '@tanstack/react-query';
import CreateDailyReportModal from '@/components/daily-reports/CreateDailyReportModal';
import DailyReportsTable from '@/components/daily-reports/DailyReportsTable';
import EmptyStateStockMarketTracker from '@/hammr-icons/EmptyStateStockMarketTracker';
import { apiRequest } from '@/utils/requestHelpers';
import { getFileAndOpen } from '@/components/project-documents/utils';
import { AWS_CUSTOMER_BUCKET } from '@/utils/constants';
import { useAwsS3 } from '@/hooks/useAwsS3';
import { addToast } from '@/hooks/useToast';
import { useAwsSts } from '@/hooks/useAwsSts';

const DailyReports = ({
  projectId,
  showCreateReportModal,
  setShowCreateReportModal,
}: {
  projectId?: number;
  showCreateReportModal: boolean;
  setShowCreateReportModal: (show: boolean) => void;
}) => {
  const { user } = useAuth();
  const [lastUpdatedReportsTimestamp, setLastUpdatedReportsTimestamp] = useState<number | null>(null);

  const s3Client = useAwsS3();
  const { stsCredentials, getStsCredentials } = useAwsSts();

  const reportsQuery = useQuery({
    queryKey: ['daily-reports', user?.companyId, projectId],
    queryFn: async () => {
      setLastUpdatedReportsTimestamp(Date.now());
      if (stsCredentials?.Expiration) {
        const expiration = new Date(stsCredentials.Expiration);
        if (new Date() >= expiration) {
          await getStsCredentials(user?.companyId);
        }
      }

      if (projectId) {
        return apiRequest(`daily-reports?projectId=${projectId}`);
      }

      return apiRequest('daily-reports');
    },
    enabled: Boolean(user?.companyId),
  });

  // check if there's no daily reports or if the filtered by projectId reports are empty
  const isEmpty =
    reportsQuery.data?.dailyReports.length === 0 ||
    (projectId && reportsQuery.data?.dailyReports.filter((report) => report.projectId === projectId).length === 0);

  return (
    <>
      {isEmpty ? (
        <div className="mt-6 flex-grow px-2">
          <div className="flex h-full items-center justify-center">
            <div className="flex flex-col items-center gap-5">
              <EmptyStateStockMarketTracker />

              <div className="text-center text-sm text-soft-400">
                There is no daily report yet.
                <br />
                Click the button below to create one.
              </div>

              <Button beforeContent={<AddLine />} onClick={() => setShowCreateReportModal(true)}>
                Create Report
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <DailyReportsTable
          parentProjectId={projectId}
          lastUpdatedReportsTimestamp={lastUpdatedReportsTimestamp}
          onRowClick={async (report) => {
            const objectId = report.objectId;
            // download from s3
            try {
              const fileUrl = await getFileAndOpen(
                s3Client.s3,
                'daily-reports',
                objectId,
                user?.companyId,
                AWS_CUSTOMER_BUCKET
              );

              // getFileAndOpen has a catch handler so we need to check for undefined
              if (!fileUrl) {
                throw new Error('File not found');
              }
            } catch (error) {
              try {
                if (error.message === 'File not found') {
                  throw new Error('File not found');
                }

                // try again but renewing credentials
                await getStsCredentials(user?.companyId);

                const fileUrl = await getFileAndOpen(
                  s3Client.s3,
                  'daily-reports',
                  objectId,
                  user?.companyId,
                  AWS_CUSTOMER_BUCKET
                );

                // getFileAndOpen has a catch handler so we need to check for undefined
                if (!fileUrl) {
                  throw new Error('File not found');
                }
              } catch (_error) {
                addToast({
                  title: 'Download Failed',
                  description: 'The daily report could not be downloaded. Please try again.',
                  type: 'error',
                });
              }
            }
          }}
        />
      )}
      <CreateDailyReportModal
        open={showCreateReportModal}
        setOpen={setShowCreateReportModal}
        callback={() => reportsQuery.refetch()}
        parentProjectId={projectId}
      />
    </>
  );
};

export default DailyReports;
