import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useAwsS3 } from '@/hooks/useAwsS3';
import { useAwsSts } from '@/hooks/useAwsSts';
import { getProjectPhotoCollections } from '@/services/project-photos';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { cn } from '@/utils/cn';
import CheckboxCircleFill from '@/hammr-icons/CheckboxCircleFill';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';
const PHOTOS_PER_ROW = 5;
const MAX_INITIAL_ROWS = 2;

dayjs.extend(isBetween);

interface PhotoGridProps {
  projectId?: number;
  selectedPhotoIds: string[];
  setSelectedPhotoIds: (ids: string[]) => void;
  setLoadedProjectPhotoIds?: (ids: string[]) => void;
  reportDate: string;
  disabled?: boolean;
}

const PhotoGrid = ({
  projectId,
  selectedPhotoIds,
  setSelectedPhotoIds,
  setLoadedProjectPhotoIds,
  reportDate,
  disabled,
}: PhotoGridProps) => {
  const { user } = useAuth();
  const { getStsCredentials, stsCredentials } = useAwsSts();
  const { s3 } = useAwsS3();
  const [showAll, setShowAll] = useState(false);

  const generateSignedUrl = useCallback(
    async (key = '1.png') => {
      if (!s3) return;

      return new Promise((resolve, reject) => {
        const params = {
          Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/project-photos`,
          Key: key,
          Expires: 21600,
        };

        s3.getSignedUrl('getObject', params, (err, url) => {
          if (err) reject(err);
          resolve(url);
        });
      });
    },
    [s3, user?.companyId]
  );

  const handlePhotoClick = (photoObjectId: string) => {
    if (disabled) return;

    if (selectedPhotoIds.includes(photoObjectId)) {
      setSelectedPhotoIds(selectedPhotoIds.filter((id) => id !== photoObjectId));
    } else {
      setSelectedPhotoIds([...selectedPhotoIds, photoObjectId]);
    }
  };

  const { data, isLoading } = useQuery({
    queryKey: ['projectPhotosCollection', user?.companyId, projectId],
    queryFn: async () => {
      if (stsCredentials?.Expiration) {
        const expiration = new Date(stsCredentials.Expiration);
        if (new Date() >= expiration) {
          await getStsCredentials(user?.companyId);
        }
      }

      const queryParams = {
        organizationId: user?.companyId,
        ...(projectId && { projectId: projectId.toString() }),
      };

      const photoCollectionData = await getProjectPhotoCollections(queryParams);

      // Flatten all photos from all collections and get their signed URLs
      const allPhotos = await Promise.all(
        photoCollectionData.projectPhotosCollections.flatMap((collection) =>
          collection.projectPhotos.map(async (photo) => ({
            ...photo,
            imageUrl: await generateSignedUrl(photo.objectId),
          }))
        )
      );

      return allPhotos;
    },
    enabled: !!s3 && !!user?.companyId && !!projectId,
  });

  const photos = useMemo(
    () =>
      data?.filter((photo) =>
        dayjs(photo.createdAt).isBetween(dayjs(reportDate).startOf('day'), dayjs(reportDate).endOf('day'))
      ) || [],
    [data, reportDate]
  );
  const photoIds = useMemo(() => photos.map((photo) => photo.objectId), [photos]);

  useEffect(() => {
    if (setLoadedProjectPhotoIds) {
      setLoadedProjectPhotoIds(photoIds);
    }
  }, [photoIds, setLoadedProjectPhotoIds]);

  const initialPhotoCount = PHOTOS_PER_ROW * MAX_INITIAL_ROWS - 1; // -1 to make room for "show more" tile
  const remainingCount = photos.length - initialPhotoCount;

  const displayedPhotos = showAll ? photos : photos.slice(0, initialPhotoCount);

  if (isLoading) {
    return (
      <div className="flex size-full items-center justify-center">
        <LoadingIndicator />
      </div>
    );
  }

  if (!displayedPhotos || displayedPhotos.length === 0) {
    return null;
  }

  return (
    <div className="mb-4 flex flex-col gap-4">
      <div className="grid grid-cols-[repeat(auto-fill,72px)] gap-2.5">
        {displayedPhotos.map((photo) => (
          <div
            key={`${photo.objectId}-${photo.id}`}
            className={cn(
              'relative size-[72px] cursor-pointer overflow-hidden rounded-6',
              selectedPhotoIds.includes(photo.objectId) && 'outline outline-2 outline-primary-base'
            )}
          >
            <Image
              onClick={() => handlePhotoClick(photo.objectId)}
              src={photo.imageUrl}
              alt={photo.name || 'Project photo'}
              className={'object-cover'}
              layout="fill"
            />
            {selectedPhotoIds.includes(photo.objectId) && (
              <div
                onClick={() => handlePhotoClick(photo.objectId)}
                className="absolute inset-0 flex items-center justify-center"
                style={{
                  background: 'linear-gradient(0deg, rgba(238, 96, 35, 0.24) 0%, rgba(238, 96, 35, 0.24) 100%)',
                }}
              >
                <CheckboxCircleFill className="text-primary-base [&>path]:bg-white" />
              </div>
            )}
          </div>
        ))}

        {!showAll && remainingCount > 0 && (
          <button
            onClick={() => setShowAll(true)}
            className={cn(
              'flex size-[72px] items-center justify-center rounded-lg',
              'text-sm font-medium text-sub-600 hover:bg-weak-100'
            )}
          >
            +{remainingCount} more
          </button>
        )}
      </div>
    </div>
  );
};

export default PhotoGrid;
