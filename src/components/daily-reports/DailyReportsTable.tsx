import { DailyReport } from '@/interfaces/daily-report';
import { UpdatedTable } from '../shared/UpdatedTable';
import { ColDef, ICellRendererParams, IServerSideGetRowsParams } from '@ag-grid-community/core';
import { Project } from '@/interfaces/project';
import { HammrUser } from '@/interfaces/user';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { MultiSelect, Item } from '@/hammr-ui/components/multi-select';
import { Input } from '@/hammr-ui/components/input';
import Search2Line from '@/hammr-icons/Search2Line';
import FileFormatIcon from '@/hammr-icons/FileFormatIcon';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import Spinner from '@/hammr-ui/components/spinner';
import { apiRequest } from '@/utils/requestHelpers';
import { logError, showErrorToast } from '@/utils/errorHandling';
import { AgGridReact } from '@ag-grid-community/react';
import debounce from 'lodash/debounce';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { addToast } from '@/hooks/useToast';
import CompactButton from '@/hammr-ui/components/CompactButton';

dayjs.extend(utc);

interface DailyReportRow {
  id: number;
  documentName: string;
  project: string;
  projectId: number;
  reportDate: number;
  createdBy: string;
  createdAt: number;
  objectId: string;
  userId: number;
  user: HammrUser;
  date: Date;
}

interface DailyReportsTableProps {
  onRowClick: (report: DailyReportRow) => void;
  parentProjectId?: number;
  lastUpdatedReportsTimestamp: number | null;
}

const DailyReportsTable = ({ onRowClick, parentProjectId, lastUpdatedReportsTimestamp }: DailyReportsTableProps) => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [openingReport, setOpeningReport] = useState<string | undefined>();
  const [deletingReportId, setDeletingReportId] = useState<number | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<DailyReportRow | null>(null);
  const tableRef = useRef<AgGridReact>(null);

  // Store selected filter values (just IDs)
  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>([]);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);

  // Store available filter options from API
  const [availableUsers, setAvailableUsers] = useState<
    {
      id: number;
      firstName: string;
      lastName: string;
    }[]
  >([]);

  const [availableProjects, setAvailableProjects] = useState<Project[]>([]);

  // Initialize with parent project ID if provided
  useEffect(() => {
    if (parentProjectId) {
      setSelectedProjectIds([parentProjectId]);
    }
  }, [parentProjectId]);

  // Create MultiSelect items from available options and selection state
  const projectItems = useMemo(() => {
    return availableProjects
      .sort((a, b) => a.name.localeCompare(b.name))
      .map((project) => ({
        label: project.name + (project.projectNumber ? ` (${project.projectNumber})` : ''),
        value: project.id,
        isSelected: selectedProjectIds.includes(project.id),
      }));
  }, [availableProjects, selectedProjectIds]);

  const userItems = useMemo(() => {
    return availableUsers
      .sort((a, b) => a.firstName.localeCompare(b.firstName))
      .map((user) => ({
        label: `${user.firstName} ${user.lastName}`,
        value: user.id,
        isSelected: selectedUserIds.includes(user.id),
      }));
  }, [availableUsers, selectedUserIds]);

  // Handle selection changes
  const handleProjectChange = (items: Item[]) => {
    setSelectedProjectIds(items.filter((item) => item.isSelected).map((item) => item.value as number));
  };

  const handleUserChange = (items: Item[]) => {
    setSelectedUserIds(items.filter((item) => item.isSelected).map((item) => item.value as number));
  };

  // Create a debounced function to update search term
  const debouncedSetSearchTerm = useCallback(
    debounce((value: string) => {
      setDebouncedSearchTerm(value);
    }, 300),
    []
  );

  // Update the debounced search term when searchTerm changes
  useEffect(() => {
    debouncedSetSearchTerm(searchTerm);
  }, [searchTerm, debouncedSetSearchTerm]);

  // Update grid when filters change
  useEffect(() => {
    if (tableRef.current?.api) {
      tableRef.current.api.refreshServerSide({ purge: true });
    }
  }, [selectedUserIds, selectedProjectIds, debouncedSearchTerm]);

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (reportId: number) => {
      setDeletingReportId(reportId);
      await apiRequest(`daily-reports/${reportId}`, {
        method: 'DELETE',
        convertToJson: false,
      });
    },
    onSuccess: (_, reportId) => {
      // Invalidate and refetch daily reports queries
      queryClient.invalidateQueries({ queryKey: ['daily-reports'] });
      setShowDeleteModal(false);
      setSelectedReport(null);
      setDeletingReportId(null);

      // Show success message
      addToast({
        type: 'success',
        title: 'Daily Report Deleted',
        description: (
          <>
            Successfully deleted the daily report{' '}
            <strong className="font-medium">{selectedReport?.documentName}</strong>.
          </>
        ),
      });
    },
    onError: (error) => {
      logError(error);
      showErrorToast(error, 'Unable to delete daily report');
      setDeletingReportId(null);
    },
  });

  const handleOpenDeleteModal = (report: DailyReportRow) => {
    setSelectedReport(report);
    setShowDeleteModal(true);
  };

  const handleDelete = async (report: DailyReportRow) => {
    deleteMutation.mutate(report.id);
  };

  const columns: ColDef<DailyReportRow>[] = [
    {
      hide: !parentProjectId,
      field: 'documentName',
      headerName: 'Document Name',
      minWidth: 200,
      sortable: false,
      cellRenderer: (params: ICellRendererParams<DailyReportRow>) => {
        return (
          <div className="flex items-center gap-2">
            {openingReport === params.data.objectId ? <Spinner /> : <FileFormatIcon type="pdf" />}
            <span>{params.value}</span>
          </div>
        );
      },
    },
    {
      hide: !!parentProjectId,
      field: 'project',
      headerName: 'Project',
      cellRenderer: (params: ICellRendererParams<DailyReportRow>) => {
        return (
          <div className="flex items-center gap-2">
            {openingReport === params.data.objectId ? <Spinner /> : <FileFormatIcon type="pdf" />}
            <span>{params.value}</span>
          </div>
        );
      },
    },
    {
      field: 'reportDate',
      headerName: 'Date',
      cellRenderer: (params: ICellRendererParams<DailyReportRow>) => {
        return dayjs(params.value).format('MM/DD/YYYY');
      },
      maxWidth: 200,
    },
    {
      field: 'createdBy',
      headerName: 'Created By',
      maxWidth: 300,
    },
    {
      field: 'createdAt',
      headerName: 'Created On',
      initialSort: 'desc',
      cellRenderer: (params: ICellRendererParams<DailyReportRow>) => {
        return dayjs(params.value).format('MM/DD/YYYY, hh:mm A');
      },
      maxWidth: 250,
    },
    {
      headerName: '',
      maxWidth: 100,
      sortable: false,
      pinned: 'right',
      cellRenderer: (params: ICellRendererParams<DailyReportRow>) => {
        const isDeleting = deletingReportId === params.data.id;
        return (
          <div className="flex h-full w-full items-center justify-center" onClick={(e) => e.preventDefault()}>
            <Tooltip content="Delete">
              <CompactButton
                size="large"
                onClick={(event) => {
                  event.preventDefault();
                  handleOpenDeleteModal(params.data);
                }}
              >
                {isDeleting ? <Spinner /> : <DeleteBinLine />}
              </CompactButton>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleReportClick = async (report: DailyReportRow) => {
    setOpeningReport(report.objectId);
    await onRowClick(report);
    setOpeningReport(undefined);
  };

  const fetchDailyReports = useCallback(
    async (params: IServerSideGetRowsParams) => {
      try {
        // Extract sorting information
        const sortModel = params.request.sortModel;
        const sortField = sortModel.length > 0 ? sortModel[0].colId : 'createdAt';
        const sortDirection = sortModel.length > 0 ? sortModel[0].sort : 'desc';

        // Extract pagination information
        const startRow = params.request.startRow;
        const endRow = params.request.endRow;
        const pageSize = endRow - startRow;
        const page = Math.floor(startRow / pageSize) + 1;

        // Make the API request with filters
        const queryParams: any = {
          page,
          pageSize,
          sortField,
          sortDirection,
        };

        if (debouncedSearchTerm) {
          queryParams.search = debouncedSearchTerm;
        }

        if (selectedUserIds.length > 0) {
          queryParams.userIds = selectedUserIds;
        }

        if (selectedProjectIds.length > 0 || parentProjectId) {
          queryParams.projectIds = parentProjectId ? [parentProjectId] : selectedProjectIds;
        }

        const response = await apiRequest(
          `daily-reports?page=${queryParams.page}&pageSize=${queryParams.pageSize}&sortField=${queryParams.sortField}&sortDirection=${queryParams.sortDirection}${queryParams.search ? `&search=${queryParams.search}` : ''}${queryParams.userIds ? `&userIds=${queryParams.userIds}` : ''}${queryParams.projectIds ? `&projectIds=${queryParams.projectIds}` : ''}`
        );

        // Update available filters from API response
        if (response.availableUsersToFilter) {
          setAvailableUsers(response.availableUsersToFilter);
        }

        if (response.availableProjectsToFilter && !parentProjectId) {
          setAvailableProjects(response.availableProjectsToFilter);
        }

        // Map the API response to row data
        const rowData = response.dailyReports.map(
          (report: DailyReport & { project: Project; user: HammrUser }): DailyReportRow => ({
            id: report.id,
            project: report.project.name,
            projectId: report.project.id,
            reportDate: dayjs(report.date).valueOf(),
            createdBy: `${report.user.firstName} ${report.user.lastName}`,
            createdAt: report.createdAt.valueOf(),
            objectId: report.objectId,
            userId: report.user.id,
            user: report.user,
            date: report.date,
            documentName: 'Daily Report ' + report.project.name + ' - ' + dayjs(report.date).format('MM/DD/YYYY'),
          })
        );

        // Return the data to AG Grid
        params.success({
          rowData: rowData,
          rowCount: response.totalCount,
        });
      } catch (error) {
        console.error('Error fetching daily reports:', error);
        params.fail();
      }
    },
    [debouncedSearchTerm, selectedUserIds, selectedProjectIds, parentProjectId, lastUpdatedReportsTimestamp]
  );

  // Create the datasource for server-side operations
  const dataSource = useMemo(
    () => ({
      getRows: fetchDailyReports,
    }),
    [fetchDailyReports]
  );

  return (
    <div className="flex flex-grow flex-col gap-6">
      <div className="flex items-center gap-4">
        <div className="relative">
          <Search2Line className="text-sub-400 absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-[300px] pl-9"
          />
        </div>

        {!parentProjectId && projectItems.length > 0 && (
          <MultiSelect
            label="All projects"
            items={projectItems}
            onChange={handleProjectChange}
            buttonProps={{
              className: 'w-[200px]',
            }}
          />
        )}

        {userItems.length > 0 && (
          <MultiSelect
            label="All creators"
            items={userItems}
            onChange={handleUserChange}
            buttonProps={{
              className: 'w-[200px]',
            }}
          />
        )}
      </div>

      <UpdatedTable<DailyReportRow>
        parentRef={tableRef}
        colDefs={columns}
        enablePagination
        onRowClicked={(event) => handleReportClick(event.data)}
        gridOptions={{
          rowModelType: 'serverSide',
          getRowId: (params) => params.data.id.toString(),
          suppressRowClickSelection: true,
        }}
        tableProps={{
          serverSideDatasource: dataSource,
        }}
        fitToWindow
      />

      <ConfirmDialog
        open={showDeleteModal}
        setOpen={setShowDeleteModal}
        onConfirm={handleDelete}
        data={selectedReport}
        icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
        confirmButtonText="Delete"
        confirmButton={{
          color: 'error',
        }}
        title="Delete daily report"
        subtitle={
          selectedReport && (
            <div>
              You&apos;re about to delete the daily report{' '}
              <span className="font-medium">{selectedReport.documentName}</span>. Do you want to proceed?
            </div>
          )
        }
      />
    </div>
  );
};

export default DailyReportsTable;
