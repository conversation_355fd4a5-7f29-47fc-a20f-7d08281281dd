import { FormControl, FormItem, FormLabel } from '@/hammr-ui/components/form';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FileUploaderProgress } from '../elements/form/FileUploader';
import { ControlledFileUploader } from '../elements/form/FileUploader';
import Button from '@/hammr-ui/components/button';
import PhotoGrid from './PhotoGrid';
import { FormData } from './CreateDailyReportModal';
import dayjs from 'dayjs';

interface ProjectPhotosSelectProps {
  form: UseFormReturn<FormData>;
  error?: string;
  projectId: number;
  uploadProgress: {
    [key: string]: number;
  };
  appendPhotos?: (files: File[]) => void;
  disabled?: boolean;
}

export default function ProjectPhotosSelect({
  form,
  error,
  projectId,
  uploadProgress,
  appendPhotos,
  disabled,
}: ProjectPhotosSelectProps) {
  const filesToUpload = form.watch('selectedPhotos');
  const selectedPhotoCount = form.watch('selectedPhotoIds').length + filesToUpload.length;
  const [loadedProjectPhotoIds, setLoadedProjectPhotoIds] = useState<string[]>([]);

  const handleSelectAll = () => {
    form.setValue('selectedPhotoIds', loadedProjectPhotoIds);
  };

  return (
    <FormItem className="space-y-3">
      <div className="flex items-center justify-between">
        <div>
          <FormLabel>Add Project Photos to Report</FormLabel>
          {selectedPhotoCount > 0 && <span className="ml-1 text-sm text-sub-600">({selectedPhotoCount} selected)</span>}
        </div>

        {loadedProjectPhotoIds.length > 0 && (
          <div>
            <Button disabled={disabled} type="button" variant="ghost" size="x-small" onClick={() => handleSelectAll()}>
              Select All
            </Button>
          </div>
        )}
      </div>
      <FormControl>
        <div className="flex flex-col">
          <PhotoGrid
            projectId={projectId}
            selectedPhotoIds={form.watch('selectedPhotoIds')}
            setSelectedPhotoIds={(ids) => form.setValue('selectedPhotoIds', ids)}
            setLoadedProjectPhotoIds={setLoadedProjectPhotoIds}
            reportDate={dayjs(form.watch('date')).format('YYYY-MM-DD')}
            disabled={disabled}
          />

          <ControlledFileUploader
            form={form}
            name={'selectedPhotos'}
            error={error}
            label=""
            appendFiles={appendPhotos}
            disabled={disabled}
            inputProps={{ accept: 'image/*', multiple: true }}
          />

          <div className="mt-5">
            <FileUploaderProgress
              filesToUpload={filesToUpload}
              uploadProgress={uploadProgress}
              form={form}
              name={'selectedPhotos'}
              showSelectedStyle={true}
              disabled={disabled}
            />
          </div>
        </div>
      </FormControl>
    </FormItem>
  );
}
