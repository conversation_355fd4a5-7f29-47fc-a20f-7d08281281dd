import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { FormV2 } from 'components/elements/Form';
import { ModalV2 } from '../elements/ModalV2';
import { TextField } from '../elements/form/TextField';
import { ControlledTextarea } from '../elements/form/ControlledTextarea';
import ControlledDateInput from '../elements/form/ControlledDateInput';
import FlashlightLine from '@/hammr-icons/FlashlightLine';
import { useQuery } from '@tanstack/react-query';
import { getProjects } from '@/services/projects';
import { useAuth } from '@/hooks/useAuth';
import * as yup from 'yup';
import ProjectPhotosSelect from './ProjectPhotosSelect';
import { AWS_CUSTOMER_BUCKET } from '@/utils/constants';
import { v4 as uuidv4 } from 'uuid';
import { useAwsS3 } from '@/hooks/useAwsS3';
import { apiRequest } from '@/utils/requestHelpers';
import ControlledCombobox from '../elements/form/ControlledCombobox';
import dayjs from 'dayjs';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { Badge } from '@/hammr-ui/components/badge';
interface CreateDailyReportModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  callback?: () => void;
  parentProjectId?: number;
}

const schema = yup.object().shape({
  date: yup.date(),
  summary: yup.string(),
  injuryNotes: yup.string(),
  equipmentNotes: yup.string(),
  materialNotes: yup.string(),
  signOffName: yup.string().required('Sign-off name is required'),
  projectId: yup.string().required('Project is required'),
  selectedPhotoIds: yup.array().of(yup.string()),
});

export type FormData = yup.InferType<typeof schema> & {
  selectedPhotos: File[];
};

export default function CreateDailyReportModal({
  open,
  setOpen,
  callback,
  parentProjectId,
}: CreateDailyReportModalProps) {
  const { user } = useAuth();
  const { s3 } = useAwsS3();
  const { addToast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});
  const [isUploading, setIsUploading] = useState(false);
  const isFormDisabled = isProcessing || isUploading;

  const form = useForm<FormData>({
    defaultValues: {
      date: new Date(),
      summary: '',
      injuryNotes: '',
      equipmentNotes: '',
      materialNotes: '',
      signOffName: '',
      projectId: parentProjectId?.toString() || '',
      // this one is for the file uploader (we need to upload)
      selectedPhotos: [],
      // this one is for the project photo grid (already uploaded)
      selectedPhotoIds: [],
    },
  });

  useEffect(() => {
    if (parentProjectId) {
      setValue('projectId', parentProjectId.toString());
    }
  }, [parentProjectId]);

  const {
    formState: { errors },
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
  } = form;

  // Custom handler for appending files to the existing selectedPhotos
  const appendPhotos = (newPhotos: File[]) => {
    const currentPhotos = watch('selectedPhotos');

    // Create a Set of existing filenames to avoid duplicates
    const existingFileNames = new Set(currentPhotos.map((file) => file.name));

    // Filter out any duplicates from new photos
    const uniqueNewPhotos = newPhotos.filter((file) => !existingFileNames.has(file.name));

    // Combine current photos with unique new photos
    setValue('selectedPhotos', [...currentPhotos, ...uniqueNewPhotos]);
  };

  const projects = useQuery({
    queryKey: ['projects', user?.companyId],
    queryFn: () => getProjects({ organizationId: user?.companyId }),
    enabled: Boolean(user?.companyId),
  });

  const projectId = parseInt(watch('projectId'));
  const date = watch('date');

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  useEffect(() => {
    // reset selected photos on project change
    setValue('selectedPhotoIds', []);
  }, [projectId, date]);

  const onSubmit = async (data) => {
    const { selectedPhotos, selectedPhotoIds, ...rest } = data;

    let uploadedPhotos = [];

    if (selectedPhotos.length > 0) {
      try {
        setIsUploading(true);
        uploadedPhotos = await uploadPhotos(data);
      } catch (err) {
        logError(err);
        showErrorToast(err, 'Unable to upload photos');
      } finally {
        setIsUploading(false);
      }
    }

    const payload = {
      ...rest,
      selectedPhotoIds: [...selectedPhotoIds, ...uploadedPhotos.map((photo) => photo.objectId)],
      projectId,
      date: dayjs(date).format('YYYY-MM-DD'),
    };

    setIsProcessing(true);

    try {
      await apiRequest('daily-reports', {
        method: 'POST',
        body: payload,
      });

      addToast({
        title: 'Created Daily Report',
        description: (
          <div>
            Successfully created a daily report for{' '}
            <span className="font-medium">{projects.data?.projects.find((p) => p.id === projectId)?.name}</span>
          </div>
        ),
        type: 'success',
      });

      callback?.();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create daily report');
    } finally {
      setIsProcessing(false);
    }
  };

  const uploadPhotos = async (data: FormData) => {
    const updatedProgress = { ...uploadProgress };

    const uploadedPhotos = await Promise.all(
      data.selectedPhotos.filter(Boolean).map(async (document) => {
        const fileExtension = document.name.indexOf('.') ? document.name.split('.').pop() : '';

        const objectId = uuidv4() + (fileExtension ? '.' + fileExtension : '');

        const params = {
          Bucket: `${AWS_CUSTOMER_BUCKET}/${user?.companyId}/daily-report-photos`,
          Key: objectId,
          Body: document,
        };

        await s3
          .putObject(params)
          .on('httpUploadProgress', (evt) => {
            const progress = Math.round((evt.loaded * 100) / evt.total);
            updatedProgress[document.name] = progress;
            setUploadProgress({ ...updatedProgress });
          })
          .promise();

        const payload = {
          name: document.name,
          objectId: objectId,
          projectId: projectId,
        };

        return payload;
      })
    );

    return uploadedPhotos;
  };

  return (
    <ModalV2 icon={<FlashlightLine />} title="Create Daily Report" open={open} setOpen={setOpen}>
      <FormV2
        onCancel={() => setOpen(false)}
        onSubmit={handleSubmit(onSubmit)}
        isLoading={isProcessing}
        submitText={isUploading ? 'Uploading...' : 'Create Report'}
      >
        <div className="space-y-4">
          <ControlledCombobox
            name="projectId"
            label="Project"
            control={control}
            rules={{ required: 'Please select a project' }}
            error={errors['projectId']?.message as string}
            className="w-full"
            required
            items={projects.data?.projects.map((project) => ({
              label: (
                <span className="flex gap-1">
                  <span className="truncate">
                    {project.name + (project.projectNumber ? ` (${project.projectNumber})` : '')}
                  </span>
                  {project.isPrevailingWage && (
                    <Tooltip content="Prevailing Wage">
                      <Badge variant="outline" color="gray">
                        PW
                      </Badge>
                    </Tooltip>
                  )}
                </span>
              ),
              value: project.id.toString(),
            }))}
            disabled={isFormDisabled || !!parentProjectId}
          />

          <ControlledDateInput label="Date" control={control} name="date" className="w-full" disabled={isProcessing} />

          <ControlledTextarea
            name="summary"
            control={control}
            label="Summary"
            error={errors['summary']?.message}
            className="w-full"
            placeholder="Enter daily report summary"
            maxLength={2000}
            disabled={isFormDisabled}
          />

          <ControlledTextarea
            name="injuryNotes"
            control={control}
            label="Injury Notes"
            error={errors['injuryNotes']?.message}
            className="w-full"
            placeholder="Enter injury notes if any"
            maxLength={2000}
            disabled={isFormDisabled}
          />

          <ControlledTextarea
            name="equipmentNotes"
            control={control}
            label="Equipment Notes"
            error={errors['equipmentNotes']?.message}
            className="w-full"
            placeholder="Enter equipment notes"
            maxLength={2000}
            disabled={isFormDisabled}
          />

          <ControlledTextarea
            name="materialNotes"
            control={control}
            label="Material Notes"
            error={errors['materialNotes']?.message}
            className="w-full"
            placeholder="Enter material notes"
            maxLength={2000}
            disabled={isFormDisabled}
          />

          <ProjectPhotosSelect
            projectId={projectId}
            form={form}
            error={errors['selectedPhotos']?.message}
            uploadProgress={uploadProgress}
            appendPhotos={appendPhotos}
            disabled={isFormDisabled}
          />

          <TextField
            name="signOffName"
            control={control}
            label="Sign-off Name"
            rules={{ required: 'Please enter sign-off name' }}
            error={errors['signOffName']?.message}
            className="w-full"
            required
            placeholder="Enter name for sign-off"
            disabled={isFormDisabled}
          />
        </div>
      </FormV2>
    </ModalV2>
  );
}
