import { Card, CardContent, CardHeader, CardTitle } from '@hammr-ui/components/card';
import { cn } from '@/utils/cn';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { Select, SelectItem } from '@hammr-ui/components/select';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
  XAxis,
  YAxis,
} from 'recharts';
import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from 'dayjs/plugin/isoWeek';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { TooltipProps } from 'recharts/types/component/Tooltip';
import { useRouter } from 'next/router';

dayjs.extend(weekOfYear);
dayjs.extend(isoWeek);

interface ProjectInfo {
  id: number;
  name: string;
  regularMinutes: number;
  overtimeMinutes: number;
  doubleOvertimeMinutes: number;
}

type TimeRange = 'today' | 'this-week' | 'last-week' | 'this-month';

const getTimeRange = (range: TimeRange) => {
  const now = dayjs();

  switch (range) {
    case 'today':
      return {
        from: now.startOf('day'),
        to: now.endOf('day'),
      };
    case 'this-week':
      return {
        from: now.startOf('isoWeek'),
        to: now.endOf('isoWeek'),
      };
    case 'last-week':
      return {
        from: now.startOf('isoWeek').subtract(1, 'week'),
        to: now.endOf('isoWeek').subtract(1, 'week'),
      };
    case 'this-month':
      return {
        from: now.startOf('month'),
        to: now.endOf('month'),
      };
    default:
      throw new Error(`Invalid time range: ${range}`);
  }
};

// If we can't find data 'this-week', we will also search for these ranges
const DEFAULT_RANGES_TO_SEARCH: TimeRange[] = ['last-week', 'this-month'];

export default function ProjectActivity({ className }: { className?: string }) {
  const router = useRouter();
  const [selectedRange, setSelectedRange] = useState<TimeRange>('this-week');
  const [hoveredProject, setHoveredProject] = useState<string | null>(null);

  const projectsQuery = useQuery({
    queryKey: ['dashboard', 'projects', selectedRange],
    queryFn: async () => {
      const timeRange = getTimeRange(selectedRange);
      return apiRequest<ProjectInfo[]>('dashboard/project-activity', {
        urlParams: {
          from: timeRange.from.valueOf(),
          to: timeRange.to.valueOf(),
        },
      });
    },
  });

  useEffect(() => {
    if (!projectsQuery.isFetching && !projectsQuery.data?.length && DEFAULT_RANGES_TO_SEARCH.length) {
      setSelectedRange((prev) => {
        const currentIndex = DEFAULT_RANGES_TO_SEARCH.indexOf(prev);
        return DEFAULT_RANGES_TO_SEARCH[currentIndex + 1] || 'this-month';
      });
    }
  }, [projectsQuery.data?.length, projectsQuery.isFetching, selectedRange]);

  const projectsWithHighestTotalHours = projectsQuery.data?.toSorted((a, b) =>
    a.regularMinutes + a.overtimeMinutes + a.doubleOvertimeMinutes <
    b.regularMinutes + b.overtimeMinutes + b.doubleOvertimeMinutes
      ? 1
      : -1
  )?.[0];

  const chartData =
    projectsQuery.data?.map((project) => ({
      name: project.name,
      id: project.id,
      regular: Math.round(project.regularMinutes / 60),
      overtime: Math.round(project.overtimeMinutes / 60),
      doubleOvertime: Math.round(project.doubleOvertimeMinutes / 60),

      // this is just a hack to have that space between the blue and red bars: 1% of total hours
      spacer:
        ((projectsWithHighestTotalHours.regularMinutes +
          projectsWithHighestTotalHours.overtimeMinutes +
          projectsWithHighestTotalHours.doubleOvertimeMinutes) /
          60) *
        0.01,
    })) || [];

  const handleProjectClick = (projectId: number) => {
    const timeRange = getTimeRange(selectedRange);
    router.push(`/timesheets?project=${projectId}&from=${timeRange.from.valueOf()}&to=${timeRange.to.valueOf()}`);
  };

  return (
    <Card className={cn('relative flex min-h-96 min-w-80 flex-col', className)}>
      <CardHeader className=" px-4">
        <CardTitle className="flex items-center justify-between gap-2  whitespace-nowrap">
          <div>Project Activity</div>
          <Select value={selectedRange} onChange={(value: TimeRange) => setSelectedRange(value)} className="max-w-32">
            <SelectItem value="this-week">This Week</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="last-week">Last Week</SelectItem>
            <SelectItem value="this-month">This Month</SelectItem>
          </Select>
        </CardTitle>
      </CardHeader>
      <CardContent className="h-[300px] flex-1 p-4 pt-0">
        {projectsQuery.isLoading ? (
          <div className="flex h-full items-center justify-center">
            <LoadingIndicator />
          </div>
        ) : (
          <div className="flex h-full flex-col">
            <div className="flex h-full flex-row gap-1">
              {/*The reason we are creating our own Y axis with project names is because recharts can't align titles to the left*/}
              <div className="flex h-full flex-col justify-around pb-8">
                {chartData.map((chart) => (
                  <span
                    key={chart.name}
                    className="flex flex-1 cursor-pointer items-center break-words text-xs text-sub-600 hover:underline"
                    onClick={() => handleProjectClick(chart.id)}
                  >
                    {chart.name}
                  </span>
                ))}
              </div>
              <ResponsiveContainer width="100%">
                <BarChart
                  data={chartData}
                  layout="vertical"
                  onMouseMove={(state) => {
                    if (state?.activePayload?.[0]) {
                      setHoveredProject(state.activePayload[0].payload.name);
                    }
                  }}
                  onMouseLeave={() => {
                    setHoveredProject(null);
                  }}
                  onClick={(data) => {
                    if (data?.activePayload?.[0]) {
                      handleProjectClick(data.activePayload[0].payload.id);
                    }
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  <CartesianGrid horizontal={false} strokeWidth={1} className="stroke-neutral-alpha-24" />
                  <XAxis
                    type="number"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'rgb(var(--raw-soft-400))' }}
                    style={{ fontSize: '0.75rem' }}
                  />
                  <YAxis type="category" dataKey="name" tickLine={false} axisLine={false} width={0} tick={false} />
                  <RechartsTooltip
                    cursor={false}
                    formatter={(value: number, name: string) => {
                      if (name === 'spacer') return [''];
                      return [`${value} hours`];
                    }}
                    content={<CustomTooltip />}
                  />
                  <Bar
                    dataKey="regular"
                    stackId="a"
                    radius={[0, 4, 4, 0]}
                    maxBarSize={32}
                    fill="rgb(var(--raw-color-blue-300))"
                  >
                    {chartData.map((entry) => (
                      <Cell
                        key={`regular-${entry.name}`}
                        fill={
                          hoveredProject === entry.name
                            ? 'rgb(var(--raw-color-blue-200))'
                            : 'rgb(var(--raw-color-blue-300))'
                        }
                      />
                    ))}
                  </Bar>
                  <Bar dataKey="spacer" fill="transparent" stackId="a" maxBarSize={1} />
                  <Bar
                    dataKey="overtime"
                    stackId="a"
                    radius={[4, 4, 4, 4]}
                    maxBarSize={32}
                    fill="rgb(var(--raw-color-blue-500))"
                  >
                    {chartData.map((entry) => (
                      <Cell
                        key={`overtime-${entry.name}`}
                        fill={
                          hoveredProject === entry.name
                            ? 'rgb(var(--raw-color-blue-400))'
                            : 'rgb(var(--raw-color-blue-500))'
                        }
                      />
                    ))}
                  </Bar>
                  <Bar dataKey="spacer" fill="transparent" stackId="a" maxBarSize={1} />
                  <Bar
                    dataKey="doubleOvertime"
                    stackId="a"
                    radius={[4, 4, 4, 4]}
                    maxBarSize={32}
                    fill="rgb(var(--raw-color-blue-800))"
                  >
                    {chartData.map((entry) => (
                      <Cell
                        key={`doubleOvertime-${entry.name}`}
                        fill={
                          hoveredProject === entry.name
                            ? 'rgb(var(--raw-color-blue-700))'
                            : 'rgb(var(--raw-color-blue-800))'
                        }
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-2 flex items-center gap-4 text-xs">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full" style={{ background: 'rgb(var(--raw-color-blue-300))' }} />
                <span className="text-sub-600">Regular</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full" style={{ background: 'rgb(var(--raw-color-blue-500))' }} />
                <span className="text-sub-600">Overtime</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full" style={{ background: 'rgb(var(--raw-color-blue-800))' }} />
                <span className="text-sub-600">Double Overtime</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function CustomTooltip({ active, payload, label }: TooltipProps<number, string>) {
  if (!active || !payload?.length) return null;

  const regularHours = payload.find((p: any) => p.name === 'regular')?.value || 0;
  const overtimeHours = payload.find((p: any) => p.name === 'overtime')?.value || 0;
  const doubleOvertimeHours = payload.find((p: any) => p.name === 'doubleOvertime')?.value || 0;

  return (
    <div className="min-w-44 rounded-12 bg-strong-950 p-3 shadow-sm">
      <h4 className="text-sm font-medium text-white-0">{label}</h4>
      {regularHours > 0 && (
        <p className="mt-1 flex h-fit gap-1 text-xs text-soft-400">
          <span className="flex size-4 items-center justify-center">
            <span className="size-3 rounded-full border-2 border-white-0 bg-blue-300"></span>
          </span>
          Regular: {regularHours} hours
        </p>
      )}
      {overtimeHours > 0 && (
        <p className="mt-1 flex h-fit gap-1 text-xs text-soft-400">
          <span className="flex size-4 items-center justify-center">
            <span className="size-3 rounded-full border-2 border-white-0 bg-blue-500"></span>
          </span>
          Overtime: {overtimeHours} hours
        </p>
      )}
      {doubleOvertimeHours > 0 && (
        <p className="mt-1 flex h-fit gap-1 text-xs text-soft-400">
          <span className="flex size-4 items-center justify-center">
            <span className="size-3 rounded-full border-2 border-white-0 bg-blue-800"></span>
          </span>
          Double Overtime: {doubleOvertimeHours} hours
        </p>
      )}
    </div>
  );
}
