import Link from 'next/link';
import { useCompany } from '@/hooks/useCompany';
import Button from '@hammr-ui/components/button';
import { RiUserLine, RiBillLine, RiFolderOpenLine, RiMegaphoneLine } from '@remixicon/react';
import { cn } from '@hammr-ui/lib/utils';

export default function QuickActions({ className }: { className?: string }) {
  const { company } = useCompany();

  return (
    <div className={cn('grid grid-cols-1 gap-3 md:grid-cols-2 lg:flex lg:flex-row', className)}>
      <Link href="/people?modal=add">
        <Button className="w-full lg:flex-1" color="neutral" variant="stroke" beforeContent={<RiUserLine />}>
          Add Employee
        </Button>
      </Link>
      <Link href="/timesheets?modal=add">
        <Button className="w-full lg:flex-1" color="neutral" variant="stroke" beforeContent={<RiBillLine />}>
          Add Timesheet
        </Button>
      </Link>
      <Link href="/projects?modal=add">
        <Button className="w-full lg:flex-1" color="neutral" variant="stroke" beforeContent={<RiFolderOpenLine />}>
          Create Project
        </Button>
      </Link>
      {company?.isMessagingEnabled && (
        <Link href="/chat?modal=broadcast">
          <Button className="w-full lg:flex-1" color="neutral" variant="stroke" beforeContent={<RiMegaphoneLine />}>
            Broadcast Message
          </Button>
        </Link>
      )}
    </div>
  );
}
