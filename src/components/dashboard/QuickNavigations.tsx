import { LinkButton } from '@/hammr-ui/components/LinkButton';
import Link from 'next/link';

const QuickNavigations: React.FC = () => {
  return (
    <>
      <h2 className="mt-10 text-xl font-medium text-strong-950">Quick navigation</h2>

      <div className="mt-5 grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6">
        <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
          <h2 className="font-medium text-strong-950">Latest Payroll</h2>
          <p className="mt-1.5 h-fit text-sm text-sub-600">
            View and run your latest payroll to ensure your employees get paid on time.
          </p>
          <LinkButton style="primary" size="medium" className="mt-4">
            <Link href="/payroll">Run Payroll</Link>
          </LinkButton>
        </section>

        <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
          <h2 className="font-medium text-strong-950">View Reports</h2>
          <p className="mt-1.5 h-fit text-sm text-sub-600">
            Get an outlook of your preview payrolls with your historical reports.
          </p>
          <LinkButton style="primary" size="medium" className="mt-4">
            <Link href="/reports">Go To Reports</Link>
          </LinkButton>
        </section>

        <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
          <h2 className="font-medium text-strong-950">Company Information</h2>
          <p className="mt-1.5 h-fit text-sm text-sub-600">Add or update any information related to your company.</p>
          <LinkButton style="primary" size="medium" className="mt-4">
            <Link href="/company-details">View Company Information</Link>
          </LinkButton>
        </section>

        <section className="rounded-16 border border-soft-200 p-5 shadow-xs">
          <h2 className="font-medium text-strong-950">View My Employees</h2>
          <p className="mt-1.5 h-fit text-sm text-sub-600">
            Add or edit your employees to keep their information up-to-date.
          </p>
          <LinkButton style="primary" size="medium" className="mt-4">
            <Link href="/people">Manage Employees</Link>
          </LinkButton>
        </section>
      </div>
    </>
  );
};

export default QuickNavigations;
