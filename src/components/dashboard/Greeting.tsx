const Greeting: React.FC = () => {
  const today = new Date();
  const currHr = today.getHours();

  if (currHr > 0 && currHr <= 12) {
    return <h1 className="text-2xl font-medium text-strong-950">Good morning!</h1>;
  } else if (currHr > 12 && currHr <= 17) {
    return <h1 className="text-2xl font-medium text-strong-950">Good afternoon!</h1>;
  } else {
    return <h1 className="text-2xl font-medium text-strong-950">Good evening!</h1>;
  }
};

export default Greeting;
