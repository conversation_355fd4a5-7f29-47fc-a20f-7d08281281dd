import { useState } from 'react';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef, ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { Profile } from '@/components/people/sections/ProfileImage';
import { useQuery } from '@tanstack/react-query';
import { userService } from '@/services/user';
import { Card, CardContent, CardHeader, CardTitle } from '@/hammr-ui/components/card';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { cn } from '@hammr-ui/lib/cn';
import { Input } from '@hammr-ui/components/input';
import { RiSearch2Line } from '@remixicon/react';
import { TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { HammrUser } from '@/interfaces/user';
import { UserTimesheet } from '@/interfaces/timesheet';
import dayjs from 'dayjs';
import EmptyStateTimeTracker from '@hammr-icons/EmptyStateTimeTracker';
import { useRouter } from 'next/router';

type User = HammrUser & {
  timesheet?: UserTimesheet;
};

function userIsOnBreak(timesheet?: UserTimesheet) {
  return timesheet?.breaks.some((item) => !item.end);
}

export default function FieldActivity({ className }: { className?: string }) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'clockIn' | 'clockOut' | 'break'>('clockIn');

  const users = useQuery<Record<typeof activeTab, User[]>>({
    queryKey: ['dashboard', 'users'],
    queryFn: () =>
      userService
        .list<User>({
          includeIsArchived: 'false',
          simple: false,
        })
        .then((users) => {
          return {
            clockIn: users.filter(
              (user) => user.timesheet?.clockIn && !user.timesheet?.clockOut && !userIsOnBreak(user.timesheet)
            ),
            clockOut: users.filter((user) => !user.timesheet),
            break: users.filter((user) => userIsOnBreak(user.timesheet)),
          };
        }),
  });

  const filteredUsers =
    users.data?.[activeTab]?.filter(
      (user) => (user.firstName + ' ' + user.lastName).toLowerCase().includes(searchTerm.toLowerCase())
      // user.earningRates.some((earningRate) => earningRate.period === 'HOURLY' && earningRate.active)
    ) ?? [];

  const colDefs: ColDef[] = [
    {
      headerName: 'Employee',
      field: 'name',
      cellRenderer: (params: ValueFormatterParams<User>) => (
        <span className="flex items-center gap-3">
          <Profile user={params.data} />
          {params.data.firstName} {params.data.lastName}
        </span>
      ),
    },
    {
      headerName: 'Position',
      field: 'position',
      cellRenderer: (params: ICellRendererParams<User>) => <span className="text-sub-600">{params.data.position}</span>,
    },
  ];

  if (activeTab === 'clockIn' || activeTab === 'break') {
    colDefs.push({
      headerName: 'Project',
      cellRenderer: (params: ICellRendererParams<User>) => (
        <span className="text-sub-600">{params.data.timesheet?.project.name}</span>
      ),
    });
  }

  if (activeTab === 'clockIn') {
    colDefs.push({
      headerName: 'Clock In Time',
      valueFormatter: (params: ValueFormatterParams<User>) =>
        params.data.timesheet?.clockIn ? dayjs(params.data.timesheet.clockIn).format('h:mm A') : undefined,
    });
  }

  if (activeTab === 'break') {
    colDefs.push({
      headerName: 'On Break Since',
      valueFormatter: (params: ValueFormatterParams<User>) => {
        const activeBreak = params.data.timesheet?.breaks.find((item) => !item.end);
        return activeBreak ? dayjs(activeBreak?.start).format('h:mm A') : undefined;
      },
    });
  }

  const oneDay = 3600 * 24 * 1000;

  return (
    <>
      <Card className={cn('relative flex h-[400px] flex-col overflow-x-auto', className)}>
        <CardHeader className="flex-none px-4 pb-[18px]">
          <CardTitle className="flex items-center justify-between gap-2 overflow-x-auto whitespace-nowrap">
            <div>Field Activity</div>
            <Input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              beforeContent={<RiSearch2Line className="size-5" />}
              boxSize="small"
              className="ml-auto min-w-32"
            />
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as typeof activeTab)}>
              <TabList>
                <TabItem value="clockIn">Clocked In ({users.data?.clockIn.length})</TabItem>
                <TabItem value="clockOut">Clocked Out ({users.data?.clockOut.length})</TabItem>
                <TabItem value="break">On Break ({users.data?.break.length})</TabItem>
              </TabList>
            </Tabs>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 px-4 pb-1 pt-0">
          {users.isPending ? (
            <div className="mt-8 flex justify-center">
              <LoadingIndicator />
            </div>
          ) : (
            <div className="h-full">
              {filteredUsers.length ? (
                <UpdatedTable<User>
                  colDefs={colDefs}
                  rowData={filteredUsers}
                  emptyRowsText="No users to show"
                  onRowClicked={(event) => {
                    if (activeTab === 'clockOut') {
                      router.push(`/people/employee/${event.data.id}`);
                    } else {
                      router.push(
                        `/timesheets?timesheetId=${event.data.timesheet.id}&from=${event.data.timesheet.clockIn}&to=${event.data.timesheet.clockOut ?? event.data.timesheet.clockIn + oneDay}`
                      );
                    }
                  }}
                  tableProps={{
                    domLayout: 'normal',
                  }}
                  defaultColDef={{
                    sortable: true,
                    resizable: true,
                  }}
                  parentContainerClassName="h-full"
                />
              ) : (
                <div className="flex h-full flex-col items-center justify-center gap-5 text-sm text-sub-600">
                  <EmptyStateTimeTracker />
                  <div className="text-center text-sm font-normal text-soft-400">
                    {activeTab === 'clockIn' && 'There are no employees clocked in.'}
                    {activeTab === 'clockOut' && 'There are no employees clocked out.'}
                    {activeTab === 'break' && 'There are no employees on a break.'}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
