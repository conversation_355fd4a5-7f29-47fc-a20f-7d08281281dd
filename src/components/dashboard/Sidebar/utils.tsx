import { Company } from 'interfaces/company';
import { User } from 'interfaces/user';
import { MenuGroupItem, MenuItem, isMenuGroupitem, isMenuItem } from './types';
import {
  RiCalendarLine,
  RiHome5Line,
  RiChat3Line,
  RiFolderOpenLine,
  RiWallet3Line,
  RiImage2Line,
  RiFileLine,
  RiTable2,
  RiGroupLine,
  RiUserLine,
  RiTeamLine,
  RiBillLine,
  RiFlashlightLine,
  RiHeart3Line,
  RiLineChartLine,
  RiBuildingLine,
  RiFileDownloadLine,
  RiBriefcase4Line,
  RiSettings2Line,
  RiMoneyDollarBoxLine,
  RiCalendar2Line,
  RiPieChartBoxLine,
  RiBuilding4Line,
  RiFirstAidKitLine,
  RiToolsFill,
  RiMap2Line,
  RiDatabase2Line,
} from '@remixicon/react';

export function hasPermission(item: MenuItem | MenuGroupItem, company: Company, user: User) {
  if (item.hiddenFn && item.hiddenFn(company, user)) {
    // special condition to hide menu item
    return false;
  }

  if (!item.visibleToRoles.includes(user.role)) {
    return false;
  }

  if (!item.workerClassifications.includes(user.workerClassification)) {
    return false;
  }

  if (item.isPayrollOnlyFeature && !company?.isPayrollEnabled) {
    // payroll feature for payroll enabled companies only
    return false;
  }

  return true;
}

export function getMenuItems(company: Company | undefined) {
  const items: Array<MenuItem | MenuGroupItem> = [];

  if (!company) {
    return;
  }

  items.push({
    url: '/dashboard',
    text: 'Dashboard',
    icon: <RiHome5Line className="size-5" />,
    isPayrollOnlyFeature: false,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
  });

  items.push({
    url: '/chat',
    text: 'Chat',
    icon: <RiChat3Line className="size-5" />,
    isPayrollOnlyFeature: false,
    visibleToRoles: ['ADMIN', 'FOREMAN', 'WORKER'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
    hiddenFn: (company) => !company?.isMessagingEnabled,
  });

  items.push({
    text: 'Field',
    icon: <RiMap2Line className="size-5" />,
    isPayrollOnlyFeature: false,
    visibleToRoles: ['ADMIN', 'FOREMAN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
    items: [
      {
        url: '/projects',
        text: 'Projects',
        icon: <RiFolderOpenLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/timesheets',
        text: 'Timesheets',
        icon: <RiBillLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN', 'FOREMAN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/scheduling',
        text: 'Scheduling',
        icon: <RiCalendarLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN', 'FOREMAN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
        hiddenFn: (company) => !company?.isSchedulingEnabled,
      },
      {
        url: '/project-photos',
        text: 'Photos',
        icon: <RiImage2Line className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN', 'FOREMAN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/project-documents',
        text: 'Documents',
        icon: <RiFileLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN', 'FOREMAN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/equipment',
        text: 'Equipment',
        icon: <RiToolsFill className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN', 'FOREMAN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
        hiddenFn: (company) => !company?.isEquipmentTrackingEnabled,
      },
      {
        url: '/daily-reports',
        text: 'Daily Reports',
        icon: <RiPieChartBoxLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN', 'FOREMAN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/injury-reports',
        text: 'Injury Reports',
        icon: <RiFirstAidKitLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
    ],
  });

  items.push({
    text: 'HR',
    icon: <RiGroupLine className="size-5" />,
    isPayrollOnlyFeature: false,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
    items: [
      {
        url: '/people',
        text: 'People',
        icon: <RiUserLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/benefits',
        text: 'Benefits',
        icon: <RiHeart3Line className="size-5" />,
        isPayrollOnlyFeature: true,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/time-off',
        text: 'Time Off',
        icon: <RiCalendar2Line className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
    ],
  });

  items.push({
    url: '/payroll',
    text: 'Payroll',
    icon: <RiFlashlightLine className="size-5" />,
    isPayrollOnlyFeature: true,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
  });

  items.push({
    url: '/reports',
    text: 'Reports',
    icon: <RiLineChartLine className="size-5" />,
    isPayrollOnlyFeature: true,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
  });

  items.push({
    text: 'Setup',
    icon: <RiDatabase2Line className="size-5" />,
    isPayrollOnlyFeature: false,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
    items: [
      {
        url: '/wage-tables',
        text: 'Wage Tables',
        icon: <RiTable2 className="size-5" />,
        isPayrollOnlyFeature: true,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/departments',
        text: 'Departments',
        icon: <RiBuilding4Line className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/crews',
        text: 'Crews',
        icon: <RiTeamLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/cost-codes',
        text: 'Cost Codes',
        icon: <RiWallet3Line className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/workers-comp-codes',
        text: 'Workers Comp',
        icon: <RiMoneyDollarBoxLine className="size-5" />,
        isPayrollOnlyFeature: false,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
    ],
  });

  items.push({
    text: 'Company',
    icon: <RiBuildingLine className="size-5" />,
    isPayrollOnlyFeature: true,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
    items: [
      {
        url: '/documents',
        text: 'Documents',
        icon: <RiFileDownloadLine className="size-5" />,
        isPayrollOnlyFeature: true,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
      {
        url: '/company-details',
        text: 'Company Details',
        icon: <RiBriefcase4Line className="size-5" />,
        isPayrollOnlyFeature: true,
        visibleToRoles: ['ADMIN'],
        workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
      },
    ],
  });

  items.push({
    url: '/settings',
    text: 'Settings',
    icon: <RiSettings2Line className="size-5" />,
    isPayrollOnlyFeature: false,
    visibleToRoles: ['ADMIN'],
    workerClassifications: ['EMPLOYEE', 'CONTRACTOR'],
  });

  items.push({
    url: '/paystubs',
    text: 'Paystubs',
    icon: <RiMoneyDollarBoxLine className="size-5" />,
    isPayrollOnlyFeature: true,
    visibleToRoles: ['FOREMAN', 'WORKER'],
    workerClassifications: ['EMPLOYEE'],
  });

  items.push({
    url: '/payments',
    text: 'Payments',
    icon: <RiMoneyDollarBoxLine className="size-5" />,
    isPayrollOnlyFeature: true,
    visibleToRoles: ['FOREMAN', 'WORKER'],
    workerClassifications: ['CONTRACTOR'],
  });

  return items;
}

export function filterMenuItems(
  items: Array<MenuItem | MenuGroupItem>,
  company: Company | undefined,
  user: User | undefined
) {
  if (!company || !user) {
    return [];
  }

  return items
    .filter((item) => hasPermission(item, company, user))
    .map((item) => {
      if (!isMenuGroupitem(item)) {
        return item;
      }

      return {
        ...item,
        items: item.items.filter((item) => hasPermission(item, company, user)),
      };
    })
    .filter((item) => isMenuItem(item) || item.items.length > 0);
}
