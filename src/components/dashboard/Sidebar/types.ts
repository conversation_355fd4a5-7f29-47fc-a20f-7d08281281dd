import { Company } from 'interfaces/company';
import { User } from 'interfaces/user';

interface MenuItemBase {
  text: string;
  icon: JSX.Element;
  isPayrollOnlyFeature?: boolean;
  visibleToRoles: ('ADMIN' | 'FOREMAN' | 'WORKER')[];
  /** employment types for which the menu item should show up */
  workerClassifications: ('EMPLOYEE' | 'CONTRACTOR')[];
  hiddenFn?: (company: Company, user: User) => boolean;
}

export interface MenuItem extends MenuItemBase {
  url: string;
}

export interface MenuGroupItem extends MenuItemBase {
  items: MenuItem[];
}

export function isMenuItem(item: MenuItem | MenuGroupItem): item is MenuItem {
  return (item as MenuItem).url !== undefined;
}

export function isMenuGroupitem(item: MenuItem | MenuGroupItem): item is MenuGroupItem {
  return (item as MenuGroupItem).items !== undefined;
}
