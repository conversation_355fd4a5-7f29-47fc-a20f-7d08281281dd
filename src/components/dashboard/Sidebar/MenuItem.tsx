import { FC } from 'react';
import Link from 'next/link';
import classNames from 'classnames';
import { useRouter } from 'next/router';

import { MenuGroupItem, MenuItem, isMenuGroupitem } from './types';
import { cn } from '@/hammr-ui/lib/utils';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';

interface SidebarMenuGroupItemProps {
  item: MenuGroupItem;
  expanded?: boolean;
  onToggleExpand?: (state: boolean) => void;
}

export const SidebarMenuGroupItem: FC<SidebarMenuGroupItemProps> = ({
  item,
  expanded,
  onToggleExpand,
}) => {
  return (
    <div>
      <button
        type="button"
        className="group flex w-full items-center gap-2 rounded-lg px-3 py-2 text-left text-sm text-sub-600 hover:bg-weak-50"
        onClick={() => onToggleExpand?.(!expanded)}
      >
        {item.icon}
        <span className="flex-1">{item.text}</span>
        <ArrowDownSLine
          className={`size-5 text-soft-400 ${expanded ? 'rotate-180' : ''}`}
        />
      </button>
      {expanded && (
        <div className="mt-1 space-y-1">
          {item.items.map((item, index) => (
            <SidebarMenuItem key={index} item={item} isNestedItem />
          ))}
        </div>
      )}
    </div>
  );
};

interface SidebarMenuItemProps {
  item: MenuItem | MenuGroupItem;
  isNestedItem?: boolean;
  expanded?: boolean;
  onToggleExpand?: (state: boolean) => void;
}

export const SidebarMenuItem: FC<SidebarMenuItemProps> = ({
  item,
  isNestedItem,
  expanded,
  onToggleExpand,
}) => {
  const { pathname } = useRouter();

  if (isMenuGroupitem(item)) {
    if (isNestedItem) {
      // support only one level of nesting
      return null;
    }

    return (
      <SidebarMenuGroupItem
        item={item}
        expanded={expanded}
        onToggleExpand={onToggleExpand}
      />
    );
  }

  if (item?.url?.startsWith('/')) {
    const isActive = pathname.startsWith(item.url);
    return (
      <Link href={item.url}>
        <a
          className={cn(
            'group relative flex items-center gap-2 rounded-lg px-3 py-2 text-sm hover:bg-weak-50',
            { 'bg-primary-base/10 text-primary-base': isActive },
            { 'pl-8': isNestedItem },
            { 'text-sub-600': !isActive }
          )}
        >
          {isActive && (
            <span className="absolute -left-5 top-1/2 h-5 w-1 -translate-y-1/2 rounded-r bg-primary-base"></span>
          )}
          {item.icon}
          <span className={cn({ 'text-strong-950': isActive })}>
            {item.text}
          </span>
        </a>
      </Link>
    );
  } else {
    return (
      <a
        href={item.url}
        target="_blank"
        rel="noopener noreferrer"
        className={classNames(
          'group flex items-center gap-2 rounded-lg px-3 py-2 text-sm hover:bg-weak-50',
          isNestedItem && 'pl-8'
        )}
      >
        {item.icon}
        <span>{item.text}</span>
      </a>
    );
  }
};
