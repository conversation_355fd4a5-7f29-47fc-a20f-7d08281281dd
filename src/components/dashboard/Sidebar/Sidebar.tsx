import { ReactNode, useRef, useState, useMemo, useEffect } from 'react';

import Link from 'next/link';
import Image from 'next/image';
import { NextPage } from 'next';

import { useAuth } from 'hooks/useAuth';
import { useToast } from 'hooks/useToast';
import { useCompany } from 'hooks/useCompany';
import { useOnClickOutside } from 'hooks/useClickOutside';
import { logError, showErrorToast } from 'utils/errorHandling';
import { filterMenuItems, getMenuItems } from './utils';
import { SidebarMenuItem } from './MenuItem';
import { isMenuItem } from './types';
import { useRouter } from 'next/router';
import classNames from 'classnames';
import { ScrollArea } from '@/hammr-ui/components/scroll-area';
import LogoutBoxRLine from '@/hammr-icons/LogoutBoxRLine';
import CloseLine from '@/hammr-icons/CloseLine';
import { cn } from '@/hammr-ui/lib/utils';
import Button from '@/hammr-ui/components/button';

interface Props {
  children: ReactNode;
  noPadding?: boolean;
}

const Sidebar: NextPage<Props> = ({ children, noPadding }) => {
  const { addToast } = useToast();
  const { company } = useCompany();
  const auth = useAuth();
  const { user } = useAuth();
  const ref = useRef();
  const { pathname } = useRouter();

  const [sidebarOpen, setSidebarOpen] = useState(false);

  useOnClickOutside(ref, () => setSidebarOpen(false));

  const sidebarMenuItems = useMemo(() => filterMenuItems(getMenuItems(company), company, user), [company, user]);

  const defaultExpandedIndex = useMemo(() => {
    return (
      sidebarMenuItems
        .map((item, index) => {
          if (isMenuItem(item)) {
            return -1;
          }

          if (item.items.some((item) => pathname.startsWith(item.url))) {
            return index;
          }

          return -1;
        })
        .find((index) => index !== -1) ?? null
    );
  }, [pathname, sidebarMenuItems]);

  const [expandedIndex, setExpandedIndex] = useState<number | null>(defaultExpandedIndex);

  useEffect(() => {
    setExpandedIndex(defaultExpandedIndex);
  }, [defaultExpandedIndex]);

  if (!auth.user) return null;

  const signOut = () => {
    auth
      .signOut()
      .then(() =>
        addToast({
          title: 'Until next time!👋',
          description: 'You are successfully signed out.',
          type: 'success',
        })
      )
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  };

  return (
    <div className="flex h-screen overflow-hidden bg-gray-100">
      <div
        className={cn(
          'font-medium md:static md:block',
          { 'fixed inset-0 z-40 flex': sidebarOpen },
          { hidden: !sidebarOpen }
        )}
      >
        <div className="flex h-full w-[17rem] flex-col border-r border-soft-200 bg-white-0">
          <header className="px-6">
            <Link
              href={
                company?.isPayrollEnabled && user.role === 'ADMIN'
                  ? '/dashboard'
                  : user.role === 'FOREMAN'
                    ? '/timesheets'
                    : '/paystubs'
              }
            >
              <a className="max-h-5p flex py-6">
                <Image width={151} height={40} className="" src="/img/hammr-logo.png" alt="Hammr Inc." />
              </a>
            </Link>
          </header>
          <hr className="mx-auto w-[14.5rem] border-soft-200" />
          <nav className="flex-grow overflow-hidden">
            <ScrollArea className="h-full">
              <div className="space-y-1 p-5">
                {sidebarMenuItems.map((item, index) => (
                  <SidebarMenuItem
                    key={index}
                    item={item}
                    expanded={expandedIndex === index}
                    onToggleExpand={(state) => (state ? setExpandedIndex(index) : setExpandedIndex(null))}
                  />
                ))}
              </div>
            </ScrollArea>
          </nav>
          <hr className="mx-auto w-[14.5rem] border-soft-200" />
          <div className="p-3">
            <div className="flex items-center justify-between rounded-10 px-3 py-3.5 hover:bg-weak-50">
              <span className="text-sm text-strong-950">{user?.name}</span>
              <a href="/" className="p-0.5 text-sub-600" onClick={() => signOut()}>
                <LogoutBoxRLine className="size-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="flex-grow bg-white-0/75 md:hidden" onClick={() => setSidebarOpen(false)}>
          <button
            className="ml-1 flex h-10 w-10 items-center justify-center rounded-full text-strong-950 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
            onClick={() => setSidebarOpen(false)}
          >
            <span className="sr-only">Close sidebar</span>
            <CloseLine />
          </button>
        </div>
      </div>

      <header className="sticky flex w-0 flex-1 flex-grow flex-col overflow-hidden">
        <div className="flex items-center justify-between border-b border-soft-200 bg-white-0 p-3 md:hidden">
          <Button
            className="p-0"
            variant="ghost"
            size="2x-small"
            aria-label="Open sidebar"
            onClick={() => setSidebarOpen(true)}
          >
            <svg
              className="h-6 w-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </Button>
          <Link href={company?.isPayrollEnabled ? '/dashboard' : '/timesheets'}>
            <a href={company?.isPayrollEnabled ? '/dashboard' : '/timesheets'}>
              <Image width={91} height={24} className="h-6 w-auto" src="/img/hammr-logo.png" alt="Hammr Inc." />
            </a>
          </Link>
        </div>

        <div className="relative z-0 flex flex-grow flex-col overflow-y-auto bg-background">
          <main className={classNames('flex flex-grow flex-col', { 'px-8 py-5': !noPadding })}>{children}</main>
        </div>
      </header>
    </div>
  );
};

export default Sidebar;
