import * as React from 'react';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@hammr-ui/components/card';
import { cn } from '@hammr-ui/lib/utils';
import Button from '@hammr-ui/components/button';
import { RiExpandDiagonalLine, RiRoadMapLine } from '@remixicon/react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { HammrUser } from '@/interfaces/user';
import { UserTimesheet } from '@/interfaces/timesheet';
import dayjs from 'dayjs';
import { Dialog, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import MapComponent, { type Location } from '@/hammr-ui/components/Map';

type UserWithLocation = HammrUser & { timesheets: UserTimesheet[] };

export default function RealTimeMap({ className, onHide }: { className?: string; onHide?: () => void }) {
  const [isExpanded, setIsExpanded] = useState(false);

  const usersLocations = useQuery({
    queryKey: ['dashboard', 'map', 'users', 'locations'],
    queryFn: () =>
      apiRequest<{
        users: UserWithLocation[];
      }>(`users/locations`).then((response) => response.users),
  });

  useEffect(() => {
    if (!usersLocations.isLoading && !usersLocations.data?.length) {
      onHide();
    }
  }, [onHide, usersLocations.data, usersLocations.isLoading]);

  if (!usersLocations.isLoading && !usersLocations.data?.length) {
    return undefined;
  }

  return (
    <>
      <Card className={cn('relative flex h-full flex-col overflow-hidden', className)}>
        <CardHeader className="px-4 pb-0">
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">Real-Time Employee Locations</div>
              <Button
                color="neutral"
                variant="stroke"
                size="x-small"
                className="px-px"
                onClick={() => setIsExpanded(true)}
              >
                <RiExpandDiagonalLine className="h-5 w-5" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="h-64 p-4 py-3.5 xl:h-full">
          {usersLocations.data && (
            <EmployeeLocationMap usersLocations={usersLocations.data} className="rounded-[10px]" />
          )}
        </CardContent>
      </Card>

      <Dialog open={isExpanded} onOpenChange={setIsExpanded}>
        <DialogSurface size="7xl" className="h-[90vh]">
          <DialogHeader title="Real-Time Employee Locations" showCloseButton={true} icon={<RiRoadMapLine />} />
          {usersLocations.data && <EmployeeLocationMap usersLocations={usersLocations.data} className="h-full" />}
        </DialogSurface>
      </Dialog>
    </>
  );
}

interface EmployeeLocationMapProps {
  usersLocations: UserWithLocation[];
  className?: string;
}

function EmployeeLocationMap({ usersLocations, className }: EmployeeLocationMapProps) {
  const allLocations = usersLocations.flatMap((user) =>
    user.timesheets.flatMap((timesheet) => {
      const locations: Location[] = [];
      timesheet.userLocations?.forEach((location) => {
        if (typeof location.locationCoordinates === 'object' && !Array.isArray(location.locationCoordinates)) {
          locations.push({
            lng: location.locationCoordinates.coordinates[0],
            lat: location.locationCoordinates.coordinates[1],
            markerInfo: {
              title: `${user.firstName} ${user.lastName}`,
              description: (
                <>
                  {location.project?.name || location.locationAddress}
                  <br />
                  Clock In: {dayjs(timesheet.clockIn).format('h:mm A')}
                </>
              ),
            },
          });
        }
      });
      return locations;
    })
  );

  return <MapComponent locations={allLocations} className={className} />;
}
