import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { cn } from '@/utils/cn';
import { Card, CardContent, CardHeader, CardTitle } from '@hammr-ui/components/card';
import ContentCard from '@hammr-ui/components/ContentCard';
import { ReactNode, useState } from 'react';
import { KeyIcon } from '@hammr-ui/components/KeyIcon';
import { Badge } from '@hammr-ui/components/badge';
import {
  RiCalendar2Line,
  RiFlashlightLine,
  RiBillLine,
  RiAlertLine,
  RiArrowRightLine,
  RiAwardLine,
  RiArrowRightSLine,
  RiFirstAidKitLine,
} from '@remixicon/react';
import CompactButton from '@hammr-ui/components/CompactButton';
import { apiRequest } from '@/utils/requestHelpers';
import { employeeCertifications } from '@/services/employee-certifications';
import { Dialog, DialogBody, DialogHeader, DialogSurface } from '@hammr-ui/components/dialog';
import QuickActionItem from '@hammr-ui/components/QuickActionItem';
import { EmployeeCertification } from '@/interfaces/employee-certifications';
import { Profile } from '@/components/people/sections/ProfileImage';
import { listPayrolls } from '@/services/payroll';
import { useCompany } from '@/hooks/useCompany';
import { PaginatedData } from '@/interfaces/pagination';
import { TimeOffRequest } from '@/interfaces/timeoff';
import dayjs from 'dayjs';
import { InjuryReport } from '@/interfaces/injury-report';
import Link from 'next/link';

interface NotificationItem {
  title: string;
  isLoading: boolean;
  count?: number;
  hidden?: boolean;
  icon: ReactNode;
  status?: string;
  isActive: boolean;
  onClick: () => void;
}

export default function Notifications({ className }: { className?: string }) {
  const { company } = useCompany();
  const [showCertifications, setShowCertifications] = useState(false);
  const router = useRouter();

  const timeOffRequests = useQuery({
    queryKey: ['dashboard', 'notifications', 'time-off-requests'],
    queryFn: () => apiRequest<PaginatedData<TimeOffRequest, 'timeOffRequests'>>(`time-off-requests?status=PENDING`),
  });

  const notifications = useQuery({
    queryKey: ['dashboard', 'notifications'],
    queryFn: () =>
      apiRequest<{
        unapprovedTimesheets: {
          count: number;
          from: number;
          to: number;
        };
        unresolvedTimesheets: {
          count: number;
          from: number;
          to: number;
        };
      }>(`dashboard/notifications`),
  });

  const expiredCertificates = useQuery({
    queryKey: ['dashboard', 'notifications', 'certifications'],
    queryFn: () => employeeCertifications.list({ expiredOrExpiring: true }),
  });

  const draftPayroll = useQuery({
    queryKey: ['dashboard', 'notifications', 'payroll'],
    queryFn: () => listPayrolls({ companyId: company.checkCompanyId, status: 'draft' }),
    enabled: !!company?.isPayrollEnabled,
  });

  const injuryReports = useQuery({
    queryKey: ['dashboard', 'notifications', 'injury-reports'],
    queryFn: () => apiRequest<InjuryReport[]>(`injury-reports/list?isResolved=false&limit=100`),
  });

  const notificationsList: NotificationItem[] = [
    {
      title: 'Time Off Requests',
      icon: <RiCalendar2Line />,
      status: 'Pending',
      count: timeOffRequests.data?.total,
      isLoading: timeOffRequests.isLoading,
      isActive: timeOffRequests.data?.total > 0,
      onClick: () => router.push('/time-off?tab=requests'),
    },
    {
      title: 'Expired Certificates',
      icon: <RiAwardLine />,
      status: 'expired',
      count: expiredCertificates.data?.length ?? 0,
      isLoading: expiredCertificates.isLoading,
      isActive: (expiredCertificates.data?.length ?? 0) > 0,
      onClick: () => setShowCertifications(true),
    },
    {
      title: 'Draft Payroll',
      icon: <RiFlashlightLine />,
      isLoading: draftPayroll.isLoading,
      hidden: !company?.isPayrollEnabled,
      isActive: (draftPayroll.data?.results.length ?? 0) > 0,
      onClick: () => router.push('/payroll'),
    },
    {
      title: 'Timesheets',
      icon: <RiBillLine />,
      status: 'unapproved',
      count: notifications.data?.unapprovedTimesheets.count,
      isLoading: notifications.isLoading,
      isActive: notifications.data?.unapprovedTimesheets.count > 0,
      onClick: () =>
        router.push(
          `/timesheets?status=submitted&from=${notifications.data?.unapprovedTimesheets.from}&to=${notifications.data?.unapprovedTimesheets.to}`
        ),
    },
    {
      title: 'Timesheet Alerts',
      icon: <RiAlertLine />,
      status: notifications.data?.unresolvedTimesheets.count === 1 ? 'alert' : 'alerts',
      count: notifications.data?.unresolvedTimesheets.count,
      isLoading: notifications.isLoading,
      isActive: notifications.data?.unresolvedTimesheets.count > 0,
      onClick: () =>
        router.push(
          `/timesheets?isResolved=false&from=${notifications.data?.unresolvedTimesheets.from}&to=${notifications.data?.unresolvedTimesheets.to}`
        ),
    },
    {
      title: 'Injury Reports',
      icon: <RiFirstAidKitLine />,
      status: 'unresolved',
      count: injuryReports.data?.length ?? 0,
      isLoading: injuryReports.isLoading,
      isActive: (injuryReports.data?.length ?? 0) > 0,
      onClick: () => router.push('/injury-reports'),
    },
  ].filter((notification) => !notification.hidden);

  const activeCount = notificationsList.filter((n) => n.isActive).length;

  const usersWithExpiredCertifications = Object.values(
    (expiredCertificates.data ?? []).reduce(
      (group, item) => {
        const userId = item.user.id;

        if (!group[userId]) {
          group[userId] = { user: item.user, items: [] };
        }

        group[userId].items.push(item);

        return group;
      },
      {} as Record<number, { user: EmployeeCertification['user']; items: EmployeeCertification[] }>
    )
  ).sort((a, b) => {
    const aEarliestDate = a.items.reduce(
      (earliest, cert) => Math.min(earliest, new Date(cert.expirationDate).getTime()),
      new Date(a.items[0].expirationDate).getTime()
    );
    const bEarliestDate = b.items.reduce(
      (earliest, cert) => Math.min(earliest, new Date(cert.expirationDate).getTime()),
      new Date(b.items[0].expirationDate).getTime()
    );
    return aEarliestDate - bEarliestDate;
  });

  return (
    <>
      <Card className={cn('relative min-h-80 min-w-[350px]', className)}>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">Notifications</div>
              <Badge color={activeCount ? 'red' : 'gray'} variant="lighter">
                {activeCount}
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col gap-0.5 whitespace-nowrap pt-0">
          {notificationsList.map((notification, index) => (
            <ContentCard
              onClick={notification.isActive ? notification.onClick : undefined}
              key={index}
              className={cn(
                notification.isActive
                  ? 'cursor-pointer hover:rounded-xl hover:bg-soft-200/50'
                  : 'cursor-default text-soft-400'
              )}
              label={notification.title}
              icon={
                <KeyIcon
                  icon={notification.isLoading ? <LoadingIndicator size="xs" showText={false} /> : notification.icon}
                  color={notification.isActive ? `orange` : 'grey'}
                  variant="lighter"
                  size="small"
                />
              }
              badge={
                notification.count ? (
                  <Badge
                    color={notification.isActive ? 'orange' : 'gray'}
                    variant="lighter"
                    size="small"
                    className="uppercase"
                  >
                    {notification.count} {notification.status}
                  </Badge>
                ) : undefined
              }
              afterContent={
                notification.isActive ? (
                  <CompactButton>
                    <RiArrowRightLine />
                  </CompactButton>
                ) : undefined
              }
            />
          ))}
        </CardContent>
      </Card>

      <Dialog open={showCertifications} onOpenChange={setShowCertifications}>
        <DialogSurface>
          <DialogHeader icon={<RiAwardLine className="text-sub-600" />} title="Expired Certifications" />
          <DialogBody className="p-3">
            {usersWithExpiredCertifications.map((group): JSX.Element => {
              const now = new Date();
              const oldestExpirationDate = group.items.reduce((oldest, cert) => {
                const date = new Date(cert.expirationDate);
                return date < oldest ? date : oldest;
              }, new Date(group.items[0].expirationDate));

              const isExpired = oldestExpirationDate < now;
              const description = (
                <div>
                  {group.items.length} certificate{group.items.length > 1 ? 's' : ''}{' '}
                  {isExpired ? 'expired since' : 'will expire soon on'}{' '}
                  {dayjs(oldestExpirationDate).format('MMM D, YYYY')}
                </div>
              );

              return (
                <Link key={group.user.id} href={`/people/employee/${group.user.id}?tab=certifications`}>
                  <QuickActionItem
                    icon={<Profile user={group.user} className="size-10" />}
                    label={`${group.user.firstName} ${group.user.lastName}`}
                    className="cursor-pointer rounded-lg hover:bg-weak-50"
                    description={description}
                    afterContent={
                      <CompactButton>
                        <RiArrowRightSLine />
                      </CompactButton>
                    }
                  />
                </Link>
              );
            })}
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </>
  );
}
