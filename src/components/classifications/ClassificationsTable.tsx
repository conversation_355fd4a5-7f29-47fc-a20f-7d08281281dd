import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import dayjs from 'dayjs';
import { Classification, EmployeeClassification } from 'interfaces/classifications';
import { useMemo, useState } from 'react';
import { formatLocaleUsa, isItemActive } from 'utils/dateHelper';
import EditClassificationModal from './EditClassificationModal';
import ClassificationDetailsModal from './ClassificationDetailsModal';
import { Tooltip } from '@hammr-ui/components/tooltip';
import CompactButton from '@/hammr-ui/components/CompactButton';
import PencilLine from '@/hammr-icons/PencilLine';
import { RiHistoryLine } from '@remixicon/react';

const ClassificationsTable = (props: {
  wageTableId: number;
  classifications: EmployeeClassification[];
  callback: () => any;
}) => {
  const { wageTableId, classifications, callback } = props;
  const [selectedClassificationRow, setSelectedClassificationRow] = useState<Classification>(undefined);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedClassificationForDetails, setSelectedClassificationForDetails] =
    useState<EmployeeClassification>(undefined);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const mappedClassificationData: (EmployeeClassification & { isActive: boolean })[] = useMemo(() => {
    return (
      classifications
        // TODO -> this filter should ideally be applied in the backend along with validators for effective/end date
        .filter((_classification) => {
          const startDate = _classification.startDate ? dayjs(_classification.startDate) : null;
          const endDate = _classification.endDate ? dayjs(_classification.endDate) : null;

          const isStartDateSameAsEndDate = startDate && endDate && startDate.isSame(endDate);
          const isStartDateAfterEndDate = startDate && endDate && startDate.isAfter(endDate);

          return !isStartDateSameAsEndDate && !isStartDateAfterEndDate;
        })
        .map((classification) => {
          const startDateStr = classification.startDate ? dayjs(classification.startDate).toString() : '';
          const endDateStr = classification.endDate ? dayjs(classification.endDate).toString() : '';
          return {
            ...classification,
            isActive: isItemActive(startDateStr, endDateStr),
          };
        })
    );
  }, [classifications]);

  // if a user creates a classification with a start date in the future, therefore not active, we should still show it in the table so it can be edited
  const uniqueClassifications = useMemo(() => {
    const uniqueClassificationNames = new Set(mappedClassificationData.map((classification) => classification.name));

    return Array.from(uniqueClassificationNames).map((name) => {
      const allClassificationsForName = mappedClassificationData.filter(
        (classification) => classification.name === name
      );

      if (allClassificationsForName.length === 1) {
        return allClassificationsForName[0];
      }

      const activeClassification = allClassificationsForName.find((classification) => classification.isActive);

      if (activeClassification) {
        return activeClassification;
      }

      const classificationWithNoEndDate = allClassificationsForName.find((classification) => !classification.endDate);

      if (classificationWithNoEndDate) {
        return classificationWithNoEndDate;
      }

      // get the classification with the latest end date
      return allClassificationsForName.sort((a, b) => {
        if (a.endDate && b.endDate) {
          return a.endDate - b.endDate > 0 ? -1 : 1;
        }
      })[0];
    });
  }, [mappedClassificationData]);

  const colDefs: ColDef<EmployeeClassification & { isActive: boolean }>[] = [
    {
      headerName: 'Classification',
      field: 'name',
      initialSort: 'asc',
      minWidth: 250,
    },
    {
      headerName: 'Base Pay',
      field: 'basePay',
      cellRenderer: (params: ValueFormatterParams) => {
        return <div>${params.value}/hour</div>;
      },
    },
    {
      headerName: 'Fringe Pay',
      field: 'fringePay',
      cellRenderer: (params: ValueFormatterParams) => {
        return <div>${params.value}/hour</div>;
      },
    },
    {
      headerName: 'Start',
      field: 'startDate',
      maxWidth: 150,
      cellRenderer: (params: ValueFormatterParams) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      headerName: 'End',
      field: 'endDate',
      maxWidth: 150,
      cellRenderer: (params: ValueFormatterParams) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      headerName: '',
      pinned: 'right',
      cellStyle: { overflow: 'visible' },
      width: 100,
      cellRenderer: (params: ValueFormatterParams) => {
        return (
          <div className="flex h-full items-center justify-end gap-3">
            <Tooltip content="View History">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedClassificationForDetails(params.data);
                  setIsDetailsModalOpen(true);
                }}
              >
                <RiHistoryLine />
              </CompactButton>
            </Tooltip>
            <Tooltip content="Edit">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedClassificationRow(params.data);
                  setIsEditModalOpen(true);
                }}
              >
                <PencilLine />
              </CompactButton>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <UpdatedTable
        colDefs={colDefs}
        rowData={uniqueClassifications}
        hideSidebar={true}
        emptyRowsText="No classifications"
      />
      <EditClassificationModal
        isOpen={isEditModalOpen}
        setIsOpen={setIsEditModalOpen}
        callback={callback}
        wageTableId={wageTableId}
        classification={selectedClassificationRow}
      />
      <ClassificationDetailsModal
        open={isDetailsModalOpen}
        setOpen={setIsDetailsModalOpen}
        classification={selectedClassificationForDetails}
        allClassifications={mappedClassificationData}
      />
    </div>
  );
};

export default ClassificationsTable;
