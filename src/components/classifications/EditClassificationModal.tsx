import ClassificationsForm from './ClassificationsForm';
import { FormV2 } from 'components/elements/Form';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Classification } from 'interfaces/classifications';
import { updateClassification } from 'services/classifications';
import dayjs from 'dayjs';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Table2Line from '@/hammr-icons/Table2Line';

interface FormValues {
  name: string;
  basePay: string;
  fringePay: string;
  effectiveDate: Date;
  endDate?: Date | null;
}

const EditClassificationModal = ({ isOpen, setIsOpen, callback, wageTableId, classification }) => {
  const { addToast } = useToast();
  const {
    formState: { errors },
    handleSubmit,
    control,
    register,
    reset,
  } = useForm<FormValues>();
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!isOpen || !classification) return;

    reset({
      name: classification.name,
      basePay: classification.basePay,
      fringePay: classification.fringePay,
      endDate: classification.endDate || null,
      effectiveDate: null, // default to null on edit
    });
  }, [isOpen, classification, reset]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const endDate = data.endDate ? dayjs(data.endDate).toDate().getTime() : null;

    // the form has startDate as effectiveDate so we remove startDate and set effectiveDate
    const { startDate, ...rest } = data;

    const formattedPayloadData = {
      ...rest,
      effectiveDate: data.startDate ? dayjs(data.startDate).startOf('day').valueOf() : null,
      wageTableId,
      basePay: parseFloat(data.basePay).toFixed(2),
      fringePay: parseFloat(data.fringePay).toFixed(2),
    };

    if (endDate) {
      formattedPayloadData.endDate = endDate;
    }

    try {
      await updateClassification(classification.id, formattedPayloadData as any as Classification);

      addToast({
        title: 'Edited Classification',
        description: (
          <>
            Successfully edited the classification <strong>{data.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to update classification');
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setIsProcessing(false);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Edit Classification" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Save"
        >
          <ClassificationsForm {...{ register, errors, control }} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default EditClassificationModal;
