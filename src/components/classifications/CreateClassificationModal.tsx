import ClassificationsForm from './ClassificationsForm';
import { FormV2 } from 'components/elements/Form';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import { Classification } from 'interfaces/classifications';
import { createClassification } from 'services/classifications';
import dayjs from 'dayjs';
import { FringeBenefit } from 'interfaces/fringes';
import { getFringeClassifications } from 'services/fringes';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Table2Line from '@/hammr-icons/Table2Line';

const CreateClassificationModal = ({ isOpen, setIsOpen, callback, wageTableId }) => {
  const { addToast } = useToast();
  const {
    formState: { errors },
    handleSubmit,
    control,
    register,
    reset,
  } = useForm();
  const [isProcessing, setIsProcessing] = useState(false);
  const [fringes, setFringes] = useState<FringeBenefit[]>([]);

  useEffect(() => {
    const fetchFringes = async () => {
      const fringes = await getFringeClassifications({ wageTableId });

      const fringesIdSet = new Set<number>([]);
      fringes.fringeBenefitClassifications.forEach((fringeBenefit) => {
        fringesIdSet.add(fringeBenefit.fringeBenefit.id);
      });

      const fringesArray = Array.from(fringesIdSet).map((id) => {
        return fringes.fringeBenefitClassifications.find((fringe) => fringe.fringeBenefit.id === id).fringeBenefit;
      });

      setFringes(fringesArray);
    };

    if (isOpen) {
      fetchFringes();
    }
  }, [isOpen]);

  const onSubmit = async (data) => {
    setIsProcessing(true);

    const endDate = data.endDate ? dayjs(data.endDate).toDate().getTime() : null;

    const fringesPayload = Object.keys(data)
      .filter((key) => key.includes('fringe_'))
      .map((key) => {
        const id = parseInt(key.split('_')[1]);
        return {
          amount: parseFloat(data[key]).toFixed(2),
          fringeBenefitId: id,
        };
      });

    const formattedPayloadData: {
      startDate: number;
      wageTableId: number;
      basePay: string;
      fringePay: string;
      name: string;
      endDate?: number;
      fringeBenefitClassifications?: Record<any, any>;
    } = {
      startDate: dayjs(data.startDate).valueOf(),
      wageTableId,
      basePay: parseFloat(data.basePay).toFixed(2),
      fringePay: parseFloat(data.fringePay).toFixed(2),
      name: data.name,
    };

    if (fringesPayload.length > 0) {
      formattedPayloadData.fringeBenefitClassifications = fringesPayload;
    }

    if (endDate) {
      formattedPayloadData.endDate = endDate;
    }

    try {
      await createClassification(formattedPayloadData as any as Classification);

      addToast({
        title: 'Created Classification',
        description: (
          <>
            Successfully created the classification <strong className="font-medium">{data.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to create classification');
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    setIsProcessing(false);

    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Create Classification" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Create"
        >
          <ClassificationsForm {...{ register, errors, control, fringes }} />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

export default CreateClassificationModal;
