import { EmployeeClassification } from 'interfaces/classifications';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import { RiHistoryLine } from '@remixicon/react';
import { ModalV2 } from '@/components/elements/ModalV2';

interface ClassificationDetailsModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  classification: EmployeeClassification | undefined;
  allClassifications: (EmployeeClassification & {
    isActive: boolean;
  })[];
}

const ClassificationDetailsModal = ({
  open,
  setOpen,
  classification,
  allClassifications,
}: ClassificationDetailsModalProps) => {
  const classificationHistory = useMemo(() => {
    if (!classification) return [];

    return allClassifications
      .filter((c) => c.name === classification.name)
      .sort((a, b) => {
        if (!a.endDate && !b.endDate) {
          return 0;
        }
        if (!a.endDate) {
          return -1;
        }
        if (!b.endDate) {
          return 1;
        }

        return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
      });
  }, [classification, allClassifications]);

  const formatDateRange = (startDate: number, endDate: number, isActive: boolean) => {
    const start = startDate ? dayjs(startDate).format('MMM D, YYYY') : '';
    if (isActive && !endDate) {
      return `${start} - Today`;
    }
    const end = endDate ? dayjs(endDate).format('MMM D, YYYY') : 'Onward';
    return `${start} - ${end}`;
  };

  if (!classification) return null;

  return (
    <ModalV2
      open={open}
      setOpen={setOpen}
      title="Prevailing Wage History"
      icon={<RiHistoryLine className="text-primary h-5 w-5" />}
    >
      <div className="space-y-4 p-5">
        <div>
          <div className="text-xs text-sub-600">Classification</div>
          <div className="mt-1 text-sm text-strong-950">{classification.name}</div>
        </div>

        <div className="border-t border-soft-200"></div>

        <div className="space-y-4">
          {classificationHistory.map((historyItem, index) => (
            <>
              {index > 0 && <div className="border-t border-soft-200"></div>}
              <div key={`${historyItem.id}-${index}`} className="grid grid-cols-12">
                <div className="col-span-5 flex items-center gap-10">
                  <div>
                    <div className="text-sm font-medium text-strong-950">${historyItem.basePay}</div>
                    <div className="mt-0.5 text-xs text-sub-600">Base Pay</div>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-strong-950">${historyItem.fringePay}</div>
                    <div className="mt-0.5 text-xs text-sub-600">Fringe Pay</div>
                  </div>
                </div>

                <div className="col-span-7 flex items-center justify-end">
                  <div className="text-sm text-strong-950">
                    {formatDateRange(historyItem.startDate, historyItem.endDate, historyItem.isActive)}
                  </div>
                </div>
              </div>
            </>
          ))}
        </div>
      </div>
    </ModalV2>
  );
};

export default ClassificationDetailsModal;
