import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Input } from '@/hammr-ui/components/input';
import ControlledDateInput from '@/components/elements/form/ControlledDateInput';
import { TextField } from '../elements/form/TextField';

const ClassificationsForm = ({ register, control, errors, defaultValues = {}, fringes = [] }) => {
  return (
    <>
      <TextField required name="name" label="Classification" placeholder="Enter classification" control={control} />

      <FormItem required error={!!errors['basePay']} className="mt-5">
        <FormLabel>Base Pay</FormLabel>
        <FormControl>
          <Input
            type="number"
            step="any"
            beforeContent="$"
            placeholder="0.00"
            {...register('basePay', {
              required: 'Please enter a base pay',
              min: { value: 0, message: 'Enter a value 0 or above ' },
            })}
          />
        </FormControl>
        <FormMessage>{errors['basePay']?.message}</FormMessage>
      </FormItem>

      <FormItem required error={!!errors['fringePay']} className="mt-5">
        <FormLabel>Fringe Pay</FormLabel>
        <FormControl>
          <Input
            type="number"
            step="any"
            beforeContent="$"
            placeholder="0.00"
            {...register('fringePay', {
              required: 'Please enter a fringe pay',
            })}
          />
        </FormControl>
        <FormMessage>{errors['fringePay']?.message}</FormMessage>
      </FormItem>

      <ControlledDateInput
        label="Effective Date"
        control={control}
        name="startDate"
        rules={{ required: 'Please select an effective date' }}
        className="mt-5 w-full"
        required
      />

      <ControlledDateInput
        label="Expires On"
        control={control}
        name="endDate"
        className="mt-5 w-full"
        dayPickerProps={{ disabled: { before: new Date() } }}
      />

      {fringes?.length > 0 && (
        <>
          <hr className="mt-5 border-soft-200" />

          <h2 className="mt-5 font-medium text-strong-950">Project Fringe Amounts</h2>

          {fringes.map((fringe) => (
            <FormItem required error={!!errors[`fringe_${fringe.id}`]} className="mt-5" key={fringe.id}>
              <FormLabel>{fringe.name}</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="any"
                  beforeContent="$"
                  placeholder="0.00"
                  {...register(`fringe_${fringe.id}`, {
                    required: 'Please enter a fringe pay',
                    min: { value: 0, message: 'Enter a value 0 or above ' },
                  })}
                />
              </FormControl>
              <FormMessage>{errors[`fringe_${fringe.id}`]?.message}</FormMessage>
            </FormItem>
          ))}
        </>
      )}
    </>
  );
};

export default ClassificationsForm;
