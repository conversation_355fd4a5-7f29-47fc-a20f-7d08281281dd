import { DeductionType, PostTaxDeduction } from '@/interfaces/post-tax-deduction';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { ModalV2 } from '../elements/ModalV2';
import Alert from '@/hammr-ui/components/Alert';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { RadioGroup, RadioGroupItem } from '@/hammr-ui/components/Radio';
import MiscellaneousDeduction from '../people/employee-view/MiscellaneousDeduction';
import ChildSupportGarnishment from '../people/employee-view/ChildSupportGarnishment';
import { RiUserLine } from '@remixicon/react';
import { Controller, useForm } from 'react-hook-form';
import { FormV2 } from '../elements/Form';
import { TextField } from '../elements/form/TextField';
import { addToast } from '@/hooks/useToast';
import { updatePostTaxDeduction } from '@/services/post-tax-deduction';
import dayjs from 'dayjs';
import { userService } from '@/services/user';
import { HammrUser } from '@/interfaces/user';
import { useAuth } from '@/hooks/useAuth';

interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
  postTaxDeduction: PostTaxDeduction;
  refresh: () => void;
  showEmployee?: boolean;
}

export type FormValues = {
  employee: string | null;
  type: DeductionType | undefined;
  description: string;
  effective_start: Date | null;
  effective_end: Date | null;
  miscellaneous: {
    amountType: 'fixed' | 'percentage';
    amount: string;
    percent: number;
    total_amount: string;
    annual_limit: number;
  };
  child_support: {
    external_id: string;
    agency: string;
    issue_date: Date;
    amount: string;
    max_percent: number;
  };
};

export default function EditPostTaxDeductionModal({ open, setOpen, postTaxDeduction, refresh, showEmployee }: Props) {
  const { user } = useAuth();

  const employeesQuery = useQuery({
    queryKey: ['check-employees'],
    async queryFn() {
      const users = await userService.list({ organizationId: user.companyId, simple: true });
      const checkEmployees = users.filter((user) => user.workerClassification === 'EMPLOYEE' && user.checkEmployeeId);
      return checkEmployees as (HammrUser & Required<Pick<HammrUser, 'checkEmployeeId'>>)[];
    },
  });

  const form = useForm<FormValues>();

  useEffect(() => {
    if (open) {
      form.reset({
        employee: postTaxDeduction.employee,
        type: postTaxDeduction.type,
        description: postTaxDeduction.description,
        effective_start: dayjs(postTaxDeduction.effective_start).toDate(),
        effective_end: postTaxDeduction.effective_end ? dayjs(postTaxDeduction.effective_end).toDate() : null,
        miscellaneous: {
          amountType: postTaxDeduction.miscellaneous?.amount ? 'fixed' : 'percentage',
          ...postTaxDeduction.miscellaneous,
        },
        child_support: {
          ...postTaxDeduction.child_support,
          issue_date: dayjs(postTaxDeduction.child_support?.issue_date).toDate(),
        },
      });
    }
  }, [open, form, postTaxDeduction]);

  const updateDeductionMutation = useMutation({
    async mutationFn(formData: FormValues) {
      if (!formData.employee || !formData.type) throw new Error('Please fill the required fields.');

      const formattedData: PostTaxDeduction = {
        id: postTaxDeduction.id,
        employee: formData.employee,
        description: formData.description,
        type: formData.type,
        effective_start: dayjs(formData.effective_start).format('YYYY-MM-DD'),
        effective_end: formData.effective_end ? dayjs(formData.effective_end).format('YYYY-MM-DD') : null,
      };

      if (formData.type === 'miscellaneous') {
        formattedData.miscellaneous = {
          ...(formData.miscellaneous.amountType === 'fixed' && { amount: formData.miscellaneous.amount.toString() }),
          ...(formData.miscellaneous.amountType === 'percentage' && { percent: formData.miscellaneous.percent }),
          ...(formData.miscellaneous.total_amount && { total_amount: formData.miscellaneous.total_amount.toString() }),
          ...(formData.miscellaneous.annual_limit && { annual_limit: formData.miscellaneous.annual_limit.toString() }),
        };
      }

      if (formData.type === 'child_support') {
        formattedData.child_support = {
          external_id: formData.child_support.external_id,
          agency: formData.child_support.agency,
          issue_date: dayjs(formData.child_support.issue_date).format('YYYY-MM-DD'),
          amount: formData.child_support.amount.toString(),
          max_percent: formData.child_support.max_percent,
        };
      }

      return updatePostTaxDeduction(postTaxDeduction.id, formattedData);
    },
    onSuccess() {
      addToast({
        title: 'Success',
        description: `Deduction updated successfully`,
        type: 'success',
      });
      setOpen(false);
      refresh();
    },
    onError(error) {
      addToast({
        title: 'Failed to update deduction',
        description: error.message,
        type: 'error',
      });
    },
  });

  return (
    <ModalV2 open={open} setOpen={setOpen} title="Edit Post-Tax Deduction" icon={<RiUserLine />}>
      <FormV2
        onSubmit={form.handleSubmit((formData) => updateDeductionMutation.mutate(formData))}
        onCancel={() => setOpen(false)}
        submitText="Update"
        isLoading={updateDeductionMutation.isPending}
      >
        {showEmployee && (
          <Controller
            control={form.control}
            name="employee"
            render={({ field }) => {
              const employee = employeesQuery.data?.find((employee) => employee.checkEmployeeId === field.value);
              return (
                <div className="mb-5">
                  <h3 className="text-sm font-medium text-strong-950">Employee</h3>
                  <p className="mt-1.5 h-fit text-sm text-strong-950">
                    {employee?.firstName} {employee?.lastName}
                  </p>
                </div>
              );
            }}
          />
        )}

        <Controller
          control={form.control}
          name="type"
          render={({ field, fieldState }) => (
            <FormItem required error={!!fieldState.error} className="gap-3" disabled>
              <FormLabel>Type</FormLabel>
              <FormControl>
                <RadioGroup value={field.value} onValueChange={field.onChange}>
                  <div className="flex items-center gap-2">
                    <RadioGroupItem value="miscellaneous" id="miscellaneous" />
                    <label htmlFor="miscellaneous" className="text-strong-900 text-sm">
                      Miscellaneous Deduction
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <RadioGroupItem value="child_support" id="childSupport" />
                    <label htmlFor="childSupport" className="text-strong-900 text-sm">
                      Child Support Garnishment
                    </label>
                  </div>
                </RadioGroup>
              </FormControl>
              <FormMessage>{fieldState.error?.message}</FormMessage>
            </FormItem>
          )}
        />

        <Alert status="information" className="mt-5">
          All deductions will start with the next pay period.
        </Alert>

        <TextField
          label="Description"
          required
          control={form.control}
          name="description"
          className="mt-5"
          placeholder="Enter description"
        />

        <Controller
          control={form.control}
          name="type"
          render={({ field }) => (field.value ? <hr className="mt-5 border-soft-200" /> : <></>)}
        />

        {form.watch('type') === 'miscellaneous' && <MiscellaneousDeduction form={form} />}

        {form.watch('type') === 'child_support' && <ChildSupportGarnishment form={form} />}
      </FormV2>
    </ModalV2>
  );
}
