import { HammrEarningRate } from '@/interfaces/earning-rate';
import { useQuery } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Dialog, DialogBody, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import { apiRequest } from '@/utils/requestHelpers';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { formatUSD } from '@/utils/format';
import HistoryLine from '@hammr-icons/HistoryLine';
import { useCompany } from 'hooks/useCompany';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

interface CompensationHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number;
}

export default function CompensationHistoryModal({ isOpen, onClose, userId }: CompensationHistoryModalProps) {
  const { company } = useCompany();
  const now = dayjs().tz(company?.timezone || 'America/Los_Angeles');

  const earningRatesQuery = useQuery({
    queryKey: ['earningRates', userId],
    queryFn: () =>
      apiRequest<{ earningRates: HammrEarningRate[] }>(`earning-rates`, {
        urlParams: { userId: userId.toString() },
      }).then((response) => response.earningRates),
    enabled: isOpen,
  });

  const sortedEarningRates = earningRatesQuery.data
    ?.filter((rate) => rate.type === 'REG')
    ?.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  // Formats the earning rate start and end dates
  const formatDateRangeOfRate = (rate: HammrEarningRate) => {
    const startDateFormatted = rate.startDate ? dayjs(rate.startDate).format('MMM D, YYYY') : 'N/A';

    if (!rate.startDate) {
      return 'N/A';
    }

    const startDate = dayjs(rate.startDate).tz(company?.timezone || 'America/Los_Angeles');
    const endDate = rate.endDate ? dayjs(rate.endDate).tz(company?.timezone || 'America/Los_Angeles') : null;

    // Check if this rate is currently active
    const isAfterStart = now.isAfter(startDate) || now.isSame(startDate);
    const isBeforeEnd = !endDate || now.isBefore(endDate) || now.isSame(endDate);
    const isCurrentlyActive = isAfterStart && isBeforeEnd;

    // If rate starts in the future
    if (now.isBefore(startDate)) {
      return `${startDateFormatted} - Future`;
    }

    // If rate is currently active and has no end date
    if (isCurrentlyActive && !endDate) {
      return `${startDateFormatted} - Current`;
    }

    // If rate is currently active but has an end date
    if (isCurrentlyActive && endDate) {
      const endDateFormatted = endDate.format('MMM D, YYYY');
      return `${startDateFormatted} - ${endDateFormatted}`;
    }

    // If rate is in the past (completed)
    if (endDate) {
      const endDateFormatted = endDate.format('MMM D, YYYY');
      return `${startDateFormatted} - ${endDateFormatted}`;
    }

    // Fallback for any other scenario
    return `${startDateFormatted} - Unknown`;
  };
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogSurface>
        <DialogHeader icon={<HistoryLine />} title="Compensation History" />
        <DialogBody>
          {earningRatesQuery.isPending ? (
            <div className="mt-8 flex justify-center">
              <LoadingIndicator />
            </div>
          ) : earningRatesQuery.isError ? (
            <div className="text-error-base">Error loading compensation history</div>
          ) : (
            <div className="space-y-5">
              {sortedEarningRates?.map((rate: HammrEarningRate) => (
                <div
                  key={rate.id}
                  className="flex items-center justify-between border-b border-soft-200 pb-5 last:border-b-0 last:pb-0"
                >
                  <div>
                    <div className="text-sm font-medium text-strong-950">
                      {formatUSD.format(parseFloat(rate.amount))}
                    </div>
                    <div className="text-xs text-sub-600">{rate.period === 'HOURLY' ? 'Hourly Rate' : 'Salary'}</div>
                  </div>
                  <div className="text-sm text-strong-950">{formatDateRangeOfRate(rate)}</div>
                </div>
              ))}
            </div>
          )}
        </DialogBody>
      </DialogSurface>
    </Dialog>
  );
}
