import { EmployeeClassification } from 'interfaces/classifications';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import { RiHistoryLine } from '@remixicon/react';
import { ModalV2 } from '@/components/elements/ModalV2';

interface EmployeeDetailsModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  employeeClassification: EmployeeClassification | undefined;
  allEmployeeClassifications: (EmployeeClassification & {
    status: boolean;
  })[];
}

const EmployeeClassificationHistoryModal = ({
  open,
  setOpen,
  employeeClassification,
  allEmployeeClassifications,
}: EmployeeDetailsModalProps) => {
  const employeeHistory = useMemo(() => {
    if (!employeeClassification) return [];

    return allEmployeeClassifications
      .filter(
        (ec) => ec.userId === employeeClassification.userId && ec.classification.name === employeeClassification.name
      )
      .sort((a, b) => {
        if (!a.endDate && !b.endDate) {
          return 0;
        }
        if (!a.endDate) {
          return -1;
        }
        if (!b.endDate) {
          return 1;
        }

        return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
      });
  }, [employeeClassification, allEmployeeClassifications]);

  const formatDateRange = (startDate: number, endDate: number, isActive: boolean) => {
    const start = startDate ? dayjs(startDate).format('MMM D, YYYY') : '';
    if (isActive && !endDate) {
      return `${start} - Today`;
    }
    const end = endDate ? dayjs(endDate).format('MMM D, YYYY') : 'Onward';
    return `${start} - ${end}`;
  };

  if (!employeeClassification) return null;

  const employeeName = `${employeeClassification.user.firstName} ${employeeClassification.user.lastName}`;

  return (
    <ModalV2
      open={open}
      setOpen={setOpen}
      title="Prevailing Wage History"
      icon={<RiHistoryLine className="text-primary h-5 w-5" />}
    >
      <div className="space-y-4 p-5">
        <div className="flex items-start justify-between">
          <div>
            <div className="text-xs text-sub-600">Employee</div>
            <div className="mt-1 text-sm text-strong-950">{employeeName}</div>
          </div>
          <div className="ml-8 flex flex-grow flex-col items-start">
            <div className="text-xs text-sub-600">Classification</div>
            <div className="mt-1 text-sm text-strong-950">{employeeClassification.classification.name}</div>
          </div>
        </div>

        <div className="border-t border-soft-200"></div>

        <div className="space-y-4">
          {employeeHistory.map((historyItem, index) => (
            <>
              {index > 0 && <div key={`${historyItem.id}-${index}-divider`} className="border-t border-soft-200"></div>}
              <div key={`${historyItem.id}-${index}`}>
                {/* Bottom row - Same as ClassificationDetailsModal */}
                <div className="grid grid-cols-12">
                  <div className="col-span-5 flex items-center gap-10">
                    <div>
                      <div className="text-sm font-medium text-strong-950">${historyItem.basePay}</div>
                      <div className="mt-0.5 text-xs text-sub-600">Base Pay</div>
                    </div>

                    <div>
                      <div className="text-sm font-medium text-strong-950">${historyItem.fringePay}</div>
                      <div className="mt-0.5 text-xs text-sub-600">Fringe Pay</div>
                    </div>
                  </div>

                  <div className="col-span-7 flex items-center justify-end">
                    <div className="text-sm text-strong-950">
                      {formatDateRange(historyItem.startDate, historyItem.endDate, historyItem.status)}
                    </div>
                  </div>
                </div>
              </div>
            </>
          ))}
        </div>
      </div>
    </ModalV2>
  );
};

export default EmployeeClassificationHistoryModal;
