import { ColDef, ICellRendererParams, ValueFormatterParams } from '@ag-grid-community/core';
import { UpdatedTable } from 'components/shared/UpdatedTable';
import { EmployeeClassification } from 'interfaces/classifications';
import { MutableRefObject, useMemo, useState } from 'react';
import { Tooltip } from '@hammr-ui/components/tooltip';
import UnassignEmployeeModal from './UnassignEmployeeModal';
import EmployeeClassificationHistoryModal from './EmployeeClassificationHistoryModal';
import { useCompany } from 'hooks/useCompany';
import { AgGridReact } from '@ag-grid-community/react';
import { customGroupValueFormatter } from 'components/timesheets/utils';
import CompactButton from '@/hammr-ui/components/CompactButton';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import PencilLine from '@/hammr-icons/PencilLine';
import { RiHistoryLine } from '@remixicon/react';
import EditPrevailingWageModal from '../wage-tables/EditPrevailingWageModal';
import { formatLocaleUsa, isItemActive } from '@/utils/dateHelper';
import dayjs from 'dayjs';

type MappedEmployeeClassification = EmployeeClassification & {
  name: string;
  userName: string;
  status: boolean;
};

const EmployeesTable = (props: {
  employeeClassifications: EmployeeClassification[];
  wageTableId: number;
  classifications: EmployeeClassification[];
  employeesTableRef: MutableRefObject<AgGridReact>;
  callback: () => void;
}) => {
  const { employeesTableRef, employeeClassifications, callback } = props;
  const { company } = useCompany();
  const [selectedEmployeeClassificationRow, setSelectedEmployeeClassification] =
    useState<EmployeeClassification>(undefined);
  const [isUnenrollModalOpen, setIsUnenrollModalOpen] = useState(false);
  const [selectedEmployeeForDetails, setSelectedEmployeeForDetails] = useState<EmployeeClassification>(undefined);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const showCustomPrevailingWage = company?.prevailingWageSettings.allowCustomPrevailingWagePerEmployee;

  const mappedEmployeeData: MappedEmployeeClassification[] = useMemo(() => {
    return (
      employeeClassifications
        // TODO -> this filter should ideally be applied in the backend along with validators for effective/end date
        .filter((empClassification) => {
          const startDate = empClassification.startDate ? dayjs(empClassification.startDate) : null;
          const endDate = empClassification.endDate ? dayjs(empClassification.endDate) : null;

          const isStartDateSameAsEndDate = startDate && endDate && startDate.isSame(endDate);
          const isStartDateAfterEndDate = startDate && endDate && startDate.isAfter(endDate);

          return !isStartDateSameAsEndDate && !isStartDateAfterEndDate;
        })
        .map((employeeClassification) => {
          const startDateString = employeeClassification.startDate
            ? dayjs(employeeClassification.startDate).toString()
            : '';
          const endDateString = employeeClassification.endDate ? dayjs(employeeClassification.endDate).toString() : '';

          return {
            ...employeeClassification,
            name: employeeClassification.classification.name,
            userName: employeeClassification.user.firstName + ' ' + employeeClassification.user.lastName,
            status: isItemActive(startDateString, endDateString),
          };
        })
    );
  }, [employeeClassifications]);

  // this is made because the backend returns an entry for each user and each classification
  // we need to group by by both user and classification, so to avoid complex group by logic in
  // ag-grid it's better to just have the logic shown here to group and select only the most recent
  // or currently active employee-classification entry.
  const uniqueEmployees = useMemo(() => {
    // Use a delimiter that's extremely unlikely to appear in classification names
    const DELIMITER = ':::';
    const uniqueEmployeeKeys = new Set(
      mappedEmployeeData.map((emp) => `${emp.user.id}${DELIMITER}${emp.classification.name}`)
    );

    return Array.from(uniqueEmployeeKeys).map((key) => {
      const delimiterIndex = key.indexOf(DELIMITER);
      const userId = key.substring(0, delimiterIndex);
      const classificationName = key.substring(delimiterIndex + DELIMITER.length);
      const allEmployeeClassificationsForKey = mappedEmployeeData.filter(
        (emp) => emp.user.id.toString() === userId && emp.classification.name === classificationName
      );

      if (allEmployeeClassificationsForKey.length === 1) {
        return allEmployeeClassificationsForKey[0];
      }

      const activeEmployeeClassification = allEmployeeClassificationsForKey.find((emp) => emp.status);

      if (activeEmployeeClassification) {
        return activeEmployeeClassification;
      }

      const employeeClassificationWithNoEndDate = allEmployeeClassificationsForKey.find((emp) => !emp.endDate);

      if (employeeClassificationWithNoEndDate) {
        return employeeClassificationWithNoEndDate;
      }

      // get the employee classification with the latest end date
      return allEmployeeClassificationsForKey.sort((a, b) => {
        if (a.endDate && b.endDate) {
          const aEndDate = dayjs(a.endDate);
          const bEndDate = dayjs(b.endDate);
          return bEndDate.diff(aEndDate, 'day');
        }
        return 0;
      })[0];
    });
  }, [mappedEmployeeData]);

  const [selectedRow, setSelectedRow] = useState<EmployeeClassification>();
  const [showPrevailingWageEditModal, setShowPrevailingWageEditModal] = useState(false);

  const colDefs: ColDef[] = [
    {
      headerName: 'Employee',
      field: 'userName',
      minWidth: 200,
      initialSort: 'asc',
    },
    {
      headerName: 'Classification',
      field: 'classification.name',
      minWidth: 250,
    },
    {
      headerName: 'Base Pay',
      field: 'basePay',
      minWidth: 120,
      valueFormatter: (params: ValueFormatterParams) => customGroupValueFormatter(params),
      cellRenderer: (params: ValueFormatterParams) => {
        if (params.node.allLeafChildren?.length > 0) {
          const employeeClassification = employeeClassifications.find(
            (empClassification) => empClassification.classification.name === params.node.key
          );

          return <div>${employeeClassification.classification.basePay}</div>;
        }

        return <div>${params.value}</div>;
      },
    },
    {
      headerName: 'Fringe Pay',
      field: 'fringePay',
      minWidth: 120,
      cellRenderer: (params: ValueFormatterParams) => {
        if (params.node.allLeafChildren?.length > 0) {
          const employeeClassification = employeeClassifications.find(
            (empClassification) => empClassification.classification.name === params.node.key
          );

          return <div>${employeeClassification.classification.fringePay}</div>;
        }

        return <div>${params.value}</div>;
      },
    },
    {
      headerName: 'Start',
      field: 'startDate',
      minWidth: 120,
      cellRenderer: (params: ValueFormatterParams) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      headerName: 'End',
      field: 'endDate',
      minWidth: 120,
      cellRenderer: (params: ValueFormatterParams) => {
        return formatLocaleUsa(params.value);
      },
    },
    {
      headerName: '',
      field: 'actions',
      cellStyle: { overflow: 'visible' },
      sortable: false,
      pinned: 'right',
      width: 130,
      cellRenderer: (params: ValueFormatterParams) => {
        return (
          <div className="flex h-full items-center justify-start gap-3">
            <Tooltip content="View History">
              <CompactButton
                size="large"
                onClick={() => {
                  setSelectedEmployeeForDetails(params.data);
                  setIsDetailsModalOpen(true);
                }}
              >
                <RiHistoryLine />
              </CompactButton>
            </Tooltip>
            {showCustomPrevailingWage && (
              <Tooltip content="Edit">
                <CompactButton
                  size="large"
                  onClick={() => {
                    setSelectedRow(params.data);
                    setShowPrevailingWageEditModal(true);
                  }}
                >
                  <PencilLine />
                </CompactButton>
              </Tooltip>
            )}
            {(params.data.status || !params.data.endDate) && (
              <Tooltip content="Remove">
                <CompactButton
                  size="large"
                  onClick={() => {
                    setSelectedEmployeeClassification(params.data);
                    setIsUnenrollModalOpen(true);
                  }}
                >
                  <DeleteBinLine />
                </CompactButton>
              </Tooltip>
            )}
          </div>
        );
      },
    },
  ].filter((colDef) => !!colDef) as ColDef[];

  return (
    <div>
      <div>
        <UpdatedTable
          colDefs={colDefs}
          rowData={uniqueEmployees}
          hideSidebar={true}
          emptyRowsText={`No employees are assigned to any classifications. Click the "Assign Employees" button to get started.`}
          showRowGroupPanel={false}
          parentRef={employeesTableRef}
          disableCustomRendering
          gridOptions={{
            groupDisplayType: 'custom',
          }}
        />
      </div>
      <UnassignEmployeeModal
        isOpen={isUnenrollModalOpen}
        setIsOpen={setIsUnenrollModalOpen}
        classification={selectedEmployeeClassificationRow}
        callback={callback}
      />
      <EmployeeClassificationHistoryModal
        open={isDetailsModalOpen}
        setOpen={setIsDetailsModalOpen}
        employeeClassification={selectedEmployeeForDetails}
        allEmployeeClassifications={mappedEmployeeData}
      />
      {selectedRow && (
        <EditPrevailingWageModal
          open={showPrevailingWageEditModal}
          setOpen={setShowPrevailingWageEditModal}
          employeeClassification={selectedRow}
        />
      )}
    </div>
  );
};

export default EmployeesTable;
