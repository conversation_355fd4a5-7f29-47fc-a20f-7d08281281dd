import { FormV2 } from 'components/elements/Form';
import { Controller, useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useToast } from 'hooks/useToast';
import { logError, showErrorToast } from 'utils/errorHandling';
import { assignEmployeeClassification } from 'services/classifications';
import { Dialog, DialogHeader, DialogSurface } from '@/hammr-ui/components/dialog';
import Table2Line from '@/hammr-icons/Table2Line';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/hammr-ui/components/form';
import { Combobox } from '@/hammr-ui/components/combobox';
import WorkerCrewSelect from '@/hammr-ui/components/WorkerCrewSelect';
import { useEmployeesQuery } from '@/hooks/data-fetching/useEmployees';
import { useAuth } from '@/hooks/useAuth';

const AssignEmployeeModal = ({ isOpen, setIsOpen, callback, classifications, employeeClassifications = [] }) => {
  const { addToast } = useToast();
  const form = useForm({ defaultValues: { selectedWorkerIds: [] } });
  const { handleSubmit, clearErrors, reset } = form;

  const [isProcessing, setIsProcessing] = useState(false);
  const [innerClassificationId, setInnerClassificationId] = useState<number>();

  useEffect(() => {
    if (!isOpen) {
      clearErrors();
      setInnerClassificationId(undefined);
      reset();
    }
  }, [isOpen]);

  const enrolledEmployeeIds = employeeClassifications
    .filter((classification) => classification.classificationId === +innerClassificationId)
    .map((classification) => classification.userId);

  const onSubmit = async (data) => {
    const { classificationId, selectedWorkerIds } = data;
    const selectedClassification = classifications.find((classification) => classification.id === classificationId);

    setIsProcessing(true);

    try {
      await assignEmployeeClassification({
        userIds: selectedWorkerIds,
        classificationId,
        basePay: parseFloat(selectedClassification.basePay).toFixed(2),
        fringePay: parseFloat(selectedClassification.fringePay).toFixed(2),
        startDate: new Date().getTime(),
      });

      addToast({
        title: 'Assigned Classification to Employees',
        description: (
          <>
            Successfully assigned the classification{' '}
            <strong className="font-medium">
              {classifications.filter((classification) => classification.id === classificationId)[0].name}
            </strong>{' '}
            to <strong className="font-medium">{selectedWorkerIds.length}</strong>{' '}
            {selectedWorkerIds.length === 1 ? 'employee' : 'employees'}.
          </>
        ),
        type: 'success',
      });

      callback();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to assign employees');
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setIsProcessing(false);
      setInnerClassificationId(undefined);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogSurface>
        <DialogHeader icon={<Table2Line className="text-sub-600" />} title="Assign Employees" />
        <FormV2
          onSubmit={handleSubmit(onSubmit)}
          onCancel={() => setIsOpen(false)}
          isLoading={isProcessing}
          submitText="Assign Employees"
        >
          <FormBody
            {...{
              innerClassificationId,
              classifications,
              setInnerClassificationId,
              enrolledEmployeeIds,
              form,
            }}
          />
        </FormV2>
      </DialogSurface>
    </Dialog>
  );
};

const FormBody = ({ classifications, setInnerClassificationId, enrolledEmployeeIds, form }) => {
  const {
    control,
    formState: { errors },
  } = form;
  const selectedWorkerIds = form.watch('selectedWorkerIds');
  const { user } = useAuth();
  const { data: employees } = useEmployeesQuery({
    companyId: user?.companyId,
    simple: true,
    includeIsArchived: true,
  });

  return (
    <>
      <FormItem required error={!!errors['classificationId']}>
        <FormLabel>Classification</FormLabel>
        <FormControl>
          <Controller
            rules={{ required: 'Please select a classification' }}
            control={control}
            name="classificationId"
            render={({ field }) => (
              <Combobox
                aria-invalid={!!errors['classificationId']}
                placeholder="Select an option"
                className="mt-1 w-full"
                items={classifications.map((classification) => {
                  return {
                    label: classification.name,
                    value: classification.id.toString(),
                  };
                })}
                value={field.value?.toString()}
                onChange={(value) => {
                  setInnerClassificationId(value);
                  field.onChange(Number(value));
                }}
              />
            )}
          />
        </FormControl>
        <FormMessage>{errors['classificationId']?.message}</FormMessage>
      </FormItem>

      <Controller
        control={control}
        name="selectedWorkerIds"
        render={({ field }) => (
          <FormItem className="mt-5">
            <FormLabel>
              Assign To{' '}
              {field.value.length ? <span className="ml-px text-sub-600">({field.value.length} selected)</span> : ''}
            </FormLabel>
            <FormControl>
              <WorkerCrewSelect
                className={!!errors.employee ? 'border-error-base' : ''}
                selectedWorkerIds={field.value}
                onChange={field.onChange}
                disabledWorkers={enrolledEmployeeIds.map((id) => ({ workerId: id, selected: true, reason: '' }))}
                showCrews
              />
            </FormControl>
          </FormItem>
        )}
      />
      <div className="mt-5">
        <div className="text-sm font-medium text-strong-950">Employees Selected</div>
        <div className="mt-3 text-sm text-strong-950">
          {selectedWorkerIds
            .map(
              (id) =>
                employees.find((employee) => employee.id === id)?.firstName +
                ' ' +
                employees.find((employee) => employee.id === id)?.lastName
            )
            .join(', ')}
        </div>
      </div>
    </>
  );
};

export default AssignEmployeeModal;
