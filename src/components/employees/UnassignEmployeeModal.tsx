import { Dispatch, SetStateAction, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from 'hooks/useToast';
import { showErrorToast, logError } from 'utils/errorHandling';
import { unassignEmployeeClassification } from 'services/classifications';
import { EmployeeClassification } from 'interfaces/classifications';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';

interface UnassignEmployeeModalProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  callback: () => void;
  classification: EmployeeClassification;
}

export default function UnassignEmployeeModal({
  isOpen,
  setIsOpen,
  callback,
  classification,
}: UnassignEmployeeModalProps) {
  const { addToast } = useToast();
  const { handleSubmit } = useForm();

  const [isProcessing, setIsProcessing] = useState(false);

  const onSubmit = async () => {
    setIsProcessing(true);

    try {
      await unassignEmployeeClassification({
        userClassificationIds: [classification.id],
        endDate: Date.now().valueOf(),
      });

      addToast({
        title: 'Unassigned Employee from Classification',
        description: (
          <>
            Successfully unassigned{' '}
            <strong className="font-md">{classification.userName}</strong> from
            the classification{' '}
            <strong className="font-medium">{classification.name}</strong>.
          </>
        ),
        type: 'success',
      });

      callback();
      setIsOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to unassign employee');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <ConfirmDialog
      open={isOpen}
      setOpen={setIsOpen}
      onConfirm={handleSubmit(onSubmit)}
      icon={<KeyIcon icon={<DeleteBinLine />} color="red" />}
      title="Unassign Employee"
      subtitle={
        <>
          You’re about to remove{' '}
          {
            <span className="font-medium">
              {classification.user.firstName +
                ' ' +
                classification.user.lastName}
            </span>
          }{' '}
          from the classification{' '}
          {
            <span className="font-medium">
              {classification.classification.name}
            </span>
          }
          . Do you want to proceed?
        </>
      }
      confirmButton={{
        color: 'error',
      }}
      confirmButtonText="Unassign"
    />
  );
}
