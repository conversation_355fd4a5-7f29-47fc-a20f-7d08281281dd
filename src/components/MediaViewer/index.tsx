import React, { FC, useMemo } from 'react';
import { DialogHeader } from '@/hammr-ui/components/dialog';
import Image2Line from '@/hammr-icons/Image2Line';
import CompactButton from '@/hammr-ui/components/CompactButton';
import DeleteBinLine from '@/hammr-icons/DeleteBinLine';
import Button from '@/hammr-ui/components/button';
import CloseLine from '@/hammr-icons/CloseLine';
import Spinner from '@/hammr-ui/components/spinner';
import { ScrollArea, ScrollBar } from '@/hammr-ui/components/scroll-area';
import { cn } from '@/hammr-ui/lib/utils';
import moment from 'moment';
import DownloadCloud2Line from '@/hammr-icons/DownloadCloud2Line';
import ArrowLeftSLine from '@/hammr-icons/ArrowLeftSLine';
import ArrowRightSLine from '@/hammr-icons/ArrowRightSLine';
import { openConfirmDialog } from '@/hammr-ui/components/ConfirmDialog';
import PlayCircleLine from '@/hammr-icons/PlayCircleLine';

interface MediaViewerBodyUIProps {
  items: MediaViewerItem[];
  currentItem: MediaViewerItem | undefined | null;
  onDelete?: (item: MediaViewerItem) => void;
  isDeleting?: boolean;
  onClose?: () => void;
  onChange?: (item: MediaViewerItem) => void;
  isFetching?: boolean;
  hasNextPage?: boolean;
  fetchNextPage?: () => void;
  deleteConfirmationDialog?: {
    title: string;
    description: string;
  };
}

export interface MediaViewerItem {
  id: string | number;
  type: 'image' | 'video';
  url: string;
  name: string;
  deletable?: boolean;
  createdAt: number;

  createdBy: string;
}

export const MediaViewerBodyUI: FC<MediaViewerBodyUIProps> = ({
  items,
  currentItem,
  deleteConfirmationDialog,
  onDelete,
  isDeleting,
  onClose,
  onChange,
  fetchNextPage,
  hasNextPage,
  isFetching,
}) => {
  const index = useMemo(() => items.findIndex((item) => item.id === currentItem?.id), [currentItem?.id, items]);

  return (
    <>
      <DialogHeader
        icon={<Image2Line className="text-sub-600" />}
        title={
          <div>
            {currentItem?.createdBy}{' '}
            {
              <span className="text-xs font-normal text-sub-600">
                {currentItem ? moment(currentItem.createdAt).format('MMM DD, YYYY [at] h:mm A') : ''}
              </span>
            }
          </div>
        }
        showCloseButton={false}
        rightContent={
          <div className="-mr-6 flex items-center gap-4">
            {currentItem && (
              <Button
                beforeContent={<DownloadCloud2Line />}
                size="2x-small"
                variant="ghost"
                color="neutral"
                onClick={() => {
                  window.open(currentItem.url);
                }}
              />
            )}
            {currentItem?.deletable && !!onDelete && (
              <Button
                beforeContent={<DeleteBinLine />}
                size="2x-small"
                variant="ghost"
                color="neutral"
                onClick={async () => {
                  const confirmResult = await openConfirmDialog({
                    title: deleteConfirmationDialog?.title ?? 'Delete media file',
                    subtitle:
                      deleteConfirmationDialog?.description ?? 'Are you sure you want to delete this media file?',
                    confirmButtonText: 'Delete',
                  });

                  if (!confirmResult.confirmed) {
                    return;
                  }

                  onDelete?.(currentItem);
                }}
                loading={isDeleting}
              />
            )}
            <Button
              beforeContent={<CloseLine />}
              size="2x-small"
              variant="ghost"
              color="neutral"
              onClick={() => {
                onClose?.();
              }}
            />
          </div>
        }
      />
      <div className="flex flex-1 flex-col overflow-hidden">
        <div className="flex flex-1 overflow-hidden">
          <div className="flex w-16 items-center justify-center">
            <CompactButton
              size="large"
              variant="outline"
              shape="round"
              disabled={index < 1}
              onClick={() => {
                onChange?.(items[index - 1]);
              }}
            >
              <ArrowLeftSLine />
            </CompactButton>
          </div>
          <div className="flex flex-1 items-center justify-center">
            {currentItem?.type === 'image' && (
              <img src={currentItem.url} alt={currentItem.name} className="max-h-full object-contain" />
            )}
            {currentItem?.type === 'video' && <video src={currentItem.url} autoPlay controls />}
          </div>
          <div className="flex w-16 items-center justify-center">
            <CompactButton
              size="large"
              variant="outline"
              shape="round"
              disabled={!items[index + 1]}
              onClick={() => {
                onChange?.(items[index + 1]);
              }}
            >
              <ArrowRightSLine />
            </CompactButton>
          </div>
        </div>
        <div>
          <ScrollArea
            onScroll={(e) => {
              const target = e.target as HTMLElement;
              if (hasNextPage && !isFetching && target.scrollWidth - (target.scrollLeft + target.clientWidth) < 100) {
                fetchNextPage();
              }
            }}
          >
            <div className="flex gap-3 border-t border-t-soft-200 p-4">
              {items.map((item) => {
                return (
                  <div
                    key={item.id}
                    className={cn(
                      'relative flex h-16 w-16 cursor-pointer items-center justify-center overflow-hidden rounded-4 text-soft-400',
                      currentItem?.id === item.id && 'ring-2 ring-primary-base ring-offset-2'
                    )}
                    onClick={() => {
                      onChange?.(item);
                    }}
                  >
                    {item.type === 'image' && <img src={item.url} alt={item.name} className="h-16 w-16 object-cover" />}
                    {item.type === 'video' && (
                      <>
                        <video src={item.url} className="h-16 w-16 object-cover" />
                        <div className="absolute inset-0 flex items-center justify-center bg-white-0/40 text-sub-600">
                          <PlayCircleLine className="rounded-full bg-strong-950/50" />
                        </div>
                      </>
                    )}
                  </div>
                );
              })}
              {isFetching && (
                <div className="flex h-16 w-16 items-center justify-center rounded-4 border border-soft-200 text-soft-400">
                  <Spinner />
                </div>
              )}
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
      </div>
    </>
  );
};
