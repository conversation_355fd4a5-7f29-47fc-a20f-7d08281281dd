import { Crew } from './crew';

export type PayrollEarningType =
  | 'regular'
  | 'overtime'
  | 'double_overtime'
  | 'pto'
  | 'sick'
  | 'cash_tips'
  | 'paycheck_tips'
  | 'bonus'
  | 'commission'
  | 'group_term_life'
  | 'other_imputed'
  | 'salaried'
  | 'hourly'
  | 'rest_and_recovery'
  | 'non_productive'
  | 'paid_holiday';

export interface PayrollItem {
  id?: string;
  payroll?: string;
  employee: string;
  payment_method?: string;
  net_pay?: string;
  earnings?: Array<PayrollItemEarning>;
  reimbursements?: Array<PayrollItemReimbursement>;
  pto_balance_hours?: number;
  sick_balance_hours?: number;
  taxes?: Array<any>;
  benefits?: Array<any>;
  post_tax_deductions?: Array<any>;
  warnings?: Array<any>;
  paper_check_number?: string;
}

export interface PayrollItemEarning {
  amount: string;
  hours?: number;
  type?: PayrollEarningType;
  workplace: string;
  code?: string;
  description?: string;
  earning_code?: string;
  earning_rate?: string;
  metadata?: any;
}

export interface PayrollItemReimbursement {
  amount: string;
  code?: string;
  description?: string;
}
