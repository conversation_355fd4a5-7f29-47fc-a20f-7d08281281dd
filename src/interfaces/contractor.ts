import { Address } from './address';
import { BankAccount } from './bank-account';
import { HammrEarningRate } from './earning-rate';
import { Onboard } from './onboard';
import { HammrUser } from './user';
import { Workplace } from './workplace';
export interface Contractor {
  id?: string;
  company: string;
  type?: string;
  first_name?: string;
  middle_name?: string;
  last_name: string;
  business_name?: string;
  dob?: string;
  start_date?: string;
  termination_date?: string;
  workplaces?: Workplace[];
  email?: string;
  ssn_last_four?: string;
  ein?: string;
  default_net_pay_split?: string;
  payment_method_preference?: string;
  onboard?: Onboard;
  address: Address;
  '1099_nec_electronic_consent_provided'?: boolean;
  metadata?: any;
}

export interface UpdateContractor {
  type?: string;
  first_name?: string;
  middle_name?: string;
  last_name?: string;
  business_name?: string;
  dob?: string;
  start_date?: string;
  termination_date?: string;
  workplaces?: string[];
  email?: string;
  ssn_last_four?: string;
  ein?: string;
  default_net_pay_split?: string;
  address?: Address;
  '1099_nec_electronic_consent_provided'?: boolean;
  metadata?: any;
  isArchived?: boolean;
}

export interface PaginatedContractors {
  next: null | string;
  previous: null | string;
  results: Contractor[];
}

export interface EnrichedContractor {
  checkContractor: Contractor;
  hammrUser: HammrUser;
  primaryEarningRate?: HammrEarningRate;
  primaryOTEarningRate?: HammrEarningRate;
  bankAccount?: BankAccount;
}
