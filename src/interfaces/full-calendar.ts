export interface FullCalendarEvent {
  id: string;
  title: string;
  groupId?: string;
  allDay?: boolean;
  start: Date;
  end: Date;
  startStr?: string;
  endStr?: string;
  url?: string;
  classNames?: string[];
  editable?: boolean;
  startEditable?: boolean;
  durationEditable?: boolean;
  resourceEditable?: boolean;
  display?: string;
  overlap?: boolean | (() => void);
  constraint?: any;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  extendedProps?: Record<any, any>;
  source?: any;
}
