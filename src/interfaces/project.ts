export interface Project {
  id: number;
  name: string;
  projectNumber: string | null;
  customerName?: string;
  address?: string;
  location?: number[]; // [long, lat]
  isGeofenced?: boolean;
  geofenceRadius?: number;
  isArchived?: boolean;
  isPrevailingWage?: boolean;
  prevailingWageCategory?: 'FEDERAL' | 'STATE' | 'LOCAL' | null;
  prevailingWageState?: string;
  prevailingWageDirProjectId?: string;
  prevailingWageAwardingBody?: string;
  prevailingWagePrimeContractor?: string;
  prevailingWagePrimeContractorAddress?: string;
  prevailingWageBidAwardDate?: Date;
  prevailingWageIsSubcontractor?: boolean;
  costBudget?: number;
  hoursBudget?: number;
  notes?: string;
  overtimeSettingsId: number | null;
  // TODO -> the fields below are NOT in the API model yet they come up in the response
  foremanId?: number;
  startDate?: Date;
  organizationId?: number;
  wageTableId?: number;
  wageTable?: {
    id: number;
    description: string;
    name: string;
  };
}

export interface CreateProject {
  name: string;
  projectNumber: string | null;
  customerName?: string;
  startDate?: number;
  address?: string;
  hoursBudget?: number;
  costBudget?: number;
  notes?: string;
  overtimeSettingsId?: number;
  isGeofenced?: boolean;
  lat?: number;
  long?: number;
  geofenceRadius?: number;
  isPrevailingWage: boolean;
  prevailingWageCategory?: 'FEDERAL' | 'STATE' | 'LOCAL' | null;
  prevailingWageState?: string | null;
  prevailingWageDirProjectId?: string;
  wageTableId?: number;
  prevailingWageAwardingBody?: string;
  prevailingWageIsSubcontractor?: boolean;
  prevailingWagePrimeContractor?: string;
  prevailingWagePrimeContractorAddress?: string;
  prevailingWageBidAwardDate?: number;
  foremanId: number;
}

export type EditProject = Partial<CreateProject> & { isArchived?: boolean };

export interface RadiusOption {
  id: number;
  value: number;
  label: string;
}
