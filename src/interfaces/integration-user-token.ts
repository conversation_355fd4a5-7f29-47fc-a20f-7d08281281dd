import { PayrollStatus } from './payroll';

export type IntegrationUserToken = {
  id: number;
  accessToken: string;
  platform: string;
  provider: string;
  userId: number;
  isEnabled: boolean;
  storeUniqueId?: string;
  createdBy: number;
  organizationId: number;
};

export type IntegrationSupportedPlatform = {
  id: string;
  urlId: string;
  name: string;
  logo: string;
  description: string;
  provider: string;
};

export type GlAccountMappingSetting = {
  autoSync: boolean;
  consolidateJournalEntryBy: string;
  createdAt: string;
  createdBy: number;
  entryDate: string;
  id: number;
  integrationUserTokenId: number;
  organizationId: number;
  updatedAt: string;
};

export type GlAccountMapping = {
  id: number;
  accountId: string;
  accountName?: string;
  accountNumber?: string;
  name?: string;
  payrollCategory?: string;
  accountType?: string;
  accountCategory?: string;
  accountCostClass?: string;
  createdBy: number;
  integrationUserTokenId: number;
  organizationId: number;
};

export type PayrollRow = {
  periodStart: number;
  periodEnd: number;
  payPeriod: number;
  payDay: number;
  type: string;
  status: string;
  approved_at: number;
  id: string;
};

export type PreviewDetails = {
  payrollId: string;
  platform: string;
  failedAt?: string;
  syncHistoryId?: string;
  integrationUserTokenId: number;
};

export type SyncHistory = {
  id: string;
  payrollId: string;
  syncedAt: string;
  failedAt: string;
};

export interface PayrollHistoryItem {
  periodStart: number;
  periodEnd: number;
  payPeriod: number;
  payDay: number;
  type: string;
  status: PayrollStatus;
  approved_at: number;
  id: string;
  syncHistoryId?: string;
  syncedAt?: string;
  failedAt?: string;
  payrollTotal: number;
  actions?: string;
  preview?: string;
  managed?: boolean;
}
