import { HammrUser } from 'interfaces/user';
import { Project } from 'interfaces/project';

export interface ProjectPhotosCollection {
  id: number; // aka collectionId
  note: string;
  timesheetId: number;
  user: Partial<HammrUser>;
  project: Partial<Project>;
  projectPhotos: ProjectPhoto[];
  updatedAt?: number; // essentially a timestamp
  createdAt?: number; // essentially a timestamp
}

export interface ProjectPhoto {
  id: number;
  objectId: string; // this is the id for the s3 object
  collectionId?: number;
  imageUrl?: string; // signed url appended to ProjectPhoto
  updatedAt?: number; // essentially a timestamp
  createdAt?: number; // essentially a timestamp
}

export interface GetProjectPhotosCollection {
  organizationId: string;
  projectId?: string;
  userId?: string;
  offset?: number;
  limit?: number;
  projectArchived?: boolean;
}
