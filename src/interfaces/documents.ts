export interface CheckDocument {
  id: string;
  category: string;
  label: string;
  jurisdiction: string;
  year: number;
  filed_on: string;
}

export interface EmployeeDocument extends CheckDocument {
  employee: string;
}

export interface CompanyTaxDocument extends CheckDocument {
  company: string;
  description: string;
  quarter: 'q1' | 'q2' | 'q3' | 'q4';
}

export interface CompanyAuthorizationDocument extends CheckDocument {
  company: string;
}

export interface ContractorTaxDocument extends CheckDocument {
  contractor: string;
  description: string;
}
