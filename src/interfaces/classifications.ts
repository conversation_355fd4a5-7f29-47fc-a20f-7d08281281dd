import { HammrUser } from './user';

export type Classification = {
  id: number;
  name: string;
  basePay: number;
  fringePay: number;
  wageTableId: number;
  organizationId: number;
  startDate: number;
  endDate: number | null;
};

export type EmployeeClassification = {
  id: number;
  createdAt: string;
  updatedAt: string;
  userId: number;
  classificationId: number;
  organizationId: number;
  wageTableId: number;
  classification: Partial<Classification>;
  user: Partial<HammrUser>;
  basePay: string;
  fringePay: string;
  startDate: number;
  endDate: number | null;
  name: string;
  userName: string;
};
