export interface InjuryReport {
  id: number;
  note: string;
  resolvedBy?: number;
  resolutionDate?: number;
  resolvedByUser?: { id: number; firstName: string; lastName: string };
  resolutionNote?: string;
  injuryPhotos?: { id: number; objectId: string }[];
  updatedAt?: number;
  isResolved?: boolean;
  user?: { id: number; firstName: string; lastName: string };
  timesheet?: { id: number; project: { id: number; name: string }; clockIn: string; clockOut: string };
  userId: number;
  timesheetId: number;
  createdAt: string;
  createdBy: number;
  organizationId: number;
  creator?: { id: number; firstName: string; lastName: string };
}
