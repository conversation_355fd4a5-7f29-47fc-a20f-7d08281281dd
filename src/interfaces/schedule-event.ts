import { HammrUser } from 'interfaces/user';
import { Notification } from 'interfaces/notification';
export interface ScheduleEvent {
  id: number;
  organizationId: number;
  startTime: number;
  endTime: number;
  note?: string;
  costCodeId?: number;
  projectId: number;
  isDeleted: boolean;
  isPublished: boolean;
  updatedAt: Date;
  createdAt: Date;
}

export interface CreateScheduleEvent {
  startTime: number;
  endTime: number;
  projectId: number;
  userIds: string;
  equipmentIds: string;
  costCodeId?: number;
  note: string;
  notificationScheduledAt?: number;
  notifyViaText: boolean;
  notifyViaPush: boolean;
  linkedEventId?: string;
}

export interface UpdateScheduleEvent {
  startTime?: number;
  endTime?: number;
  projectId?: number;
  costCodeId?: number | null;
  note?: string;
  notificationScheduledAt?: number | null;
  notifyViaText?: boolean;
  notifyViaPush?: boolean;
  linkedEventId?: string | null;
  addUsers?: string; // comma separated list of user ids
  removeUsers?: string; // comma separated list of user ids
  timezone?: string;
  equipmentIds?: string; // comma separated list of equipment ids
}

export interface DenormalizedScheduleEvent {
  eventId: number;
  address?: string;
  assigned: Partial<HammrUser>[];
  costCodeId?: number;
  costCodeName?: string;
  email?: string;
  endTime: Date;
  eventColor?: string;
  firstName: string;
  fullName?: string;
  geofenceRadius?: number;
  isGeofenced?: boolean;
  isPublished?: boolean;
  lastName: string;
  location?: any;
  note?: string;
  phone?: string;
  position?: string;
  projectId: number;
  projectName: string;
  startTime: Date;
  userId: number;
  notification?: Notification;
  linkedEventId?: string;
}
