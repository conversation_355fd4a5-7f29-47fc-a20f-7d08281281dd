import { <PERSON>mrUser } from './user';
import { Project } from './project';
import { CostCode } from './cost-code';
import { EmployeeClassification } from './classifications';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { UserLocationDetails } from '@/interfaces/userlocation';
import { InjuryReport } from '@/interfaces/injury-report';
export type TimesheetStatus = 'SUBMITTED' | 'APPROVED' | 'PAID' | 'CLOCKED_IN';

export interface WageSummary {
  isPrevailingWage: boolean;
  classificationName: string;
  checkRegEarningCodeId: string;
  checkOtEarningCodeId: string;
  hourlyWage: number;
  regularMinutes: number;
  overtimeMinutes: number;
  breakMinutes: number;
  regularWages: number;
  overtimeWages: number;
  totalWages: number;
}

export interface Break {
  id?: number;
  start: number;
  end: number;
  isDeleted?: boolean;
}

export interface UserTimesheet {
  id: number;
  status: TimesheetStatus;
  // breakDuration comes in seconds from the backend
  breakDuration: number;
  clockIn: number;
  clockInPhotoObjectId: string | null;
  clockInLocation: string;
  clockOut: number;
  clockOutPhotoObjectId: string | null;
  clockOutLocation: string;
  createdBy: string;
  description: string;
  editedBy: string;
  hideGeofenceWarning: boolean;
  hasTimesheetHistory?: boolean;
  hasUnresolvedAlerts?: boolean;
  isManual?: boolean;
  breaks?: Break[];
  user?: Partial<HammrUser>;
  project?: Partial<Project>;
  costCode?: Partial<CostCode>;
  workersCompCode?: WorkersCompCode;
  regularMinutes?: number;
  overtimeMinutes?: number;
  driveTimeMinutes: number;
  driveTimeWages: number;
  otDuration: number;
  dotDuration: number;
  breakMinutes?: number;
  regularWages?: number;
  overtimeWages?: number;
  doubleOvertimeWages?: number;
  doubleOvertimeMinutes?: number;
  totalWages?: number;
  totalMinutes?: number;
  classificationId?: number;
  userClassification?: Partial<EmployeeClassification>;
  userLocations?: UserLocationDetails[];
  hourlyWage?: number;
  approvedAt: number | null;
  otHourlyWage?: number;
  dotHourlyWage?: number;
  injuryReport?: InjuryReport;
  equipmentId: number | null;
  equipment: {
    id: number;
    name: string;
    hourlyCost: number;
    year: number;
    category: {
      id: number;
      name: string;
    };
  } | null;
  isManualOvertimeApplied: boolean;
}

export interface TimesheetHistory {
  createdAt: string;
  newValue: string;
  previousValue: string;
  property: string;
  type: 'UPDATE';
}

export interface DashboardUserTimesheetSummary {
  id: number;
  checkEmployeeId: string;
  checkContractorId: string;
  firstName: string;
  lastName: string;
  hourlyWage: number;
  totalMinutes: number;
  regularMinutes: number;
  overtimeMinutes: number;
  breakMinutes: number;
  totalWages: number;
  timeWorkedByDay?: Record<string, string | number | Date>[];
  nonPWTimesheetsSummary?: WageSummary[];
  pwTimesheetsSummary?: WageSummary[];
}

export interface CreateUserTimesheet {
  userId: number; // this is the backend/Hammr user id
  projectId: number;
  costCodeId?: number;
  description?: string;
  clockIn: string;
  clockOut: string;
  breakDuration?: number;
  breaks?: Break[];
  classificationId?: number;
  userClassificationId?: number;
}

export interface UpdateUserTimesheet {
  id?: number;
  projectId?: number;
  codeCodeId?: number;
  breakDuration?: number;
  breaks?: Break[];
  clockIn?: number;
  clockOut?: number;
  otDuration?: number | null;
  dotDuration?: number | null;
  isManualOvertimeApplied?: boolean;
}

export interface ProjectDetails {
  id: number;
  name: string;
  isGeofenced: boolean;
  geofenceRadius: number;
  location: number[];
}
