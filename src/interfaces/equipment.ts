import { ScheduleEvent } from './schedule-event';
import { UserTimesheet } from './timesheet';
import { UserLocationDetails } from './userlocation';

export interface Equipment {
  id: number;
  name: string;
  categoryName: string;
  categoryId: number;
  category: { id: number; name: string };
  year: string;
  hourlyCost: string;
  isArchived: boolean;
  lastUsedBy?: string;
  lastUsedProject?: string;
  lastUsedOn?: string;
  timesheets: UserTimesheet[];
  lastUsedLocation?: UserLocationDetails;
  createdAt: string;
  updatedAt: string;
  equipmentScheduleEvents?: {
    equipmentId: number;
    scheduleEventId: number;
    scheduleEvent: ScheduleEvent & {
      project?: {
        id: number;
        name: string;
        address?: string | null;
        location?: {
          crs?: {
            type: string;
            properties: {
              name: string;
            };
          };
          type: string;
          coordinates: number[];
        };
        isGeofenced?: boolean;
        geofenceRadius?: number;
        isPrevailingWage?: boolean;
        wageTableId?: number | null;
      };
      users?: Array<{
        id: number;
        firstName?: string;
        lastName?: string;
        email?: string | null;
        position?: string | null;
        phone?: string | null;
      }>;
    };
  }[];
}

export interface CreateEquipmentPayload {
  name: string;
  categoryName: string;
  year: string;
  hourlyCost: string;
}

export interface UpdateEquipmentPayload {
  name?: string;
  categoryName?: string;
  year?: string;
  hourlyCost?: string;
}

export interface MappedScheduleEvent extends Partial<ScheduleEvent> {
  startTime: number;
  endTime: number;
  projectName?: string;
  projectAddress?: string | null;
}
