import { HammrUser } from 'interfaces/user';

export interface Crew {
  id: number;
  organizationId: number;
  name: string;
  crewLead: number;
  crewLeadUser?: Partial<HammrUser>;
  updatedAt: Date;
  createdAt: Date;
  crewMembers: {
    id: number;
    userId?: number;
    crewMemberUser: Partial<HammrUser>;
  }[];
}

export interface CreateCrew {
  name: string;
  crewLead?: number;
  crewMembers?: string; // comma delimited string of user ids
}

export interface EditCrew {
  name?: string;
  crewLead?: number;
  crewMembers?: string; // comma delimited string of user ids
}
