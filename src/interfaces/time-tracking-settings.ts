export enum TimesheetRoundingType {
  RoundDown = 'ROUND_DOWN',
  RoundUp = 'ROUND_UP',
  Nearest = 'ROUND_NEAREST',
  EmployeeFriendly = 'EMPLOYEE_FRIENDLY',
}

export enum TimesheetRoundingInterval {
  Five = 'FIVE_MINUTES',
  Ten = 'TEN_MINUTES',
  Fifteen = 'FIFTEEN_MINUTES',
}

export interface TimeTrackingSettings {
  clockInReminderAt: string;
  clockOutReminderAt: string;
  defaultClockInTime: string;
  defaultClockOutTime: string;
  allowWorkersToAddEditTime: boolean;
  allowWorkersToTrackTime: boolean;
  allowForemanToCreateProjects: boolean;
  areBreaksPaid: boolean;
  areRealtimeBreaksEnabled: boolean;
  areRealtimeBreakRemindersEnabled: boolean;
  useDecimalHours: boolean;
  breakOptions: number[];
  locationBreadcrumbingEnabled: boolean;
  realtimeBreakStartReminderAt: string;
  realtimeBreakEndReminderAfter: number;
  timesheetRoundingEnabled: boolean;
  timesheetRoundingInterval: TimesheetRoundingInterval;
  timesheetRoundingType: TimesheetRoundingType;
  isCostCodeRequired: boolean;
  isInjuryReportRequired: boolean;
  isDriveTimeEnabled: boolean;
  driveTimeRate: number;
  isClockinClockoutPhotosEnabled: boolean;
}
