import { Dayjs } from 'dayjs';

export const PAY_FREQUENCY = ['weekly', 'biweekly', 'semimonthly', 'monthly', 'quarterly', 'annually'] as const;
export type PayFrequency = (typeof PAY_FREQUENCY)[number];
export const WEEK_DAYS = ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY'] as const;
export const WEEK_DAYS_SHORT = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'] as const;

export type WeekDay = (typeof WEEK_DAYS)[number];

export interface PaySchedule {
  payFrequency: PayFrequency;
  firstPayday: Dayjs;
  secondPayday?: Dayjs;
  firstPeriodEnd: Dayjs;
  checkPayScheduleId: string;
  weekStartDay?: WeekDay;
}

export const payFrequencyOptions: Array<{ name: string; value: PayFrequency; description: string }> = [
  { name: 'Weekly', value: 'weekly', description: 'once every week' },
  { name: 'Bi-Weekly', value: 'biweekly', description: 'once every two weeks' },
  { name: 'Semi-Monthly', value: 'semimonthly', description: 'twice every month' },
];
