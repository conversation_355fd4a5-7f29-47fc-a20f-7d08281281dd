import { ProjectDetails } from './timesheet';
import { TimesheetAlert } from './timesheet-alert';

export type LocationEvent =
  | 'CLOCK_IN'
  | 'CLOCK_OUT'
  | 'BREAK_START'
  | 'BREAK_END'
  | 'GEOFENCE_ENTER'
  | 'GEOFENCE_EXIT'
  | 'LOCATION_CHANGE';

export interface UserLocationDetails {
  time: string;
  mapPinsColor: string;
  location?: {
    place?: string;
    latlng?: {
      lat: number;
      lng: number;
    };
  };

  locationAddress: string;
  // TODO: standardise value of `locationCoordinates` returned from API
  locationCoordinates?:
    | [number, number]
    | {
        crs: {
          name: string;
          properties: { name: string };
        };
        type: string;
        coordinates: [number, number];
      };

  type: string;
  horizontalAccuracy?: number;
  isWarn?: boolean;
  project?: ProjectDetails;
  isStrikeout?: boolean;
  isEdited?: boolean;
  alert?: TimesheetAlert;
  id: number;
  loggedAt: number;
  locationEvent: LocationEvent;
  geofenceProject: any;
  strikeout: boolean;
  timesheetAlert: TimesheetAlert;
}
