import { Address } from 'interfaces/address';
import { PayFrequency, PaySchedule } from './payschedule';
import { TimeTrackingSettings } from './time-tracking-settings';
import { OvertimeSettings } from './overtime-settings';
import { PrevailingWageSettings } from './prevailing-wage-settings';

export interface CheckCompany {
  legal_name: string;
  trade_name?: string;
  business_type: 'llc' | 's_corporation'; // TODO add the rest of business types
  email?: string;
  phone: string;
  address: Address;
  pay_frequency?: PayFrequency;
  start_date?: string;
  processing_period?: string;
  bank_accounts?: string[];
  onboard?: {
    blocking_steps: string[];
    remaining_steps: string[];
    status: 'completed' | 'blocking' | 'pending';
  };
  // other fields are also part of the object, but not needed currently
}

// the primary difference between HammrOrganization and Company is that Company = Partial<HammrOrganization>
// there are some legacy / existing check fields in Copmany as well (legacy from Firebase)
export interface Company {
  id: number;
  checkCompanyId: string;
  name: string;
  adminId?: string;
  slug: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  isCompanyRegistered: boolean;
  areBreaksPaid?: boolean;
  paySchedule?: PaySchedule;
  timeTrackingSettings: TimeTrackingSettings;
  overtimeSettings: OvertimeSettings;
  prevailingWageSettings: PrevailingWageSettings;
  overtimeMultiplier?: number;
  isPayrollEnabled: boolean;
  isSchedulingEnabled: boolean;
  isMessagingEnabled: boolean;
  isEquipmentTrackingEnabled: boolean;
  timezone: string;
  schedulingSettings: {
    createdAt: string;
    defaultNotificationTime: string;
    id: number;
    organizationId: number;
    updatedAt: string;
  };
}

export interface HammrOrganization {
  id: number;
  name: string;
  isRegisteredOnCheck: boolean;
  checkCompanyId?: string;
  slug?: string;
  areBreaksPaid?: boolean;
  useDecimalHours?: boolean;
  breakOptions?: number[];
  isPayrollEnabled: boolean;
  isSchedulingEnabled: boolean;
  isEquipmentTrackingEnabled: boolean;
  isMessagingEnabled: boolean;
  timezone: string;
  paySchedule?: Partial<Record<keyof PaySchedule, string>>;
  timeTrackingSettings: TimeTrackingSettings;
  overtimeSettings: OvertimeSettings;
  prevailingWageSettings: PrevailingWageSettings;
  schedulingSettings: {
    createdAt: string;
    defaultNotificationTime: string;
    id: number;
    organizationId: number;
    updatedAt: string;
  };
  allowWorkersToAddEditTime?: boolean;
  areRealtimeBreaksEnabled?: boolean;
  areRealtimeBreakRemindersEnabled?: boolean;
  realtimeBreakStartReminderAt?: string;
  realtimeBreakEndReminderAfter?: number;
  locationBreadcrumbingEnabled?: boolean;
}

export interface UpdateHammrOrganization {
  name?: string;
  isRegisteredOnCheck?: boolean;
  checkCompanyId?: string;
  slug?: string;
  areBreaksPaid?: boolean;
  useDecimalHours?: boolean;
  breakOptions?: number[];
  isPayrollEnabled?: boolean;
  isSchedulingEnabled?: boolean;
  isMessagingEnabled?: boolean;
  timezone?: string;
  allowWorkersToAddEditTime?: boolean;
  areRealtimeBreaksEnabled?: boolean;
  areRealtimeBreakRemindersEnabled?: boolean;
  realtimeBreakStartReminderAt?: string;
  realtimeBreakEndReminderAfter?: number;
  tradeName?: string;
  otherBusinessName?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  phone?: string;
}
