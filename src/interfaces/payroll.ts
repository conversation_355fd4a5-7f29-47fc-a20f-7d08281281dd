import { PayrollItem } from 'interfaces/payroll-item';
import { PayFrequency, WeekDay } from './payschedule';
import { UserTimesheet } from './timesheet';
import { HammrOrganization } from './company';
import { TimeOffRequest } from './timeoff';
import { TimesheetRoundingInterval, TimesheetRoundingType } from './time-tracking-settings';

type EarningMetadataKeys = 'is_custom_salaried';

export type Reimbursement = {
  amount: string;
  code?: string;
  description: string;
  metadata?: any;
};

export interface PatchPayrollEarningPayload {
  earnings?: Array<{
    resourceId: string;
    description: string;
    amount?: string;
    type: string;
    hours?: number;
    earning_code?: string;
    metadata?: Record<EarningMetadataKeys, any>;
  }>;
  reimbursements?: Array<{
    resourceId: string;
    amount: string;
    code?: string;
    description: string;
  }>;
}

export type PayrollTotals = {
  employee_gross: string;
  employee_reimbursements: string;
  employee_net: string;
  employee_taxes: string;
  employee_benefits: string;
  post_tax_deductions: string;
  contractor_gross: string;
  contractor_reimbursements: string;
  contractor_net: string;
  company_taxes: string;
  company_benefits: string;
  liability: string;
  cash_requirement: string;
};

export interface PayrollPreviewResponseCheck {
  id: string;
  company: string;
  period_start: string;
  period_end: string;
  approval_deadline: string;
  reopen_deadline: string;
  approved_at: string | null;
  payday: string;
  status: string;
  managed: boolean;
  type: string;
  pay_frequency: PayFrequency;
  pay_schedule: string | null;
  funding_payment_method: string;
  processing_period: string;
  off_cycle_options: any | null;
  totals: PayrollTotals;
  items: Array<PayrollItem>;
  contractor_payments: any[];
  is_void: boolean;
  metadata: Record<string, any>;
  warnings: any[];
  bank_account: string;
}

export interface PayrollOrganization extends HammrOrganization {
  isChurned: boolean;
  stripeCustomerId: string | null;
  metadata: any | null;
  createdAt: string;
  updatedAt: string;
  featureFlag: {
    isSchedulingEnabled: boolean;
    isPayrollEnabled: boolean;
    isMessagingEnabled: boolean;
    isEquipmentTrackingEnabled: boolean;
  };
  paySchedule: {
    id: number;
    payFrequency: PayFrequency;
    firstPayday: string;
    secondPayday: string | null;
    firstPeriodEnd: string;
    isActive: boolean;
    checkPayScheduleId: string;
    createdAt: string;
    updatedAt: string;
    organizationId: number;
  };
  timeTrackingSettings: {
    id: number;
    organizationId: number;
    clockInReminderAt: string | null;
    clockOutReminderAt: string | null;
    allowWorkersToTrackTime: boolean;
    allowWorkersToAddEditTime: boolean;
    areBreaksPaid: boolean;
    areRealtimeBreaksEnabled: boolean;
    areRealtimeBreakRemindersEnabled: boolean;
    realtimeBreakStartReminderAt: string;
    realtimeBreakEndReminderAfter: number;
    useDecimalHours: boolean;
    breakOptions: number[];
    locationBreadcrumbingEnabled: boolean;
    isClockinClockoutPhotosEnabled: boolean;
    showWagesToWorkers: boolean;
    timesheetRoundingEnabled: boolean;
    timesheetRoundingType: TimesheetRoundingType;
    timesheetRoundingInterval: TimesheetRoundingInterval;
    isCostCodeRequired: boolean;
    isInjuryReportRequired: boolean;
    allowForemanToCreateProjects: boolean;
    isDriveTimeEnabled: boolean;
    driveTimeRate: number;
    defaultClockInTime: string;
    defaultClockOutTime: string;
    createdAt: string;
    updatedAt: string;
  };
  prevailingWageSettings: {
    id: number;
    allowCustomPrevailingWagePerEmployee: boolean;
    overridePwIfBelowRegularRate: boolean;
    calculateOvertimeOnCashFringe: boolean;
    createdAt: string;
    updatedAt: string;
    organizationId: number;
  };
  overtimeSettings: {
    id: number;
    name: string;
    description: string | null;
    weeklyOvertimeEnabled: boolean;
    dailyOvertimeEnabled: boolean;
    dailyOvertimeThreshold: number;
    weeklyOvertimeThreshold: number;
    overtimeMultiplier: number;
    weekStartDay: WeekDay;
    dailyDoubleOvertimeEnabled: boolean;
    dailyDoubleOvertimeThreshold: number;
    overtimeDistribution: 'SEQUENTIAL' | 'WEIGHTED';
    overtimeDays: WeekDay[];
    doubleOvertimeDays: WeekDay[];
    isActive: boolean;
    isDefault: boolean;
    createdAt: string;
    updatedAt: string;
    organizationId: number;
  };
}

export interface PeriodDates {
  periodStartTimestamp: number;
  periodEndTimestamp: number;
  adjustedDates: {
    start: string;
    end: string;
  };
  startDayOfWeek: string;
}

export interface EmployeeNotFinishedOnboarding {
  id: number;
  checkEmployeeId: string;
  firstName: string;
  lastName: string;
}

export interface PerDiemReimbursement extends Pick<Reimbursement, 'code'> {
  id: number;
  reimbursementAmount: string;
  resourceId: string;
}

export interface PayrollPreviewResponse {
  payroll: PayrollPreviewResponseCheck;
  organization: PayrollOrganization;
  periodDates: PeriodDates;
  timesheets: UserTimesheet[];
  timeOffRequests: TimeOffRequest[];
  userEarningsSummaries: EmployeeSummary[];
  salariedEmployeeSummaries: EmployeeSummary[];
  contractorSummaries: EmployeeSummary[];
  hasUnapprovedTimesheets: boolean;
  hasMissingPayrollUsers: boolean;
  usersMissingCheckIds: any[];
  employeesNotFinishedOnboarding: EmployeeNotFinishedOnboarding[];
  contractorsNotFinishedOnboarding: any[];
  perDiemReimbursements: PerDiemReimbursement[];
}

export interface TimesheetSummary {
  isPrevailingWage: boolean;
  description: string;
  classificationName: string;
  checkRegEarningCodeId: string;
  checkOtEarningCodeId: string;
  hourlyWage: number;
  otHourlyWage: number;
  dotHourlyWage?: number;
  regularMinutes: number;
  overtimeMinutes: number;
  doubleOvertimeMinutes?: number;
  breakMinutes: number;
  regularWages: number;
  overtimeWages: number;
  doubleOvertimeWages: number;
  totalWages: number;
}

export interface EmployeeSummary {
  id: number;
  firstName: string;
  lastName: string;
  compensationType: string;
  hourlyWage: number;
  otHourlyWage: number;
  dotHourlyWage: number;
  salary: number;
  checkRegEarningRateId: string | null;
  checkOtEarningRateId: string | null;
  checkDotEarningRateId: string | null;
  checkEmployeeId: string | null;
  checkContractorId: string | null;
  regularMinutes: number;
  overtimeMinutes: number;
  doubleOvertimeMinutes: number;
  totalMinutes: number;
  breakMinutes: number;
  totalWages: number;
  checkDynamicFringeBenefit: any | null;
  timeWorkedByDay: any[];
  nonPWTimesheetsSummary: Array<TimesheetSummary>;
  pwTimesheetsSummary: Array<TimesheetSummary>;
  checkBenefitOverrides: any[];
  reimbursements: Reimbursement[];
  otherEarnings: any[];
  paymentMethod: string | null;
  skip?: boolean;
}

export interface TransformedEmployeeSummary extends Partial<EmployeeSummary> {
  employee?: string;
  grossPay?: number;
  employmentType?: 'hourly' | 'salaried' | 'contractors';
  type?: string;
  canDelete?: boolean;
  showPerDiemTooltip?: boolean;
}

export interface PayrollResponse {
  calculatedGross: number;
  approvalDeadline: string;
  hasMissingPayrollUsers: boolean;
  hasUnapprovedTimesheets: boolean;
  hourlyEmployeeSummaries: Array<EmployeeSummary>;
  salariedSummaries: Array<EmployeeSummary>;
  contractorSummaries: Array<EmployeeSummary>;
  payroll: Payroll;
  payrollId: string;
  periodEnd: string;
  periodStart: string;
}

export interface Payroll {
  id?: string;
  company: string;
  period_start: string;
  period_end: string;
  payday: string;
  type?: 'regular' | 'off_cycle';
  pay_frequency?: PayFrequency;
  pay_schedule?: string;
  off_cycle_options?: PayrollOffCycleOption;
  approval_deadline?: string;
  approved_at?: string;
  status?: PayrollStatus;
  managed?: boolean;
  totals?: any;
  items?: Array<PayrollItem>;
  contractor_payments?: Array<any>;
}

export type PayrollStatus = 'draft' | 'processing' | 'paid' | 'pending' | 'failed' | 'partially_paid';

export interface PayrollOffCycleOption {
  force_supplemental_withholding?: boolean;
  apply_benefits?: boolean;
  apply_post_tax_deductions?: boolean;
}

export interface PayrollFilterOptions {
  type?: 'regular' | 'off_cycle';
}

export interface PaydayResponse {
  approval_deadline: string;
  impacted_by_weekend_or_holiday: boolean;
  payday: string;
  period_end: string;
  period_start: string;
}
