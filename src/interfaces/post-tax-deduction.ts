export type DeductionType = 'miscellaneous' | 'child_support';

interface MiscellaneousDeduction {
  total_amount?: string;
  amount?: string;
  percent?: number;
}

interface ChildSupportGarnishment {
  external_id: string;
  agency: string;
  issue_date: string;
  amount: string;
  max_percent: number;
}

export interface PostTaxDeduction {
  id: string;
  type: DeductionType;
  employee: string;
  description: string;
  effective_start: string;
  effective_end: string | null;
  miscellaneous?: MiscellaneousDeduction;
  child_support?: ChildSupportGarnishment;
}

export type AddPostTaxDeduction = Omit<PostTaxDeduction, 'id'>;
