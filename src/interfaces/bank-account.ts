interface RawBankAccount {
  account_number: string;
  routing_number: string;
  institution_name?: string;
  subtype: string;
}

interface PlaidBankAccount {
  name?: string;
  plaid_public_token?: string;
  institution_name?: string;
  mask?: string;
  type?: string;
  subtype?: string;
  verified?: boolean;
  microdeposit_verification_status?: string;
}

export interface BankAccount {
  id: string;
  employee: string;
  company?: string;
  contractor?: string;
  raw_bank_account?: RawBankAccount;
  institution_name?: string;
  account_number?: string;
  routing_number?: string;
  subtype?: string;
  plaid_bank_account?: PlaidBankAccount;
  plaid_processor_token?: string;
}
