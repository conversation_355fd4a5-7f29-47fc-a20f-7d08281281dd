import { HammrUser } from './user';

// TODO -> this is not a final list
export type BenefitCategory =
  | 'MEDICAL'
  | 'DENTAL'
  | 'VISION'
  | 'LIFE'
  | 'DISABILITY'
  | '401K'
  | 'ROTH_401K'
  | 'FSA_MEDICAL'
  | 'FSA_DEPENDENT_CARE'
  | 'HSA'
  | 'SIMPLE_IRA';
export type ContributionType = 'AMOUNT' | 'PERCENT' | 'DYNAMIC';

export interface CompanyBenefit {
  id: number;
  name: string;
  category: BenefitCategory;
  contributionType: ContributionType;
  benefitStartDate: string; // YYYY-MM-DD
  benefitEndDate: string; // YYYY-MM-DD
  benefitProviderName?: string;
  benefitProviderAddress?: string;
  benefitProviderPhone?: string;
  isApprovedFringe: boolean;
  checkCompanyBenefitId: string;
  metadata?: JSON;
  organizationId: number;
  numberEnrolled: number;
}

export interface CompanyBenefitModified extends Omit<CompanyBenefit, 'benefitStartDate' | 'benefitEndDate'> {
  benefitStartDate: string; // YYYY-MM-DD
  benefitEndDate: string; // YYYY-MM-DD
}

export interface EmployeeBenefit {
  id: number;
  name: string;
  period: 'MONTHLY' | null;
  companyContributionPercent: number;
  employeeContributionPercent: number;
  companyPeriodAmount: string;
  employeePeriodAmount: string;
  companyContributionAmount: string;
  employeeContributionAmount: string;
  contributionType: ContributionType;
  benefitStartDate: string; // YYYY-MM-DD
  benefitEndDate: string; // YYYY-MM-DD
  metadata?: JSON;
  checkBenefitId: string;
  companyBenefitId: number;
  userId: number;
  companyBenefitName?: string;
  employeeName: string;
  fringeHourlyContribution?: string;
  updatedAt: string;
  changeReason: string | null;
}

export interface EmployeeBenefitResponse extends EmployeeBenefit {
  user: Pick<HammrUser, 'id' | 'firstName' | 'lastName' | 'email' | 'position' | 'phone' | 'role'>;
}
