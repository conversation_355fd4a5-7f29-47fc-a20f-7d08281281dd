import { HammrEarningRate } from 'interfaces/earning-rate';
import { Address } from './address';
import { BankAccount } from './bank-account';
import { Onboard } from './onboard';
import { Workplace } from './workplace';

import { HammrUser } from './user';

export interface UpdateEmployee {
  first_name?: string;
  middle_name?: string;
  last_name?: string;
  email?: string;
  dob?: string;
  residence?: Address;
  start_date?: string;
  workplaces?: string[];
  ssn_last_four?: string;
  payment_method_preference?: 'manual' | 'direct_deposit';
  active?: boolean;
  metadata?: any;
  hammrUserId?: number;
}

export interface Employee {
  id: string;
  first_name?: string;
  middle_name?: string;
  last_name: string;
  email?: string;
  dob: string;
  residence: Address;
  company: string;
  start_date: string;
  workplaces: string[];
  ssn_last_four?: string;
  primary_workplace?: string;
  payment_method_preference?: 'manual' | 'direct_deposit';
  active?: boolean;
  onboard?: Onboard;
  bank_accounts?: string[];
  metadata?: any;
}

export interface PaginatedEmployees {
  next: null | string;
  previous: null | string;
  results: EnrichedEmployee[] | Employee[];
}

export interface EnrichedEmployee {
  checkEmployee?: Employee;
  hammrUser: HammrUser;
  workplaces?: Workplace[];
  bankAccount?: BankAccount;
  primaryEarningRate?: HammrEarningRate;
  primaryOTEarningRate?: HammrEarningRate;
}
