import { CheckCompany } from './company';
import { Contractor } from './contractor';

export interface ContractorPayment {
  payroll: string;
  payday: string;
  period_start: string;
  period_end: string;
  company: CheckCompany;
  void_of: string;
  voided_by: string;
  contractor: Contractor;
  net_pay: string;
  net_pay_ytd: string;
  amount: string;
  amount_ytd: string;
  reimbursement_amount: string;
  reimbursement_amount_ytd: string;
  paper_check_number: string;
  payment_method: 'direct_deposit' | 'manual';
}
