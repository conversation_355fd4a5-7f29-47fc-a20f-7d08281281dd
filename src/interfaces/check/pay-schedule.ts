export interface PaySchedule {
  id?: string;
  company: string;
  name?: string;
  pay_frequency: string;
  first_payday: string;
  first_period_end: string;
}

export interface PayDay {
  payday: string;
  period_start: string;
  period_end: string;
  approval_deadline: string;
  impacted_by_weekend_or_holiday: boolean;
}

export interface PayDaysResponse {
  next: string;
  previous: string;
  results: PayDay[];
}
