export interface ListResponse<T> {
  next: string;
  previous: string;
  results: T[];
}

export interface CheckCompanyDefinedAttributeValue {
  name: string;
  value: string | number;
  effective_start?: string;
}

export interface CheckCompanyDefinedAttribute {
  name: string;
  value: string;
  type: string;
  label: string;
  description: string;
  options: any; // this is usually the list of options used in a select box
  effective_start?: string; // YYYY-MM-DD
  default_value: unknown;
}
