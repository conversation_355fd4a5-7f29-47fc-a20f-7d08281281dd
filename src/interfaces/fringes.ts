import { EmployeeBenefit } from './benefit';
import { Classification } from './classifications';

export const FRINGE_CATEGORY_TYPES = [
  'HEALTH_AND_WELFARE',
  'PENSION',
  'TRAINING_FUND_CONTRIBUTION',
  'VACATION',
  'OTHER',
] as const;

export type FringesCategories = (typeof FRINGE_CATEGORY_TYPES)[number];

export type FringeBenefitClassification = {
  id: number;
  amount: string;
  startDate: number;
  endDate: number | null;
  fringeBenefitId: number;
  classificationId: number;
  createdAt: number;
  updatedAt: number;
  organizationId: number;
};

export type FringeBenefitClassificationObject = {
  id: number;
  amount: number;
  startDate: number;
  endDate: number | null;
  fringeBenefitId: number;
  classificationId: number;
  createdAt: number;
  updatedAt: number;
  organizationId: number;
  fringeBenefit: FringeBenefit;
  classification: FringeBenefitClassification & {
    wageTableId: number;
    name: string;
  };
};

export type FringeBenefit = {
  id: number;
  name: string;
  category: FringesCategories;
  createdAt: number;
  updatedAt: number;
  wageTableId: number;
  organizationId: number;
};

export type Fringe = {
  id: number;
  name: string;
  category: FringesCategories;
  createdAt: number;
  updatedAt: number;
  wageTableId: number;
  organizationId: number;
  classificationName: string;
  fringeBenefitClassifications: FringeBenefitClassification;
};

export type FringeBenefitStatement = {
  basePay: string;
  cashFringe: string;
  classification: Classification;
  classificationId: number;
  dotRateOfPay: number;
  employeeBenefits: EmployeeBenefit[];
  endDate: number | null;
  fringeBenefits: { id: number; name: string; amount: string }[];
  fringePay: string;
  id: number;
  otRateOfPay: number;
  regRateOfPay: number;
  startDate: number;
  sumOfEmployeeBenefits: number;
  sumOfFringeBenefits: number;
  user: { id: number; firstName: string; lastName: string };
  userId: number;
  wageTableId: number;
};
