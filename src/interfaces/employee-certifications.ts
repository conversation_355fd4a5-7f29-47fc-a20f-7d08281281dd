import { HammrUser } from '@/interfaces/user';

export interface EmployeeCertification {
  id: number;
  name: string;
  objectId: string;
  title: string | null;
  number: string | null;
  completionDate: string | null;
  expirationDate: string | null;
  issuingEntity: string | null;
  notes: string | null;
  user: Pick<HammrUser, 'id' | 'firstName' | 'lastName' | 'profilePhotoObjectId'>;
  createdAt: number;
}
