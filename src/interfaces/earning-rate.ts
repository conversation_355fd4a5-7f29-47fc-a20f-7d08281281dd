export const employmentOptions = [{ type: 'Salaried' }, { type: 'Hourly' }];

export type EarningRatePeriod = 'hourly' | 'annually';

export type HammrEarningRatePeriod = 'HOURLY' | 'ANNUALLY' | 'PIECE_RATE';

export type HammrEarningRateType = 'REG' | 'OT';

export interface EarningRate {
  id?: string;
  employee: string;
  amount: string;
  period: EarningRatePeriod;
  name?: string;
  active?: boolean;
}

export interface UpdateEarningRate {
  name?: string;
  active?: boolean;
}

export interface HammrEarningRate {
  id: string;
  amount: string;
  period: HammrEarningRatePeriod;
  name: string;
  active: boolean;
  type: HammrEarningRateType;
  startDate?: Date;
  endDate?: Date;
  weeklyHours?: number;
  createdAt?: Date;
}
