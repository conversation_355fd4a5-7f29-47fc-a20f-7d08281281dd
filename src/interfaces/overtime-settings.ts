import { WeekDay } from './payschedule';

export interface OvertimeSettings {
  weeklyOvertimeEnabled: boolean;
  dailyOvertimeEnabled: boolean;
  dailyOvertimeThreshold: number;
  weeklyOvertimeThreshold: number;
  weekStartDay: WeekDay;
  // TODO -> remove comment - dev new fields april-2025 below this line
  name: string;
  description?: string;
  overtimeMultiplier: number;
  dailyDoubleOvertimeEnabled: boolean;
  dailyDoubleOvertimeThreshold: number;
  overtimeDistribution: 'SEQUENTIAL' | 'WEIGHTED';
  overtimeDays: WeekDay[];
  doubleOvertimeDays: WeekDay[];
  id?: number;
  createdAt?: string;
  updatedAt?: string;
  organizationId?: number;
  isActive?: boolean;
  isDefault?: boolean;
}
