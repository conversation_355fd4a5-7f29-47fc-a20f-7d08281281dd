import { HammrUser } from '@/interfaces/user';

export enum TimeOffPolicyType {
  PTO = 'PTO',
  SICK = 'SICK',
  UNPAID = 'UNPAID',
  PERSONAL = 'PERSONAL',
  BEREAVEMENT = 'BEREAVEMENT',
  JURY_DUTY = 'JURY_DUTY',
  VOLUNTEER = 'VOLUNTEER',
}

export enum AccrualMethod {
  FIXED = 'FIXED',
  ACCRUED = 'ACCRUED',
  HOURS_WORKED = 'HOURS_WORKED',
}

export interface TimeOffSummary {
  available: number;
  accruedBasedOnAccrualLimit: number;
  used: number;
  accrued: number;
  carryover: number;
}

export interface TimeOffPolicyEnrollment {
  id: number;
  startingBalance: number;
  timeOffPolicyId: number;
  userId: number;
  startDate: string; // date string
}

export interface TimeOffPolicy {
  id: number;
  name: string;
  type: TimeOffPolicyType;
  isLimited: boolean;
  endDate: string | null;
  createdAt: string;
  updatedAt: string;
  organizationId: number;
  accrualMethod: AccrualMethod | null;
  accrualHoursRate: number | null;
  accrualLimit: number | null;
  accrualHoursInterval: number | null;
  accrualResetDate: string | null;
  carryoverLimit: number | null;
  addNewEmployeesAutomatically: boolean;
  users?: (HammrUser & { timeOffSummary?: TimeOffSummary; timeOffPolicyEnrollment: TimeOffPolicyEnrollment })[];

  // ----------------------------------------------------------------------------------
  // these fields are available when requesting a specific user's time-off policies.
  // their names are very close to the fields of `TimeOffSummary` because they are actually the same thing
  availableHours: number;
  accruedHoursBasedOnAccrualLimit: number;
  usedHours: number;
  accruedHours: number;
  carryoverHours: number;
  // ----------------------------------------------------------------------------------
}

export enum TimeOffRequestStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
  PAID = 'PAID',
}

export interface TimeOffRequest {
  id: number;
  startDate: string;
  endDate: string;
  userId: number;
  totalHours: number;
  requestNotes: string;
  status: TimeOffRequestStatus;
  reviewedBy: number;
  timeOffPolicyId: number;
  declineNotes: string | null;
  checkPayrollId: string | null;
  user: HammrUser;
  reviewer?: HammrUser;
  timeOffPolicy: TimeOffPolicy;
  createdAt: string;
  updatedAt: string;
}
