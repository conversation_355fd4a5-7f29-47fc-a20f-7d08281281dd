export enum REPORT_FORMAT {
  LCPTracker = 'LCPTracker',
  DepartmentOfLabor = 'DepartmentOfLabor',
  DepartmentOfIndustrialRelations = 'DepartmentOfIndustrialRelations',
  DepartmentOfLaborAndIndustries = 'DepartmentOfLaborAndIndustries',
  JSON = 'JSON',
}

export interface CertifiedPayrollReportPayload {
  reportFormat: REPORT_FORMAT;
  signatoryName: string;
  signatoryTitle: string;
  projectIds: number[];
  isLastCertifiedPayrollReport: boolean;
  payrollId: string;
  remarks?: string;
  payrollWeekEnding?: string; // date in YYYY-MM-DD format
  reportType?: string;
}
