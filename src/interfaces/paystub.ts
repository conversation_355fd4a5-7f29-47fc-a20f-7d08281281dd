import { CheckCompany } from 'interfaces/company';
import { Employee } from 'interfaces/employee';
import { PayrollEarningType } from 'interfaces/payroll-item';

export interface Earning {
  type: PayrollEarningType;
  code?: string;
  earning_code?: string;
  amount: string;
  amount_ytd: string;
  hours: number;
  hours_ytd: number;
  name: string;
  current_earnings: {
    description: string;
    amount: string;
    hours: number;
    workplace: string;
    rate: string;
  }[];
}

interface EarningSummary {
  earnings: string;
  earnings_ytd: string;
  reimbursements: string;
  reimbursements_ytd: string;
  employee_taxes: string;
  employee_taxes_ytd: string;
  company_taxes: string;
  company_taxes_ytd: string;
  employee_benefit_contributions: string;
  employee_benefit_contributions_ytd: string;
  company_benefit_contributions: string;
  company_benefit_contributions_ytd: string;
  post_tax_deductions: string;
  post_tax_deductions_ytd: string;
  net_pay: string;
  net_pay_ytd: string;
}

export interface EmployeePaystubs {
  payroll: string;
  payday: string;
  period_start: string;
  period_end: string;
  company: CheckCompany;
  void_of?: string;
  voided_by?: string;
  employee: Employee;
  earnings: Earning[];
  reimbursements: any[];
  employee_taxes: any[];
  company_taxes: any[];
  employee_benefit_contributions: any[];
  company_benefit_contributions: any[];
  post_tax_deductions: any[];
  summary: EarningSummary;
  time_off_balances: {
    pto_balance_hours: number;
    sick_balance_hours: number;
    state_covid_sick_balance_hours?: any;
  };
}
