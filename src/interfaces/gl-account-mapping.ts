export type DepartmentsWithCustomMappings = {
  id: number;
  name: string;
  hasCustomMappings: boolean;
};

export type GlAccount = {
  accountNumber: string;
  id: string;
  name: string;
  platformId: string;
  accountCategory: string;
  accountType: string;
};

export type GlAccountMappingResponse = {
  data: {
    payrollCategoryTypes: Array<{
      id: string;
      name: string;
      isHeader: boolean;
    }>;
    payrollCategoryTypesMapping: Record<string, string>;
    glAccountMappings: Array<
      {
        id: string;
        payrollCategory: string;
        accountId: string;
        platformId: string;
        accountName: string;
      } & Pick<GlAccount, 'accountCategory' | 'accountType'>
    >;
  };
};
