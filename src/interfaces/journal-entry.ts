export interface JournalEntryLineItem {
  account_id: string;
  total_amount: number;
  description: string;
}

export interface JournalEntry {
  transaction_date: string;
  currency_code: string;
  memo: string;
  line_items: JournalEntryLineItem[];
}

export interface JournalEntryDisplayItem extends JournalEntryLineItem {
  account_name?: string;
  debit?: number | null;
  credit?: number | null;
}
