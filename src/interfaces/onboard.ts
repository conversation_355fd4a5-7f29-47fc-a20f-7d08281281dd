export type OnboardStatus = 'completed' | 'needs_attention' | 'blocking';

export interface Onboard {
  status: OnboardStatus;
  blocking_steps: string[];
  remaining_steps: string[];
  [key: string]: any; // Additional properties exist for details on each step
}

export type OnboardEvent =
  | 'check-onboard-app-loaded'
  | 'check-onboard-app-completed'
  | 'check-onboard-ssn-updated'
  | 'check-onboard-payment-method-updated'
  | 'check-onboard-bank-account-updated'
  | 'check-onboard-tax-form-updated'
  | 'check-onboard-authorization-form-updated'
  | 'check-onboard-tax-setup-form-updated'
  | 'check-onboard-filing-authorization-form-updated';
