import { useState } from 'react';
import { useAwsS3 } from './useAwsS3';
import { useAuth } from './useAuth';
import { useMutation } from '@tanstack/react-query';

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

export function useUploadFile(path: string) {
  const { s3 } = useAwsS3();
  const { user } = useAuth();
  const [uploadProgress, setUploadProgress] = useState(0);

  const { mutateAsync: uploadAsync, isPending: isUploading } = useMutation({
    mutationFn: async ({ key, content }: { key: string; content: unknown }) => {
      setUploadProgress(0);
      const params = {
        Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/${path}`,
        Key: key,
        Body: content,
      };

      await s3
        .putObject(params)
        .on('httpUploadProgress', (evt) => {
          const progress = Math.round((evt.loaded * 100) / evt.total);
          setUploadProgress(progress);
        })
        .promise();

      return key;
    },
  });

  return { uploadAsync, isUploading, uploadProgress };
}
