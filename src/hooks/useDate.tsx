import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { useCompany } from './useCompany';
import advancedFormat from 'dayjs/plugin/advancedFormat';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);
dayjs.extend(advancedFormat);

export const useDate = () => {
  const { company } = useCompany();

  const parseInOrgTimezone = (date: Date | string | number) => {
    return dayjs(date).tz(company.timezone);
  };

  const formatUsa = (date: Date | string | number, options: { showTimezone?: boolean; showTime?: boolean } = {}) => {
    let formattedDate = dayjs(date).format('MM/DD/YYYY');

    if (options.showTime) {
      formattedDate = `${formattedDate} ${parseInOrgTimezone(date).format('h:mm A')}`;
    }

    if (options.showTimezone) {
      formattedDate = `${formattedDate} ${parseInOrgTimezone(date).format('z')}`;
    }

    return formattedDate;
  };

  const isWeekend = (date: Date | string | number | Dayjs) => {
    return dayjs(date).day() === 0 || dayjs(date).day() === 6;
  };

  /**
   * Replace a GMT offset (e.g. "GMT-0700", "GMT-7") in the supplied string with
   * the appropriate US-style timezone abbreviation (PST/PDT, MST/MDT, CST/CDT, EST/EDT).
   *
   * The mapping takes daylight-saving time into account using US DST rules
   * (second Sunday in March → first Sunday in November).
   *
   * Only GMT-7 through GMT-4 are handled; all other offsets will be left untouched
   * and "GMT" without an offset is converted to "UTC".
   */
  const formatTimezone = (dateString: string) => {
    const gmtRegex = /GMT([+-]\d{1,4})/g;

    // Helper: detect if a dayjs date falls inside US DST period for its year
    const isUsDst = (d: dayjs.Dayjs) => {
      const year = d.year();

      // DST start: second Sunday in March (at 2 a.m. local)
      const marchFirst = dayjs(new Date(year, 2, 1)); // March = 2
      const firstSundayOffset = (7 - marchFirst.day()) % 7; // distance to first Sunday
      const dstStart = marchFirst.add(firstSundayOffset + 7, 'day').hour(2);

      // DST end: first Sunday in November (at 2 a.m. local)
      const novemberFirst = dayjs(new Date(year, 10, 1)); // November = 10
      const firstSundayOffsetNov = (7 - novemberFirst.day()) % 7;
      const dstEnd = novemberFirst.add(firstSundayOffsetNov, 'day').hour(2);

      return d.isAfter(dstStart) && d.isBefore(dstEnd);
    };

    // Helper: map offset → abbreviation based on DST flag
    const getUsTzAbbrev = (offsetHours: number, dst: boolean): string | null => {
      switch (offsetHours) {
        case -7:
          return dst ? 'PDT' : 'MST';
        case -6:
          return dst ? 'MDT' : 'CST';
        case -5:
          return dst ? 'CDT' : 'EST';
        case -4:
          return dst ? 'EDT' : 'AST';
        default:
          return null;
      }
    };

    // Replace each GMT occurrence with the proper abbreviation
    return dateString.replace(gmtRegex, (match, offsetPart) => {
      // Convert offset string (e.g. "+0300", "-7") ➜ hours integer
      let numeric = parseInt(offsetPart, 10);
      if (Math.abs(numeric) > 50) {
        // e.g. -0700 → -7
        numeric = numeric / 100;
      }

      // Parse the date if possible to evaluate DST; fall back to today
      const date = dayjs(dateString);
      const dst = isUsDst(date.isValid() ? date : dayjs());

      const abbr = getUsTzAbbrev(numeric, dst);
      if (abbr) return abbr;

      // Fallbacks
      if (match === 'GMT') return 'UTC';
      return match;
    });
  };

  return {
    dayjs: dayjs,
    parseInOrgTimezone,
    formatUsa,
    isWeekend,
    formatTimezone,
  };
};
