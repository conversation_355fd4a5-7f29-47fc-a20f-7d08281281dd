import { createContext, ReactNode, useCallback, useContext, useState } from 'react';

import { WeekDay } from '@/interfaces/payschedule';
import dayjs, { Dayjs, OpUnitType } from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';

// Enable ISO week support for dayjs
dayjs.extend(isoWeek);

export type DateFilter = 'ALL_TIME' | 'PAY_PERIOD' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM';

// Date Selection Context
const dateSelectionContext = createContext<any>(null);

const { Provider } = dateSelectionContext;

export function DateSelectionProvider({ children }: { children: ReactNode }) {
  const dateSelection = useDateSelectionProvider();
  return <Provider value={dateSelection}>{children}</Provider>;
}

export function useDateSelection() {
  const context = useContext(dateSelectionContext);

  if (!context) {
    throw new Error('useDateSelection must be used within a DateSelectionProvider');
  }

  return context;
}

export interface StepChange {
  operation: string;
  amount: number;
  unit: OpUnitType;
}

export const filteredDateChangeMappings = (operation: string) => {
  const map: Record<string, StepChange[]> = {
    PREV_DAILY: [{ operation: 'subtract', amount: 1, unit: 'day' }],
    NEXT_DAILY: [{ operation: 'add', amount: 1, unit: 'day' }],
    PREV_WEEKLY: [{ operation: 'subtract', amount: 1, unit: 'week' }],
    NEXT_WEEKLY: [{ operation: 'add', amount: 1, unit: 'week' }],
    PREV_MONTHLY: [{ operation: 'subtract', amount: 1, unit: 'month' }],
    NEXT_MONTHLY: [{ operation: 'add', amount: 1, unit: 'month' }],
  };

  return map[operation];
};

// the gist of this is yield to usePayPeriodSelection if PAY_PERIOD is selected, otherwise use this
const useDateSelectionProvider = () => {
  // by default the currentStartDateSelected should be set to the pay period dates from usePayPeriodSelection
  const [currentSelectedDateFilter, setCurrentSelectedDateFilter] = useState<DateFilter>('PAY_PERIOD');
  // Use lazy initialization to create stable initial dates
  const [currentStartDateSelected, setCurrentStartDateSelected] = useState<Dayjs>(() => dayjs().startOf('week'));
  const [currentEndDateSelected, setCurrentEndDateSelected] = useState<Dayjs>(() => dayjs().endOf('week'));
  const [initialized, setInitialized] = useState<boolean>(false);

  // function to determine date filter
  const registerDatesByFilter = useCallback(
    (
      filterSelected: DateFilter,
      dateInputs?: {
        startDate?: Dayjs;
        endDate?: Dayjs;
        weekStartDay?: WeekDay;
      }
    ) => {
      const currentDate = dayjs();
      setInitialized(true);

      switch (filterSelected) {
        case 'PAY_PERIOD':
          if (dateInputs?.startDate) setCurrentStartDateSelected(dateInputs.startDate);
          if (dateInputs?.endDate) setCurrentEndDateSelected(dateInputs.endDate);
          return {
            startDate: dateInputs ? dateInputs.startDate : null, // pass in the start date from usePayPeriodSelection
            endDate: dateInputs ? dateInputs.endDate : null, // pass the end date from usePayPeriodSelection
          };
        case 'DAILY':
          const updatedStartDate = currentDate.startOf('day');
          const updatedEndDate = currentDate.endOf('day');
          setCurrentStartDateSelected(updatedStartDate);
          setCurrentEndDateSelected(updatedEndDate);
          return {
            startDate: updatedStartDate,
            endDate: updatedEndDate,
          };
        case 'WEEKLY':
          const electedWeekStartDate = dateInputs?.weekStartDay || 'MONDAY';
          // Convert WeekDay to ISO weekday number (1-7, Monday-Sunday)
          const weekdayMap: Record<WeekDay, number> = {
            MONDAY: 1,
            TUESDAY: 2,
            WEDNESDAY: 3,
            THURSDAY: 4,
            FRIDAY: 5,
            SATURDAY: 6,
            SUNDAY: 7,
          };
          const weekdayNumber = weekdayMap[electedWeekStartDate] || 1;
          const updatedStartDateWeekly = currentDate.isoWeekday(weekdayNumber).startOf('day');
          const updatedEndDateWeekly = updatedStartDateWeekly.add(6, 'days').endOf('day');
          setCurrentStartDateSelected(updatedStartDateWeekly);
          setCurrentEndDateSelected(updatedEndDateWeekly);
          return {
            startDate: updatedStartDateWeekly,
            endDate: updatedEndDateWeekly,
          };
        case 'MONTHLY':
          const updatedStartDateMonthly = currentDate.startOf('month');
          const updatedEndDateMonthly = currentDate.endOf('month');
          setCurrentStartDateSelected(updatedStartDateMonthly);
          setCurrentEndDateSelected(updatedEndDateMonthly);
          return {
            startDate: updatedStartDateMonthly,
            endDate: updatedEndDateMonthly,
          };
        case 'CUSTOM':
          if (dateInputs?.startDate) setCurrentStartDateSelected(dateInputs.startDate);
          if (dateInputs?.endDate) setCurrentEndDateSelected(dateInputs.endDate);
          return {
            startDate: dateInputs?.startDate ? dateInputs.startDate.startOf('day') : null,
            endDate: dateInputs?.endDate ? dateInputs.endDate.endOf('day') : null,
          };
      }
    },
    []
  );

  return {
    currentStartDateSelected,
    setCurrentStartDateSelected,
    currentEndDateSelected,
    setCurrentEndDateSelected,
    currentSelectedDateFilter,
    setCurrentSelectedDateFilter,
    registerDatesByFilter,
    initialized,
    setInitialized,
  };
};
