import { createContext, useCallback, useContext } from 'react';
import { useQuery } from '@tanstack/react-query';

import { useAuth } from 'hooks/useAuth';

import { AWSSecurityToken } from 'interfaces/sts';
import { apiRequest } from 'utils/requestHelpers';

const awsStsContext = createContext({});
const { Provider } = awsStsContext;

export function AwsStsProvider(props) {
  const awsSts = useAwsStsProvider();
  return <Provider value={awsSts}>{props.children}</Provider>;
}

export const useAwsSts: any = () => {
  return useContext(awsStsContext);
};

const useAwsStsProvider = () => {
  const auth = useAuth();

  const isTokenExpired = useCallback((stsCredentials: AWSSecurityToken | null) => {
    if (!stsCredentials || !stsCredentials.Expiration) {
      return true;
    }

    const currentTime = new Date();
    const expirationTime = new Date(stsCredentials.Expiration);
    return currentTime >= expirationTime;
  }, []);

  const stsQuery = useQuery({
    queryKey: ['sts-credentials', auth.user?.companyId],
    queryFn: async (): Promise<AWSSecurityToken> => {
      if (!auth.user?.companyId) {
        throw new Error('Company ID is required');
      }

      const res = await apiRequest('sts-token', {
        urlParams: { organizationId: auth.user.companyId },
        convertToJson: false,
      });
      return res.json();
    },
    enabled: !!auth.user?.companyId,
    staleTime: 1000 * 60 * 30, // 30 minutes
    refetchInterval: (query) => {
      // Auto-refetch before token expires
      if (query.state.data && !isTokenExpired(query.state.data)) {
        const expirationTime = new Date(query.state.data.Expiration);
        const currentTime = new Date();
        const timeUntilExpiry = expirationTime.getTime() - currentTime.getTime();
        // Refetch when 80% of the token lifetime has passed
        return Math.max(timeUntilExpiry * 0.8, 60000); // minimum 1 minute
      }
      return false;
    },
  });

  const getStsCredentials = useCallback(async () => {
    const res = await stsQuery.refetch();
    return res.data;
  }, [stsQuery]);

  return {
    stsCredentials: stsQuery.data || null,
    getStsCredentials,
    isLoading: stsQuery.isPending,
    error: stsQuery.error,
  };
};
