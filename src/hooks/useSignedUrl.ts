import { useAwsS3 } from './useAwsS3';
import { useAuth } from './useAuth';
import { useQuery } from '@tanstack/react-query';

const CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

export function useSignedUrl(path: string, key: string) {
  const { s3 } = useAwsS3();
  const { user } = useAuth();

  const { isPending: isLoading, data: url } = useQuery({
    queryKey: ['useSignedUrl', user?.companyId, path, key],
    queryFn: async () => {
      if (!key) {
        return null;
      }

      return new Promise<string>((resolve, reject) => {
        const params = {
          Bucket: `${CUSTOMER_BUCKET}/${user?.companyId}/${path}`,
          Key: key,
          Expires: 21600, // 6 hours - input in seconds
        };

        s3.getSignedUrl('getObject', params, (err, url) => {
          if (err) {
            reject(err);
          }
          resolve(url);
        });
      });
    },
    staleTime: 1000 * 60 * 60 * 6, // 6 hours
    enabled: !!s3 && !!user?.companyId,
  });

  return [url, isLoading] as const;
}
