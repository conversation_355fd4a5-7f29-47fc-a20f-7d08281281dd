import { useEffect, useState } from 'react';

import { useDeepObjectMemo } from 'hooks/useDeepObjectMemo';

import { HammrUser } from 'interfaces/user';
import { getHammrUsers } from 'services/user';
import { sortListAlphabetically } from 'utils/collectionHelpers';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';

/**
 * @deprecated use `useEmployeesQuery` instead
 */
export function useEmployees(orgId: number, filterParams: Record<string, unknown> = {}) {
  const [employees, setEmployees] = useState<HammrUser[]>([]);

  const filterParamsRef = useDeepObjectMemo(filterParams);

  useEffect(() => {
    const fetchInitialData = async () => {
      const employeesData = await getHammrUsers({
        organizationId: orgId,
        ...filterParamsRef,
      });
      // need to enhance data because label<PERSON><PERSON> takes a singular field prop
      const enhancedEmployees = employeesData.users.map((user) => {
        return { ...user, fullName: `${user.firstName} ${user.lastName}` };
      });
      // sort employees by first name alphebetically as well
      const sortedEnhancedEmployees = sortListAlphabetically(enhancedEmployees, 'firstName');
      setEmployees(sortedEnhancedEmployees);
    };

    if (orgId) {
      fetchInitialData();
    }
  }, [orgId, filterParamsRef]);

  return employees;
}

export function useEmployeesQuery(filterParams: Record<string, unknown> = {}) {
  return useQuery<HammrUser[]>({
    queryKey: ['employees'],
    async queryFn() {
      const data = await apiRequest<{ users: HammrUser[] }>('users', { urlParams: filterParams });
      const usersWithFullName = data.users.map((user) => {
        return { ...user, fullName: `${user.firstName} ${user.lastName}` };
      });
      return sortListAlphabetically(usersWithFullName, 'firstName');
    },
  });
}
