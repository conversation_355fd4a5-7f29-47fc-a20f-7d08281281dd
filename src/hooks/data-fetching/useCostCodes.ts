import { CostCode } from 'interfaces/cost-code';
import { useEffect, useState } from 'react';
import { getCostCodes } from 'services/cost-codes';
import { sortListAlphabetically } from 'utils/collectionHelpers';

export function useCostCodes(orgId: string) {
  const [costCodes, setCostCodes] = useState<CostCode[]>([]);

  useEffect(() => {
    if (!orgId) return;

    getCostCodes({ organizationId: orgId }).then((res) => {
      const costCodesSorted = sortListAlphabetically(res, 'name');
      setCostCodes(costCodesSorted);
    });
  }, [orgId]);

  return costCodes;
}
