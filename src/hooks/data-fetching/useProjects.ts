import { Project } from 'interfaces/project';
import { useEffect, useMemo, useState } from 'react';
import { getProjects } from 'services/projects';
import { logError, showErrorToast } from 'utils/errorHandling';
import { sortListAlphabetically } from 'utils/collectionHelpers';

interface UseProjectsOptions {
  includePlaceHolder?: boolean;
  placeholderText?: string;
  placeHolderValue?: number;
  includeIsArchived?: boolean;
  wageTableId?: number;
}

export function useProjects(orgId: string, options?: UseProjectsOptions): Project[] {
  const [projects, setProjects] = useState<Project[]>([]);

  useEffect(() => {
    if (!orgId) return;

    getProjects({
      organizationId: orgId,
      includeIsArchived: options?.includeIsArchived ? 'true' : 'false',
      ...(options?.wageTableId && {
        wageTableId: String(options?.wageTableId),
      }),
    })
      .then((res) => {
        // sort alphabetically?
        // first map res.projects into name and id props
        if (res && res.projects) {
          const remappedProjects = res.projects.map((proj) => {
            return {
              ...proj,
              name: proj.name,
              id: proj.id,
              isArchived: proj.isArchived,
              customerName: proj.customerName,
            };
          });

          const remappedProjectsSorted = sortListAlphabetically(remappedProjects, 'name');

          setProjects(remappedProjectsSorted);
        }
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  }, [orgId, options?.includeIsArchived, options?.wageTableId]);

  return useMemo(() => {
    if (!options?.includePlaceHolder) return projects;

    const placeholderProject = {
      name: options?.placeholderText ?? 'Select Project',
      id: options?.placeHolderValue ?? 0,
      isArchived: false,
      customerName: '',
    };

    return [placeholderProject, ...projects] as Project[];
  }, [
    projects,
    options?.includePlaceHolder,
    options?.placeholderText,
    options?.placeHolderValue,
    options?.wageTableId,
  ]);
}
