export default function EmptyStateScheduleHoliday() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="148" height="148" viewBox="0 0 148 148" fill="none">
      <rect width="148" height="148" rx="74" fill="rgb(var(--raw-weak-100))" />
      <path
        d="M130.237 109.162C130.237 109.162 124.231 107.372 122.427 113.352C124.231 107.373 118.237 105.543 118.237 105.543C118.237 105.543 124.243 107.332 126.047 101.352C124.243 107.332 130.237 109.162 130.237 109.162Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M84.3659 35.8064C85.9356 36.0678 87.4199 35.0071 87.6813 33.4375C87.9426 31.8678 86.882 30.3835 85.3123 30.1221C83.7426 29.8608 82.2583 30.9214 81.997 32.4911C81.7356 34.0608 82.7963 35.5451 84.3659 35.8064Z"
        fill="rgb(var(--raw-strong-400))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M123.372 73.8757C124.942 74.137 126.426 73.0764 126.687 71.5067C126.949 69.937 125.888 68.4527 124.318 68.1914C122.749 67.9301 121.264 68.9907 121.003 70.5604C120.742 72.13 121.802 73.6144 123.372 73.8757Z"
        fill="rgb(var(--raw-strong-400))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M83.1406 98.756C109.312 77.9371 100.04 47.4348 100.04 47.4348L111.514 51.9552C111.514 51.9552 101.722 85.875 103.679 115.697L83.1406 98.756Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M106.577 48.7233C106.577 48.7233 87.8167 33.5797 67.8499 63.5574L81.9538 61.3733L87.3847 55.1125L92.199 58.1527C92.199 58.1527 101.881 56.8089 106.577 48.7233Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M107.287 48.4908C107.287 48.4908 114.781 25.5752 79.6665 17.5559L86.717 29.9648L94.4985 32.8183L93.3661 38.3985C93.3661 38.3985 98.0609 46.9723 107.287 48.4908Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M106.523 48.647C106.523 48.647 129.178 40.3985 138.355 75.2284L125.72 68.5925L122.61 60.9098L117.071 62.2262C117.071 62.2262 108.346 57.8179 106.523 48.647Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M107.1 47.2998C107.1 47.2998 109.185 23.2803 144.634 29.6623L133.285 38.316L125.008 37.8921L123.863 43.4697C123.863 43.4697 116.184 49.5182 107.1 47.2998Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M69.0726 117.999C68.9014 117.999 68.7272 117.979 68.5539 117.937C67.3669 117.652 66.6361 116.46 66.9216 115.273L74.1586 85.1769C74.4441 83.9914 75.6418 83.2576 76.8238 83.5453C78.0108 83.8302 78.7416 85.0229 78.456 86.2099L71.2191 116.306C70.9752 117.319 70.0703 117.999 69.0726 117.999Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M104 122.12C103.382 122.12 102.769 121.863 102.331 121.361L57.028 69.2882C56.2273 68.3674 56.3244 66.9719 57.2452 66.1705C58.1639 65.3691 59.5608 65.4669 60.3629 66.3877L105.666 118.46C106.467 119.381 106.37 120.777 105.449 121.578C105.031 121.942 104.514 122.12 104 122.12Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M71.9047 116.267C71.9047 116.267 56.5296 121.465 42.9805 101.082C36.9247 91.9724 31.9951 77.6388 30.7578 73.8816C30.5665 73.3006 30.8131 72.672 31.3548 72.3876C32.9665 71.5418 37.0753 69.9725 46.2042 69.248C55.2686 68.5285 59.1695 68.8259 60.6791 69.0626C61.1781 69.1408 61.5745 69.5122 61.6935 70.0032C62.371 72.799 65.1501 82.8475 71.8445 91.3162C82.1768 104.387 97.8992 104.014 102.472 103.593C103.657 103.484 104.818 104.245 104.999 105.422C105.111 106.155 104.866 107.017 103.761 107.879C100.921 110.093 71.9047 116.267 71.9047 116.267Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M76.3089 135.057C75.6572 135.057 75.0125 134.771 74.5759 134.221L26.6145 73.7532C25.8563 72.7964 26.0167 71.4065 26.9727 70.6483C27.9288 69.8887 29.3178 70.0484 30.0775 71.0052L78.039 131.473C78.7972 132.43 78.6368 133.82 77.6808 134.578C77.2743 134.9 76.7902 135.057 76.3089 135.057Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M30.2684 125.948C29.2598 125.948 28.3484 125.253 28.1153 124.227C27.8448 123.037 28.5908 121.853 29.7807 121.582L74.5028 111.418C75.689 111.149 76.8767 111.892 77.1479 113.084C77.4184 114.274 76.6724 115.458 75.4825 115.728L30.7605 125.893C30.5958 125.93 30.4311 125.948 30.2684 125.948Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M38.9739 125.134C38.8028 125.134 38.6286 125.114 38.4552 125.072C37.2682 124.787 36.5374 123.595 36.823 122.408L44.0599 92.312C44.3455 91.125 45.5432 90.3985 46.7251 90.6805C47.9122 90.9654 48.643 92.158 48.3574 93.345L41.1205 123.441C40.8765 124.454 39.9717 125.134 38.9739 125.134Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx="37.5638"
        cy="32.6304"
        r="14.0922"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M37.5608 14.0817V7.94064"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M37.5609 57.3176V51.1765"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M56.1106 32.6268L62.2516 32.6268"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.8735 32.6268L19.0145 32.6268"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M50.6776 19.5135L55.02 15.1711"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.1047 50.0867L24.447 45.7444"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M50.6783 45.744L55.0207 50.0863"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.1057 15.1719L24.4481 19.5142"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M29.2575 28.2594L31.9558 30.958L29.2575 33.6563"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M45.8673 28.2592L43.169 30.9577L45.8673 33.6561"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M42.1014 37.9944H33.0268"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
