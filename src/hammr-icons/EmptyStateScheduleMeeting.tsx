export default function EmptyStateScheduleMeeting() {
  return (
    <svg width="109" height="108" viewBox="0 0 109 108" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Empty States [HR Management] [1.0]">
        <rect x="1" width="108" height="108" rx="54" fill="rgb(var(--raw-weak-100))" />
        <path
          id="vector"
          d="M100.241 89.6179C100.241 89.6179 95.8583 88.3122 94.5422 92.6757C95.8582 88.3122 91.4844 86.9768 91.4844 86.9768C91.4844 86.9768 95.8673 88.2824 97.1833 83.9189C95.8673 88.2824 100.241 89.6179 100.241 89.6179Z"
          fill="rgb(var(--raw-white-0))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_2"
          d="M9.13639 29.919C9.13639 29.919 5.39333 34.3102 9.75676 38.0553C5.39326 34.3101 1.62045 38.6757 1.62045 38.6757C1.62045 38.6757 5.3635 34.2845 1 30.5393C5.3635 34.2845 9.13639 29.919 9.13639 29.919Z"
          fill="rgb(var(--raw-white-0))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_3"
          d="M30.762 26.4706L18.9008 72.0722C18.2609 74.5322 19.7364 77.0452 22.1964 77.6851L79.2985 92.5377C81.7585 93.1775 84.2715 91.702 84.9114 89.242L96.7726 43.6403C97.4125 41.1803 95.937 38.6673 93.4769 38.0275L36.3749 23.1749C33.9148 22.535 31.4019 24.0106 30.762 26.4706Z"
          fill="rgb(var(--raw-soft-200))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_4"
          d="M43.9939 17.9029C44.003 18.4373 43.9393 18.9994 43.791 19.5693C43.4317 20.9599 42.6445 22.0644 41.7566 22.712C41.3133 23.0371 40.8503 23.2487 40.4044 23.3502C39.9567 23.4512 39.5288 23.4489 39.0993 23.3382C38.113 23.0816 37.1055 23.6732 36.8489 24.6595C36.5924 25.6458 37.184 26.6533 38.1703 26.9099C39.1939 27.1772 40.2442 27.1734 41.2256 26.9482C42.7018 26.6096 44.023 25.7951 45.0794 24.6794C46.1361 23.5613 46.9374 22.1312 47.3627 20.4983C47.5947 19.6061 47.6986 18.7112 47.6839 17.8402C47.6665 16.8212 46.8265 16.0092 45.8075 16.0266C44.7886 16.0439 43.9766 16.8839 43.9939 17.9029Z"
          fill="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_5"
          d="M63.7727 23.3427C63.7581 23.7874 63.6934 24.248 63.5722 24.7139C63.2129 26.1044 62.4257 27.2089 61.5378 27.8565C61.0945 28.1817 60.6315 28.3932 60.1857 28.4947C59.738 28.5957 59.31 28.5934 58.8806 28.4827C57.8943 28.2262 56.8868 28.8178 56.6302 29.8041C56.3736 30.7904 56.9652 31.7979 57.9515 32.0545C58.9752 32.3217 60.0255 32.318 61.0069 32.0928C62.483 31.7542 63.8043 30.9397 64.8607 29.8239C65.9174 28.7059 66.7186 27.2757 67.1439 25.6429C67.3337 24.9135 67.4377 24.1824 67.4613 23.4641C67.4948 22.4456 66.6963 21.5927 65.6777 21.5592C64.6591 21.5257 63.8062 22.3242 63.7727 23.3427Z"
          fill="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_6"
          d="M83.54 27.8439C83.5893 28.4796 83.5344 29.1634 83.3535 29.8585C82.9942 31.2491 82.207 32.3535 81.319 33.0011C80.8757 33.3263 80.4128 33.5378 79.9669 33.6393C79.5192 33.7403 79.0912 33.738 78.6618 33.6273C77.6755 33.3708 76.668 33.9624 76.4114 34.9487C76.1549 35.935 76.7465 36.9425 77.7328 37.1991C78.7564 37.4663 79.8067 37.4626 80.7881 37.2374C82.2643 36.8988 83.5855 36.0843 84.6419 34.9686C85.6986 33.8505 86.4999 32.4203 86.9252 30.7875C87.2082 29.699 87.3007 28.607 87.2195 27.5587C87.1407 26.5426 86.2532 25.7828 85.2371 25.8616C84.2211 25.9404 83.4612 26.8279 83.54 27.8439Z"
          fill="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_7"
          d="M91.9432 36.8488L80.0819 82.4504C79.4418 84.9111 76.9298 86.3861 74.4691 85.746L17.3637 70.8926C14.9062 70.2534 13.4313 67.7414 14.0713 65.2806L25.9326 19.6789C26.5727 17.2182 29.0847 15.7432 31.5421 16.3824L88.6475 31.2359C91.1083 31.8759 92.5833 34.388 91.9432 36.8488Z"
          fill="rgb(var(--raw-white-0))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_8"
          d="M91.9426 36.8488L89.9559 44.4866L23.9453 27.3168L25.932 19.679C26.572 17.2182 29.0841 15.7433 31.5414 16.3824L88.6469 31.2359C91.1076 31.876 92.5826 34.388 91.9426 36.8488Z"
          fill="rgb(var(--raw-sub-300))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_9"
          d="M39.1005 23.3381C38.6716 23.2255 38.2967 23.019 37.9549 22.7126C37.4454 22.2558 37.0215 21.5549 36.8019 20.6871C36.5814 19.8215 36.5681 18.8031 36.8398 17.7608C37.1991 16.3702 37.9863 15.2658 38.8743 14.6182C39.3176 14.293 39.7805 14.0815 40.2264 13.9799C40.6741 13.879 41.1021 13.8813 41.5315 13.9919C41.8731 14.0813 42.1786 14.2305 42.4627 14.4415C42.8869 14.7569 43.263 15.2208 43.5383 15.815C43.8134 16.4076 43.9823 17.1252 43.9951 17.9027C44.0124 18.9217 44.8524 19.7337 45.8714 19.7164C46.8903 19.6991 47.7024 18.8591 47.6851 17.8401C47.6551 16.1516 47.1853 14.5471 46.3028 13.2189C45.8613 12.5559 45.3139 11.9633 44.667 11.4815C44.021 10.9998 43.2741 10.6314 42.4605 10.4203C41.4368 10.153 40.3865 10.1567 39.4051 10.3819C37.929 10.7205 36.6077 11.535 35.5514 12.6508C34.4946 13.7688 33.6934 15.199 33.2681 16.8318C32.704 19.0095 32.8927 21.1973 33.7293 23.0322C34.1488 23.9488 34.7355 24.7792 35.4846 25.4545C36.2319 26.1294 37.1473 26.6444 38.1714 26.9098C39.1577 27.1663 40.1652 26.5748 40.4218 25.5884C40.6784 24.6021 40.0868 23.5946 39.1005 23.3381Z"
          fill="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_10"
          d="M58.8817 28.4826C58.4528 28.37 58.0779 28.1635 57.7362 27.8572C57.2266 27.4004 56.8027 26.6995 56.5831 25.8317C56.3626 24.966 56.3493 23.9476 56.621 22.9054C56.9804 21.5148 57.7676 20.4103 58.6555 19.7627C59.0988 19.4376 59.5617 19.226 60.0076 19.1245C60.4553 19.0235 60.8833 19.0258 61.3127 19.1365C61.6703 19.2301 61.9887 19.3892 62.2839 19.6163C62.7244 19.9556 63.1115 20.4592 63.3822 21.1014C63.6529 21.7418 63.8016 22.5142 63.7739 23.3427C63.7405 24.3613 64.539 25.2142 65.5576 25.2476C66.5762 25.2811 67.429 24.4825 67.4625 23.464C67.5196 21.68 67.0885 19.9711 66.2061 18.5527C65.7644 17.8447 65.2066 17.2103 64.5388 16.6948C63.8722 16.1795 63.0937 15.7859 62.2417 15.5649C61.2181 15.2976 60.1678 15.3013 59.1864 15.5265C57.7102 15.8651 56.389 16.6796 55.3326 17.7954C54.2759 18.9134 53.4746 20.3436 53.0493 21.9764C52.4852 24.1541 52.6739 26.3419 53.5105 28.1768C53.93 29.0934 54.5167 29.9238 55.2659 30.5991C56.0132 31.274 56.9285 31.789 57.9527 32.0544C58.939 32.3109 59.9465 31.7193 60.2031 30.733C60.4596 29.7467 59.868 28.7392 58.8817 28.4826Z"
          fill="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_11"
          d="M78.6551 33.6272C78.2262 33.5146 77.8513 33.3081 77.5095 33.0018C77 32.545 76.5761 31.8441 76.3564 30.9763C76.136 30.1106 76.1227 29.0923 76.3944 28.05C76.7537 26.6594 77.5409 25.5549 78.4289 24.9073C78.8721 24.5822 79.3351 24.3706 79.781 24.2691C80.2287 24.1681 80.6566 24.1704 81.0861 24.2811C81.4084 24.3654 81.6983 24.5031 81.9689 24.6954C82.373 24.9831 82.7346 25.4015 83.0124 25.9396C83.2897 26.4762 83.4782 27.1286 83.5332 27.8438C83.612 28.8599 84.4996 29.6198 85.5156 29.541C86.5317 29.4622 87.2915 28.5747 87.2128 27.5586C87.0899 25.987 86.5807 24.5069 85.7032 23.2854C85.2644 22.6757 84.7314 22.1322 84.1108 21.69C83.4909 21.2477 82.7826 20.9087 82.0151 20.7094C80.9915 20.4422 79.9412 20.4459 78.9598 20.6711C77.4836 21.0097 76.1624 21.8242 75.106 22.94C74.0493 24.058 73.248 25.4882 72.8228 27.121C72.2587 29.2987 72.4474 31.4865 73.2839 33.3213C73.7035 34.2379 74.2901 35.0684 75.0393 35.7437C75.7866 36.4185 76.702 36.9336 77.7261 37.1989C78.7124 37.4555 79.7199 36.8639 79.9765 35.8776C80.2331 34.8913 79.6414 33.8838 78.6551 33.6272Z"
          fill="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_12"
          d="M54.3592 98.0141L23.8307 89.9943C23.7457 89.972 23.667 89.9459 23.5884 89.9199C23.5821 89.9236 23.5771 89.9223 23.5734 89.916C23.4758 89.901 23.3808 89.876 23.2859 89.8511C20.0871 89.0108 18.1782 85.7416 19.0185 82.5429C19.528 80.6036 20.9329 79.14 22.6744 78.4701C23.7977 78.0385 25.0672 77.9446 26.3268 78.2755L57.182 82.0212C53.2756 81.6575 49.5745 84.1529 48.5465 88.0664C47.4042 92.4148 50.0058 96.8704 54.3592 98.0141Z"
          fill="rgb(var(--raw-sub-300))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path
          id="vector_13"
          d="M72.089 62.5022L64.303 92.2063C63.1607 96.5546 58.705 99.1563 54.3567 98.014C50.0033 96.8704 47.4016 92.4148 48.5439 88.0664C49.572 84.1529 53.2731 81.6575 57.1795 82.0212L26.3243 78.2755C25.0647 77.9446 23.7951 78.0385 22.6719 78.4701L29.7699 51.385C30.3384 49.2209 32.5536 47.9274 34.7129 48.4946L69.2049 57.5556C71.3691 58.1242 72.6576 60.338 72.089 62.5022Z"
          fill="rgb(var(--raw-weak-100))"
          stroke="rgb(var(--raw-soft-400))"
        />
        <path id="vector_14" d="M37.2734 57.558L41.9917 65.5936" stroke="rgb(var(--raw-soft-400))" />
        <path id="vector_15" d="M43.6528 59.2167L35.6172 63.9349" stroke="rgb(var(--raw-soft-400))" />
        <path id="vector_16" d="M56.75 62.6249L61.4682 70.6604" stroke="rgb(var(--raw-soft-400))" />
        <path id="vector_17" d="M63.1294 64.2836L55.0938 69.0019" stroke="rgb(var(--raw-soft-400))" />
        <path id="vector_18" d="M37.8906 71.5255L54.4248 75.8261" stroke="rgb(var(--raw-soft-400))" />
        <path
          id="vector_19"
          d="M47.6875 74.0744L51.9537 75.184L51.2924 77.7264C51.004 78.8353 49.8696 79.5014 48.7607 79.2129L48.5127 79.1484C47.4038 78.86 46.7378 77.7256 47.0262 76.6168L47.6875 74.0744Z"
          fill="rgb(var(--raw-white-0))"
          stroke="rgb(var(--raw-soft-400))"
        />
      </g>
    </svg>
  );
}
