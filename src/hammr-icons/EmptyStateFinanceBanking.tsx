const EmptyStateFinanceBanking = (props) => (
  <svg width="148" height="149" viewBox="0 0 148 149" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <rect width="148" height="148" rx="74" className="fill-weak-100" />
    <path
      d="M116.118 44.5246L91.3563 60.1001V89.3606C75.1983 89.3606 62.1025 76.258 62.1025 60.1001C62.1025 43.9421 75.1983 30.8461 91.3563 30.8461C101.79 30.8461 110.942 36.3066 116.118 44.5246Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M120.609 60.1001H91.3555L116.118 44.5247C118.963 49.0299 120.609 54.3752 120.609 60.1001Z"
      className="fill-sub-300 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M120.609 60.1003C120.609 76.2583 107.514 89.3609 91.3555 89.3609V60.1003H120.609Z"
      className="fill-white-0 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M80.483 57.7289H36.6226C32.869 57.7289 29.8262 60.7717 29.8262 64.5253V113.618C29.8262 117.372 32.869 120.415 36.6226 120.415H80.483C84.2365 120.415 87.2794 117.372 87.2794 113.618V64.5253C87.2794 60.7717 84.2365 57.7289 80.483 57.7289Z"
      className="fill-white-0 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M78.206 63.4734H38.8991C37.4808 63.4734 36.3311 64.6232 36.3311 66.0415V74.4483C36.3311 75.8666 37.4808 77.0164 38.8991 77.0164H78.206C79.6243 77.0164 80.7741 75.8666 80.7741 74.4483V66.0415C80.7741 64.6232 79.6243 63.4734 78.206 63.4734Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M45.7715 84.392H38.7064C37.3945 84.392 36.3311 85.4554 36.3311 86.7673V88.6837C36.3311 89.9956 37.3945 91.059 38.7064 91.059H45.7715C47.0833 91.059 48.1468 89.9956 48.1468 88.6837V86.7673C48.1468 85.4554 47.0833 84.392 45.7715 84.392Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M62.085 84.392H55.0199C53.708 84.392 52.6445 85.4555 52.6445 86.7674V88.6838C52.6445 89.9956 53.708 91.0591 55.0199 91.0591H62.085C63.3968 91.0591 64.4603 89.9956 64.4603 88.6838V86.7674C64.4603 85.4555 63.3968 84.392 62.085 84.392Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M78.3984 84.392H71.3333C70.0215 84.392 68.958 85.4554 68.958 86.7673V88.6837C68.958 89.9956 70.0215 91.059 71.3333 91.059H78.3984C79.7103 91.059 80.7738 89.9956 80.7738 88.6837V86.7673C80.7738 85.4554 79.7103 84.392 78.3984 84.392Z"
      className="fill-strong-400 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M45.7715 96.197H38.7064C37.3945 96.197 36.3311 97.2604 36.3311 98.5723V100.489C36.3311 101.801 37.3945 102.864 38.7064 102.864H45.7715C47.0833 102.864 48.1468 101.801 48.1468 100.489V98.5723C48.1468 97.2604 47.0833 96.197 45.7715 96.197Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M62.085 96.1971H55.0199C53.708 96.1971 52.6445 97.2606 52.6445 98.5724V100.489C52.6445 101.801 53.708 102.864 55.0199 102.864H62.085C63.3968 102.864 64.4603 101.801 64.4603 100.489V98.5724C64.4603 97.2606 63.3968 96.1971 62.085 96.1971Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M78.3984 96.197H71.3333C70.0215 96.197 68.958 97.2604 68.958 98.5723V100.489C68.958 101.801 70.0215 102.864 71.3333 102.864H78.3984C79.7103 102.864 80.7738 101.801 80.7738 100.489V98.5723C80.7738 97.2604 79.7103 96.197 78.3984 96.197Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M45.7715 108.002H38.7064C37.3945 108.002 36.3311 109.066 36.3311 110.378V112.294C36.3311 113.606 37.3945 114.67 38.7064 114.67H45.7715C47.0833 114.67 48.1468 113.606 48.1468 112.294V110.378C48.1468 109.066 47.0833 108.002 45.7715 108.002Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M62.085 108.002H55.0199C53.708 108.002 52.6445 109.066 52.6445 110.378V112.294C52.6445 113.606 53.708 114.669 55.0199 114.669H62.085C63.3968 114.669 64.4603 113.606 64.4603 112.294V110.378C64.4603 109.066 63.3968 108.002 62.085 108.002Z"
      className="fill-soft-200 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M78.3984 108.002H71.3333C70.0215 108.002 68.958 109.066 68.958 110.378V112.294C68.958 113.606 70.0215 114.67 71.3333 114.67H78.3984C79.7103 114.67 80.7738 113.606 80.7738 112.294V110.378C80.7738 109.066 79.7103 108.002 78.3984 108.002Z"
      className="fill-sub-300 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M114.234 101.139L116.608 104.355C118.33 106.688 114.979 112.084 109.119 116.409C103.265 120.73 97.1212 122.343 95.3992 120.01L93.0254 116.794C93.4439 117.361 94.1296 117.697 95.0081 117.815C96.1141 117.967 97.5287 117.773 99.1166 117.266C101.431 116.535 104.122 115.138 106.749 113.199C109.377 111.259 111.504 109.099 112.891 107.099C113.843 105.731 114.445 104.436 114.625 103.335C114.772 102.46 114.653 101.706 114.234 101.139Z"
      className="fill-sub-300 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M114.234 101.139C114.652 101.706 114.771 102.46 114.625 103.335C114.445 104.436 113.843 105.731 112.89 107.099C111.504 109.099 109.376 111.259 106.749 113.199C104.122 115.138 101.431 116.535 99.1163 117.266C97.5284 117.773 96.1137 117.967 95.0078 117.815C94.1293 117.697 93.4435 117.361 93.0251 116.794C91.303 114.461 94.6545 109.066 100.509 104.744C106.368 100.419 112.512 98.8063 114.234 101.139Z"
      className="fill-white-0 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M48.0612 47.9063L44.9588 50.4267C42.7083 52.2551 37.1629 49.1574 32.5707 43.5046C27.9827 37.8572 26.0866 31.7948 28.3371 29.9665L31.4395 27.4461C30.8927 27.8904 30.5889 28.591 30.512 29.474C30.4113 30.5858 30.6706 31.99 31.2504 33.5526C32.0887 35.8308 33.6087 38.454 35.6678 40.9885C37.7269 43.5231 39.983 45.5482 42.0454 46.8404C43.4562 47.7282 44.7775 48.2695 45.8864 48.3987C46.7664 48.5043 47.5144 48.3506 48.0612 47.9063Z"
      className="fill-sub-300 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M48.0607 47.9063C47.5138 48.3506 46.7658 48.5043 45.8858 48.3988C44.7769 48.2696 43.4556 47.7282 42.0448 46.8405C39.9825 45.5483 37.7262 43.523 35.6672 40.9886C33.6082 38.4541 32.0881 35.8309 31.2499 33.5527C30.67 31.99 30.4107 30.5858 30.5115 29.4741C30.5884 28.591 30.892 27.8904 31.439 27.4462C33.6896 25.6178 39.235 28.7156 43.8229 34.363C48.4151 40.0158 50.3113 46.0779 48.0607 47.9063Z"
      className="fill-white-0 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M86.5633 6.77256C86.5633 6.77256 81.1912 13.0749 87.4537 18.45C81.1911 13.0748 75.7762 19.3404 75.7762 19.3404C75.7762 19.3404 81.1483 13.038 74.8857 7.66282C81.1483 13.038 86.5633 6.77256 86.5633 6.77256Z"
      className="fill-white-0 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M52.4265 139.427C52.4265 139.427 44.1714 140.085 44.7989 148.314C44.1714 140.085 35.9121 140.686 35.9121 140.686C35.9121 140.686 44.1672 140.028 43.5397 131.799C44.1672 140.028 52.4265 139.427 52.4265 139.427Z"
      className="fill-white-0 stroke-strong-400"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default EmptyStateFinanceBanking;
