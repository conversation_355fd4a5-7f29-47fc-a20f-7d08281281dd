// the sub-300 color here looks off in dark mode, because, even though it's
// neutral-600 everywhere else, in illustrations it should be neutral-700
export default function EmptyStateTimeTracker() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="148" height="148" viewBox="0 0 148 148" fill="none">
      <rect width="148" height="148" rx="74" fill="rgb(var(--raw-weak-100))" />
      <path
        d="M126.908 119.268V119.218"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M126.885 113.974V113.656"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M126.908 119.21V119.218C126.908 120.161 126.229 120.797 124.964 120.797H26.2219C24.9108 120.797 24.2284 120.111 24.2859 119.107V113.87C24.3664 114.721 25.0373 115.281 26.2219 115.281H124.964C126.102 115.281 126.766 114.767 126.885 113.974L126.908 119.21Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M24.2859 113.87V113.721"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M109.731 43.0414H41.4567C39.2771 43.0414 37.5101 44.8083 37.5101 46.9879V89.5236C37.5101 91.7032 39.2771 93.4702 41.4567 93.4702H109.731C111.91 93.4702 113.677 91.7032 113.677 89.5236V46.9879C113.677 44.8083 111.91 43.0414 109.731 43.0414Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M108.187 48.059H43.0005V88.4525H108.187V48.059Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M126.885 113.974C126.766 114.767 126.102 115.281 124.964 115.281H26.2219C25.0373 115.281 24.3664 114.721 24.2859 113.87C24.2322 113.295 24.4546 112.586 24.9683 111.831L36.1931 95.3999C36.9291 94.3188 38.6734 93.4716 40.1072 93.4716H111.082C112.516 93.4716 114.257 94.3188 114.997 95.3999L126.217 111.831C126.766 112.632 126.98 113.379 126.885 113.974Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M83.7997 118.485H67.0149C66.2696 118.485 65.6192 117.979 65.4351 117.257L64.9335 115.289H85.8811L85.3795 117.257C85.1954 117.979 84.545 118.485 83.7997 118.485Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M52.1838 99.7729H45.2684C44.5598 99.7729 44.166 99.4521 44.386 99.0614L45.3936 97.2719C45.5968 96.911 46.3018 96.622 46.9706 96.622H53.4978C54.1667 96.622 54.5949 96.911 54.4527 97.2719L53.7473 99.0614C53.5933 99.4521 52.8924 99.7729 52.1838 99.7729Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M65.6188 99.7729H58.7034C57.9948 99.7729 57.5243 99.4521 57.6507 99.0614L58.2301 97.2719C58.347 96.911 58.9827 96.622 59.6515 96.622H66.1788C66.8476 96.622 67.3451 96.911 67.2892 97.2719L67.012 99.0614C66.9515 99.4521 66.3274 99.7729 65.6188 99.7729Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M79.0534 99.7729H72.138C71.4294 99.7729 70.882 99.4521 70.9151 99.0614L71.0662 97.2719C71.0967 96.911 71.6633 96.622 72.3321 96.622H78.8594C79.5282 96.622 80.0948 96.911 80.1253 97.2719L80.2764 99.0614C80.3094 99.4521 79.762 99.7729 79.0534 99.7729Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M92.488 99.7729H85.5727C84.8641 99.7729 84.24 99.4521 84.1794 99.0614L83.9023 97.2719C83.8464 96.911 84.3438 96.622 85.0127 96.622H91.5399C92.2088 96.622 92.8446 96.911 92.9614 97.2719L93.5407 99.0614C93.6673 99.4521 93.1966 99.7729 92.488 99.7729Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M105.923 99.7729H99.0072C98.2986 99.7729 97.5977 99.4521 97.4437 99.0614L96.7383 97.2719C96.596 96.911 97.0243 96.622 97.6931 96.622H104.22C104.889 96.622 105.594 96.911 105.797 97.2719L106.805 99.0614C107.025 99.4521 106.631 99.7729 105.923 99.7729Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M53.9392 105.339H42.5298C41.6027 105.339 41.1104 104.879 41.425 104.32L42.4603 102.481C42.7509 101.965 43.6882 101.553 44.5584 101.553H55.2678C56.138 101.553 56.7102 101.965 56.5431 102.481L55.9479 104.32C55.767 104.879 54.8663 105.339 53.9392 105.339Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M68.9743 105.339H61.3733C60.5944 105.339 60.0612 104.952 60.1805 104.481L60.7251 102.331C60.8345 101.899 61.5147 101.553 62.2458 101.553H69.3805C70.1116 101.553 70.6751 101.899 70.6386 102.331L70.4571 104.481C70.4173 104.952 69.7532 105.339 68.9743 105.339Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M83.7411 105.339H76.14C75.3612 105.339 74.7352 104.952 74.7418 104.481L74.772 102.331C74.778 101.899 75.3755 101.553 76.1066 101.553H83.2414C83.9724 101.553 84.6186 101.899 84.6855 102.331L85.0184 104.481C85.0913 104.952 84.5199 105.339 83.7411 105.339Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M98.0624 105.339H90.8742C90.1135 105.339 89.4115 104.961 89.3078 104.5L88.8149 102.313C88.7199 101.891 89.2228 101.553 89.937 101.553H96.6841C97.3982 101.553 98.1075 101.891 98.2705 102.313L99.1159 104.5C99.2938 104.961 98.8232 105.339 98.0624 105.339Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M109.156 105.339H104.178C103.524 105.339 102.85 105.014 102.674 104.617L101.602 102.205C101.442 101.843 101.811 101.553 102.425 101.553H107.097C107.71 101.553 108.371 101.843 108.575 102.205L109.933 104.617C110.156 105.014 109.81 105.339 109.156 105.339Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M47.0301 112.131H38.5925C37.7279 112.131 37.2956 111.654 37.6217 111.074L39.1031 108.443C39.3993 107.917 40.2894 107.497 41.0956 107.497H48.9624C49.7685 107.497 50.2563 107.917 50.0489 108.443L49.0119 111.074C48.7836 111.654 47.8947 112.131 47.0301 112.131Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M96.206 112.131H54.9844C54.1198 112.131 53.5733 111.654 53.7609 111.074L54.6126 108.443C54.7829 107.917 55.5725 107.497 56.3786 107.497H94.8118C95.6179 107.497 96.4074 107.917 96.5778 108.443L97.4295 111.074C97.6171 111.654 97.0706 112.131 96.206 112.131Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M112.598 112.131H104.16C103.296 112.131 102.407 111.654 102.179 111.074L101.141 108.443C100.934 107.917 101.422 107.497 102.228 107.497H110.095C110.901 107.497 111.791 107.917 112.087 108.443L113.569 111.074C113.895 111.654 113.463 112.131 112.598 112.131Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M72.4457 75.2368H55.0326C54.3975 75.2368 53.8826 75.7517 53.8826 76.3868V83.0889C53.8826 83.7241 54.3975 84.239 55.0326 84.239H72.4457C73.0809 84.239 73.5958 83.7241 73.5958 83.0889V76.3868C73.5958 75.7517 73.0809 75.2368 72.4457 75.2368Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M102.02 75.2368H88.8073C88.1721 75.2368 87.6572 75.7517 87.6572 76.3868V83.0889C87.6572 83.7241 88.1721 84.239 88.8073 84.239H102.02C102.655 84.239 103.17 83.7241 103.17 83.0889V76.3868C103.17 75.7517 102.655 75.2368 102.02 75.2368Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M86.5072 63.7546H63.2117C62.5765 63.7546 62.0616 64.2695 62.0616 64.9047V71.6068C62.0616 72.242 62.5765 72.7569 63.2117 72.7569H86.5072C87.1424 72.7569 87.6573 72.242 87.6573 71.6068V64.9047C87.6573 64.2695 87.1424 63.7546 86.5072 63.7546Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M29.3795 49.4708C29.3795 49.4708 23.3734 47.6815 21.5698 53.6612C23.3733 47.6816 17.3795 45.8515 17.3795 45.8515C17.3795 45.8515 23.3856 47.6407 25.1891 41.6612C23.3856 47.6407 29.3795 49.4708 29.3795 49.4708Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M137 74.4112C137 74.4112 131.012 78.0204 134.589 84C131.012 78.0205 125 81.5889 125 81.5889C125 81.5889 130.988 77.9795 127.411 72C130.988 77.9795 137 74.4112 137 74.4112Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M62.049 52.0944H49.4835C48.8483 52.0944 48.3334 52.6093 48.3334 53.2444V59.9465C48.3334 60.5817 48.8483 61.0966 49.4835 61.0966H62.049C62.6841 61.0966 63.199 60.5817 63.199 59.9465V53.2444C63.199 52.6093 62.6841 52.0944 62.049 52.0944Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M102.603 52.0944H90.0379C89.4027 52.0944 88.8878 52.6093 88.8878 53.2444V59.9465C88.8878 60.5817 89.4027 61.0966 90.0379 61.0966H102.603C103.239 61.0966 103.753 60.5817 103.753 59.9465V53.2444C103.753 52.6093 103.239 52.0944 102.603 52.0944Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M91.9178 56.4187C92.5915 56.4187 93.1377 55.8726 93.1377 55.1988C93.1377 54.5251 92.5915 53.9789 91.9178 53.9789C91.2441 53.9789 90.6979 54.5251 90.6979 55.1988C90.6979 55.8726 91.2441 56.4187 91.9178 56.4187Z"
        fill="rgb(var(--raw-strong-400))"
      />
      <path
        d="M78.1149 17.9365H72.8003V25.3755H78.1149V17.9365Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M95.3481 28.6575L93.2202 26.5295L90.2417 29.508L92.3696 31.636L95.3481 28.6575Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M75.4111 67.4958C87.733 67.4958 97.7218 57.507 97.7218 45.1852C97.7218 32.8633 87.733 22.8745 75.4111 22.8745C63.0893 22.8745 53.1005 32.8633 53.1005 45.1852C53.1005 57.507 63.0893 67.4958 75.4111 67.4958Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M75.4113 62.072C84.7377 62.072 92.2982 54.5115 92.2982 45.1851C92.2982 35.8588 84.7377 28.2983 75.4113 28.2983C66.085 28.2983 58.5245 35.8588 58.5245 45.1851C58.5245 54.5115 66.085 62.072 75.4113 62.072Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M78.9238 14.7505H71.8987C70.4945 14.7505 69.3561 15.8889 69.3561 17.2931V17.2932C69.3561 18.6974 70.4945 19.8358 71.8987 19.8358H78.9238C80.3281 19.8358 81.4664 18.6974 81.4664 17.2932V17.2931C81.4664 15.8889 80.3281 14.7505 78.9238 14.7505Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M96.9474 27.7056L94.1346 24.8929C93.5724 24.3306 92.6608 24.3306 92.0985 24.8929L92.0985 24.8929C91.5363 25.4551 91.5363 26.3667 92.0985 26.929L94.9113 29.7417C95.4735 30.3039 96.3851 30.3039 96.9473 29.7417L96.9473 29.7417C97.5096 29.1794 97.5096 28.2679 96.9474 27.7056Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M75.4111 34.9485H75.4111C74.2327 34.9485 73.2775 35.9038 73.2775 37.0822V45.0577C73.2775 46.2361 74.2327 47.1914 75.4111 47.1914H75.4111C76.5895 47.1914 77.5448 46.2361 77.5448 45.0577V37.0822C77.5448 35.9038 76.5895 34.9485 75.4111 34.9485Z"
        fill="rgb(var(--raw-strong-400))"
      />
      <path
        d="M82.2067 45.1852V45.1851C82.2067 44.0068 81.2515 43.0515 80.0731 43.0515H75.4113C74.2329 43.0515 73.2777 44.0068 73.2777 45.1851V45.1852C73.2777 46.3635 74.2329 47.3188 75.4113 47.3188H80.0731C81.2515 47.3188 82.2067 46.3635 82.2067 45.1852Z"
        fill="rgb(var(--raw-strong-400))"
      />
      <path
        d="M93.7124 19.7548V13.8079"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M101.521 27.3172H107.468"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M97.9728 22.0648L105.069 13.8079"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
