export default function EmptyStateDailyWorkHours() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="148" height="148" viewBox="0 0 148 148" fill="none">
      <rect width="148" height="148" rx="74" fill="rgb(var(--raw-weak-100))" />
      <path
        d="M42.4319 120.821C42.4319 120.821 36.3825 120.841 36.3825 126.87C36.3825 120.841 30.3331 120.821 30.3331 120.821C30.3331 120.821 36.3825 120.8 36.3825 114.771C36.3825 120.8 42.4319 120.821 42.4319 120.821Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M90.4289 16.1516C90.4289 16.1516 84.3796 16.1721 84.3796 22.2009C84.3796 16.1721 78.3302 16.1516 78.3302 16.1516C78.3302 16.1516 84.3796 16.1309 84.3796 10.1022C84.3796 16.1309 90.4289 16.1516 90.4289 16.1516Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M63.2641 103.702H82.4344L84.6666 120.009H60.9006L63.2641 103.702Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M59.1448 103.702H78.3152L80.5473 120.009H56.7813L59.1448 103.702Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.2057 38.3127C25.2057 35.5285 27.4627 33.2715 30.2468 33.2715H116.954C119.739 33.2715 121.996 35.5285 121.996 38.3127V98.8064C121.996 101.591 119.739 103.848 116.954 103.848H30.2468C27.4627 103.848 25.2057 101.591 25.2057 98.8064V38.3127Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
      />
      <path
        d="M19.1563 38.3127C19.1563 35.5285 21.4133 33.2715 24.1975 33.2715H110.905C113.689 33.2715 115.946 35.5285 115.946 38.3127V98.8064C115.946 101.591 113.689 103.848 110.905 103.848H24.1975C21.4133 103.848 19.1563 101.591 19.1563 98.8064V38.3127Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
      />
      <path
        d="M19.1563 88.7241H109.897C112.681 88.7241 114.938 90.9811 114.938 93.7652V98.8063C114.938 101.59 112.681 103.847 109.897 103.847H24.1975C21.4133 103.847 19.1563 101.59 19.1563 98.8063V88.7241Z"
        fill="rgb(var(--raw-strong-400))"
      />
      <path
        d="M45.0735 62.2926L42.8052 60.0242"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M31.2825 60.0242L29.0142 62.2926"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M33.9716 47.0182C38.0687 45.321 42.765 47.2661 44.4622 51.3632C46.1594 55.4602 44.2133 60.1565 40.1172 61.8528C36.0202 63.55 31.3239 61.6038 29.6277 57.5078C27.9304 53.4117 29.8756 48.7144 33.9716 47.0182"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M36.7933 50.8532V55.0035L40.0553 56.9928"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M32.5416 44.4977C31.3402 44.1564 29.9983 44.4425 29.0528 45.3879C28.2388 46.2029 27.9156 47.313 28.0561 48.3739"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M41.5594 44.4977C42.7609 44.1564 44.1018 44.4425 45.0473 45.3879C45.8623 46.2029 46.1855 47.313 46.0449 48.3739"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect x="55.7232" y="42.9979" width="7.85508" height="7.85508" rx="0.980636" fill="rgb(var(--raw-soft-200))" />
      <rect x="55.7232" y="58.3656" width="7.85508" height="7.85508" rx="0.980636" fill="rgb(var(--raw-soft-200))" />
      <rect x="70.2117" y="44.569" width="35.3418" height="3.80468" rx="0.980636" fill="rgb(var(--raw-soft-200))" />
      <rect x="70.2117" y="60.5638" width="20.7523" height="3.80468" rx="0.980636" fill="rgb(var(--raw-soft-200))" />
      <rect x="29.222" y="72.5013" width="7.85508" height="7.85508" rx="0.980636" fill="rgb(var(--raw-soft-200))" />
      <rect x="43.9985" y="74.5272" width="43.6365" height="3.80468" rx="0.980636" fill="rgb(var(--raw-soft-200))" />
      <circle
        cx="67.6026"
        cy="96.3586"
        r="3.44035"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M116.923 123.203L80.2188 123.18C80.1166 123.18 80.0204 123.174 79.9243 123.168C79.9183 123.174 79.9123 123.174 79.9063 123.168C79.7921 123.18 79.6779 123.18 79.5638 123.18C75.7178 123.177 72.607 120.063 72.6094 116.217C72.6108 113.885 73.7598 111.825 75.5213 110.558C76.6575 109.741 78.058 109.262 79.5723 109.262L115.384 104.381C110.883 105.123 107.455 109.021 107.452 113.726C107.449 118.954 111.689 123.199 116.923 123.203Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M126.402 78.0301L126.399 113.738C126.396 118.966 122.151 123.206 116.923 123.203C111.689 123.2 107.449 118.955 107.452 113.726C107.455 109.021 110.883 105.123 115.384 104.381L79.572 109.263C78.0576 109.262 76.6572 109.741 75.5209 110.558L75.5216 77.9989C75.5232 75.3969 77.6337 73.289 80.2297 73.2906L121.699 73.316C124.301 73.3176 126.403 75.4281 126.402 78.0301Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M85.7817 82.7314L93.4577 90.3796"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M93.4435 82.7174L85.7953 90.3934"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M109.188 82.688L116.864 90.3362"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M116.849 82.6743L109.201 90.3503"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M90.5894 98.2612L110.456 98.2252"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
