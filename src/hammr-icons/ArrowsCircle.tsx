interface Props extends React.SVGProps<SVGSVGElement> {}

export default function ArrowsCircle(props: Props) {
  return (
    <svg {...props} width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7 2C8.71788 2 10.2343 2.86641 11.1348 4.1875H9.5V5.4375H13.25V1.6875H12V3.2496C10.8601 1.73229 9.04525 0.75 7 0.75C3.54822 0.75 0.75 3.54822 0.75 7H2C2 4.23857 4.23857 2 7 2ZM12 7C12 9.76144 9.76144 12 7 12C5.28215 12 3.76567 11.1336 2.86527 9.8125H4.5V8.5625H0.75V12.3125H2V10.7504C3.13988 12.2677 4.95477 13.25 7 13.25C10.4517 13.25 13.25 10.4517 13.25 7H12Z" />
    </svg>
  );
}
