import { forwardRef, LegacyRef } from 'react';

const InfoCustomFill = forwardRef((props: React.SVGProps<SVGSVGElement>, ref: LegacyRef<SVGSVGElement>) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.5 19.5C16.6421 19.5 20 16.1421 20 12C20 7.85786 16.6421 4.5 12.5 4.5C8.35786 4.5 5 7.85786 5 12C5 16.1421 8.35786 19.5 12.5 19.5ZM13.839 15.8503L13.9588 15.3607C13.8968 15.3898 13.7968 15.4231 13.6598 15.461C13.5224 15.4988 13.3987 15.5181 13.29 15.5181C13.0584 15.5181 12.8954 15.4802 12.8008 15.4039C12.7068 15.3276 12.66 15.1841 12.66 14.9739C12.66 14.8906 12.6741 14.7665 12.7037 14.604C12.7323 14.4405 12.7653 14.2951 12.8019 14.168L13.249 12.5851C13.2928 12.4398 13.3229 12.2801 13.339 12.1058C13.3555 11.9319 13.3632 11.8102 13.3632 11.7412C13.3632 11.4074 13.2462 11.1365 13.0121 10.9275C12.778 10.7187 12.4447 10.6143 12.0128 10.6143C11.7724 10.6143 11.5183 10.6571 11.2493 10.7424C10.9804 10.8275 10.6992 10.9301 10.4051 11.0498L10.285 11.5398C10.3726 11.5074 10.477 11.4725 10.5992 11.4364C10.7208 11.4005 10.8401 11.3819 10.9562 11.3819C11.1932 11.3819 11.3528 11.4223 11.4364 11.5019C11.5201 11.5817 11.5621 11.7236 11.5621 11.9266C11.5621 12.0388 11.5488 12.1635 11.5213 12.299C11.4941 12.4354 11.4601 12.5796 11.4202 12.7318L10.9711 14.321C10.9312 14.488 10.902 14.6374 10.8836 14.7701C10.8654 14.9026 10.8567 15.0327 10.8567 15.1591C10.8567 15.4858 10.9774 15.755 11.2187 15.9675C11.46 16.1792 11.7983 16.2857 12.2332 16.2857C12.5165 16.2857 12.7651 16.2487 12.979 16.1742C13.1927 16.1 13.4797 15.9921 13.839 15.8503ZM13.7594 9.42024C13.9682 9.22658 14.0722 8.99105 14.0722 8.71526C14.0722 8.44009 13.9684 8.20409 13.7594 8.00797C13.551 7.81239 13.2999 7.71429 13.0063 7.71429C12.7117 7.71429 12.4596 7.81216 12.2493 8.00797C12.0389 8.20409 11.9336 8.44001 11.9336 8.71526C11.9336 8.99105 12.0389 9.2265 12.2493 9.42024C12.46 9.6146 12.7117 9.71186 13.0063 9.71186C13.3 9.71186 13.551 9.6146 13.7594 9.42024Z"
      fill="currentColor"
    />
  </svg>
));

InfoCustomFill.displayName = 'InfoCustomFill';

export default InfoCustomFill;
