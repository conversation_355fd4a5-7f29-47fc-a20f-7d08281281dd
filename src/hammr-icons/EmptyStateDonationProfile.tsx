export default function EmptyStateDonationProfile() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="148" height="148" viewBox="0 0 148 148" fill="none">
      <rect x="-0.000244141" width="148" height="148" rx="74" fill="rgb(var(--raw-weak-100))" />
      <path
        d="M115.151 73.7284V122.026C115.151 125.563 112.292 128.422 108.755 128.422H39.2519C37.8359 128.422 36.5217 127.961 35.4647 127.189C33.8794 126.023 32.8496 124.147 32.8496 122.026V73.7284C32.8496 72.6443 33.1206 71.6213 33.5948 70.727L33.8591 70.2799C34.9973 68.4982 36.9891 67.326 39.2519 67.326H108.755C110.483 67.326 112.048 68.0105 113.199 69.1214L113.206 69.1282C113.315 69.2297 113.416 69.3383 113.518 69.4534C114.534 70.5847 115.151 72.082 115.151 73.7284Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M92.3896 101.461C90.9804 103.649 89.1038 105.803 86.7732 107.958C83.0606 111.223 79.0973 114.191 74.9172 116.819L74.0094 117.389L73.1016 116.833C68.8876 114.238 64.9039 111.264 61.1777 107.917C57.0722 104.015 54.2335 99.889 53.0073 95.614C57.4313 92.3079 63.3728 90.9596 68.7047 92.4839C72.2818 93.5071 75.4321 95.6208 78.6095 97.5515C81.7937 99.4892 85.1811 101.312 88.8871 101.664C90.0456 101.772 91.2379 101.705 92.3896 101.461Z"
        fill="rgb(var(--raw-strong-400))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M95.6824 90.8514C95.6824 94.5979 94.5848 98.0733 92.3898 101.461C91.2381 101.705 90.0457 101.773 88.8872 101.664C85.1813 101.312 81.7939 99.4893 78.6096 97.5516C75.4323 95.6208 72.2821 93.5072 68.7049 92.484C63.373 90.9596 57.4315 92.308 53.0075 95.614C52.5536 94.0422 52.3232 92.4502 52.3232 90.8445C52.3368 85.1062 56.1985 80.1063 61.6997 78.7379C66.2862 77.5928 71.0286 79.2257 74.0028 82.7553C76.3063 80.0047 79.7141 78.3652 83.3656 78.3652H83.3859C86.6447 78.3652 89.7678 79.6795 92.0714 82.0168C94.3883 84.3611 95.6824 87.5385 95.6824 90.8514Z"
        fill="rgb(var(--raw-weak-100))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M113.518 69.4535C113.416 69.3384 113.315 69.2298 113.206 69.1283L113.199 69.1215C112.048 68.0105 110.483 67.3261 108.755 67.3261H39.252C36.9891 67.3261 34.9974 68.4983 33.8591 70.28L39.8684 60.2125L39.882 60.1855C40.7831 58.4579 42.5919 57.2791 44.6719 57.2791H103.329C104.785 57.2791 106.113 57.8549 107.082 58.7967H107.089C107.346 59.0474 107.583 59.3251 107.78 59.6232L107.8 59.6639L108.145 60.2533L113.518 69.4535Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M88.7125 64.3283H59.2881L60.7576 60.1475H87.2429L88.7125 64.3283Z"
        fill="rgb(var(--raw-weak-100))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M88.7146 64.3266H59.2847L60.7547 60.1464H87.2445L88.7146 64.3266Z"
        fill="rgb(var(--raw-weak-100))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M83.9522 60.1463C83.7421 60.5934 83.5186 61.0407 83.2679 61.4878C82.7055 62.5108 82.0823 63.4592 81.4184 64.3266H76.6082C77.7397 63.1476 78.81 61.7385 79.7314 60.1463C79.8195 59.9973 79.9076 59.8483 79.9889 59.6993C81.5674 56.8401 82.4414 53.8999 82.5972 51.3526C82.7123 49.6113 82.4821 48.0533 81.9197 46.827C81.4726 45.8515 80.8154 45.0858 79.955 44.6116L83.2408 46.4069C86.5335 48.2293 86.7773 54.198 83.9522 60.1463Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M82.5971 51.3525C82.4413 53.8998 81.5673 56.8401 79.9888 59.6992C79.9075 59.8482 79.8194 59.9972 79.7313 60.1463C78.8099 61.7384 77.7396 63.1475 76.6081 64.3265H64.8334C64.515 63.1204 64.4404 61.691 64.6165 60.1463C64.8875 57.7886 65.7344 55.1599 67.1571 52.5924C70.6868 46.2239 76.4184 42.6469 79.9548 44.6116C80.8152 45.0858 81.4725 45.8514 81.9195 46.8269C82.4819 48.0533 82.7122 49.6113 82.5971 51.3525Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M65.4341 37.7261L63.1305 40.6792C60.6249 43.8563 53.96 42.7782 48.2414 38.2683C42.5281 33.7626 39.9261 27.5325 42.4317 24.3554L44.7353 21.4024C44.1264 22.1744 43.8176 23.1349 43.785 24.2048C43.7393 25.5536 44.1323 27.0794 44.9092 28.6433C46.0335 30.9268 47.975 33.3006 50.5391 35.3228C53.1031 37.3449 55.8642 38.6796 58.352 39.2448C60.054 39.6358 61.6293 39.6623 62.9304 39.3035C63.9631 39.0224 64.8252 38.4982 65.4341 37.7261Z"
        fill="rgb(var(--sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M65.4344 37.7261C64.8255 38.4982 63.9634 39.0224 62.9307 39.3035C61.6297 39.6623 60.0543 39.6358 58.3523 39.2448C55.8645 38.6796 53.1034 37.3449 50.5394 35.3228C47.9754 33.3006 46.0339 30.9268 44.9095 28.6434C44.1326 27.0794 43.7397 25.5536 43.7853 24.2048C43.8179 23.135 44.1267 22.1744 44.7356 21.4024C47.2413 18.2252 53.9061 19.3034 59.6194 23.8091C65.338 28.319 67.94 34.5489 65.4344 37.7261Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M106.883 5.13344L109.897 7.35712C113.14 9.77674 112.24 16.4681 107.885 22.3055C103.534 28.1374 97.3761 30.9053 94.133 28.4857L91.1193 26.262C91.9074 26.8499 92.8758 27.1329 93.9462 27.1369C95.2957 27.1464 96.8104 26.7128 98.353 25.8942C100.605 24.7092 102.926 22.7048 104.879 20.0874C106.832 17.4702 108.092 14.6743 108.591 12.1723C108.936 10.4604 108.92 8.88499 108.527 7.59395C108.218 6.56916 107.671 5.7214 106.883 5.13344Z"
        fill="rgb(var(--sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M106.883 5.13327C107.671 5.72124 108.218 6.56899 108.527 7.59378C108.92 8.88475 108.936 10.4603 108.591 12.1722C108.092 14.6741 106.832 17.47 104.879 20.0872C102.927 22.7046 100.606 24.7089 98.3531 25.894C96.8105 26.7126 95.2958 27.1462 93.9462 27.1367C92.8759 27.1328 91.9074 26.8498 91.1193 26.2618C87.8762 23.8421 88.7755 17.1508 93.1266 11.3189C97.4817 5.48153 103.64 2.71365 106.883 5.13327Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.5713 42.9338C25.5713 42.9338 19.564 41.1442 17.7601 47.125C19.5639 41.1442 13.5688 39.3138 13.5688 39.3138C13.5688 39.3138 19.5763 41.1033 21.3801 35.1225C19.5763 41.1033 25.5713 42.9338 25.5713 42.9338Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M135.607 79.2644C135.607 79.2644 130.476 85.2833 136.457 90.4165C130.476 85.2831 125.305 91.2668 125.305 91.2668C125.305 91.2668 130.435 85.248 124.454 80.1146C130.435 85.248 135.607 79.2644 135.607 79.2644Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
