export default function EmptyStateHRNotes({ className }: { className?: string }) {
  return (
    <svg
      width="148"
      height="148"
      viewBox="0 0 148 148"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="148" height="148" rx="74" className="fill-weak-100" />
      <path
        d="M42.517 33.5224C42.4182 33.9019 42.2458 34.2413 41.986 34.5661C41.5985 35.0502 40.9966 35.4969 40.2009 35.8199C39.4078 36.1432 38.431 36.3364 37.3635 36.3357C36.5919 36.3358 35.7737 36.236 34.943 36.0218C33.1792 35.5697 31.7161 34.6819 30.7658 33.6763C30.019 32.8894 28.7757 32.857 27.9889 33.6038C27.202 34.3506 27.1696 35.5938 27.9164 36.3806C29.4362 37.9777 31.5259 39.1951 33.9624 39.8259C35.1104 40.1218 36.2549 40.264 37.3635 40.2641C39.4072 40.2618 41.3355 39.7868 42.9416 38.834C43.7429 38.3567 44.4629 37.7551 45.0482 37.027C45.6332 36.3004 46.0794 35.4438 46.321 34.5032C46.5918 33.4527 45.9598 32.3816 44.9093 32.1108C43.8589 31.84 42.7877 32.472 42.517 33.5224Z"
        className="fill-strong-400"
      />
      <path
        d="M37.8408 51.6608C37.7421 52.0403 37.5696 52.3797 37.3098 52.7045C36.9223 53.1885 36.3205 53.6353 35.5247 53.9583C34.7317 54.2815 33.7548 54.4748 32.6874 54.474C31.9158 54.4741 31.0977 54.3744 30.267 54.1602C28.5031 53.7081 27.0401 52.8204 26.0898 51.8147C25.343 51.0279 24.0997 50.9954 23.3129 51.7422C22.526 52.489 22.4935 53.7322 23.2403 54.5191C24.7602 56.1161 26.8499 57.3335 29.2863 57.9643C30.4344 58.2602 31.5789 58.4025 32.6875 58.4026C34.7311 58.4002 36.6595 57.9253 38.2656 56.9724C39.0669 56.4951 39.7869 55.8935 40.3722 55.1654C40.9572 54.4388 41.4033 53.5822 41.645 52.6416C41.9158 51.5912 41.2838 50.5201 40.2333 50.2492C39.1828 49.9784 38.1117 50.6103 37.8408 51.6608Z"
        className="fill-strong-400"
      />
      <path
        d="M32.8792 70.9069C32.7805 71.2864 32.608 71.6257 32.3482 71.9506C31.9607 72.4346 31.3589 72.8814 30.5631 73.2044C29.7701 73.5276 28.7932 73.7208 27.7257 73.7201C26.9541 73.7202 26.136 73.6205 25.3053 73.4063C23.5414 72.9541 22.0784 72.0664 21.128 71.0608C20.3812 70.2739 19.138 70.2415 18.3511 70.9883C17.5643 71.7351 17.5318 72.9783 18.2786 73.7652C19.7985 75.3622 21.8882 76.5796 24.3246 77.2104C25.4726 77.5063 26.6171 77.6485 27.7257 77.6486C29.7694 77.6463 31.6978 77.1714 33.3039 76.2185C34.1052 75.7412 34.8251 75.1396 35.4104 74.4115C35.9955 73.6849 36.4416 72.8283 36.6833 71.8877C36.9541 70.8373 36.322 69.7661 35.2716 69.4953C34.2211 69.2245 33.15 69.8564 32.8792 70.9069Z"
        className="fill-strong-400"
      />
      <path
        d="M120.127 45.5726L54.408 28.4787C50.1487 27.3708 45.7978 29.9256 44.6899 34.1849L27.596 99.904C26.4881 104.163 29.0429 108.514 33.3022 109.622L99.0213 126.716C103.281 127.824 107.631 125.269 108.739 121.01L125.833 55.2907C126.941 51.0314 124.386 46.6805 120.127 45.5726Z"
        className="fill-soft-200 stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
      <path
        d="M118.997 46.3497L103.321 106.618C101.298 113.079 94.898 117.384 87.9315 116.544C87.4769 116.489 87.0276 116.413 86.5879 116.318L85.4912 116.035C85.4619 116.028 85.4326 116.02 85.4043 116.008C78.9014 114.01 74.5615 107.588 75.4001 100.594C76.3469 92.7309 83.4897 87.1212 91.354 88.0638L30.5335 80.0718L30.5293 80.0708C30.1869 79.9959 29.8361 79.9367 29.481 79.8943C27.2863 79.6276 25.1662 80.0587 23.3442 81.0132L37.8513 25.2429C38.9583 20.9836 43.3095 18.4266 47.5678 19.5378L113.287 36.6322C117.547 37.7393 120.104 42.0903 118.997 46.3497Z"
        className="fill-white-0 stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
      <path
        d="M85.4044 116.008L27.2826 100.89C27.1767 100.885 27.0719 100.876 26.964 100.862C21.1698 100.167 17.0436 94.907 17.7371 89.1169C18.1682 85.5226 20.3582 82.5693 23.3443 81.0131C25.1663 80.0587 27.2863 79.6276 29.4811 79.8943C29.8362 79.9367 30.187 79.9959 30.5293 80.0707L30.5336 80.0718L91.354 88.0637C83.4897 87.1212 76.347 92.7308 75.4002 100.594C74.5616 107.588 78.9014 114.01 85.4044 116.008Z"
        className="fill-sub-300 stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
      <path
        d="M85.4912 116.035L86.588 116.318C86.2153 116.24 85.8511 116.146 85.4912 116.035Z"
        className="stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
      <path
        d="M103.321 106.618L103.687 105.217C103.586 105.695 103.466 106.16 103.321 106.618Z"
        className="stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
      <path
        d="M30.7658 33.6763C30.31 33.1964 29.9718 32.6936 29.7544 32.2109C29.5365 31.7268 29.4376 31.2682 29.4374 30.8397C29.4375 30.6131 29.4647 30.393 29.5215 30.1722C29.6203 29.7927 29.7927 29.4534 30.0525 29.1286C30.44 28.6445 31.0418 28.1978 31.8376 27.8748C32.6307 27.5515 33.6075 27.3583 34.675 27.359C35.4465 27.3589 36.2647 27.4586 37.0954 27.6729C38.8212 28.1155 40.2592 28.9743 41.2103 29.9536C41.687 30.442 42.0415 30.9566 42.2691 31.451C42.4975 31.947 42.6009 32.417 42.6011 32.8551C42.601 33.0816 42.5738 33.3017 42.517 33.5225C42.2461 34.573 42.8782 35.6441 43.9287 35.9149C44.9792 36.1856 46.0503 35.5536 46.3211 34.5031C46.4617 33.958 46.5297 33.4034 46.5296 32.855C46.5298 31.7892 46.2745 30.7541 45.8363 29.8053C45.1764 28.3798 44.1163 27.1355 42.7916 26.1231C41.4649 25.1118 39.8644 24.3304 38.0762 23.8688C36.9282 23.5729 35.7836 23.4306 34.6751 23.4305C32.6314 23.4329 30.703 23.9078 29.0969 24.8607C28.2956 25.338 27.5757 25.9396 26.9903 26.6677C26.4053 27.3943 25.9592 28.2509 25.7175 29.1915C25.5768 29.7366 25.5089 30.2912 25.509 30.8396C25.5088 31.8818 25.753 32.895 26.1736 33.8263C26.5948 34.7591 27.1905 35.6153 27.9164 36.3806C28.6632 37.1674 29.9065 37.1999 30.6933 36.4531C31.4802 35.7063 31.5126 34.4631 30.7658 33.6763Z"
        className="fill-strong-400"
      />
      <path
        d="M26.0897 51.8147C25.6339 51.3348 25.2957 50.832 25.0784 50.3493C24.8604 49.8652 24.7615 49.4066 24.7613 48.978C24.7614 48.7515 24.7886 48.5314 24.8454 48.3106C24.9442 47.9311 25.1166 47.5918 25.3764 47.2669C25.7639 46.7828 26.3657 46.3362 27.1615 46.0131C27.9546 45.6899 28.9314 45.4967 29.9989 45.4974C30.7705 45.4973 31.5887 45.597 32.4193 45.8113C34.1451 46.2538 35.5831 47.1127 36.5342 48.092C37.0109 48.5804 37.3654 49.095 37.593 49.5894C37.8214 50.0853 37.9248 50.5553 37.925 50.9935C37.9249 51.22 37.8977 51.4401 37.8409 51.6609C37.57 52.7113 38.2021 53.7825 39.2526 54.0532C40.303 54.324 41.3741 53.692 41.6449 52.6416C41.7856 52.0964 41.8536 51.5418 41.8535 50.9935C41.8536 49.9276 41.5984 48.8925 41.1602 47.9437C40.5003 46.5183 39.4401 45.2739 38.1154 44.2616C36.7887 43.2502 35.1883 42.4688 33.4 42.0072C32.252 41.7113 31.1075 41.569 29.9989 41.569C27.9552 41.5714 26.0269 42.0463 24.4208 42.9991C23.6195 43.4764 22.8995 44.078 22.3142 44.8062C21.7292 45.5327 21.283 46.3893 21.0414 47.3299C20.9007 47.875 20.8327 48.4296 20.8328 48.978C20.8327 50.0202 21.0768 51.0334 21.4974 51.9647C21.9186 52.8975 22.5143 53.7537 23.2403 54.519C23.9871 55.3059 25.2303 55.3384 26.0172 54.5916C26.804 53.8447 26.8365 52.6015 26.0897 51.8147Z"
        className="fill-strong-400"
      />
      <path
        d="M21.1281 71.0608C20.6723 70.5809 20.334 70.0781 20.1167 69.5954C19.8988 69.1113 19.7998 68.6527 19.7996 68.2241C19.7998 67.9976 19.827 67.7775 19.8838 67.5567C19.9825 67.1772 20.155 66.8379 20.4148 66.5131C20.8022 66.029 21.4041 65.5823 22.1999 65.2593C22.9929 64.936 23.9698 64.7428 25.0372 64.7435C25.8088 64.7434 26.6269 64.8431 27.4576 65.0574C29.1834 65.4999 30.6214 66.3588 31.5726 67.3381C32.0493 67.8265 32.4037 68.3411 32.6314 68.8355C32.8597 69.3314 32.9632 69.8014 32.9634 70.2395C32.9632 70.4661 32.936 70.6862 32.8792 70.9069C32.6084 71.9574 33.2404 73.0285 34.2909 73.2993C35.3414 73.5701 36.4125 72.9381 36.6833 71.8876C36.824 71.3425 36.892 70.7879 36.8919 70.2395C36.892 69.1737 36.6368 68.1386 36.1986 67.1898C35.5387 65.7643 34.4785 64.52 33.1538 63.5076C31.8271 62.4963 30.2267 61.7149 28.4384 61.2533C27.2904 60.9574 26.1459 60.8151 25.0373 60.815C22.9936 60.8174 21.0652 61.2923 19.4592 62.2451C18.6578 62.7224 17.9379 63.3241 17.3526 64.0522C16.7675 64.7788 16.3214 65.6354 16.0798 66.5759C15.9391 67.1211 15.8711 67.6757 15.8712 68.224C15.8711 69.2663 16.1152 70.2795 16.5358 71.2108C16.957 72.1436 17.5527 72.9998 18.2787 73.7651C19.0255 74.5519 20.2688 74.5844 21.0556 73.8376C21.8425 73.0908 21.8749 71.8476 21.1281 71.0608Z"
        className="fill-strong-400"
      />
      <path d="M57.8436 34.3527L62.6644 42.5226L54.2508 47.4873" className="fill-white-0" />
      <path
        d="M56.5321 35.1265L60.5792 41.985L53.4769 46.176C52.7526 46.6033 52.512 47.5369 52.9394 48.2611C53.3667 48.9853 54.3003 49.226 55.0245 48.7987L63.4382 43.8339C64.1625 43.4066 64.4031 42.473 63.9757 41.7488L59.1548 33.5788C58.7275 32.8546 57.7939 32.6139 57.0697 33.0413C56.3454 33.4686 56.1047 34.4022 56.5321 35.1265Z"
        className="fill-strong-400"
      />
      <path d="M98.2607 44.7722L90.0907 49.5931L95.0554 58.0067" className="fill-white-0" />
      <path
        d="M97.4868 43.4609L89.3169 48.2818C88.9715 48.4855 88.7163 48.8247 88.6163 49.213C88.5162 49.6013 88.5756 50.0216 88.7793 50.3669L93.744 58.7806C94.1714 59.5048 95.105 59.7455 95.8292 59.3181C96.5534 58.8908 96.7941 57.9572 96.3667 57.233L92.1759 50.1307L99.0345 46.0836C99.7587 45.6563 99.9994 44.7227 99.572 43.9985C99.1447 43.2743 98.2111 43.0335 97.4868 43.4609Z"
        className="fill-strong-400"
      />
      <path
        d="M60.1586 65.4671L81.1912 70.8894C82.0055 71.0993 82.8358 70.6093 83.0457 69.795C83.2557 68.9807 82.7657 68.1504 81.9514 67.9405L60.9188 62.5182C60.1045 62.3083 59.2742 62.7983 59.0643 63.6126C58.8543 64.4269 59.3443 65.2572 60.1586 65.4671Z"
        className="fill-strong-400"
      />
      <path
        d="M129.5 23.639C129.5 23.639 122.306 23.6634 122.306 30.8333C122.306 23.6634 115.111 23.639 115.111 23.639C115.111 23.639 122.306 23.6144 122.306 16.4445C122.306 23.6144 129.5 23.639 129.5 23.639Z"
        className="stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.5556 119.222C20.5556 119.222 14.3889 119.243 14.3889 125.389C14.3889 119.243 8.22223 119.222 8.22223 119.222C8.22223 119.222 14.3889 119.201 14.3889 113.056C14.3889 119.201 20.5556 119.222 20.5556 119.222Z"
        className="stroke-strong-400"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M97.5912 109.072L96.8001 109.612C92.3647 112.651 86.3124 111.515 83.2777 107.083C80.2386 102.652 81.3669 96.6001 85.8024 93.561L86.5935 93.0206L97.5912 109.072Z"
        className="fill-soft-200 stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
      <path
        d="M137.847 71.4529L136.369 81.6769C136.282 82.2648 135.954 82.7922 135.461 83.1294L100.094 107.356L97.5913 109.072L86.5934 93.0205L89.0965 91.3043L124.463 67.078C124.956 66.7408 125.566 66.6241 126.145 66.7581L136.213 69.0709C137.294 69.3173 138.007 70.3548 137.847 71.4529Z"
        className="fill-white-0 stroke-strong-400"
        strokeMiterlimit="10"
        strokeLinejoin="round"
      />
    </svg>
  );
}
