const formats = {
  pdf: 'bg-error-base',

  doc: 'bg-information-base',
  docx: 'bg-information-base',

  xls: 'bg-success-base',
  xlsx: 'bg-success-base',

  video: 'bg-warning-base',

  jpg: 'bg-feature-base',
  png: 'bg-feature-base',
  jpeg: 'bg-feature-base',
  gif: 'bg-feature-base',
  webp: 'bg-feature-base',
};

const FileFormatIcon = ({
  type = 'FILE',
  color,
  ...props
}: React.SVGProps<SVGSVGElement> & {
  type?: `${string}${string}${string}` | `${string}${string}${string}${string}`;
  color?: string;
}) => (
  <div className="relative">
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M24.2 31.4H7.8C5.4804 31.4 3.6 29.5196 3.6 27.2V4.8C3.6 2.4804 5.4804 0.6 7.8 0.6H16.471C17.5737 0.6 18.6321 1.03361 19.4178 1.80722L27.1467 9.41728C27.9485 10.2067 28.4 11.2849 28.4 12.4101V27.2C28.4 29.5196 26.5196 31.4 24.2 31.4Z"
        className="fill-white-0 stroke-sub-300"
        strokeWidth="1.2"
      />
      <path
        d="M18 1V7.8C18 9.56731 19.4327 11 21.2 11H28"
        className="stroke-sub-300"
        strokeWidth="1.2"
      />
    </svg>

    <div
      className={`rounded-2 absolute -left-[3px] top-[calc(50%-3px)] rounded px-[3px] py-[3px] font-semibold uppercase leading-none text-white ${color ?? formats[type] ?? 'bg-neutral-500'}`}
      style={{ fontSize: 9 }}
    >
      {type}
    </div>
  </div>
);

export default FileFormatIcon;
