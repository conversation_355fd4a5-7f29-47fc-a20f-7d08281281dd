interface Props extends React.SVGProps<SVGSVGElement> {}

export default function ToolsLine(props: Props) {
  return (
    <svg {...props} width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5.49776 3.45322C5.99804 3.2747 6.5407 3.25216 7.05406 3.38857C7.56742 3.52498 8.02732 3.81391 8.373 4.21721C8.71868 4.62051 8.93388 5.11918 8.99017 5.64736C9.04646 6.17555 8.94119 6.70838 8.68826 7.17547L16.72 15.208L15.6595 16.2685L7.62701 8.23597C7.15982 8.48789 6.62726 8.59236 6.09951 8.53559C5.57177 8.47883 5.0736 8.2635 4.67067 7.91797C4.26774 7.57245 3.97895 7.11296 3.84236 6.60005C3.70577 6.08713 3.72779 5.54487 3.90551 5.04472L5.58326 6.72247C5.68704 6.82992 5.81118 6.91562 5.94843 6.97458C6.08569 7.03354 6.23331 7.06458 6.38269 7.06587C6.53207 7.06717 6.6802 7.03871 6.81846 6.98214C6.95672 6.92558 7.08233 6.84204 7.18796 6.73641C7.29359 6.63078 7.37712 6.50517 7.43369 6.36692C7.49026 6.22866 7.51872 6.08052 7.51742 5.93114C7.51612 5.78176 7.48509 5.63414 7.42613 5.49689C7.36717 5.35963 7.28146 5.23549 7.17401 5.13172L5.49701 3.45247L5.49776 3.45322ZM13.273 4.86622L15.6595 3.54022L16.72 4.60072L15.394 6.98722L14.068 7.25272L12.478 8.84347L11.4168 7.78297L13.0075 6.19222L13.273 4.86622V4.86622ZM8.23451 10.9652L9.29501 12.0257L5.31776 16.003C5.18254 16.1386 5.00056 16.2173 4.80914 16.223C4.61771 16.2288 4.43135 16.1611 4.28825 16.0338C4.14515 15.9065 4.05616 15.7293 4.03952 15.5385C4.02288 15.3478 4.07986 15.1578 4.19876 15.0077L4.25726 14.9425L8.23451 10.9652Z"
        fill="#525866"
      />
    </svg>
  );
}
