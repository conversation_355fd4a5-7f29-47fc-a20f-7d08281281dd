export default function EmptyStateSpendingSummary({ className }: { className?: string }) {
  return (
    <svg
      width="148"
      height="148"
      viewBox="0 0 148 148"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect width="148" height="148" rx="74" fill="rgb(var(--raw-weak-100))" />
      <path
        d="M74.1984 22.8406V36.8778C73.3251 36.9093 72.4673 36.9799 71.6176 37.0824C55.8259 38.9707 43.5827 52.402 43.5827 68.7052C43.5827 68.9807 43.5827 69.2481 43.5905 69.5235H27.5156C27.5156 44.6046 47.0291 24.2491 71.6176 22.9114C72.4674 22.8642 73.3329 22.8406 74.1984 22.8406Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M120.881 69.5236H107.284C107.292 69.256 107.292 68.9807 107.292 68.7052C107.292 68.0915 107.276 67.4777 107.237 66.8797C106.954 61.7732 105.459 56.9892 103.051 52.8113C102.524 51.8906 101.95 51.0094 101.336 50.1517L112.194 42.3936C112.808 43.251 113.398 44.1325 113.941 45.0295C116.671 49.4435 118.686 54.3455 119.811 59.5779C120.33 61.9464 120.668 64.3855 120.802 66.8797C120.857 67.7532 120.881 68.6346 120.881 69.5236Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M60.6094 52.8252L67.3146 59.5226"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M67.3068 52.8213L60.6094 59.5265"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M81.0781 52.8134L87.7833 59.5107"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M87.7834 52.8094L81.0859 59.5147"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M64.7969 66.413L82.1722 66.403"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M116.837 42.958L113.941 45.0273L103.051 52.8092C102.524 51.8887 101.95 51.0073 101.336 50.1498C95.5527 42.0924 86.1107 36.8522 75.4412 36.8522C75.0241 36.8522 74.6071 36.86 74.198 36.8758C73.3247 36.9073 72.4669 36.978 71.6172 37.0804V19.6913C90.2495 19.6913 106.757 28.8738 116.837 42.958Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M127.484 69.5238H107.294C107.349 67.8007 107.263 66.0618 107.027 64.2991C106.987 64.0317 106.948 63.7562 106.901 63.4888C106.806 62.8829 106.696 62.2771 106.562 61.6948L119.813 59.5781L126.682 58.4845C127.272 62.1983 127.532 65.8885 127.484 69.5238Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M40.0852 95.7863C44.503 95.7863 48.0844 92.2049 48.0844 87.7871C48.0844 83.3692 44.503 79.7878 40.0852 79.7878C35.6673 79.7878 32.0859 83.3692 32.0859 87.7871C32.0859 92.2049 35.6673 95.7863 40.0852 95.7863Z"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M40.0825 91.2601C42.0007 91.2601 43.5556 89.7051 43.5556 87.787C43.5556 85.8688 42.0007 84.3138 40.0825 84.3138C38.1643 84.3138 36.6094 85.8688 36.6094 87.787C36.6094 89.7051 38.1643 91.2601 40.0825 91.2601Z"
        fill="rgb(var(--raw-soft-300))"
      />
      <path
        d="M73.9367 96.3434C78.3546 96.3434 81.9359 92.762 81.9359 88.3442C81.9359 83.9263 78.3546 80.345 73.9367 80.345C69.5189 80.345 65.9375 83.9263 65.9375 88.3442C65.9375 92.762 69.5189 96.3434 73.9367 96.3434Z"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M73.9341 91.8174C75.8522 91.8174 77.4072 90.2624 77.4072 88.3442C77.4072 86.4261 75.8522 84.8711 73.9341 84.8711C72.0159 84.8711 70.4609 86.4261 70.4609 88.3442C70.4609 90.2624 72.0159 91.8174 73.9341 91.8174Z"
        fill="rgb(var(--raw-sub-300))"
      />
      <path
        d="M107.788 96.3435C112.206 96.3435 115.787 92.7622 115.787 88.3443C115.787 83.9265 112.206 80.3451 107.788 80.3451C103.37 80.3451 99.7891 83.9265 99.7891 88.3443C99.7891 92.7622 103.37 96.3435 107.788 96.3435Z"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M107.786 91.8174C109.704 91.8174 111.259 90.2624 111.259 88.3442C111.259 86.4261 109.704 84.8711 107.786 84.8711C105.867 84.8711 104.312 86.4261 104.312 88.3442C104.312 90.2624 105.867 91.8174 107.786 91.8174Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path d="M85.4603 104.361H64.3438V109.984H85.4603V104.361Z" fill="rgb(var(--raw-soft-200))" />
      <path d="M82.9775 115.681H66.8359V121.304H82.9775V115.681Z" fill="rgb(var(--raw-soft-300))" />
      <path d="M116.087 115.681H99.9453V121.304H116.087V115.681Z" fill="rgb(var(--raw-soft-300))" />
      <path d="M48.5947 115.682H32.4531V121.304H48.5947V115.682Z" fill="rgb(var(--raw-soft-300))" />
      <path d="M118.578 104.361H97.4609V109.984H118.578V104.361Z" fill="rgb(var(--raw-soft-200))" />
      <path d="M51.0853 104.361H29.9688V109.984H51.0853V104.361Z" fill="rgb(var(--raw-soft-200))" />
      <path
        d="M11.3506 18.0846C11.3506 18.0846 16.0478 24.9049 22.8606 20.247C16.0477 24.905 20.6983 31.7569 20.6983 31.7569C20.6983 31.7569 16.0011 24.9367 9.18815 29.5947C16.0011 24.9367 11.3506 18.0846 11.3506 18.0846Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M131.908 111.61C131.908 111.61 130.406 119.754 138.518 121.278C130.406 119.754 128.85 127.888 128.85 127.888C128.85 127.888 130.351 119.744 122.24 118.22C130.351 119.744 131.908 111.61 131.908 111.61Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
