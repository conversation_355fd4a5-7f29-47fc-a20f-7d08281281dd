import { forwardRef, LegacyRef } from 'react';

const AwardLine = forwardRef((props: React.SVGProps<SVGSVGElement>, ref: LegacyRef<SVGSVGElement>) => (
  <svg ref={ref} width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clipPath="url(#clip0_41_937)">
      <path
        d="M17 14.9207V21.1055C17 21.1851 16.9789 21.2633 16.9388 21.3321C16.8987 21.4009 16.8411 21.4578 16.7719 21.4971C16.7026 21.5363 16.6241 21.5565 16.5445 21.5555C16.4649 21.5546 16.387 21.5325 16.3187 21.4916L12.5 19.2002L8.68129 21.4916C8.61293 21.5325 8.53491 21.5546 8.45522 21.5555C8.37553 21.5565 8.29702 21.5362 8.22773 21.4968C8.15843 21.4575 8.10083 21.4004 8.06082 21.3315C8.0208 21.2626 7.99981 21.1843 7.99999 21.1046V14.9216C6.83552 13.9893 5.98938 12.7183 5.5785 11.2843C5.16762 9.85025 5.21229 8.32402 5.70633 6.9165C6.20037 5.50897 7.11939 4.28965 8.33639 3.42702C9.55339 2.5644 11.0083 2.10107 12.5 2.10107C13.9917 2.10107 15.4466 2.5644 16.6636 3.42702C17.8806 4.28965 18.7996 5.50897 19.2936 6.9165C19.7877 8.32402 19.8324 9.85025 19.4215 11.2843C19.0106 12.7183 18.1645 13.9893 17 14.9216V14.9207ZM9.79999 15.9764V18.7214L12.5 17.1014L15.2 18.7214V15.9764C14.3421 16.3233 13.4253 16.5012 12.5 16.5002C11.5746 16.5012 10.6578 16.3233 9.79999 15.9764ZM12.5 14.7002C13.9322 14.7002 15.3057 14.1312 16.3184 13.1185C17.3311 12.1059 17.9 10.7323 17.9 9.30017C17.9 7.868 17.3311 6.49449 16.3184 5.4818C15.3057 4.4691 13.9322 3.90017 12.5 3.90017C11.0678 3.90017 9.69431 4.4691 8.68161 5.4818C7.66892 6.49449 7.09999 7.868 7.09999 9.30017C7.09999 10.7323 7.66892 12.1059 8.68161 13.1185C9.69431 14.1312 11.0678 14.7002 12.5 14.7002Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_41_937">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
));

AwardLine.displayName = 'AwardLine';

export default AwardLine;
