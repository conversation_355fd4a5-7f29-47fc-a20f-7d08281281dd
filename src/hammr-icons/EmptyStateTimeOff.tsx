export default function EmptyStateTimeOff() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="148" height="152" viewBox="0 0 148 152" fill="none">
      <rect y="1" width="148" height="148" rx="74" fill="rgb(var(--raw-weak-300))" />
      <path
        d="M124.335 50.0754V116.568C124.335 120.156 121.428 123.063 117.84 123.063H34.5741C30.9908 123.063 28.0841 120.156 28.0841 116.568V50.0754C28.0841 46.4873 30.9908 43.5805 34.5741 43.5805H117.84C121.428 43.5805 124.335 46.4873 124.335 50.0754Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M45.6393 31.6486C46.0346 32.7803 46.2534 34.0243 46.2534 35.3303C46.2534 40.554 42.7554 44.7887 38.4402 44.7887"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M74.4793 31.6486C74.8747 32.7803 75.0934 34.0243 75.0934 35.3303C75.0934 40.554 71.5954 44.7887 67.2802 44.7887"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M103.319 31.6486C103.714 32.7803 103.933 34.0243 103.933 35.3303C103.933 40.554 100.435 44.7887 96.1195 44.7887"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M115.325 41.7541V108.246C115.325 111.835 112.419 114.741 108.83 114.741H25.5643C21.9812 114.741 19.0743 111.835 19.0743 108.246V41.7541C19.0743 38.166 21.981 35.2593 25.5643 35.2593H108.83C112.419 35.2593 115.325 38.166 115.325 41.7541Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M115.325 41.7541V52.8909H19.0743V41.7541C19.0743 38.166 21.981 35.2593 25.5643 35.2593H108.83C112.419 35.2593 115.325 38.166 115.325 41.7541Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M38.4406 44.7895C34.1256 44.7895 30.6275 40.5548 30.6275 35.331C30.6275 30.1073 34.1255 25.8727 38.4406 25.8727C41.6769 25.8727 44.4537 28.2547 45.6397 31.6495"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M67.2807 44.7883C62.9656 44.7883 59.4675 40.5535 59.4675 35.3298C59.4675 30.1061 62.9655 25.8714 67.2807 25.8714C70.5169 25.8714 73.2937 28.2534 74.4798 31.6482"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M96.12 44.7883C91.805 44.7883 88.3068 40.5535 88.3068 35.3298C88.3068 30.1061 91.8048 25.8714 96.12 25.8714C99.3563 25.8714 102.133 28.2534 103.319 31.6482"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M45.6595 58.3214H31.0603C29.8874 58.3214 28.9366 59.2721 28.9366 60.445V69.0455C28.9366 70.2183 29.8874 71.1691 31.0603 71.1691H45.6595C46.8324 71.1691 47.7832 70.2183 47.7832 69.0455V60.445C47.7832 59.2721 46.8324 58.3214 45.6595 58.3214Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M103.339 58.3214H88.7396C87.5667 58.3214 86.6159 59.2721 86.6159 60.445V69.0455C86.6159 70.2183 87.5667 71.1691 88.7396 71.1691H103.339C104.512 71.1691 105.463 70.2183 105.463 69.0455V60.445C105.463 59.2721 104.512 58.3214 103.339 58.3214Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M74.4988 76.8976H59.8996C58.7267 76.8976 57.7759 77.8484 57.7759 79.0213V87.6217C57.7759 88.7946 58.7267 89.7454 59.8996 89.7454H74.4988C75.6717 89.7454 76.6225 88.7946 76.6225 87.6217V79.0213C76.6225 77.8484 75.6717 76.8976 74.4988 76.8976Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M74.4988 95.4753H59.8996C58.7267 95.4753 57.7759 96.426 57.7759 97.5989V106.199C57.7759 107.372 58.7267 108.323 59.8996 108.323H74.4988C75.6717 108.323 76.6225 107.372 76.6225 106.199V97.5989C76.6225 96.426 75.6717 95.4753 74.4988 95.4753Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M103.339 95.4753H88.7396C87.5667 95.4753 86.6159 96.426 86.6159 97.5989V106.199C86.6159 107.372 87.5667 108.323 88.7396 108.323H103.339C104.512 108.323 105.463 107.372 105.463 106.199V97.5989C105.463 96.426 104.512 95.4753 103.339 95.4753Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M45.535 95.4753H30.9358C29.7629 95.4753 28.8121 96.426 28.8121 97.5989V106.199C28.8121 107.372 29.7629 108.323 30.9358 108.323H45.535C46.7079 108.323 47.6587 107.372 47.6587 106.199V97.5989C47.6587 96.426 46.7079 95.4753 45.535 95.4753Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M34.9706 98.6344L41.5 105.164"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M41.5 98.6344L34.9706 105.164"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M45.535 76.8976H30.9358C29.7629 76.8976 28.8121 77.8484 28.8121 79.0213V87.6217C28.8121 88.7946 29.7629 89.7454 30.9358 89.7454H45.535C46.7079 89.7454 47.6587 88.7946 47.6587 87.6217V79.0213C47.6587 77.8484 46.7079 76.8976 45.535 76.8976Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M34.9706 80.0569L41.5 86.5861"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M41.5 80.0569L34.9706 86.5861"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M74.4988 58.3214H59.8996C58.7267 58.3214 57.7759 59.2721 57.7759 60.445V69.0455C57.7759 70.2183 58.7267 71.1691 59.8996 71.1691H74.4988C75.6717 71.1691 76.6225 70.2183 76.6225 69.0455V60.445C76.6225 59.2721 75.6717 58.3214 74.4988 58.3214Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M63.9348 61.4806L70.4642 68.01"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M70.4642 61.4806L63.9348 68.01"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M103.339 76.8976H88.7398C87.5669 76.8976 86.6161 77.8484 86.6161 79.0212V87.6217C86.6161 88.7946 87.5669 89.7454 88.7398 89.7454H103.339C104.512 89.7454 105.463 88.7946 105.463 87.6217V79.0212C105.463 77.8484 104.512 76.8976 103.339 76.8976Z"
        fill="rgb(var(--raw-soft-200))"
      />
      <path
        d="M63.9348 80.0569L70.4642 86.5861"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M70.4642 80.0569L63.9348 86.5861"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M117.316 129.746C131.323 129.746 142.678 118.392 142.678 104.385C142.678 90.3775 131.323 79.0226 117.316 79.0226C103.309 79.0226 91.9544 90.3775 91.9544 104.385C91.9544 118.392 103.309 129.746 117.316 129.746Z"
        fill="rgb(var(--raw-soft-200))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M117.316 124.496C128.424 124.496 137.428 115.491 137.428 104.384C137.428 93.2758 128.424 84.2712 117.316 84.2712C106.208 84.2712 97.2037 93.2758 97.2037 104.384C97.2037 115.491 106.208 124.496 117.316 124.496Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M107.609 72.2551C108.125 72.9155 104.808 76.3555 100.204 79.9465C95.6032 83.5347 91.4592 85.9132 90.944 85.2527C87.3559 80.652 88.1738 74.0089 92.7745 70.4208C97.3789 66.8296 104.021 67.6544 107.609 72.2551Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M127.026 72.2551C126.511 72.9155 129.827 76.3555 134.431 79.9465C139.032 83.5347 143.176 85.9132 143.691 85.2527C147.279 80.652 146.461 74.0089 141.861 70.4208C137.256 66.8296 130.614 67.6544 127.026 72.2551Z"
        fill="rgb(var(--raw-sub-300))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M106.739 97.2228L110.175 100.66L106.739 104.096"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M127.893 97.2227L124.457 100.66L127.893 104.096"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M123.094 111.548H111.537"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M108.678 1.0001C108.678 1.0001 103.305 7.30245 109.568 12.6775C103.305 7.30234 97.8905 13.5679 97.8905 13.5679C97.8905 13.5679 103.263 7.26557 97 1.89036C103.263 7.26557 108.678 1.0001 108.678 1.0001Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M49.5411 141.654C49.5411 141.654 41.2861 142.312 41.9135 150.541C41.2861 142.312 33.0268 142.914 33.0268 142.914C33.0268 142.914 41.2819 142.256 40.6544 134.027C41.2819 142.256 49.5411 141.654 49.5411 141.654Z"
        fill="rgb(var(--raw-white-0))"
        stroke="rgb(var(--raw-strong-400))"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
