import { App<PERSON><PERSON>, EmergencyContactRelationship, Ethnicity, Gender, VeteranStatus } from '@/interfaces/user';

export interface Option {
  value: string;
  label: string;
}

export function getOptionLabel(value: string, options: Option[]): string {
  const option = options.find((option) => option.value === value);
  return option ? option.label : null;
}

export const GenderOptions: Option[] = [
  { value: Gender.Male, label: 'Male' },
  { value: Gender.Female, label: 'Female' },
  { value: Gender.NonBinary, label: 'Non Binary' },
  { value: Gender.PreferNotToAnswer, label: 'Prefer Not to Answer' },
];

export const EthnicityOptions: Option[] = [
  { value: Ethnicity.AmericanIndian, label: 'American Indian or Alaska Native' },
  { value: Ethnicity.Asian, label: 'Asian' },
  { value: Ethnicity.BlackOrAfricanAmerican, label: 'Black/African American' },
  { value: Ethnicity.HispanicOrLatino, label: 'Hispanic/Latino' },
  {
    value: Ethnicity.NativeHawaiianOrOtherPacificIslander,
    label: 'Native Hawaiian/Other Pacific Islander',
  },
  { value: Ethnicity.White, label: 'White' },
  { value: Ethnicity.TwoOrMoreRaces, label: 'Two or More Races (Not Hispanic or Latino)' },
  { value: Ethnicity.PreferNotToAnswer, label: 'Prefer Not to Answer' },
];

export const VeteranStatusOptions: Option[] = [
  {
    value: VeteranStatus.ActiveDutyWartimeOrCampaignBadgeVeteran,
    label: 'Active Duty Wartime/Campaign Badge Veteran',
  },
  { value: VeteranStatus.ArmedForcesServiceMedalVeteran, label: 'Armed Forces Service Medal Veteran' },
  { value: VeteranStatus.DisabledVeteran, label: 'Disabled Veteran' },
  { value: VeteranStatus.RecentlySeparatedVeteran, label: 'Recently Separated Veteran' },
  {
    value: VeteranStatus.NotAVeteran,
    label: 'Not a Veteran',
  },
  { value: VeteranStatus.PreferNotToAnswer, label: 'Prefer Not to Answer' },
];

export const EmergencyContactRelationshipOptions: Option[] = [
  { value: EmergencyContactRelationship.Spouse, label: 'Spouse' },
  { value: EmergencyContactRelationship.Parent, label: 'Parent' },
  { value: EmergencyContactRelationship.Child, label: 'Child' },
  { value: EmergencyContactRelationship.Sibling, label: 'Sibling' },
  { value: EmergencyContactRelationship.Other, label: 'Other' },
];

export const AppRoleOptions: Option[] = [
  { value: AppRole.Admin, label: 'Admin' },
  { value: AppRole.Foreman, label: 'Foreman' },
  { value: AppRole.Worker, label: 'Worker' },
];
