import { OvertimeSettings } from '@/interfaces/overtime-settings';
import { apiRequest } from '../utils/requestHelpers';

interface UpdateData {
  weeklyOvertimeEnabled: boolean;
  dailyOvertimeEnabled: boolean;
  dailyOvertimeThreshold: number;
  weeklyOvertimeThreshold: number;
  name: string;
  description: string;
  dailyDoubleOvertimeEnabled: boolean;
  overtimeDays: string[];
  doubleOvertimeDays: string[];
  isActive: boolean;
  overtimeDistribution: 'SEQUENTIAL' | 'WEIGHTED' | 'MINIMUM_RATE';
}

// Interface for create payload that omits required fields that will be set by the server
export interface CreateOvertimeSettingsPayload {
  name: string;
  description?: string;
  weeklyOvertimeEnabled: boolean;
  dailyOvertimeEnabled: boolean;
  dailyDoubleOvertimeEnabled: boolean;
  overtimeDays: string[];
  doubleOvertimeDays: string[];
}

export default {
  async getAll(): Promise<OvertimeSettings[]> {
    const data = await apiRequest('overtime-settings');
    return data.overtimeSettings;
  },
  async get(id: number): Promise<OvertimeSettings> {
    const data = await apiRequest(`overtime-settings/${id}`);
    return data.overtimeSettings;
  },
  async create(payload: CreateOvertimeSettingsPayload): Promise<OvertimeSettings> {
    const data = await apiRequest('overtime-settings', {
      method: 'POST',
      body: payload,
    });
    return data.overtimeSettings;
  },
  update(id: number, data: Partial<UpdateData>) {
    return apiRequest(`overtime-settings/${id}`, {
      method: 'PATCH',
      body: data,
    });
  },
  archive(id: number) {
    return this.update(id, { isActive: false });
  },
  unarchive(id: number) {
    return this.update(id, { isActive: true });
  },
};
