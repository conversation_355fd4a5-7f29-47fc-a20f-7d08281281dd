import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { apiRequest } from '@/utils/requestHelpers';

interface CreateWorkersCompCodePayload {
  name: string;
  code: string;
  isArchived?: boolean;
}

interface AssignCostCodesPayload {
  costCodeIds: number[];
}

interface AssignWorkersPayload {
  userId: number;
  workersCompCodeId: number | null;
}

export const workersCompCodesService = {
  get: (includeArchived = false) => {
    return apiRequest<WorkersCompCode[]>(`workers-comp-codes?includeArchived=${includeArchived}`, {
      method: 'GET',
    });
  },
  create: (payload: CreateWorkersCompCodePayload) =>
    apiRequest<WorkersCompCode>('workers-comp-codes', {
      method: 'POST',
      body: payload,
    }),

  update: (id: number, payload: CreateWorkersCompCodePayload) =>
    apiRequest<WorkersCompCode>(`workers-comp-codes/${id}`, {
      method: 'PATCH',
      body: payload,
    }),

  assignCostCodes: (id: number, payload: AssignCostCodesPayload) =>
    apiRequest<WorkersCompCode>(`workers-comp-codes/${id}/cost-codes`, {
      method: 'PATCH',
      body: payload,
    }),

  assignWorkers: (payload: AssignWorkersPayload[]) =>
    apiRequest<void>('workers-comp-codes/assign-workers', {
      method: 'PATCH',
      body: payload,
    }),
};
