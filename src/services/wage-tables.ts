import { WageTable } from 'interfaces/wage-table';
import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const createWageTable = async (data: WageTable): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/wage-tables`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating wage table: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const updateWageTable = async (id: number, data: Partial<WageTable>): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/wage-tables/${id}`, {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error updating wage table: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getWageTable = async (id: number): Promise<{ wageTable: WageTable }> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/wage-tables/${id}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error getting wage table: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
