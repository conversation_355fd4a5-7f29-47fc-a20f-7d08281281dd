import moment from 'moment';

import { apiRequestCheck, defaultHammrHeaders } from 'utils/requestHelpers';
import { validateNetworkResponse } from 'utils/errorHandling';
import { PayDay, PaySchedule } from '../interfaces/check/pay-schedule';

export const createPaySchedule = (data: PaySchedule): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    };
    fetch(`/api/pay_schedules`, options)
      // .then(validateNetworkResponse) // Custom condition is applied on component
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const updatePaySchedule = (id: string, data: PaySchedule): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    };
    fetch(`/api/pay_schedules/${id}`, options)
      // .then(validateNetworkResponse) // Custom condition is applied on component
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const listPayschedules = async (companyId: string): Promise<any> => {
  return apiRequestCheck(`pay_schedules?company=${companyId}`);
};

export const getPaySchedule = (id: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };
    fetch(`/api/pay_schedules/${id}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const getPayDays = (payScheduleId: string, start: string): Promise<PayDay[]> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };
    let url = `/api/pay_schedules/${payScheduleId}/paydays`;
    if (start) {
      url += `?start=${start}`;
    }
    fetch(url, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve((await response.json()).results as PayDay[]);
      })
      .catch((err) => reject(err));
  });
};

export const getEligablePayDays = (payScheduleId: string, maxNumberOfPeriods: number): Promise<any> => {
  return new Promise((resolve, reject) => {
    // set to "pacific time", is this required / mandatory? - deprecation warning for moment.js
    const pacificTime = new Date().toLocaleString('en-US', {
      timeZone: 'America/Los_Angeles',
    });
    const currentDate = moment(pacificTime).format().split('T')[0];

    // Payrolls must be approved by 5pm PT time on the day of the approval deadline.
    const hour = new Date(pacificTime).getHours();

    getPayDays(payScheduleId, currentDate)
      .then(async (periods) => {
        const upcomingPeriods = [];
        for (let i = 0; i < periods?.length; i++) {
          const period = periods[i];

          // Second condition checks if past 5pm approval deadline.
          if (period.approval_deadline > currentDate || (period.approval_deadline == currentDate && hour < 17)) {
            upcomingPeriods.push({
              title: `${period['period_start'].replaceAll('-', '/')} - ${period['period_end'].replaceAll('-', '/')}`,
              paySchedule: period,
            });
          }

          if (upcomingPeriods.length >= maxNumberOfPeriods) {
            break;
          }
        }
        resolve(upcomingPeriods);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getUpcomingPayday = (checkCompanyId: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    listPayschedules(checkCompanyId)
      .then(async (res) => {
        const payScheduleList = await res.json();

        // Only using the first pay schedule in list, by this logic the first in the list is
        // the upcoming payroll.
        const paySchedule = payScheduleList.results[0].id;

        getEligablePayDays(paySchedule, 1)
          .then((periods) => {
            const period = periods[0];
            const paySchedule = period?.paySchedule;
            resolve(paySchedule.payday);
          })
          .catch((err) => {
            reject(err);
          });
      })
      .catch((err) => {
        reject(err);
      });
  });
};

export const getUpcomingPaydayForEmployees = (checkCompanyId: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    listPayschedules(checkCompanyId)
      .then(async (payScheduleList) => {
        // Only using the first pay schedule in list, by this logic the first in the list is
        // the upcoming payroll.
        const paySchedule = payScheduleList.results[0].id;
        // set to "pacific time", is this required / mandatory? - deprecation warning for moment.js
        const pacificTime = new Date().toLocaleString('en-US', {
          timeZone: 'America/Los_Angeles',
        });
        const currentDate = moment(pacificTime).format().split('T')[0];

        getPayDays(paySchedule, currentDate)
          .then(async (periods) => {
            resolve(periods[0].payday);
          })
          .catch((err) => {
            reject(err);
          });
      })
      .catch((err) => {
        reject(err);
      });
  });
};
