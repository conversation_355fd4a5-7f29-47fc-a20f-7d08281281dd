import { FringeBenefitStatement } from '@/interfaces/fringes';
import { apiRequest } from 'utils/requestHelpers';

export const getFringeClassifications = ({ wageTableId }) => {
  return apiRequest('fringe-benefit-classifications', {
    method: 'GET',
    urlParams: { wageTableId },
  });
};

export const getFringe = (id) => {
  return apiRequest(`fringe-benefits/${id}`);
};

export const createFringe = (fringe) => {
  return apiRequest('fringe-benefits', {
    method: 'POST',
    body: fringe,
  });
};

export const updateFringe = (id, fringe) => {
  return apiRequest(`fringe-benefits/${id}`, {
    method: 'PATCH',
    body: fringe,
  });
};

export const updateClassificationFringe = (id, fringe) => {
  return apiRequest(`fringe-benefit-classifications/${id}`, {
    method: 'PATCH',
    body: fringe,
  });
};

export const getFringeBenefitsStatement = ({ wageTableId }: { wageTableId: number }) => {
  return apiRequest<{ wageTableData: FringeBenefitStatement[] }>(`wage-tables/${wageTableId}/benefit-statement`);
};

export const getFringeBenefitsReport = ({ wageTableId, weekEnding, projectId }) => {
  return apiRequest(`wage-tables/${wageTableId}/benefit-report`, {
    urlParams: { weekEnding, projectId },
  });
};

export const createFringeBenefitClassifications = (payload) => {
  return apiRequest('fringe-benefit-classifications', {
    method: 'POST',
    body: payload,
  });
};
