import { apiRequest } from 'utils/requestHelpers';
import { CreateProject, EditProject, Project } from '@/interfaces/project';
import { UserTimesheet } from '@/interfaces/timesheet';
import { HammrUserCrew } from '@/interfaces/user';

export const getProjects = async (urlParamsObj?: Record<string, string>) => {
  return apiRequest<{ projects: Project[] }>(`projects?${new URLSearchParams(urlParamsObj)}`);
};

export const getOneProject = async (id: number, urlParamsObj?: Record<string, string>) => {
  return apiRequest<{ project: Project; totalHours: number; totalCost: number }>(`projects/${id}`, {
    urlParams: urlParamsObj,
  });
};

export const getOneProjectWithActuals = async (id: number, urlParamsObj?: Record<string, string | number>) => {
  return apiRequest<{
    project: Project;
    totalHours: number;
    totalCost: number;
    timesheets: (UserTimesheet & { user: Partial<HammrUserCrew> })[];
  }>(`projects/${id}/project-actuals`, {
    urlParams: urlParamsObj,
  });
};

export const createProject = async (data: CreateProject) => {
  return apiRequest<{ project: Project }>('projects', { body: data, method: 'POST' });
};

export const updateProject = async (id: number, data: EditProject) => {
  return apiRequest<{ project: Project }>(`projects/${id}`, { body: data, method: 'PATCH' });
};
