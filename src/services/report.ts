import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { apiRequest, defaultHammrHeaders } from 'utils/requestHelpers';

export const getCertifiedPayrollReport = async (body) => {
  return apiRequest('report/certified-payroll', {
    method: 'POST',
    body: body,
    convertToJson: false,
  });
};

export const get401kReport = async (body) => {
  return apiRequest('report/401k', {
    method: 'POST',
    body: body,
    convertToJson: false,
  });
};

export const getEmployeeHoursByDayReport = async (queryParams: Record<string, any>) => {
  try {
    // queryParams should contain userId, to, from
    const serializedParams = new URLSearchParams(queryParams).toString();

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/report/employees-hours-by-day?${serializedParams}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'text/csv;charset=UTF-8',
        },
      }
    );
    await validateNetworkResponse(res);
    return res;
  } catch (err) {
    console.log(`Error fetching employee hours by day report: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
