import { CompanyTaxDocument, ContractorTaxDocument, EmployeeDocument } from 'interfaces/documents';
import { validateNetworkResponse } from 'utils/errorHandling';

import { defaultHammrHeaders } from 'utils/requestHelpers';
import { ListResponse } from '@/interfaces/check';

export const listEmployeeTaxDocuments = async (employeeId: string): Promise<ListResponse<EmployeeDocument>> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/json',
    },
  };
  const response = await fetch(`/api/documents/employee_tax_documents?employee=${employeeId}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data;
};

export const employeeDocumentDownload = async (documentId: string, label: string): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/pdf',
    },
  };
  const res = await fetch(`/api/documents/employee_tax_documents/${documentId}/download`, options);
  await validateNetworkResponse(res);
  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${label}.pdf`;
  document.body.appendChild(a);
  a.click();
  a.remove();
  return '';
};

export const listCompanyTaxDocuments = async (companyId: string): Promise<ListResponse<CompanyTaxDocument>> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/json',
    },
  };
  const response = await fetch(`/api/documents/company_tax_documents?company=${companyId}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data;
};

export const companyTaxDocumentDownload = async (documentId: string, label: string): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/pdf',
    },
  };
  const res = await fetch(`/api/documents/company_tax_documents/${documentId}/download`, options);
  await validateNetworkResponse(res);
  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${label}.pdf`;
  document.body.appendChild(a);
  a.click();
  a.remove();
  return '';
};

export const listCompanyAuthorizationDocuments = async (
  companyId: string
): Promise<ListResponse<CompanyTaxDocument>> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/json',
    },
  };
  const response = await fetch(`/api/documents/company_authorization_documents?company=${companyId}`, options);

  await validateNetworkResponse(response);

  const data = await response.json();
  return data;
};

export const companyAuthorizationDocumentDownload = async (documentId: string, label: string): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/pdf',
    },
  };
  const res = await fetch(`/api/documents/company_authorization_documents/${documentId}/download`, options);

  await validateNetworkResponse(res);

  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${label}.pdf`;
  document.body.appendChild(a);
  a.click();
  a.remove();
  return '';
};

// add contractor documents here
export const listContractorTaxDocuments = async (
  contractorId: string
): Promise<ListResponse<ContractorTaxDocument>> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/json',
    },
  };
  const response = await fetch(`/api/documents/contractor_tax_documents?contractor=${contractorId}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data;
};

export const contractorDocumentDownload = async (documentId: string, label: string): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/pdf',
    },
  };
  const res = await fetch(`/api/documents/contractor_tax_documents/${documentId}/download`, options);
  await validateNetworkResponse(res);
  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${label}.pdf`;
  document.body.appendChild(a);
  a.click();
  a.remove();
  return '';
};
