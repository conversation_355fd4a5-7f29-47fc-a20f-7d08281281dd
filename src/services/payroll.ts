import { Payroll, PayrollFilterOptions, PayrollPreviewResponse, PayrollStatus } from 'interfaces/payroll';
import { apiRequest, apiRequestCheck, fixPaginationUrl, downloadFileFromResponse } from 'utils/requestHelpers';
import moment from 'moment';

import { defaultHammrHeaders } from 'utils/requestHelpers';
import { validateNetworkResponse } from 'utils/errorHandling';
import { removeEmptyFields } from 'utils/utils';

const sortOrder = { pending: 0, processing: 1, draft: 2, paid: 3 };

// this function should only be used in the context of paying a payroll - it gets the data before it's going to be processed
// even though this is a GET request, the decisions we've made regarding this endpoint means this will update the payroll
// and if it's already been processed (i.e not draft) then Check will throw an error.
// for information on a payroll use getPayrollDataStatic
export const getPayrollData = (id: string) => {
  return apiRequest(`payrolls/${id}`);
};

export const getCashRequirementReport = (payrollId: string) => {
  return apiRequestCheck(`payrolls/${payrollId}/reports/cash_requirement`);
};

// Useful helper functions for Check's payroll API.
// UPDATE: These functions are not currently in-use since migrating to Run Payroll Component.

export const listPayrolls = async ({
  companyId,
  payday_after,
  payday_before,
  type,
  status,
}: {
  companyId: string;
  payday_after?: string;
  payday_before?: string;
  type?: 'regular' | 'offcycle';
  status?: PayrollStatus;
}) => {
  const urlParams = removeEmptyFields({
    company: companyId,
    payday_after,
    payday_before,
    type,
    status,
  });

  return apiRequestCheck('payrolls', {
    urlParams,
  }) as Promise<{ next: string; previous: string; results: Payroll[] }>;
};

export const createDraftPayroll = (body: {
  payday: string;
  periodStart: string;
  periodEnd: string;
  type?: 'regular' | 'off_cycle';
  offCycleOptions?: {
    force_supplemental_withholding?: boolean;
    apply_benefits?: boolean;
    apply_post_tax_deductions?: boolean;
  };
}) => {
  return apiRequest('payrolls', {
    method: 'POST',
    body,
  });
};

export const createDraftPayrollCheck = (data: Payroll): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
      body: JSON.stringify(data),
    };
    fetch(`/api/payrolls`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const updateDraftPayrollCheck = (id: string, data: Partial<Payroll>): Promise<any> => {
  return apiRequestCheck(`payrolls/${id}`, {
    method: 'PATCH',
    body: data,
  });
};

export const updateDraftPayroll = (
  id: string,
  data: Partial<Payroll> & {
    paymentMethodOverride?: { [key: string]: 'direct_deposit' | 'manual' };
    salariedEmployeesToSkip?: string[];
  }
): Promise<any> => {
  return apiRequest(`payrolls/${id}`, {
    method: 'PATCH',
    body: data,
  });
};

export const approvePayroll = (id: string): Promise<any> => {
  return apiRequest(`payrolls/${id}/approve`, { method: 'POST' });
};

export const exportPaystubs = async (id: string, fileName: string): Promise<void> => {
  const paystubsResponse = await apiRequest(`payrolls/${id}/paystubs`, {
    method: 'GET',
    convertToJson: false,
  });

  if (paystubsResponse.ok) {
    await downloadFileFromResponse(paystubsResponse, fileName);
    return;
  }
};

export const exportPayrollJournal = (id: string, companyId: string, isDraft: boolean = false): Promise<void> => {
  return new Promise((resolve, reject) => {
    const endpoint = isDraft
      ? `payrolls/${id}/preview`
      : `companies/${companyId}/reports/payroll_journal?payroll=${id}`;

    apiRequestCheck(endpoint, {
      method: 'GET',
      convertToJson: false,
      headers: { Accept: 'text/csv' },
    })
      .then(async (response) => {
        try {
          await downloadFileFromResponse(response, `payroll_journal_${id}.csv`);
          resolve();
        } catch (error) {
          reject(error);
        }
      })
      .catch((err) => reject(err));
  });
};

export const exportCashRequirement = (id: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    apiRequestCheck(`payrolls/${id}/reports/cash_requirement`, {
      method: 'GET',
      convertToJson: false,
      headers: { Accept: 'text/csv' },
    })
      .then(async (response) => {
        try {
          await downloadFileFromResponse(response, `cash_requirement_${id}.csv`);
          resolve();
        } catch (error) {
          reject(error);
        }
      })
      .catch((err) => reject(err));
  });
};

export const reopenPayroll = (id: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/payrolls/${id}/reopen`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const getPayroll = (id: string): Promise<any> => {
  return apiRequestCheck(`payrolls/${id}`);
};

export const deletePayroll = (id: string): Promise<any> => {
  return apiRequestCheck(`payrolls/${id}`, {
    method: 'DELETE',
    convertToJson: false,
  });
};

export const getPayrolls = (companyId: string): Promise<any> => {
  return apiRequestCheck('payrolls', {
    urlParams: {
      company: companyId,
    },
  });
};

export const listRegularPayroll = (companyId: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };
    fetch(`/api/payrolls?company=${companyId}&type=regular&limit=15`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const getPayrollPreview = (id: string): Promise<any> => {
  return apiRequestCheck(`payrolls/${id}/preview`);
};

export const getPayrollDataStatic = (id: string): Promise<PayrollPreviewResponse> => {
  return apiRequest(`payrolls/${id}/payroll-data`);
};

export const paginatePayrolls = (
  checkCompanyId: string,
  paginateUrl?: string,
  filterOptions?: PayrollFilterOptions
): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };

    let url = `/api/payrolls?company=${checkCompanyId}`;
    if (paginateUrl) {
      url = fixPaginationUrl(paginateUrl);
    } else if (filterOptions) {
      Object.entries(filterOptions).forEach(([key, value]) => {
        url += `&${key}=${value}`;
      });
    }

    fetch(url, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const paidDisplayCutoff = moment().subtract(7, 'd').format('YYYY-MM-DD');

        const data = await response.json();
        data.results = data.results
          .filter(
            (payroll) => payroll.status !== 'paid' || (payroll.status === 'paid' && payroll.payday >= paidDisplayCutoff)
          )
          .sort((a, b) => {
            if (sortOrder[a.status] === sortOrder[b.status]) {
              return b.payday.localeCompare(a.payday);
            }

            return sortOrder[a.status] - sortOrder[b.status];
          });
        resolve(data);
      })
      .catch((err) => reject(err));
  });
};

export const patchPayrollEarning = (id: string, body: any) => {
  // TODO -> url is still not available
  return apiRequest(`payrolls/${id}`, {
    method: 'PATCH',
    body,
  });
};
