import { EarningRate, UpdateEarningRate, HammrEarningRate } from 'interfaces/earning-rate';
import { validateNetworkResponse, logError, handleNetworkError } from 'utils/errorHandling';

import { defaultHammrHeaders } from 'utils/requestHelpers';

// We're not going to replace Check's earning rates because there's some other services/files that rely on it. We will instead add Hammr versions at the top of the file.

// Hammr version(s)

export const getHammrEarningRate = async (earningRateId: string): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/earning-rates/${earningRateId}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });

    await validateNetworkResponse(res);
    const data = await res.json();
    return data;
  } catch (err) {
    console.log(`Error fetching earning rate: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getHammrEarningRates = async (urlParamsObj: Record<string, string>): Promise<any> => {
  const transformedUrlParams = new URLSearchParams(urlParamsObj).toString();
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/earning-rates?${transformedUrlParams}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching earning rates: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createHammrEarningRate = async (data: Partial<HammrEarningRate>): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/earning-rates`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating earning rate: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getCompensationTypeFromHammrEarningRate = (earningRate: HammrEarningRate): string => {
  if (earningRate.period === 'ANNUALLY') {
    return 'Salaried';
  } else if (earningRate.period === 'HOURLY') {
    return 'Hourly';
  } else {
    return '';
  }
};

// Legacy - Check version (keep beecause other services/files rely on it)

export const getEarningRate = async (earningRateId: string): Promise<EarningRate> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Access-Control-Allow-Origin': '*',
    },
  };
  const response = await fetch(`/api/earning_rates/${earningRateId}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data as EarningRate;
};

export const createEarningRate = async (data: EarningRate): Promise<any> => {
  const options: RequestInit = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
    body: JSON.stringify(data),
  };
  const response = await fetch(`/api/earning_rates`, options);
  await validateNetworkResponse(response);
  return response;
};

export const updateEarningRate = async (id: string, data: UpdateEarningRate): Promise<any> => {
  const options: RequestInit = {
    method: 'PATCH',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
    body: JSON.stringify(data),
  };
  const response = await fetch(`/api/earning_rates/${id}`, options);
  await validateNetworkResponse(response);
  return response;
};

export const markEarningRateAsInactive = async (id: string): Promise<any> => {
  const options: RequestInit = {
    method: 'PATCH',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
    body: JSON.stringify({ active: false }),
  };
  const response = await fetch(`/api/earning_rates/${id}`, options);
  await validateNetworkResponse(response);
  return response;
};

export const getCompensationTypeFromEarningRate = (earningRate: EarningRate): string => {
  if (earningRate.period === 'annually') {
    return 'Salaried';
  } else if (earningRate.period === 'hourly') {
    return 'Hourly';
  } else {
    return '';
  }
};

export const listEarningRates = async (checkEmployeeId: string, activeStatus = true): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
  };
  const response = await fetch(`/api/earning_rates?active=${activeStatus}&employee=${checkEmployeeId}`, options);
  await validateNetworkResponse(response);
  return response;
};
