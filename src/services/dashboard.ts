import { defaultHammrHeaders } from 'utils/requestHelpers';

import { DashboardUserTimesheetSummary } from 'interfaces/timesheet';
import {
  handleNetworkError,
  logError,
  validateNetworkResponse,
} from 'utils/errorHandling';

export interface GetEmployeeHoursWagesResponse {
  employees: DashboardUserTimesheetSummary[];
  totalMinutes: number;
  totalWages: number;
}

export const getEmployeeHoursWages = async (
  queryParams
): Promise<GetEmployeeHoursWagesResponse> => {
  try {
    const serializedParams = new URLSearchParams(queryParams).toString();

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/dashboard/employees-hours-wages?${serializedParams}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
        },
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching employee hours wages: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getProjectListDashboard = async (queryParams) => {
  try {
    const serializedParams = new URLSearchParams(queryParams).toString();

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/dashboard/projects?${serializedParams}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
        },
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching project list dashboard: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
