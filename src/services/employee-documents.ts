import { apiRequest } from 'utils/requestHelpers';
import { logError, handleNetworkError } from 'utils/errorHandling';

export const employeeDocuments = {
  list: (urlParamsObj: Record<string, string>) =>
    apiRequest('employee-documents', { method: 'GET', urlParams: urlParamsObj }),
  create: (data: { name: string; objectId: string; userId: string }) =>
    apiRequest('employee-documents', { method: 'POST', body: data }),
  delete: (id: number) => apiRequest(`employee-documents/${id}`, { method: 'DELETE' }),
};
