import { WorkplaceForm, Workplace } from 'interfaces/workplace';
import { validateNetworkResponse } from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const createWorkplace = (data: WorkplaceForm): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    };
    fetch(`/api/workplaces`, options)
      // .then(validateNetworkResponse) // Custom condition is applied on component
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const getWorkplace = (id: string): Promise<Workplace> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/workplaces/${id}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data as Workplace);
      })
      .catch((err) => reject(err));
  });
};

export const updateWorkplace = (id: string, data: WorkplaceForm): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    };
    fetch(`/api/workplaces/${id}`, options)
      // .then(validateNetworkResponse) // Custom condition is applied on component
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const listWorkplaces = (checkCompanyId: string): Promise<Array<Workplace>> => {
  return new Promise((resolve, reject) => {
    if (!checkCompanyId) {
      reject('Invalid Company Id');
    }
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };
    fetch(`/api/workplaces?company=${checkCompanyId}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        let data = await response.json();
        let results = data?.results || [];
        while (data?.next) {
          data = await getWorkplaceNextPage(data.next);
          if (data?.results) results = results.concat(data.results);
        }
        resolve(results);
      })
      .catch((err) => reject(err));
  });
};

const getWorkplaceNextPage = (nextUrl: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };
    const url = `/api/${nextUrl.replace('https://sandbox.checkhq.com/', '').replace('https://api.checkhq.com/', '')}`;
    fetch(url, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data);
      })
      .catch((err) => reject(err));
  });
};
