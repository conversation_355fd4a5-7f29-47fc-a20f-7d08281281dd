import { TimesheetRoundingInterval, TimesheetRoundingType } from '@/interfaces/time-tracking-settings';
import { apiRequest } from '../utils/requestHelpers';

interface UpdateData {
  areRealtimeBreaksEnabled: boolean;
  breakOptions: number[];
  areRealtimeBreakRemindersEnabled: boolean;
  realtimeBreakStartReminderAt: string;
  realtimeBreakEndReminderAfter: number;
  allowWorkersToAddEditTime: boolean;
  allowWorkersToTrackTime: boolean;
  allowForemanToCreateProjects: boolean;
  useDecimalHours: boolean;
  locationBreadcrumbingEnabled: boolean;
  timesheetRoundingEnabled: boolean;
  timesheetRoundingInterval: TimesheetRoundingInterval;
  timesheetRoundingType: TimesheetRoundingType;
  isInjuryReportRequired: boolean;
  isClockinClockoutPhotosEnabled: boolean;
  defaultClockInTime: string;
  defaultClockOutTime: string;
}

export default {
  update(data: Partial<UpdateData>) {
    return apiRequest('time-tracking-settings', {
      method: 'PATCH',
      body: data,
    });
  },
};
