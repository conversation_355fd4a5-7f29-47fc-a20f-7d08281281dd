import { AddPostTaxDeduction, PostTaxDeduction } from 'interfaces/post-tax-deduction';
import { validateNetworkResponse } from 'utils/errorHandling';

import { apiRequestCheck, defaultHammrHeaders } from 'utils/requestHelpers';
import { ListResponse } from '@/interfaces/check';

export const createPostTaxDeduction = (data: AddPostTaxDeduction): Promise<PostTaxDeduction> => {
  return apiRequestCheck(`post_tax_deductions`, {
    method: 'POST',
    body: data,
  });
};

export const updatePostTaxDeduction = (id: string, postTaxDeduction: PostTaxDeduction): Promise<any> => {
  return apiRequestCheck(`post_tax_deductions/${id}`, {
    method: 'PATCH',
    body: postTaxDeduction,
  });
};

export const listPostTaxDeductionsByEmployeeId = (employeeId: string): Promise<ListResponse<PostTaxDeduction>> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/post_tax_deductions?employee=${employeeId}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data);
      })
      .catch((err) => reject(err));
  });
};

export function listPostTaxDeductionsByCompanyId(companyId: string): Promise<ListResponse<PostTaxDeduction>> {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/post_tax_deductions?company=${companyId}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data);
      })
      .catch((err) => reject(err));
  });
}

export function listPostTaxDeductionsByQueryParams(params: string): Promise<ListResponse<PostTaxDeduction>> {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/post_tax_deductions?${params}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data);
      })
      .catch((err) => reject(err));
  });
}

export function deletePostTaxDeduction(id: string) {
  return apiRequestCheck(`post_tax_deductions/${id}`, {
    method: 'DELETE',
    convertToJson: false,
  });
}
