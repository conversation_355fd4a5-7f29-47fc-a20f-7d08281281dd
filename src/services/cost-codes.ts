import { CostCode } from '@/interfaces/cost-code';
import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { apiRequest, defaultHammrHeaders } from 'utils/requestHelpers';

export const getCostCodes = async (urlParamsObj: Record<string, string | number>): Promise<CostCode[]> => {
  const stringifiedParams = Object.fromEntries(
    Object.entries(urlParamsObj).map(([key, value]) => [key, String(value)])
  );

  const transformedUrlParams = new URLSearchParams(stringifiedParams).toString();
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/cost-codes?${transformedUrlParams}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data.costCodes;
  } catch (err) {
    logError(err);
    handleNetworkError(err);
    throw err;
  }
};

interface CostCodeInput {
  // Define the expected properties for creating/updating a cost code
  name?: string;
  code?: string;
  description?: string;
  // Add other properties as needed
}

export const createCostCode = async (data: CostCodeInput): Promise<CostCode> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/cost-codes`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    logError(err);
    handleNetworkError(err);
    throw err;
  }
};

export const updateCostCode = async (id: number, data: CostCodeInput): Promise<CostCode> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/cost-codes/${id}`, {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    logError(err);
    handleNetworkError(err);
    throw err;
  }
};

export function assignWorkersCompCode(costCodeId: number, workersCompCodeId: number) {
  return apiRequest(`cost-codes/${costCodeId}/workers-comp-code`, { method: 'PATCH', body: { workersCompCodeId } });
}
