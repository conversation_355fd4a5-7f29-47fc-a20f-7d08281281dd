import { EmployeeBenefit, EmployeeBenefitResponse } from 'interfaces/benefit';
import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { apiRequest, defaultHammrHeaders } from 'utils/requestHelpers';
import { removeEmptyFields } from 'utils/utils';

export const unenrollEmployeeBenefit = async (id, data: EmployeeBenefit): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/employee-benefits/${id}/deactivate`, {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating benefit: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const editEmployeeBenefit = async (id, data: Partial<EmployeeBenefit>): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/employee-benefits/${id}`, {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating benefit: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createEmployeeBenefit = async (data: Partial<EmployeeBenefit>): Promise<any> => {
  return apiRequest('employee-benefits', { method: 'POST', body: data });
};

export const getEmployeeBenefit = async (benefitId: string): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/employee-benefits/${benefitId}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    });

    await validateNetworkResponse(res);

    const json = await res.json();

    return json.data;
  } catch (err) {
    console.log(`Error fetching benefits: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getEmployeeBenefits = async (urlParamsObj: {
  userId?: string;
  companyBenefitId: string;
}): Promise<{ employeeBenefits: EmployeeBenefitResponse[] }> => {
  const urlParams = removeEmptyFields(urlParamsObj);
  return apiRequest('employee-benefits', { urlParams });
};
