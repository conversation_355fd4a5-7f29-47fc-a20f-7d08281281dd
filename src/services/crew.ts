import {
  handleNetworkError,
  logError,
  validateNetworkResponse,
} from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const getCrews = async (
  urlParamsObj: Record<string, string>
): Promise<any> => {
  // keys allowed in urlParamsObj should be organizationId and etc
  const transformedUrlParams = new URLSearchParams(urlParamsObj).toString();
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/crews?${transformedUrlParams}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
        },
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching projects: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createCrew = async (data: any) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/crews`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating project: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const updateCrew = async (id: number, data: any) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/crews/${id}`,
      {
        method: 'PATCH',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error updating project: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const deleteCrew = async (crewId: number) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/crews/${crewId}`,
      {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error deleting crew: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
