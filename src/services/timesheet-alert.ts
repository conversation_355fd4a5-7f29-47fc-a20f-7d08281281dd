import { UpdateTimesheetAlert } from 'interfaces/timesheet-alert';
import {
  handleNetworkError,
  logError,
  validateNetworkResponse,
} from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const updateTimesheetAlert = async (
  id: number,
  data: UpdateTimesheetAlert
) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheet-alert/${id}`,
      {
        method: 'PATCH',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error updating timesheet alert: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
