import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { defaultHammrHeaders, apiRequest } from 'utils/requestHelpers';
import { Department } from '@/interfaces/department';
export const departmentsService = {
  list: () => apiRequest<{ departments: Department[] }>('departments'),
  create: (data: any) => apiRequest('departments', { method: 'POST', body: data }),
  update: (id: number, data: any) => apiRequest(`departments/${id}`, { method: 'PATCH', body: data }),
  delete: (id: number) => apiRequest(`departments/${id}`, { method: 'DELETE' }),
  updateDepartmentMember: (data: { userId: number; departmentId: number }) =>
    apiRequest('department/members', { method: 'PATCH', body: data }),
  bulkAssignDepartmentMembers: (data: { departmentId: number; userIds: number[] }) =>
    apiRequest('department/members/bulk-assign', { method: 'PATCH', body: data }),
};
