import { handleNetworkError, logError } from '@/utils/errorHandling';
import { apiRequest } from 'utils/requestHelpers';
import { IntegrationUserToken } from '@/interfaces/integration-user-token';

export default {
  getHammrUserTokens: () =>
    apiRequest<{
      integrationUserTokens: IntegrationUserToken[];
    }>('integration-user-tokens', { method: 'GET' }).then((result) => result.integrationUserTokens),
  createHammrUserToken: (data) => apiRequest('integration-user-tokens', { method: 'POST', body: data }),
  deleteHammrUserToken: (userTokenId: number) =>
    apiRequest(`integration-user-tokens/${userTokenId}`, { method: 'DELETE' }),
  createJournalEntry: (data) => apiRequest('integration-user-tokens/journal-entry', { method: 'POST', body: data }),
  previewJournalEntry: (data) =>
    apiRequest('integration-user-tokens/journal-entry?preview=true', { method: 'POST', body: data }),

  updateJournalEntry: (data) => apiRequest('integration-user-tokens/journal-entry', { method: 'PATCH', body: data }),
  getSupportedPlatforms: () => apiRequest('integration-user-tokens/supported-platforms', { method: 'GET' }),

  // Updated CSV export using apiRequest
  exportPayrollJournalEntryToCsv: async (data: { payrollId: string }) => {
    try {
      // Call our apiRequest with custom headers and convertToJson = false
      const res = await apiRequest('integration-user-tokens/payroll-journal-entry-to-csv', {
        method: 'POST',
        body: data,
        convertToJson: false, // so we get raw Response
      });

      // Convert the Response to a blob
      const blob = await res.blob();

      // Create a download link for the CSV
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'foundation_journal_entry.csv';
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error exporting payroll journal entry to CSV:', err);
      logError(err);
      handleNetworkError(err);
    }
  },
};
