import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const createScheduleEvent = async (data: any) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/schedule-events`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating schedule event: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const updateScheduleEvent = async (id: number, data: any) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/schedule-events/${id}`, {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error updating schedule event: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const deleteScheduleEvent = async (eventId: string) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/schedule-events/${eventId}`, {
      method: 'DELETE',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error deleting schedule event: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const deleteManyScheduleEvents = async (linkedEventId: string) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/schedule-events?linkedEventId=${linkedEventId}`,
      {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error deleting linked schedule events: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const resendEventNotification = async (eventId: number) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/schedule-events/${eventId}/send-update-notifications`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
      }
    );
    await validateNetworkResponse(res);
  } catch (err) {
    console.log(`Error scheduling resend event notification: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
