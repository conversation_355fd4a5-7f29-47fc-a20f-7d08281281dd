import { EmployeeClassification } from '@/interfaces/classifications';
import { apiRequest } from 'utils/requestHelpers';
import { removeEmptyFields } from 'utils/utils';

export const getClassifications = ({ wageTableId }) => {
  return apiRequest('classifications', {
    method: 'GET',
    urlParams: { wageTableId },
  });
};

export const createClassification = (classification) => {
  return apiRequest('classifications', {
    method: 'POST',
    body: classification,
  });
};

export const updateClassification = (id, classification) => {
  return apiRequest(`classifications/${id}`, {
    method: 'PATCH',
    body: classification,
  });
};

export async function getEmployeeClassifications({
  wageTableId,
  userIds,
  active,
}: {
  wageTableId?: number;
  userIds?: number[] | string; // number array must have 1 element only, otherwise do a .join(',') on array and pass the string. this is a backend issue
  active?: boolean;
}) {
  const urlParams = removeEmptyFields({ wageTableId, userIds, active });
  const data = await apiRequest<{ userClassifications: EmployeeClassification[] }>('user-classifications', {
    urlParams,
  });
  return data.userClassifications;
}

export const assignEmployeeClassification = ({
  classificationId,
  userIds,
  basePay,
  fringePay,
  startDate,
}: {
  classificationId: number;
  userIds: number[];
  basePay: string;
  fringePay: string;
  startDate: number;
}) => {
  return apiRequest('user-classifications/assign', {
    method: 'POST',
    body: { classificationId, userIds, basePay, fringePay, startDate },
  });
};

export const unassignEmployeeClassification = ({
  userClassificationIds,
  endDate,
}: {
  userClassificationIds: number[];
  endDate: number;
}) => {
  return apiRequest('user-classifications/unassign', {
    method: 'POST',
    body: { userClassificationIds, endDate },
  });
};

export const updateEmployeeClassifications = ({
  effectiveDate,
  payload,
}: {
  effectiveDate: number;
  payload: { userClassificationId; basePay; fringePay }[];
}) => {
  return apiRequest(`user-classifications`, {
    method: 'PATCH',
    body: { effectiveDate, userClassifications: payload },
  });
};
