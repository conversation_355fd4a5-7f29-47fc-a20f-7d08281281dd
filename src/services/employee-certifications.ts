import { apiRequest } from 'utils/requestHelpers';
import { EmployeeCertification } from '@/interfaces/employee-certifications';

export const employeeCertifications = {
  list: (params: { userId?: string; expiredOrExpiring?: boolean }) =>
    apiRequest<EmployeeCertification[]>('employee-certifications', { urlParams: params }),
  create: (data: {
    title: string;
    objectId: string;
    userId: string;
    number?: string;
    issuingEntity?: string;
    completionDate?: string;
    expirationDate?: string;
    notes?: string;
  }) => apiRequest('employee-certifications', { method: 'POST', body: data }),
  delete: (id: number) => apiRequest(`employee-certifications/${id}`, { method: 'DELETE' }),
};
