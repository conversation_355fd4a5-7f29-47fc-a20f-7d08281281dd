import { validateNetworkResponse } from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const generateSimplyInsuredComponentLink = (
  companyId: string,
  email: string
): Promise<any> => {
  return new Promise((resolve, reject) => {
    // more explicit truthy check(s)
    const emailToUse = email ? email : '<EMAIL>';

    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
      body: JSON.stringify({
        integration_partner: 'int_Xf90bJAKE9Lf2jVjbgne',
        email: emailToUse,
      }),
    };
    fetch(`/api/companies/${companyId}/components/integrations`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(await response.json());
      })
      .catch((err) => reject(err));
  });
};
