import { Contractor, EnrichedContractor, PaginatedContractors, UpdateContractor } from 'interfaces/contractor';
import { ContractorPayment } from 'interfaces/contractor-payment';
import { HammrUser } from 'interfaces/user';

import { defaultHammrHeaders, fixPaginationUrl } from 'utils/requestHelpers';
import { logError, validateNetworkResponse } from 'utils/errorHandling';
import { AppNetworkError } from 'utils/AppError';
import { ListResponse } from '@/interfaces/check';
import { getBankAccountsForContractor } from './bank-account';
import { getActiveEarningRate } from '@/utils/temporalUtils';

export const createContractor = async (data: Contractor): Promise<any> => {
  const options: RequestInit = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
    body: JSON.stringify(data),
  };
  const response = await fetch(`/api/contractors`, options);
  await validateNetworkResponse(response);
  return response;
};

export const getContractor = async (id: string): Promise<Contractor> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Access-Control-Allow-Origin': '*',
    },
  };
  const response = await fetch(`/api/contractors/${id}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data as Contractor;
};

export const getBulkContractors = async (queryParams: string): Promise<PaginatedContractors> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Access-Control-Allow-Origin': '*',
    },
  };
  const response = await fetch(`/api/contractors?${queryParams}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data;
};

export const updateContractor = async (id: string, data: UpdateContractor): Promise<any> => {
  const options: RequestInit = {
    method: 'PATCH',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
    body: JSON.stringify(data),
  };
  const response = await fetch(`/api/contractors/${id}`, options);
  // await validateNetworkResponse(response); // constom condtion applied on component
  return response;
};

export const listContractors = async (checkCompanyId: string): Promise<Contractor[]> => {
  if (!checkCompanyId) {
    throw new Error('Invalid Company Id');
  }

  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Content-Type': 'application/json',
    },
  };

  const response = await fetch(`/api/contractors?company=${checkCompanyId}&active=true`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  const result = [];
  data.results.forEach((element) => {
    result.push(element as Contractor);
  });
  return result;
};

export const enrichContractor = async ({
  contractor,
  hammrUser,
}: {
  contractor: Contractor;
  hammrUser: HammrUser;
}): Promise<EnrichedContractor> => {
  try {
    const bankAccounts = await getBankAccountsForContractor(contractor.id);

    // Use timezone-aware logic to find active earning rates
    // Default to Pacific timezone if company timezone is not available
    const primaryEarningRate = getActiveEarningRate(
      hammrUser?.earningRates || [],
      'America/Los_Angeles', // Default timezone - will be overridden by component logic
      'REG'
    );

    const primaryOTEarningRate = getActiveEarningRate(
      hammrUser?.earningRates || [],
      'America/Los_Angeles', // Default timezone - will be overridden by component logic
      'OT'
    );

    const customContractor = {
      checkContractor: contractor,
      hammrUser: hammrUser,
      bankAccount: bankAccounts?.length ? bankAccounts[0] : undefined,
      primaryEarningRate,
      primaryOTEarningRate,
    };

    return customContractor;
  } catch (err) {
    console.log(err);
    logError(err);
    throw new AppNetworkError(err);
  }
};

export const paginateContractors = async (checkCompanyId: string, paginateUrl?: string): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
  };
  const url = paginateUrl != null ? fixPaginationUrl(paginateUrl) : `/api/contractors?company=${checkCompanyId}`;
  const response = await fetch(url, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  const filteredContractors = data.results;
  data.results = filteredContractors.map((contractor) => {
    return {
      checkContractor: contractor,
    };
  });
  return data;
};

export const listContractorPayments = async (contractorId: string): Promise<ListResponse<ContractorPayment>> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
  };
  const response = await fetch(`/api/contractors/${contractorId}/payments`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data;
};

export const getContractorPaymentPdf = async (contractorId: string, payrollId: string, label: string): Promise<any> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      Accept: 'application/pdf',
      ...defaultHammrHeaders,
    },
  };
  const res = await fetch(`/api/contractors/${contractorId}/payments/${payrollId}`, options);
  await validateNetworkResponse(res);
  const blob = await res.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${label}.pdf`;
  document.body.appendChild(a);
  a.click();
  a.remove();

  return '';
};

// contractor onboard link / etc?
export const generateContractorOnboardLink = async (contractorId: string): Promise<any> => {
  const options: RequestInit = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...defaultHammrHeaders,
    },
  };
  const response = await fetch(`/api/contractors/${contractorId}/onboard`, options);
  await validateNetworkResponse(response);
  return await response.json();
};
