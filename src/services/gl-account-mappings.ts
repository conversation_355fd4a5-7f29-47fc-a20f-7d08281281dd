import { apiRequest, defaultHammrHeaders } from 'utils/requestHelpers';

export const getGlAccountMappings = async (
  platform: string,
  integrationUserTokenId: number,
  departmentId?: number | string
) => {
  const departmentParam = departmentId !== undefined ? `&departmentId=${departmentId}` : '';
  const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/gl-account-mappings?platform=${platform}&integrationUserTokenId=${integrationUserTokenId}${departmentParam}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: defaultHammrHeaders,
    credentials: 'include',
  });

  if (!response.ok) {
    if (response.status === 451) {
      throw new Error('Rutter connection expired. Please disable connection and re-authenticate.');
    }
    const errorData = await response.json().catch(() => ({ message: 'Failed to fetch GL account mappings' }));
    throw new Error(errorData.message || errorData.error || 'Failed to fetch GL account mappings');
  }

  if (response.status === 202) {
    // Means we are waiting for rutter to finish syncing
    return null;
  }

  return await response.json();
};

export const getDepartmentsWithCustomMappings = async (platform: string, integrationUserTokenId: number) => {
  return apiRequest(
    `gl-account-mappings/departments?platform=${platform}&integrationUserTokenId=${integrationUserTokenId}`,
    {
      method: 'GET',
    }
  );
};

export const createGlAccountMapping = async (data) => {
  return apiRequest('gl-account-mappings', {
    method: 'POST',
    body: data,
  });
};

export const updateGlAccountMapping = async (data, glAccountMappingId: number) => {
  return apiRequest(`gl-account-mappings/${glAccountMappingId}`, {
    method: 'PATCH',
    body: data,
  });
};

export const saveDepartmentMappings = async (
  departmentId: number,
  data: {
    mappings: Record<string, { accountId: string; accountName: string; accountType: string; accountCategory: string }>;
    integrationUserTokenId: number;
  }
) => {
  return apiRequest(`gl-account-mappings/departments/${departmentId}`, {
    method: 'POST',
    body: data,
  });
};

export const deleteDepartmentMappings = async (departmentId: number) => {
  return apiRequest(`gl-account-mappings/departments/${departmentId}`, {
    method: 'DELETE',
  });
};

export const triggerIncrementalSync = async (integrationUserTokenId: number) => {
  return apiRequest('gl-account-mappings/trigger-sync', {
    method: 'POST',
    body: { integrationUserTokenId },
  });
};
