import {
  handleNetworkError,
  logError,
  validateNetworkResponse,
} from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const getProjectDocuments = async (
  urlParamsObj: Record<string, string>
) => {
  const transformedUrlParams = new URLSearchParams(urlParamsObj).toString();
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-documents?${transformedUrlParams}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
        },
      }
    );

    await validateNetworkResponse(res);

    if (!res.ok) {
      throw new Error(res.statusText);
    }

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching project documents: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const deleteProjectDocument = async (id: number) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-documents/${id}`,
      {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
        },
      }
    );
    await validateNetworkResponse(res);
    if (!res.ok) {
      throw new Error(res.statusText);
    }
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error deleting project document: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createProjectDocument = async (data: any) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-documents`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating project document: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
