import {
  handleNetworkError,
  logError,
  validateNetworkResponse,
} from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const getProjectPhotoCollections = async (
  urlParamsObj: Record<string, string>
) => {
  const transformedUrlParams = new URLSearchParams(urlParamsObj).toString();
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-photos-collections?${transformedUrlParams}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
        },
      }
    );

    await validateNetworkResponse(res);

    if (!res.ok) {
      throw new Error(res.statusText);
    }

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching project photo collections: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createProjectPhoto = async (data: any) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-photos`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating project: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createProjectPhotoCollection = async (data: any) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/project-photos-collections`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating project: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
