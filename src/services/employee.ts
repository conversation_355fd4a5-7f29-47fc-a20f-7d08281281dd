import { Employee, EnrichedEmployee, PaginatedEmployees } from 'interfaces/employee';
import { defaultHammrHeaders, fixPaginationUrl } from 'utils/requestHelpers';

import { getBankAccountsForEmployee } from 'services/bank-account';
import { getWorkplace } from 'services/workplace';
import { userService } from 'services/user';

import { EmployeePaystubs } from 'interfaces/paystub';
import { logError, validateNetworkResponse } from 'utils/errorHandling';
import { AppNetworkError } from 'utils/AppError';
import { ListResponse } from '@/interfaces/check';
import { getActiveEarningRate } from '@/utils/temporalUtils';

export const getEmployee = (id: string): Promise<Employee> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Access-Control-Allow-Origin': '*',
      },
    };
    fetch(`/api/employees/${id}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data as Employee);
      })
      .catch((err) => reject(err));
  });
};

export const getBulkEmployees = (queryParams: string): Promise<PaginatedEmployees> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Access-Control-Allow-Origin': '*',
      },
    };
    fetch(`/api/employees?${queryParams}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data);
      })
      .catch((err) => reject(err));
  });
};

export const getEnrichedEmployee = async (id: number): Promise<EnrichedEmployee> => {
  try {
    const user = await userService.get(id);
    const checkEmployeeId = user.checkEmployeeId;

    const employee = checkEmployeeId ? await getEmployee(checkEmployeeId) : undefined;

    const requests = employee?.id
      ? ([
          getBankAccountsForEmployee(employee.id),
          ...employee?.workplaces?.map((workplaceId) => getWorkplace(workplaceId)),
        ] as const)
      : undefined;

    const [bankAccounts, ...workplaces] = requests ? await Promise.all(requests) : [];

    // Use timezone-aware logic to find active earning rates
    // Default to Pacific timezone if company timezone is not available
    const primaryEarningRate = getActiveEarningRate(
      user?.earningRates || [],
      user?.organization?.timezone || 'America/Los_Angeles',
      'REG'
    );

    const primaryOTEarningRate = getActiveEarningRate(
      user?.earningRates || [],
      user?.organization?.timezone || 'America/Los_Angeles',
      'OT'
    );

    return {
      checkEmployee: employee,
      hammrUser: user,
      workplaces: workplaces,
      bankAccount: bankAccounts?.length ? bankAccounts[0] : undefined,
      primaryEarningRate,
      primaryOTEarningRate,
    };
  } catch (err) {
    console.log(err);
    logError(err);
    throw new AppNetworkError(err);
  }
};

export const paginateEmployees = (checkCompanyId: string, paginateUrl?: string, activeOnly?: boolean): Promise<any> => {
  return new Promise(async (resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    const url = paginateUrl != null ? fixPaginationUrl(paginateUrl) : `/api/employees?company=${checkCompanyId}`;
    fetch(url, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        const filteredEmployees = activeOnly
          ? data.results.filter((employee) => employee.active === true)
          : data.results;
        data.results = filteredEmployees.map((employee) => {
          return {
            checkEmployee: employee,
          };
        });
        resolve(data);
      })
      .catch((err) => reject(err));
  });
};

export const getEmployeePaystubPdf = (employeeId: string, payrollId: string, label: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        Accept: 'application/pdf',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/employees/${employeeId}/paystubs/${payrollId}`, options)
      .then((res) => res.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${label}.pdf`;
        document.body.appendChild(a);
        a.click();
        a.remove();
        resolve('');
      })
      .catch((err) => reject(err));
  });
};

export const listEmployeePaystubs = (employeeId: string): Promise<ListResponse<EmployeePaystubs>> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/employees/${employeeId}/paystubs`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        resolve(data);
      })
      .catch((err) => reject(err));
  });
};

export const generateEmployeeOnboardLink = (employeeId: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/employees/${employeeId}/onboard`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(await response.json());
      })
      .catch((err) => reject(err));
  });
};
