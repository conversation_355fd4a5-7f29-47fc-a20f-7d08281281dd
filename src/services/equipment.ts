import { Equipment, CreateEquipmentPayload, UpdateEquipmentPayload } from '@/interfaces/equipment';
import { apiRequest } from '../utils/requestHelpers';

export const equipmentService = {
  async getAll({
    isArchived,
    categoryIds,
  }: {
    isArchived?: boolean;
    categoryIds?: number[];
  } = {}): Promise<Equipment[]> {
    const data = await apiRequest('equipments', {
      urlParams: {
        includeIsArchived: 'true',
        categoryIds: categoryIds?.length ? categoryIds.join(',') : null,
      },
    });
    // Filter the results on the client side if isArchived is specified
    const equipment = data.equipment;
    if (isArchived !== undefined) {
      return equipment.filter((item) => item.isArchived === isArchived);
    }
    return equipment;
  },

  async get(id: number): Promise<Equipment> {
    const data = await apiRequest(`equipments/${id}`);
    return data.equipment;
  },

  async create(payload: CreateEquipmentPayload): Promise<Equipment> {
    const data = await apiRequest('equipments', {
      method: 'POST',
      body: payload,
    });
    return data.equipment;
  },

  async update(id: number, payload: UpdateEquipmentPayload): Promise<Equipment> {
    const data = await apiRequest(`equipments/${id}`, {
      method: 'PATCH',
      body: payload,
    });
    return data.equipment;
  },

  async archive(id: number): Promise<Equipment> {
    return this.update(id, { isArchived: true });
  },

  async restore(id: number): Promise<Equipment> {
    return this.update(id, { isArchived: false });
  },

  async delete(id: number): Promise<void> {
    await apiRequest(`equipments/${id}`, {
      method: 'DELETE',
    });
  },
};
