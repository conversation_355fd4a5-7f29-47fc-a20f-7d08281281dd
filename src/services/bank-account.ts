import { BankAccount } from 'interfaces/bank-account';
import { validateNetworkResponse } from 'utils/errorHandling';

import { defaultHammrHeaders } from 'utils/requestHelpers';

export const getBankAccount = async (bankAccountId: string): Promise<BankAccount> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Access-Control-Allow-Origin': '*',
    },
  };
  const response = await fetch(`/api/bank_accounts/${bankAccountId}`, options);
  await validateNetworkResponse(response);
  const data = await response.json();
  return data as BankAccount;
};

export const getBankAccountsForEmployee = async (employeeId: string): Promise<BankAccount[]> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Access-Control-Allow-Origin': '*',
    },
  };
  const response = await fetch(`/api/bank_accounts?employee=${employeeId}`, options);

  await validateNetworkResponse(response);

  const data = await response.json();
  return data.results as BankAccount[];
};

export const getBankAccountsForContractor = async (contractorId: string): Promise<BankAccount[]> => {
  const options: RequestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      ...defaultHammrHeaders,
      'Access-Control-Allow-Origin': '*',
    },
  };
  const response = await fetch(`/api/bank_accounts?contractor=${contractorId}`, options);

  await validateNetworkResponse(response);

  const data = await response.json();
  return data.results as BankAccount[];
};
