import { TimeOffPolicyType } from '@/interfaces/timeoff';
import type { BadgeProps } from '@hammr-ui/components/badge';

export const getPolicyTypeBadgeProps = (type: TimeOffPolicyType) => {
  const config: Record<TimeOffPolicyType, { color: BadgeProps['color']; label: string }> = {
    PTO: { color: 'blue', label: 'PTO' },
    SICK: { color: 'red', label: 'Sick' },
    UNPAID: { color: 'gray', label: 'Unpaid' },
    PERSONAL: { color: 'yellow', label: 'Personal Days' },
    VOLUNTEER: { color: 'green', label: 'Volunteer' },
    JURY_DUTY: { color: 'purple', label: 'Jury Duty' },
    BEREAVEMENT: { color: 'orange', label: 'Bereavement' },
  };

  return config[type] || { color: 'gray', label: type };
};

export const getPolicyName = (type: TimeOffPolicyType) => {
  const config: Record<TimeOffPolicyType, string> = {
    PTO: 'Paid Time Off',
    SICK: 'Sick',
    UNPAID: 'Unpaid',
    PERSONAL: 'Personal Days',
    VOLUNTEER: 'Volunteer',
    JURY_DUTY: 'Jury Duty',
    BEREAVEMENT: 'Bereavement',
  };

  return config[type] || 'Paid Time Off';
};
