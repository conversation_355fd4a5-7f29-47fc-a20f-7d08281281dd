import { PayrollItem } from 'interfaces/payroll-item';
import { validateNetworkResponse } from 'utils/errorHandling';

import { apiRequestCheck, defaultHammrHeaders } from 'utils/requestHelpers';

export const createPayrollItems = (data: PayrollItem | PayrollItem[]) => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    };
    fetch(`/api//payroll_items`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const getPayrollItem = (payrollId: string) => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };
    fetch(`/api/payroll_items/${payrollId}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};

export const updatePayrollItem = (payrollId: string, data: Partial<PayrollItem>) => {
  return apiRequestCheck(`payroll_items/${payrollId}`, {
    method: 'PATCH',
    body: data,
  });
};

export const listPayrollItems = (payrollId?: string, employeeId?: string) => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    };

    let url = `/api/payroll_items`;
    if (payrollId && employeeId) {
      url += `?payroll=${payrollId}&employee=${employeeId}`;
    } else if (payrollId) {
      url += `?payroll=${payrollId}`;
    } else if (employeeId) {
      url += `?employee=${employeeId}`;
    }

    fetch(url, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        const data = await response.json();
        const result = [];
        data.results.forEach((element) => {
          result.push(element as PayrollItem);
        });
        resolve(result);
      })
      .catch((err) => reject(err));
  });
};

export const deletePayrollItem = (payrollId: string) => {
  return new Promise((resolve, reject) => {
    const options: RequestInit = {
      method: 'DELETE',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...defaultHammrHeaders,
      },
    };
    fetch(`/api/payroll_items/${payrollId}`, options)
      .then(validateNetworkResponse)
      .then(async (response) => {
        resolve(response);
      })
      .catch((err) => reject(err));
  });
};
