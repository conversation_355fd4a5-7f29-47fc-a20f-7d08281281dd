import { CompanyBenefit } from 'interfaces/benefit';
import {
  handleNetworkError,
  logError,
  validateNetworkResponse,
} from 'utils/errorHandling';
import { defaultHammrHeaders } from 'utils/requestHelpers';

export const updateBenefit = async (
  id: number,
  data: Partial<CompanyBenefit>
): Promise<any> => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/company-benefits/${id}`,
      {
        method: 'PATCH',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating benefit: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const createBenefit = async (data: CompanyBenefit): Promise<any> => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/company-benefits`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating benefit: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getBenefit = async (benefitId: string): Promise<any> => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/company-benefits/${benefitId}`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();

    return json.data;
  } catch (err) {
    console.log(`Error fetching benefits: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getBenefits = async (): Promise<{
  companyBenefits: CompanyBenefit[];
}> => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/company-benefits`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          ...defaultHammrHeaders,
          'Content-Type': 'application/json',
        },
      }
    );

    await validateNetworkResponse(res);

    const json = await res.json();

    return json.data;
  } catch (err) {
    console.log(`Error fetching benefits: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};
