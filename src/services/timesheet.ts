import { CreateUserTimesheet, UpdateUserTimesheet } from 'interfaces/timesheet';
import { handleNetworkError, logError, validateNetworkResponse } from 'utils/errorHandling';
import { apiRequest, defaultHammrHeaders } from 'utils/requestHelpers';

export const createTimesheet = async (data: CreateUserTimesheet): Promise<any> => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets`, {
      method: 'POST',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error creating timesheet: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export interface GetTimesheetResponse {
  overtimeMinutes: number;
  timesheets: any[];
  totalBreakDurationMinutes: number;
  totalMinutes: number;
  totalWages: number;
}

export const getTimesheet = async (queryParams: Record<string, any>): Promise<GetTimesheetResponse> => {
  try {
    // queryParams should contain userId, to, from
    const serializedParams = new URLSearchParams(queryParams).toString();

    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets?${serializedParams}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching timesheet: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const updateTimesheet = async (id: number, data: UpdateUserTimesheet) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets/${id}`, {
      method: 'PATCH',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error updating timesheet: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const deleteTimesheet = async (timesheetId: string) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets/${timesheetId}`, {
      method: 'DELETE',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': 'application/json',
      },
    });
    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error deleting timesheet: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getTimesheetHistory = async (timesheetId: string) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets/${timesheetId}/history`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });
    await validateNetworkResponse(res);

    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching timesheet: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getTimesheetDetails = async (param: number) => {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets/${param}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
      },
    });
    await validateNetworkResponse(res);
    const json = await res.json();
    return json.data;
  } catch (err) {
    console.log(`Error fetching timesheet: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const getTimesheetsReport = async (queryParams: Record<string, any>) => {
  try {
    const serializedParams = new URLSearchParams(queryParams).toString();

    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/timesheets?${serializedParams}`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        ...defaultHammrHeaders,
        'Content-Type': queryParams?.export ? 'text/csv;charset=UTF-8' : 'application/json',
      },
    });

    await validateNetworkResponse(res);

    if (queryParams?.export) {
      return res;
    }

    const json = await res.json();

    return json.data;
  } catch (err) {
    console.log(`Error fetching timesheet report: ${err}`);
    logError(err);
    handleNetworkError(err);
  }
};

export const approveTimesheets = async (timesheetIds: number[]) => {
  return apiRequest('timesheets/approve', {
    method: 'POST',
    body: { timesheetIds },
  });
};
