import Layout from 'components/dashboard/Layout';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { getEnrichedEmployee } from 'services/employee';
import EmployeeInfo from 'components/people/employee-view/EmployeeInfo';
import { useCompany } from 'hooks/useCompany';
import EmployeeTaxDocuments from 'components/people/employee-view/EmployeeTaxDocuments';
import Paystubs from 'components/people/employee-view/Paystubs';
import GarnishmentsDeductions from 'components/people/employee-view/GarnishmentsDeductions';
import { useAuth } from 'hooks/useAuth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import Button from '@hammr-ui/components/button';
import ConfirmArchiveUserModal from '@/components/people/nonpayroll-view/ConfirmArchiveUserModal';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import ArchiveLine from '@hammr-icons/ArchiveLine';
import UploadCloud2Line from '@hammr-icons/UploadCloud2Line';
import AddPayrollEmployeeModal from '@/components/people/AddPayrollEmployeeModal';
import AddLine from '@/hammr-icons/AddLine';
import EmployeeDocumentsView from '@/components/people/employee-view/EmployeeDocuments';
import EmployeeCertificationsView from '@/components/people/employee-view/EmployeeCertifications';
import { Badge } from '@/hammr-ui/components/badge';
import { ProfileImage } from '@/components/people/sections/ProfileImage';
import EmployeeTimeOff from '@/components/people/employee-view/EmployeeTimeOff';
import CreateTimeOffRequestsModal from '@/components/time-off-requests/CreateTimeOffRequestsModal';
import Benefits from '@/components/people/employee-view/Benefits';
import { Menu, MenuContent, MenuTrigger } from '@/hammr-ui/components/Menu';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import TimesheetExport from '@/components/timesheets/TimesheetExport';
import { AgGridReact } from '@ag-grid-community/react';
import { useDateSelection } from '@/hooks/useDateSelection';
import Timesheets from '@/components/timesheets/Timesheets';
import { useTimesheetListStore } from '@/components/timesheets/store';
import { WashingtonFieldsModal } from '@/components/people/sections/WashingtonStateEmployment';
import OnboardingLink from '@/components/people/OnboardingLink';
import { showErrorToast } from '@/utils/errorHandling';
import { INVALID_WORKER_CLASSIFICATION } from '@/utils/constants';
import OnboardStatus from '@/components/people/OnboardStatus';
import { ListResponse } from '@/interfaces/check';
import { PostTaxDeduction } from '@/interfaces/post-tax-deduction';
import { listPostTaxDeductionsByEmployeeId } from '@/services/post-tax-deduction';
import AddPostTaxDeductionModal from '@/components/post-tax-deductions/AddPostTaxDeductionModal';

const ALL_TABS = [
  'Employee Info',
  'Employee Documents',
  'Certifications',
  'Timesheets',
  'Time-Off',
  'Benefits',
  'Paystubs',
  'Tax Documents',
  'Garnishments & Deductions',
] as const;

const NON_PAYROLL_TABS = ['Employee Info', 'Employee Documents', 'Certifications', 'Timesheets'] as const;

type PayrollTab = (typeof ALL_TABS)[number];
type NonPayrollTab = (typeof NON_PAYROLL_TABS)[number];
type Tab = PayrollTab | NonPayrollTab;

const formatTabValue = (tab: string) => tab.replaceAll(' ', '-').toLowerCase();

const EmployeeView: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const employee_id = router.query.employee_id ? Number(router.query.employee_id) : undefined;
  const { company } = useCompany();
  const [showAddToPayrollModal, setShowAddToPayrollModal] = useState(false);
  const [showArchiveModal, setShowArchiveModal] = useState(false);
  const [showPostTaxDeductionModal, setShowPostTaxDeductionModal] = useState(false);
  const [showEmployeeUploadModal, setShowEmployeeUploadModal] = useState(false);
  const [showEmployeeCertificationModal, setShowEmployeeCertificationModal] = useState(false);
  const [showTimeOffRequestModal, setShowTimeOffRequestModal] = useState(false);
  const queryClient = useQueryClient();
  const [showWashingtonFieldsForm, setShowWashingtonFieldsForm] = useState<{
    userId: number;
    name: string;
    workersCompCodeId: number;
    checkEmployeeId: string;
  }>();

  const employee = useQuery({
    queryKey: ['employee', employee_id],
    queryFn: async () => {
      const employee = await getEnrichedEmployee(employee_id);

      // This is to prevent people typing in contractor_ids into the url of an employee
      if (employee.hammrUser.workerClassification !== 'EMPLOYEE') {
        throw new Error(INVALID_WORKER_CLASSIFICATION);
      }

      return employee;
    },
    enabled: Boolean(employee_id) && user?.isCompanyAdmin,
    retry: 1,
  });

  const refreshEmployee = () => {
    queryClient.invalidateQueries({ queryKey: ['employee', employee_id] });
  };

  const allowEdit = employee.data?.hammrUser?.isArchived === false;

  // Determine which tabs to show based on whether the employee is in payroll
  const TABS = employee.data?.hammrUser?.checkEmployeeId ? ALL_TABS : NON_PAYROLL_TABS;

  const currentTab = TABS.find((tab) => formatTabValue(tab) === router.query.tab) || 'Employee Info';

  const handleTabChange = (newTab: Tab) => {
    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: formatTabValue(newTab) },
      },
      undefined,
      { shallow: true }
    );
  };

  // timesheets tab
  const gridRef = useRef<AgGridReact>(null);
  const { currentStartDateSelected, currentEndDateSelected } = useDateSelection();
  const isExportingRef = useRef(false);
  const [showTimesheetModal, setShowTimesheetModal] = useState(false);
  useEffect(() => {
    useTimesheetListStore.setState((state) => ({
      ...state,
      selectedEmployeeIds: [employee_id],
      selectedProjectIds: [],
    }));
  }, [employee_id]);

  const deductions = useQuery<ListResponse<PostTaxDeduction>>({
    queryKey: ['postTaxDeductions', employee.data?.checkEmployee?.id],
    queryFn: () => {
      return listPostTaxDeductionsByEmployeeId(employee.data.checkEmployee.id);
    },
    enabled: !!employee.data?.checkEmployee?.id,
  });

  if (employee.error) {
    if (employee.error.message === INVALID_WORKER_CLASSIFICATION) {
      router.push(`/people/contractor/${employee_id}`);
    } else {
      showErrorToast(employee.error);
      router.push('/people');
    }
    return;
  }

  if (!user || !user?.isCompanyAdmin) return null;

  if (!employee_id || !employee.data || !company) {
    return (
      <Layout>
        <div className="mt-1/5 flex flex-col items-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  const HeaderButtons = () => {
    if (currentTab === 'Garnishments & Deductions')
      return (
        <Button
          color="primary"
          className="mt-4"
          beforeContent={<AddLine />}
          onClick={() => setShowPostTaxDeductionModal(true)}
        >
          Add Post-Tax Deduction
        </Button>
      );

    if (currentTab === 'Employee Documents') {
      return (
        <Button onClick={() => setShowEmployeeUploadModal(true)} beforeContent={<UploadCloud2Line />}>
          Upload Document
        </Button>
      );
    }

    if (currentTab === 'Certifications') {
      return (
        <Button onClick={() => setShowEmployeeCertificationModal(true)} beforeContent={<AddLine />}>
          Add Certification
        </Button>
      );
    }

    if (currentTab === 'Timesheets') {
      return (
        <div className="flex items-center gap-3">
          <Menu>
            <MenuTrigger asChild>
              <Button variant="outline" afterContent={<ArrowDownSLine />}>
                Export
              </Button>
            </MenuTrigger>
            <MenuContent className="min-w-48" align="end">
              <TimesheetExport
                gridRef={gridRef}
                currentStartDateSelected={currentStartDateSelected}
                currentEndDateSelected={currentEndDateSelected}
                isExportingRef={isExportingRef}
              />
            </MenuContent>
          </Menu>
          <Button beforeContent={<AddLine />} onClick={() => setShowTimesheetModal(true)}>
            Add Timesheet
          </Button>
        </div>
      );
    }

    if (currentTab === 'Time-Off') {
      return (
        <Button onClick={() => setShowTimeOffRequestModal(true)} beforeContent={<AddLine />}>
          Create Time Off Request
        </Button>
      );
    }

    if (currentTab === 'Benefits') {
      return <Button onClick={() => router.push('/benefits')}>Enroll Employee</Button>;
    }

    return (
      <div className="flex items-center gap-3">
        <Button
          color="neutral"
          variant="stroke"
          beforeContent={employee.data.hammrUser.isArchived ? <UploadCloud2Line /> : <ArchiveLine />}
          onClick={() => setShowArchiveModal(true)}
        >
          {employee.data.hammrUser.isArchived ? 'Unarchive' : 'Archive'}
        </Button>

        {company.isPayrollEnabled &&
          (employee.data.hammrUser.checkEmployeeId ? (
            <OnboardingLink checkId={employee.data.hammrUser.checkEmployeeId} userId={employee_id} />
          ) : (
            <Button variant="lighter" color="primary" onClick={() => setShowAddToPayrollModal(true)}>
              Add To Payroll
            </Button>
          ))}
      </div>
    );
  };

  return (
    <Layout>
      <ConfirmArchiveUserModal
        open={showArchiveModal}
        setOpen={setShowArchiveModal}
        isArchived={employee.data.hammrUser.isArchived}
        currentRowData={employee.data.hammrUser}
        callback={() => refreshEmployee()}
      />

      <CreateTimeOffRequestsModal
        open={showTimeOffRequestModal}
        setOpen={setShowTimeOffRequestModal}
        selectedEmployee={employee_id}
        afterSubmit={() => {
          queryClient.invalidateQueries({ queryKey: ['time-off-requests'] });
          queryClient.invalidateQueries({ queryKey: ['user', employee_id, 'time-off-policies'] });
        }}
      />

      <AddPayrollEmployeeModal
        open={showAddToPayrollModal}
        setOpen={(isOpen) => {
          setShowAddToPayrollModal(isOpen);
        }}
        checkCompanyId={user?.checkCompanyId}
        handleSuccess={() => {
          refreshEmployee();
          setShowAddToPayrollModal(false);
        }}
        showWashingtonFormStep={(data) => {
          setShowAddToPayrollModal(false);
          setShowWashingtonFieldsForm(data);
        }}
        existingEmployee={employee.data}
        isForAddingToPayroll={true}
      />

      <WashingtonFieldsModal
        open={!!showWashingtonFieldsForm}
        setOpen={() => setShowWashingtonFieldsForm(undefined)}
        userId={showWashingtonFieldsForm?.userId ?? 0}
        workersCompCodeId={showWashingtonFieldsForm?.workersCompCodeId}
        checkEmployeeId={showWashingtonFieldsForm?.checkEmployeeId ?? ''}
        onSuccess={() => {
          refreshEmployee();

          setShowWashingtonFieldsForm(undefined);
        }}
      />

      <PageHeader
        noPadding
        title={
          <div className="flex items-center gap-3">
            {employee.data.hammrUser.firstName} {employee.data.hammrUser.lastName}
            <Badge color={employee.data.hammrUser.isArchived ? 'gray' : 'green'} variant="lighter">
              {employee.data.hammrUser.isArchived ? 'Archived' : 'Active'}
            </Badge>
          </div>
        }
        subtitle={
          company.isPayrollEnabled && (
            <>
              Payroll Onboard Status: <OnboardStatus user={employee.data?.checkEmployee} />
            </>
          )
        }
        icon={<ProfileImage user={employee.data.hammrUser} onProfilePhotoChanged={() => refreshEmployee()} />}
        headerRight={<HeaderButtons />}
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="People" onClick={() => router.push('/people')} />
            <BreadcrumbItem text={`${employee.data.hammrUser.firstName} ${employee.data.hammrUser.lastName}`} active />
          </Breadcrumbs>
        }
      />

      <div className="relative flex flex-grow flex-col">
        <Tabs
          className="flex flex-grow flex-col"
          value={formatTabValue(currentTab)}
          onValueChange={(value) => handleTabChange(value as Tab)}
        >
          <TabsList>
            {(employee.data.hammrUser.checkEmployeeId ? ALL_TABS : NON_PAYROLL_TABS).map((tab) => (
              <TabsTrigger key={tab} value={formatTabValue(tab)}>
                {tab}
              </TabsTrigger>
            ))}
          </TabsList>
          <TabsContent value="employee-info">
            <EmployeeInfo
              employee={employee.data}
              isFetchingEmployee={employee.isFetching}
              refresh={() => refreshEmployee()}
              allowEdit={allowEdit}
            />
          </TabsContent>
          {employee.data?.checkEmployee ? (
            <TabsContent value="paystubs">
              <Paystubs
                checkEmployeeId={employee.data.checkEmployee.id}
                employeeFullName={`${employee.data.hammrUser.firstName} ${employee.data.hammrUser.lastName}`}
              />
            </TabsContent>
          ) : undefined}

          {employee.data?.checkEmployee ? (
            <TabsContent value="benefits">
              <Benefits
                hammrEmployeeId={employee.data.hammrUser.id}
                employeeFullName={`${employee.data.hammrUser.firstName} ${employee.data.hammrUser.lastName}`}
              />
            </TabsContent>
          ) : undefined}

          <TabsContent value="garnishments-&-deductions">
            <GarnishmentsDeductions employeeCheckId={employee.data.checkEmployee?.id} />
          </TabsContent>
          {employee.data.checkEmployee ? (
            <TabsContent value="tax-documents">
              <EmployeeTaxDocuments
                checkEmployeeId={employee.data.checkEmployee.id}
                employeeFullName={`${employee.data.hammrUser.firstName} ${employee.data.hammrUser.lastName}`}
              />
            </TabsContent>
          ) : undefined}
          <TabsContent value="employee-documents">
            <EmployeeDocumentsView
              openUploadModal={showEmployeeUploadModal}
              setOpenUploadModal={setShowEmployeeUploadModal}
            />
          </TabsContent>
          <TabsContent value="time-off">
            <EmployeeTimeOff userId={employee.data.hammrUser.id} />
          </TabsContent>
          <TabsContent value="certifications">
            <EmployeeCertificationsView
              openUploadModal={showEmployeeCertificationModal}
              setOpenUploadModal={setShowEmployeeCertificationModal}
            />
          </TabsContent>
          <TabsContent value="timesheets" className="flex flex-grow">
            <Timesheets
              isExportingRef={isExportingRef}
              showTimesheetModal={showTimesheetModal}
              setShowTimesheetModal={setShowTimesheetModal}
              gridRef={gridRef}
              selectedWorkerId={employee_id}
            />
          </TabsContent>
        </Tabs>
      </div>
      <AddPostTaxDeductionModal
        open={showPostTaxDeductionModal}
        setOpen={setShowPostTaxDeductionModal}
        employeeCheckId={employee.data.checkEmployee?.id}
      />
    </Layout>
  );
};

export default EmployeeView;
