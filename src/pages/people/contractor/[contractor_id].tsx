import Layout from 'components/dashboard/Layout';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { enrichContractor, getContractor } from 'services/contractor';
import ContractorInfo from 'components/people/contractor-view/ContractorInfo';
import { useCompany } from 'hooks/useCompany';
import ContractorDocuments from 'components/people/contractor-view/ContractorDocuments';
import Payments from 'components/people/contractor-view/Payments';
import { useAuth } from 'hooks/useAuth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import Button from '@hammr-ui/components/button';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import ArchiveLine from '@hammr-icons/ArchiveLine';
import UploadCloud2Line from '@hammr-icons/UploadCloud2Line';
import ConfirmArchiveUserModal from '@/components/people/nonpayroll-view/ConfirmArchiveUserModal';
import { Badge } from '@/hammr-ui/components/badge';
import { ProfileImage } from '@/components/people/sections/ProfileImage';
import { userService } from '@/services/user';
import OnboardingLink from '@/components/people/OnboardingLink';
import { showErrorToast } from '@/utils/errorHandling';
import { INVALID_WORKER_CLASSIFICATION } from '@/utils/constants';
import OnboardStatus from '@/components/people/OnboardStatus';

const TABS = ['Contractor Info', 'Payments', 'Tax Documents'] as const;
type Tab = (typeof TABS)[number];

const formatTabValue = (tab: string) => tab.replaceAll(' ', '-').toLowerCase();

export default function ContractorView() {
  const { user } = useAuth();
  const router = useRouter();
  const { contractor_id } = router.query;
  const { company } = useCompany();
  const [showConfirmDeleteModal, setShowConfirmDeleteModal] = useState(false);
  const queryClient = useQueryClient();

  const contractor = useQuery({
    queryKey: ['contractor', contractor_id],
    queryFn: async () => {
      const hammrUser = await userService.get(Number(contractor_id));

      if (hammrUser.workerClassification !== 'CONTRACTOR') {
        throw new Error(INVALID_WORKER_CLASSIFICATION);
      }

      const checkContractor = await getContractor(hammrUser.checkContractorId);
      return await enrichContractor({ contractor: checkContractor, hammrUser });
    },
    enabled: !!contractor_id && user?.isCompanyAdmin,
    retry: 1,
  });

  const refreshContractor = () => {
    queryClient.invalidateQueries({ queryKey: ['contractor', contractor_id] });
  };

  if (contractor.error) {
    if (contractor.error.message === INVALID_WORKER_CLASSIFICATION) {
      router.push(`/people/employee/${contractor_id}`);
    } else {
      showErrorToast(contractor.error);
      router.push('/people');
    }
    return;
  }

  const allowEdit = contractor.data?.hammrUser?.isArchived === false;

  const currentTab = TABS.find((tab) => formatTabValue(tab) === router.query.tab) || 'Contractor Info';

  const handleTabChange = (newTab: Tab) => {
    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: formatTabValue(newTab) },
      },
      undefined,
      { shallow: true }
    );
  };

  if (!user || !user?.isCompanyAdmin) return null;

  if (!(contractor_id && contractor.data && company)) {
    return (
      <Layout>
        <div className="mt-1/5 flex flex-col items-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  return (
    <Layout noPadding>
      <ConfirmArchiveUserModal
        open={showConfirmDeleteModal}
        setOpen={setShowConfirmDeleteModal}
        isArchived={contractor.data.hammrUser.isArchived}
        currentRowData={contractor.data.hammrUser}
        callback={() => refreshContractor()}
      />

      <PageHeader
        title={
          <div className="flex items-center gap-3">
            {contractor.data.checkContractor.first_name} {contractor.data.checkContractor.last_name}
            <Badge color={contractor.data.hammrUser.isArchived ? 'gray' : 'green'} variant="lighter">
              {contractor.data.hammrUser.isArchived ? 'Archived' : 'Active'}
            </Badge>
          </div>
        }
        subtitle={
          company.checkCompanyId && (
            <>
              Payroll Onboard Status: <OnboardStatus user={contractor.data.checkContractor} />
            </>
          )
        }
        icon={<ProfileImage user={contractor.data.hammrUser} onProfilePhotoChanged={() => refreshContractor()} />}
        headerRight={
          <div className="flex items-center gap-3">
            <Button
              color="neutral"
              variant="stroke"
              beforeContent={contractor.data.hammrUser.isArchived ? <UploadCloud2Line /> : <ArchiveLine />}
              onClick={() => setShowConfirmDeleteModal(true)}
            >
              {contractor.data.hammrUser.isArchived ? 'Unarchive' : 'Archive'}
            </Button>
            {contractor.data.checkContractor.id && (
              <OnboardingLink checkId={contractor.data.checkContractor.id} userId={Number(contractor_id)} />
            )}
          </div>
        }
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="People" onClick={() => router.push('/people')} />
            <BreadcrumbItem
              text={`${contractor.data.checkContractor.first_name} ${contractor.data.checkContractor.last_name}`}
              active
            />
          </Breadcrumbs>
        }
      />

      <div className="relative flex flex-1 flex-col px-8">
        <Tabs value={formatTabValue(currentTab)} onValueChange={(value) => handleTabChange(value as Tab)}>
          <TabsList>
            {TABS.map((tab) => (
              <TabsTrigger key={tab} value={formatTabValue(tab)}>
                {tab}
              </TabsTrigger>
            ))}
          </TabsList>
          <TabsContent value="contractor-info">
            <ContractorInfo contractor={contractor.data} refresh={() => refreshContractor()} allowEdit={allowEdit} />
          </TabsContent>
          <TabsContent value="payments">
            <Payments checkContractorId={contractor.data?.checkContractor?.id} />
          </TabsContent>
          <TabsContent value="documents">
            <ContractorDocuments contractorId={contractor.data?.checkContractor?.id} />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}
