import { useState } from 'react';
import PayrollUsers from 'components/people/PayrollUsers';
import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';
import { userService } from 'services/user';
import { sortListAlphabetically } from 'utils/collectionHelpers';
import Layout from '@/components/dashboard/Layout';
import { UsersTab } from '@/components/people/nonpayroll-view/UsersView';
import { useQuery } from '@tanstack/react-query';
import NonPayrollUsers from '@/components/people/NonPayroll';

export const usePeopleManagement = () => {
  const { user } = useAuth();
  const [selectedTab, setSelectedTab] = useState<UsersTab>('Active');

  const usersQuery = useQuery({
    queryKey: ['users', user?.companyId, selectedTab],
    queryFn: async () => {
      if (!user?.companyId) return [];

      const active = selectedTab === 'Active';
      const users = await userService.list({
        organizationId: user.companyId,
        includeIsArchived: active ? 'false' : 'true',
        simple: true,
      });

      if (active === false) {
        const archivedUsers = users.filter((user) => user.isArchived === true);
        return sortListAlphabetically(archivedUsers, 'firstName');
      }
      return sortListAlphabetically(users, 'firstName');
    },
    enabled: !!user?.companyId,
  });

  return {
    usersQuery,
    selectedTab,
    setSelectedTab,
    tabs: ['Active', 'Archived'] as UsersTab[],
  };
};

export default function People() {
  const { user } = useAuth();
  const { company } = useCompany();

  if (!user || !user?.isCompanyAdmin) return null;

  return <Layout>{company ? company.isPayrollEnabled ? <PayrollUsers /> : <NonPayrollUsers /> : undefined}</Layout>;
}
