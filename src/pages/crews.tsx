import { useState, useEffect, useCallback } from 'react';

import { useAuth } from 'hooks/useAuth';

import Layout from 'components/dashboard/Layout';
import CrewsView from 'components/crews/CrewsView';

import { getCrews } from 'services/crew';
import CreateCrewModal from 'components/crews/CreateCrewModal';
import ConfirmDeleteCrewModal from 'components/crews/ConfirmDeleteCrewModal';

import { Crew } from 'interfaces/crew';
import EditCrewModal from 'components/crews/EditCrewModal';
import { logError, showErrorToast } from 'utils/errorHandling';
import TeamLine from '@/hammr-icons/TeamLine';
import Button from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import MembersListModal from '@/components/crews/MembersListModal';
import EmptyStateTrainingAnalyis from '@/hammr-icons/EmptyStateTrainingAnalyis';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

const Crews: React.FC = () => {
  const { user } = useAuth();

  const [crews, setCrews] = useState(null);
  const [isFetching, setIsFetching] = useState(false);
  const [showCreateCrewModal, setShowCreateCrewModal] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [showEditCrewModal, setShowEditCrewModal] = useState(false);
  const [showConfirmDeleteCrewModal, setShowConfirmDeleteCrewModal] = useState(false);

  const [selectedCrew, setSelectedCrew] = useState<Crew>(null);

  function handleCheckMembers(index: number) {
    setSelectedCrew(crews[index]);
    setShowMembersModal(true);
  }

  const handleEditCrewClick = (index: number) => {
    setSelectedCrew(crews[index]);
    setShowEditCrewModal(true);
  };

  const handleDeleteCrewClick = (index: number) => {
    setSelectedCrew(crews[index]);
    setShowConfirmDeleteCrewModal(true);
  };

  const refresh = useCallback(() => {
    setIsFetching(true);
    getCrews({
      organizationId: user?.companyId,
    })
      .then((res) => {
        setCrews(res.crews);
        setIsFetching(false);
      })
      .catch((err) => {
        logError(err);
        showErrorToast(err);
      });
  }, [user?.companyId]);

  useEffect(() => {
    if (user?.companyId) {
      refresh();
    }
  }, [refresh, user?.companyId]);

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout noPadding>
      <PageHeader
        title="Crews"
        icon={<TeamLine />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateCrewModal(true)}>
            Create Crew
          </Button>
        }
      />
      {isFetching && (
        <div className="flex h-full items-center justify-center">
          <LoadingIndicator />
        </div>
      )}
      {!isFetching && user?.isCompanyAdmin ? (
        <article className="px-8 py-6">
          {crews && crews.length ? (
            <CrewsView
              crews={crews}
              checkMembersCallback={handleCheckMembers}
              rowActionCallback={handleEditCrewClick}
              deleteActionCallback={handleDeleteCrewClick}
            />
          ) : (
            <div className="flex flex-col items-center justify-center py-28">
              <EmptyStateTrainingAnalyis />
              <p className="mt-5 text-center text-sm text-soft-400">
                There is no crew yet.
                <br />
                Click the button below to add one.
              </p>
              <Button className="mt-5" beforeContent={<AddLine />} onClick={() => setShowCreateCrewModal(true)}>
                Create Crew
              </Button>
            </div>
          )}
        </article>
      ) : null}
      <CreateCrewModal
        open={showCreateCrewModal}
        setOpen={setShowCreateCrewModal}
        orgId={user?.companyId ? Number(user.companyId) : undefined}
        callback={refresh}
      />
      <MembersListModal open={showMembersModal} setOpen={setShowMembersModal} currentCrewData={selectedCrew} />
      <EditCrewModal
        open={showEditCrewModal}
        setOpen={setShowEditCrewModal}
        orgId={user?.companyId ? Number(user.companyId) : undefined}
        currentCrewData={selectedCrew}
        callback={refresh}
      />
      <ConfirmDeleteCrewModal
        open={showConfirmDeleteCrewModal}
        setOpen={setShowConfirmDeleteCrewModal}
        currentCrewData={selectedCrew}
        callback={refresh}
      />
    </Layout>
  );
};

export default Crews;
