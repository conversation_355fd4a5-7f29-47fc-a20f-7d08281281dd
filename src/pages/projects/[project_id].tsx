import { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Project } from 'interfaces/project';
import { getOneProject } from 'services/projects';
import ProjectDetails from 'components/projects/ProjectDetails';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import FolderOpenLine from '@/hammr-icons/FolderOpenLine';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import Button from '@hammr-ui/components/button';
import EditProjectModal from 'components/projects/EditProjectModal';
import ConfirmArchiveActionModal from 'components/projects/ConfirmArchiveActionModal';
import { <PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@/hammr-ui/components/FlatTabs';
import { ProjectTimesheetsTable } from '@/components/projects/ProjectTimesheetsTable';
import AddLine from '@/hammr-icons/AddLine';
import { useDateSelection } from '@/hooks/useDateSelection';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import UploadCloud2Line from '@/hammr-icons/UploadCloud2Line';
import { cn } from '@/utils/cn';
import { Menu, MenuContent, MenuTrigger } from '@/hammr-ui/components/Menu';
import TimesheetExport from '@/components/timesheets/TimesheetExport';
import { AgGridReact } from '@ag-grid-community/react';
import ProjectDocumentsTable from '@/components/projects/ProjectDocumentsTable';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import ProjectPhotos from '@/components/project-photos/ProjectPhotos';
import ProjectActuals from '@/components/projects/ProjectActuals';
import { setTabQuery } from '@/utils/utils';
import DailyReports from '@/components/daily-reports/DailyReports';

type ProjectTabKey = 'general' | 'timesheets' | 'documents' | 'photos' | 'daily-reports' | 'project-actuals';

const tabs: { key: ProjectTabKey; name: string }[] = [
  { key: 'general', name: 'General' },
  { key: 'project-actuals', name: 'Project Actuals' },
  { key: 'timesheets', name: 'Timesheets' },
  { key: 'documents', name: 'Documents' },
  { key: 'photos', name: 'Photos' },
  { key: 'daily-reports', name: 'Daily Reports' },
];

export function ProjectDetailsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { project_id } = router.query;
  const [selectedTab, setSelectedTab] = useState<ProjectTabKey>('general');

  const [showEditProjectModal, setShowEditProjectModal] = useState(false);
  const [showConfirmArchiveActionModal, setShowConfirmArchiveActionModal] = useState(false);
  const [showCreateReportModal, setShowCreateReportModal] = useState(false);
  const { currentStartDateSelected, currentEndDateSelected } = useDateSelection();

  const [showUploadModal, setShowUploadModal] = useState(false);
  const gridRef = useRef<AgGridReact>(null);
  const isExportingRef = useRef(false);
  const [showTimesheetModal, setShowTimesheetModal] = useState(false);

  const { data, isLoading, refetch } = useQuery<{ project: Project; totalHours: number; totalCost: number }>({
    queryKey: ['project', project_id],
    queryFn: async () => {
      const projectsData = await getOneProject(parseInt(project_id as string), {
        organizationId: user?.companyId,
        includeIsArchived: 'true',
      });
      return projectsData;
    },
    enabled: !!user?.companyId && !!project_id,
  });

  useEffect(() => {
    if (!showTimesheetModal && project_id) {
      refetch();
    }
  }, [selectedTab]);

  useEffect(() => {
    if (router.query.tab) {
      const tabToSelect = tabs.find((tab) => router.query.tab === tab.key);
      if (tabToSelect) {
        setSelectedTab(tabToSelect.key as ProjectTabKey);
      }
    }
  }, [router.query]);

  if (!user || !user?.isCompanyAdmin) return null;

  const { project, totalCost, totalHours } = data ?? {};

  if (isLoading || !project) {
    return (
      <Layout>
        <div className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  return (
    <Layout noPadding>
      <PageHeader
        title={project.name}
        icon={<FolderOpenLine />}
        headerRight={
          <div className="flex gap-3">
            {selectedTab === 'general' ? (
              <>
                <Button
                  variant="outline"
                  color="neutral"
                  className="min-w-20"
                  onClick={() => setShowConfirmArchiveActionModal(true)}
                  beforeContent={<ArchiveLine />}
                >
                  {project.isArchived ? 'Unarchive' : 'Archive'}
                </Button>
              </>
            ) : selectedTab === 'timesheets' ? (
              <div className="flex items-center gap-3">
                <Menu>
                  <MenuTrigger asChild>
                    <Button variant="outline" afterContent={<ArrowDownSLine />}>
                      Export
                    </Button>
                  </MenuTrigger>
                  <MenuContent className={cn('min-w-48')} align="end">
                    <TimesheetExport
                      gridRef={gridRef}
                      currentStartDateSelected={currentStartDateSelected}
                      currentEndDateSelected={currentEndDateSelected}
                      isExportingRef={isExportingRef}
                    />
                  </MenuContent>
                </Menu>
                <Button beforeContent={<AddLine />} onClick={() => setShowTimesheetModal(true)}>
                  Add Timesheet
                </Button>
              </div>
            ) : selectedTab === 'documents' ? (
              <Button beforeContent={<UploadCloud2Line />} onClick={() => setShowUploadModal(true)}>
                Upload Document
              </Button>
            ) : selectedTab === 'daily-reports' ? (
              <Button beforeContent={<AddLine />} onClick={() => setShowCreateReportModal(true)}>
                Create Report
              </Button>
            ) : null}
          </div>
        }
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Project List" onClick={() => router.push('/projects')} />
            <BreadcrumbItem text={project.name} active />
          </Breadcrumbs>
        }
      />
      <div className="relative flex flex-grow flex-col px-8">
        <Tabs
          className="flex flex-grow flex-col"
          value={selectedTab}
          onValueChange={(value) => setSelectedTab(value as ProjectTabKey)}
        >
          <TabsList>
            {tabs.map((tab) => (
              <TabsTrigger onClick={() => setTabQuery(router, tab.key)} key={tab.key} value={tab.key}>
                {tab.name}
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="general">
            <ProjectDetails
              {...project}
              totalHours={totalHours}
              totalCost={totalCost}
              setShowEditProjectModal={setShowEditProjectModal}
              setSelectedTab={setSelectedTab}
            />
          </TabsContent>
          <TabsContent value="project-actuals">
            <ProjectActuals {...project} totalHours={totalHours} totalCost={totalCost} />
          </TabsContent>
          <TabsContent className="data-[state=active]:flex data-[state=active]:grow" value="timesheets">
            <ProjectTimesheetsTable
              projectId={parseInt(project_id as string)}
              isExportingRef={isExportingRef}
              showTimesheetModal={showTimesheetModal}
              setShowTimesheetModal={setShowTimesheetModal}
              gridRef={gridRef}
            />
          </TabsContent>

          <TabsContent className="data-[state=active]:flex data-[state=active]:grow" value="documents">
            <ProjectDocumentsTable
              projectId={project.id}
              showUploadModal={showUploadModal}
              setShowUploadModal={setShowUploadModal}
            />
          </TabsContent>
          <TabsContent value="photos">
            <ProjectPhotos projectId={project.id} />
          </TabsContent>
          <TabsContent className="data-[state=active]:flex data-[state=active]:grow" value="daily-reports">
            <DailyReports
              projectId={project.id}
              showCreateReportModal={showCreateReportModal}
              setShowCreateReportModal={setShowCreateReportModal}
            />
          </TabsContent>
        </Tabs>
      </div>
      <EditProjectModal
        open={showEditProjectModal}
        setOpen={setShowEditProjectModal}
        currentProject={project}
        callback={() => refetch()}
      />
      <ConfirmArchiveActionModal
        open={showConfirmArchiveActionModal}
        setOpen={setShowConfirmArchiveActionModal}
        currentRowData={project}
        callback={() => refetch()}
      />
    </Layout>
  );
}

export default ProjectDetailsPage;
