import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import Button from '@hammr-ui/components/button';
import CreateProjectModal from 'components/projects/CreateProjectModal';
import EditProjectModal from 'components/projects/EditProjectModal';
import ConfirmArchiveActionModal from 'components/projects/ConfirmArchiveActionModal';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Project } from 'interfaces/project';
import { getProjects } from 'services/projects';
import { useRouter } from 'next/router';
import AddLine from '@/hammr-icons/AddLine';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import FolderOpenLine from '@/hammr-icons/FolderOpenLine';
import { ColDef, IRowNode, ValueFormatterParams } from '@ag-grid-community/core';
import { Badge } from '@hammr-ui/components/badge';
import { AgGridReact } from '@ag-grid-community/react';
import { Tooltip } from '@hammr-ui/components/tooltip';
import CompactButton from '@hammr-ui/components/CompactButton';
import PencilLine from '@hammr-icons/PencilLine';
import ArchiveLine from '@hammr-icons/ArchiveLine';
import ArrowGoBackLine from '@hammr-icons/ArrowGoBackLine';
import { MultiSelect } from '@hammr-ui/components/multi-select';
import { ScrollArea, ScrollBar } from '@hammr-ui/components/scroll-area';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { TabItem, TabList, Tabs } from '@hammr-ui/components/tabs';

export type ProjectTab = 'Active' | 'Archived';

export default function ProjectsPage() {
  const { user } = useAuth();
  const router = useRouter();

  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);
  const [showEditProjectModal, setShowEditProjectModal] = useState(false);
  const [showConfirmArchiveActionModal, setShowConfirmArchiveActionModal] = useState(false);
  const [selectedTab, setSelectedTab] = useState<ProjectTab>('Active');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);

  const projectIdsString = router.query.projects ? (router.query.projects as string) : '';
  const tabs: ProjectTab[] = ['Active', 'Archived'];

  const projects = useQuery<Project[]>({
    queryKey: ['projects', user?.companyId, selectedTab],
    queryFn: async () => {
      const isActive = selectedTab === 'Active';
      const projectsData = await getProjects({
        organizationId: user?.companyId,
        includeIsArchived: isActive ? 'false' : 'true',
      });

      if (!isActive) {
        return projectsData.projects.filter((proj) => proj.isArchived === true);
      }
      return projectsData.projects;
    },
    enabled: !!user?.companyId && !!selectedTab,
  });

  useEffect(() => {
    if (router.query.modal === 'add') {
      setShowCreateProjectModal(true);
    }
  }, [router.query.modal]);

  const handleCloseModal = () => {
    setShowCreateProjectModal(false);
    router.replace('/projects', undefined, { shallow: true });
  };

  const handleEditProjectClick = (id: number) => {
    const selectedProject = projects.data?.find((project) => project.id === id);
    setSelectedProject(selectedProject);
    setShowEditProjectModal(true);
  };

  const handleProjectClick = (id: number) => {
    router.push(`/projects/${id}`);
  };

  const handleArchiveActionProjectClick = (id: number) => {
    const selectedProject = projects.data?.find((project) => project.id === id);
    setSelectedProject(selectedProject);
    setShowConfirmArchiveActionModal(true);
  };

  const colDefs: ColDef[] = [
    {
      headerName: 'Name',
      field: 'name',
      initialSort: 'asc',
      minWidth: 210,
    },
    {
      headerName: 'Customer name',
      field: 'customerName',
      minWidth: 160,
    },
    {
      headerName: 'Project Number',
      field: 'projectNumber',
      cellRenderer: (params: ValueFormatterParams) => {
        return <span className="text-sub-600">{params.value}</span>;
      },
    },
    {
      headerName: 'Geofence',
      field: 'isGeofencedFormatted',
      minWidth: 120,
      maxWidth: 130,
      cellRenderer: (params: ValueFormatterParams) => {
        const component =
          params.value === 'ON' ? (
            <Badge variant="lighter" color="green" size="medium">
              ON
            </Badge>
          ) : (
            <Badge variant="lighter" color="red" size="medium">
              OFF
            </Badge>
          );

        return <div className="flex h-full items-center">{component}</div>;
      },
    },
    {
      headerName: 'Prevailing wage',
      minWidth: 160,
      maxWidth: 170,
      field: 'isPrevailingWage',
      cellRenderer: (params: ValueFormatterParams) => {
        const component =
          params.value === 'ON' ? (
            <Badge variant="lighter" color="green" size="medium">
              ON
            </Badge>
          ) : (
            <Badge variant="lighter" color="red" size="medium">
              OFF
            </Badge>
          );

        return <div className="flex h-full items-center">{component}</div>;
      },
    },
    {
      headerName: 'Actions',
      minWidth: 140,
      maxWidth: 150,
      field: 'actions',
      sortable: false,
      cellStyle: { overflow: 'visible' },
      cellRenderer: (params: ValueFormatterParams) => {
        return projectActions(params.data, params.node);
      },
    },
  ];

  const defaultColDef = {
    menuTabs: [],
  };

  const gridRef = useRef<AgGridReact>(null);

  const active = selectedTab === 'Active';

  const projectActions = (project, node?) => {
    return (
      <div key={`project-${project.id}`} className="flex h-full justify-start space-x-2 lg:space-x-4">
        <a
          className="flex cursor-pointer items-center justify-center"
          onClick={() => {
            handleEditProjectClick(project.id);
          }}
        >
          <Tooltip content="Edit">
            <CompactButton size="large" variant="ghost">
              <PencilLine />
            </CompactButton>
          </Tooltip>
        </a>

        <a
          className="flex cursor-pointer items-center justify-center"
          onClick={() => {
            // show confirm archive/unarchive modal
            // this is used for both archive and unarchive
            handleArchiveActionProjectClick(project.id);
          }}
        >
          <Tooltip content={active ? 'Archive' : 'Unarchive'}>
            <CompactButton size="large" variant="ghost">
              {active ? <ArchiveLine /> : <ArrowGoBackLine />}
            </CompactButton>
          </Tooltip>
        </a>
      </div>
    );
  };

  const mapProjectsForTable = (projects: Project[]) => {
    if (!projects) return [];

    return projects.map((project: Project) => {
      return {
        ...project,
        id: project.id,
        name: project.name,
        customerName: project.customerName,
        isGeofencedFormatted: project.isGeofenced ? 'ON' : 'OFF',
        isPrevailingWage: project.isPrevailingWage ? 'ON' : 'OFF',
      };
    });
  };

  const mappedRowData = useMemo(() => {
    if (!projects.data) return [];
    let mappedProjects = projects.data;

    const projectIds = projectIdsString
      .split(',')
      .filter((v) => typeof parseInt(v) === 'number' && !isNaN(parseInt(v)))
      .map(Number);

    if (projectIds.length) {
      mappedProjects = projects.data.filter((project) => projectIds.includes(project.id));
    }
    return mapProjectsForTable(mappedProjects);
  }, [projects, projectIdsString]);

  useEffect(() => {
    gridRef.current?.api?.setFilterModel(null);
  }, [active]);

  const emptyRowsText = `No ${active ? 'active' : 'archived'} projects.`;

  function getFilterOptions<T extends ReturnType<typeof mapProjectsForTable>[number]>(
    projects: T[],
    field: keyof T,
    selectedValues: string[]
  ) {
    return projects
      .map((row) => {
        const label =
          ((row[field] as string) ?? '') +
          (field === 'name' && row['projectNumber']?.trim() ? ` (${row['projectNumber']})` : '');
        return {
          label,
          value: row.id,
          isSelected: selectedValues.includes(label),
        };
      })
      .sort((a, b) => (a.label as string).localeCompare(b.label as string))
      .filter((item, index, self) => self.findIndex((t) => t.label === item.label) === index);
  }

  const [selectedProjectsFilters, setSelectedProjectsFilters] = useState<string[]>([]);
  const [selectedCustomerNamesFilters, setSelectedCustomerNamesFilters] = useState([]);
  const [selectedGeofenceFilters, setSelectedGeofenceFilters] = useState([]);
  const [selectedPrevailingWageFilters, setSelectedPrevailingWageFilters] = useState([]);

  const projectNameFilters = getFilterOptions(mappedRowData, 'name', selectedProjectsFilters);
  const customerNameFilters = getFilterOptions(mappedRowData, 'customerName', selectedCustomerNamesFilters);
  const geofenceFilters = getFilterOptions(mappedRowData, 'isGeofencedFormatted', selectedGeofenceFilters);
  const prevailingWageFilters = getFilterOptions(mappedRowData, 'isPrevailingWage', selectedPrevailingWageFilters);

  const isExternalFilterPresent = useCallback(() => true, []);

  const doesExternalFilterPass = useCallback(
    (node: IRowNode<ReturnType<typeof mapProjectsForTable>[number]>) => {
      const { data } = node;
      if (!data) return false;

      const label = data.name + (data.projectNumber?.trim() ? ` (${data.projectNumber})` : '');

      return (
        (selectedProjectsFilters.length === 0 ? true : selectedProjectsFilters.includes(label)) &&
        (selectedCustomerNamesFilters.length === 0 ? true : selectedCustomerNamesFilters.includes(data.customerName)) &&
        (selectedGeofenceFilters.length === 0 ? true : selectedGeofenceFilters.includes(data.isGeofencedFormatted)) &&
        (selectedPrevailingWageFilters.length === 0
          ? true
          : selectedPrevailingWageFilters.includes(data.isPrevailingWage))
      );
    },
    [selectedCustomerNamesFilters, selectedGeofenceFilters, selectedPrevailingWageFilters, selectedProjectsFilters]
  );

  if (!user || !user?.isCompanyAdmin) return null;

  if (projects.isLoading || !projects.data) {
    return (
      <Layout>
        <div className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  return (
    <Layout noPadding>
      <PageHeader
        title="Projects"
        icon={<FolderOpenLine />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateProjectModal(true)}>
            Create Project
          </Button>
        }
      />
      <div className="relative mt-6 flex flex-1 flex-col px-8">
        <div className={'relative flex flex-col gap-4 xl:flex-row'}>
          <Tabs value={selectedTab ?? ''} onValueChange={(tab) => setSelectedTab(tab as typeof selectedTab)}>
            <TabList>
              {tabs.map((tab) => (
                <TabItem key={tab} value={tab}>
                  {tab}
                </TabItem>
              ))}
            </TabList>
          </Tabs>

          <ScrollArea className="flex-none">
            <div className="flex gap-3">
              <MultiSelect
                buttonProps={{ className: 'w-[12.5rem]' }}
                popoverProps={{ align: 'start', className: 'w-auto' }}
                label="All Projects"
                items={projectNameFilters}
                onChange={(newItems) => {
                  setSelectedProjectsFilters(newItems.filter((item) => item.isSelected).map((item) => item.label));
                  gridRef.current.api.onFilterChanged();
                }}
              />
              <MultiSelect
                buttonProps={{ className: 'w-[12.5rem]' }}
                label="All Customers"
                items={customerNameFilters}
                onChange={(newItems) => {
                  setSelectedCustomerNamesFilters(newItems.filter((item) => item.isSelected).map((item) => item.label));
                  gridRef.current.api.onFilterChanged();
                }}
              />
              <MultiSelect
                buttonProps={{ className: 'w-36' }}
                popoverProps={{ align: 'end' }}
                label="Geofence"
                searchable={false}
                items={geofenceFilters}
                onChange={(newItems) => {
                  setSelectedGeofenceFilters(newItems.filter((item) => item.isSelected).map((item) => item.label));
                  gridRef.current.api.onFilterChanged();
                }}
              />
              <MultiSelect
                buttonProps={{ className: 'w-40' }}
                popoverProps={{ align: 'end' }}
                label="Prevailing Wage"
                items={prevailingWageFilters}
                onChange={(newItems) => {
                  setSelectedPrevailingWageFilters(
                    newItems.filter((item) => item.isSelected).map((item) => item.label)
                  );
                  gridRef.current.api.onFilterChanged();
                }}
              />
              {selectedProjectsFilters.length ||
              selectedCustomerNamesFilters.length ||
              selectedGeofenceFilters.length ||
              selectedPrevailingWageFilters.length ? (
                <Button
                  size="small"
                  variant="outline"
                  color="neutral"
                  onClick={() => {
                    setSelectedProjectsFilters([]);
                    setSelectedCustomerNamesFilters([]);
                    setSelectedGeofenceFilters([]);
                    setSelectedPrevailingWageFilters([]);
                    gridRef.current.api.onFilterChanged();
                  }}
                >
                  Clear All
                </Button>
              ) : undefined}
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
        <div className="hide-filter-indicator mt-4 flex flex-grow pb-10">
          <UpdatedTable
            colDefs={colDefs}
            rowData={mappedRowData}
            onRowClicked={(event) => handleProjectClick(event.data.id)}
            hideSidebar={true}
            defaultColDef={defaultColDef}
            parentRef={gridRef}
            emptyRowsText={emptyRowsText}
            tableProps={{
              isExternalFilterPresent,
              doesExternalFilterPass,
            }}
          />
        </div>
      </div>
      <CreateProjectModal open={showCreateProjectModal} setOpen={handleCloseModal} />
      {selectedProject && (
        <EditProjectModal
          open={showEditProjectModal}
          setOpen={setShowEditProjectModal}
          currentProject={selectedProject}
          callback={() => projects.refetch()}
          key={selectedProject.id}
        />
      )}
      <ConfirmArchiveActionModal
        open={showConfirmArchiveActionModal}
        setOpen={setShowConfirmArchiveActionModal}
        currentRowData={selectedProject}
        callback={() => projects.refetch()}
      />
    </Layout>
  );
}
