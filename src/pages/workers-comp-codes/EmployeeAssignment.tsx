import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Select, SelectItem } from '@/hammr-ui/components/select';
import { Input } from '@/hammr-ui/components/input';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { showErrorToast } from 'utils/errorHandling';
import { useCompany } from '@/hooks/useCompany';
import Search2Line from '@hammr-icons/Search2Line';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { HammrUser } from '@/interfaces/user';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/user';
import { AgGridReact } from '@ag-grid-community/react';
import Button from '@hammr-ui/components/button';
import AddLine from '@hammr-icons/AddLine';
import Link from 'next/link';
import EmptyStateHRNotes from '@hammr-icons/EmptyStateHRNotes';
import { Company } from '@/interfaces/company';
import EmptyWorkersCompCodes from '@/pages/workers-comp-codes/EmptyWorkersCompCodes';
import WorkersCompCodeModal from '@/pages/workers-comp-codes/WorkersCompCodeModal';
import { ModalV2 } from '@/components/elements/ModalV2';
import { useForm } from 'react-hook-form';
import MoneyDollarBoxLine from '@hammr-icons/MoneyDollarBoxLine';
import ControlledSelect from '@/components/elements/form/ControlledSelect';
import { FormV2 } from '@/components/elements/Form';
import { Tooltip } from '@hammr-ui/components/tooltip';
import InfoCustomFill from '@hammr-icons/InfoCustomFill';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { beautifyWashingtonStateErrorsFromCheck } from '@/components/people/sections/WashingtonStateEmployment';

const SelectionUI = ({
  gridRef,
  selectOptions,
  isGridReady,
}: {
  gridRef: React.RefObject<AgGridReact>;
  selectOptions: { label: string; value: number }[];
  isGridReady: boolean;
}) => {
  const [showBulkAssignModal, setShowBulkAssignModal] = useState(false);
  const [hasSelectedRows, setHasSelectedRows] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<{ workersCompCode: number }>({
    defaultValues: {
      workersCompCode: undefined,
    },
  });

  useEffect(() => {
    if (!isGridReady) return;

    const api = gridRef.current?.api;
    if (!api) return;

    const checkSelection = () => {
      const selectedCount = api.getSelectedRows().length;
      setHasSelectedRows(selectedCount > 0);
    };

    api.addEventListener('selectionChanged', checkSelection);
    checkSelection();

    return () => {
      api.removeEventListener('selectionChanged', checkSelection);
    };
  }, [gridRef, isGridReady]);

  const getSelectedEmployees = () => {
    return gridRef.current?.api?.getSelectedRows() || [];
  };

  const bulkAssignMutation = useMutation({
    mutationFn: (params: { userId: number; workersCompCodeId: number | null }[]) =>
      workersCompCodesService.assignWorkers(params).catch(beautifyWashingtonStateErrorsFromCheck),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      setShowBulkAssignModal(false);
      gridRef.current?.api?.deselectAll();
      form.reset();
    },
    onError: (error) => {
      showErrorToast(error, 'Failed to bulk assign workers comp code');
    },
  });

  const handleBulkAssign = (data: { workersCompCode: number }) => {
    const selectedEmployees = getSelectedEmployees();
    const assignments = selectedEmployees.map((employee) => ({
      userId: employee.id,
      workersCompCodeId: data.workersCompCode,
    }));

    bulkAssignMutation.mutate(assignments);
  };

  return (
    <>
      {hasSelectedRows && <Button onClick={() => setShowBulkAssignModal(true)}>Bulk Assign</Button>}

      <ModalV2
        open={showBulkAssignModal}
        setOpen={setShowBulkAssignModal}
        title="Assign Workers Comp"
        icon={<MoneyDollarBoxLine />}
      >
        <FormV2
          onSubmit={form.handleSubmit(handleBulkAssign)}
          isLoading={bulkAssignMutation.isPending}
          submitText="Assign"
          onCancel={() => {
            setShowBulkAssignModal(false);
            form.reset();
          }}
        >
          <div className="mb-1.5 text-sm font-medium text-sub-600">
            Employees Selected ({getSelectedEmployees().length})
          </div>
          <div className="mb-5 text-sm">
            {getSelectedEmployees().map((emp) => (
              <div key={emp.id} className="text-strong-950">
                {emp.firstName + ' ' + emp.lastName}
              </div>
            ))}
          </div>
          <ControlledSelect name="workersCompCode" label="Workers Comp" control={form.control} className="w-full">
            {selectOptions.map((item) => (
              <SelectItem key={item.value} value={item.value.toString()}>
                {item.label}
              </SelectItem>
            ))}
          </ControlledSelect>
        </FormV2>
      </ModalV2>
    </>
  );
};

export default function EmployeeAssignment() {
  const { company } = useCompany();
  const [searchTerm, setSearchTerm] = useState('');
  const queryClient = useQueryClient();
  const gridRef = useRef<AgGridReact>(null);
  const [showModal, setShowModal] = useState(false);
  const [isGridReady, setIsGridReady] = useState(false);

  const workersCompCodes = useQuery({
    queryKey: ['workersCompCodes'],
    queryFn: () => workersCompCodesService.get(),
  });

  const employees = useQuery({
    queryFn: () =>
      userService.list({
        organizationId: (company as Company).id,
        simple: true,
        workerClassification: 'EMPLOYEE',
      }),
    queryKey: ['employees'],
    enabled: Boolean(company),
  });

  const assignWorkersMutation = useMutation({
    mutationFn: (params: { userId: number; workersCompCodeId: number | null }[]) => {
      return workersCompCodesService
        .assignWorkers(Array.isArray(params) ? params : [params])
        .catch(beautifyWashingtonStateErrorsFromCheck);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
    },
    onError: (error) => {
      showErrorToast(error, 'Failed to assign workers comp code');
    },
  });

  const filteredEmployees = useMemo(() => {
    if (!employees.data) return [];
    return employees.data.filter((employee) =>
      (employee.firstName + employee.lastName).toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [employees, searchTerm]);

  const mappedEmployees = useMemo(() => {
    return filteredEmployees.map((employee) => ({
      ...employee,
      employeeLastName: employee.lastName,
      employeeFirstName: employee.firstName,
    }));
  }, [filteredEmployees]);

  const handleWorkersCompCodeChange = (userId: number, workersCompCodeId: number | null) => {
    assignWorkersMutation.mutate([{ userId, workersCompCodeId }]);
  };

  const selectOptions = useMemo(() => {
    return (
      workersCompCodes.data?.map((code) => ({
        label: `${code.code} • ${code.name}`,
        value: code.id,
      })) ?? []
    );
  }, [workersCompCodes]);

  const colDefs: ColDef<HammrUser & { employeeLastName?: string; employeeFirstName?: string }>[] = [
    {
      headerName: '',
      headerCheckboxSelection: true,
      checkboxSelection: true,
      maxWidth: 50,
      pinned: 'left',
    },
    {
      headerName: 'Employee',
      field: 'employeeLastName',
      cellRenderer: (
        params: ValueFormatterParams<HammrUser & { employeeLastName?: string; employeeFirstName?: string }>
      ) => {
        return `${params.data?.employeeFirstName} ${params.data?.employeeLastName}`;
      },
      flex: 3,
    },
    {
      headerName: 'Workers Comp',
      headerComponent: () => (
        <div className="flex items-center gap-2 text-sub-600">
          Workers Comp
          <Tooltip
            content="Code Assignment Priority"
            description="The system assigns a workers comp code to a timesheet based on its cost code, if available. If no cost code is found, it will default to the workers comp code from the employee's profile."
          >
            <span>
              <InfoCustomFill className="h-4 w-4" />
            </span>
          </Tooltip>
        </div>
      ),
      field: 'workersCompCode',
      flex: 4,
      cellRenderer: (params: ValueFormatterParams<HammrUser>) => (
        <div>
          {assignWorkersMutation.isPending && assignWorkersMutation.variables[0].userId === params.data?.id ? (
            <div className="absolute left-0 top-0 z-10 flex h-full w-full bg-white-0/60">
              <div className="m-auto">
                <LoadingIndicator showText={false} />
              </div>
            </div>
          ) : undefined}
          <Select
            value={params.data?.workersCompCode?.id.toString()}
            onChange={(value) => handleWorkersCompCodeChange(params.data?.id as number, value as number)}
            className="w-full"
          >
            {workersCompCodes.data?.map((item) => (
              <SelectItem key={item.id} value={item.id.toString()}>
                {item.code} • {item.name}
              </SelectItem>
            ))}
          </Select>
        </div>
      ),
    },
  ];

  if (workersCompCodes.isFetched && !workersCompCodes.data?.length) {
    return (
      <>
        <EmptyWorkersCompCodes onButtonClick={() => setShowModal(true)} />
        <WorkersCompCodeModal open={showModal} setOpen={setShowModal} />
      </>
    );
  }

  if (employees.isFetched && !employees.data?.length) {
    return (
      <>
        {
          <div className="mt-32 flex flex-col items-center justify-center pb-8">
            <EmptyStateHRNotes />
            <div className="mt-5 text-center text-sm">
              <div className="text-soft-400">There is no employee yet.</div>
              <div className="text-soft-400">Click the button below to add one.</div>
            </div>
            <Link href="/people">
              <Button beforeContent={<AddLine />} className="mt-5">
                Add Employee
              </Button>
            </Link>
          </div>
        }
      </>
    );
  }

  return (
    <>
      <div className="max-w-[540px] p-4">
        <div className="mb-4 flex items-center gap-2">
          <Input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            beforeContent={<Search2Line />}
            boxSize="medium"
            className="flex-1"
          />
          <SelectionUI gridRef={gridRef} selectOptions={selectOptions} isGridReady={isGridReady} />
        </div>

        {workersCompCodes.isLoading || employees.isLoading ? (
          <div className="mt-8 flex justify-center">
            <LoadingIndicator />
          </div>
        ) : (
          <UpdatedTable<HammrUser>
            colDefs={colDefs}
            rowData={mappedEmployees}
            parentRef={gridRef}
            defaultColDef={{
              sortable: true,
              resizable: true,
            }}
            gridOptions={{
              rowSelection: 'multiple',
              onGridReady: () => {
                setIsGridReady(true);
              },
            }}
          />
        )}
      </div>
    </>
  );
}
