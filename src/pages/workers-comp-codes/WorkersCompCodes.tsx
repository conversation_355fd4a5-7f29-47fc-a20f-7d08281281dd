import React, { useRef, useState } from 'react';
import { <PERSON>b<PERSON><PERSON>, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import CompactButton from '@/hammr-ui/components/CompactButton';
import PencilLine from '@/hammr-icons/PencilLine';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import InfoCustomFill from '@/hammr-icons/InfoCustomFill';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { showErrorToast } from 'utils/errorHandling';
import WorkersCompCodeModal from '@/pages/workers-comp-codes/WorkersCompCodeModal';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { KeyIcon } from '@hammr-ui/components/KeyIcon';
import ArrowGoBackLine from '@/hammr-icons/ArrowGoBackLine';
import { Item, MultiSelect } from '@/hammr-ui/components/multi-select';
import { Badge } from '@/hammr-ui/components/badge';
import { useCompany } from '@/hooks/useCompany';
import { CostCode } from '@/interfaces/cost-code';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { getCostCodes } from '@/services/cost-codes';
import { Company } from '@/interfaces/company';
import { AgGridReact } from '@ag-grid-community/react';
import EmptyWorkersCompCodes from '@/pages/workers-comp-codes/EmptyWorkersCompCodes';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

function CostCodeCell({
  workersCompCode,
  assignedCostCodes,
  costCodes,
  onUpdate,
}: {
  workersCompCode: WorkersCompCode;
  costCodes: CostCode[];
  assignedCostCodes: number[];
  onUpdate: (selectedIds: number[]) => Promise<void>;
}) {
  const [selectedIds, setSelectedIds] = useState(workersCompCode.costCodes.map((item) => item.id));

  const itemsWithSelection: Item[] = costCodes
    .filter((costCode) => !assignedCostCodes.includes(costCode.id) || selectedIds.includes(costCode.id))
    .map((item) => ({
      labelWhenSelected: (
        <Badge key={item.id} variant="lighter" color="gray" shape="round">
          {item.name}
        </Badge>
      ),
      value: item.id,
      label: item.name,
      isSelected: selectedIds?.includes(item.id),
    }));

  return (
    <MultiSelect
      label="Select an option"
      buttonProps={{ className: 'border-none p-2  [&>svg]:hidden' }}
      items={itemsWithSelection}
      popoverProps={{
        onPointerDownOutside: async () => {
          await onUpdate(selectedIds);
        },
      }}
      onChange={async (items) => {
        const selectedIds = items.filter((item) => item.isSelected).map((item) => item.value as number);
        setSelectedIds(selectedIds);
      }}
    />
  );
}

export default function WorkersCompCodes() {
  const { toast } = useToast();
  const [selectedCode, setSelectedCode] = useState<WorkersCompCode>();
  const [showModal, setShowModal] = useState(false);
  const gridRef = useRef<AgGridReact<WorkersCompCode>>(null);
  const { company } = useCompany();
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [codeToArchive, setCodeToArchive] = useState<WorkersCompCode>();
  const [activeTab, setActiveTab] = useState<'active' | 'archived'>('active');
  const queryClient = useQueryClient();

  const showArchived = activeTab === 'archived';

  const workersCompCodes = useQuery({
    queryKey: ['workersCompCodes', activeTab],
    queryFn: () =>
      workersCompCodesService
        .get(showArchived)
        .then((items) => items.filter((item) => item.isArchived === showArchived)),
  });

  const costCodes = useQuery({
    queryKey: ['costCodes', company?.id],
    queryFn: () => getCostCodes({ organizationId: (company as Company).id }),
    enabled: !!company,
  });

  const archiveMutation = useMutation({
    mutationFn: (code: WorkersCompCode) =>
      workersCompCodesService.update(code.id, {
        code: code.code,
        name: code.name,
        isArchived: activeTab === 'active',
      }),
    onSuccess: (_, code) => {
      queryClient.invalidateQueries({ queryKey: ['workersCompCodes'] });
      toast({
        title: activeTab === 'active' ? 'Archived code' : 'Unarchived code',
        description: `Successfully ${activeTab === 'active' ? 'archived' : 'unarchived'} ${code.name} ・ ${code.code}`,
        toastVariant: 'success',
      });
    },
    onError: (error) => {
      showErrorToast(error, `Failed to ${activeTab === 'active' ? 'archive' : 'unarchive'} code`);
    },
  });

  const assignCostCodesMutation = useMutation({
    mutationFn: ({ codeId, costCodeIds }: { codeId: number; costCodeIds: number[] }) =>
      workersCompCodesService.assignCostCodes(codeId, { costCodeIds }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['workersCompCodes'] });
    },
  });

  const handleArchive = (code: WorkersCompCode) => {
    setCodeToArchive(code);
    setShowArchiveDialog(true);
  };

  const handleUnarchive = (code: WorkersCompCode) => {
    setCodeToArchive(code);
    setShowArchiveDialog(true);
  };

  const confirmArchive = async (code: WorkersCompCode) => {
    archiveMutation.mutate(code);
  };

  const colDefs: ColDef<WorkersCompCode & { actions?: '' }>[] = [
    {
      headerName: 'Name',
      field: 'name',
      flex: 2,
    },
    {
      headerName: 'Code',
      field: 'code',
      flex: 1,
    },
    {
      headerName: 'Cost Code Assigned',
      hide: showArchived,
      headerComponent: () => (
        <div className="flex items-center gap-2 text-sub-600">
          Cost Code Assigned
          <Tooltip content="Timesheets using these cost codes will automatically apply the assigned workers comp code.">
            <span>
              <InfoCustomFill className="h-4 w-4" />
            </span>
          </Tooltip>
        </div>
      ),
      field: 'costCodes',
      flex: 2,
      cellRenderer: (params: ValueFormatterParams<WorkersCompCode>) => {
        return (
          <CostCodeCell
            workersCompCode={params.data as WorkersCompCode}
            assignedCostCodes={
              workersCompCodes.data?.flatMap((workersCompCode) =>
                workersCompCode.costCodes.map((costCode) => costCode.id)
              ) ?? []
            }
            costCodes={costCodes.data ?? []}
            onUpdate={async (selectedIds) => {
              assignCostCodesMutation.mutate({
                codeId: (params.data as WorkersCompCode).id,
                costCodeIds: selectedIds,
              });
            }}
          />
        );
      },
    },
    {
      headerName: '',
      field: 'actions',
      sortable: false,
      width: 120,
      cellStyle: { overflow: 'visible' },
      cellRenderer: (params: ValueFormatterParams<WorkersCompCode>) => {
        return (
          <div className="flex justify-end gap-2">
            <CompactButton
              onClick={() => {
                setSelectedCode(params.data as WorkersCompCode);
                setShowModal(true);
              }}
            >
              <PencilLine className="h-5 w-5 text-soft-400" />
            </CompactButton>
            {activeTab === 'active' ? (
              <CompactButton onClick={() => handleArchive(params.data as WorkersCompCode)}>
                <ArchiveLine className="h-5 w-5 text-soft-400" />
              </CompactButton>
            ) : (
              <CompactButton onClick={() => handleUnarchive(params.data as WorkersCompCode)}>
                <ArrowGoBackLine className="h-5 w-5 text-soft-400" />
              </CompactButton>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'active' | 'archived')} className="mb-6">
        <TabList>
          <TabItem value="active">Active</TabItem>
          <TabItem value="archived">Archived</TabItem>
        </TabList>
      </Tabs>

      {workersCompCodes.isLoading ? (
        <div className="mt-8 flex justify-center">
          <LoadingIndicator />
        </div>
      ) : workersCompCodes.isFetched && !workersCompCodes.data?.length ? (
        <EmptyWorkersCompCodes onButtonClick={() => setShowModal(true)} activeTab={activeTab} />
      ) : (
        <div className="max-w-[728px]">
          <UpdatedTable<WorkersCompCode>
            colDefs={colDefs}
            rowData={workersCompCodes.data}
            parentRef={gridRef}
            defaultColDef={{
              sortable: true,
              resizable: true,
            }}
          />
        </div>
      )}

      <WorkersCompCodeModal open={showModal} setOpen={setShowModal} currentCode={selectedCode} />
      <ConfirmDialog
        open={showArchiveDialog}
        setOpen={setShowArchiveDialog}
        data={codeToArchive}
        onConfirm={confirmArchive}
        title={`${activeTab === 'active' ? 'Archive' : 'Unarchive'} Workers Comp Code`}
        subtitle={`You're about to ${activeTab === 'active' ? 'archive' : 'unarchive'} the workers comp code ${codeToArchive?.code} • ${codeToArchive?.name}. Do you want to proceed?`}
        icon={<KeyIcon icon={activeTab === 'active' ? <ArchiveLine /> : <ArrowGoBackLine />} />}
        confirmButtonText={activeTab === 'active' ? 'Archive' : 'Unarchive'}
      />
    </div>
  );
}
