import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useToast } from '@/hammr-ui/hooks/use-toast';
import { WorkersCompCode } from '@/interfaces/WorkersCompCode';
import { workersCompCodesService } from '@/services/workers-comp-codes';
import { logError, showErrorToast } from 'utils/errorHandling';
import MoneyDollarBoxLine from '@hammr-icons/MoneyDollarBoxLine';
import { ModalV2 } from '@/components/elements/ModalV2';
import { FormV2 } from '@/components/elements/Form';
import { useQueryClient } from '@tanstack/react-query';
import { TextField } from '@/components/elements/form/TextField';

interface WorkersCompCodeModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  callback?: () => void;
  currentCode?: WorkersCompCode;
}

export default function WorkersCompCodeModal({ open, setOpen, callback, currentCode }: WorkersCompCodeModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<{ name: string; code: string }>({
    defaultValues: {
      name: '',
      code: '',
    },
  });
  const { handleSubmit, register, reset } = form;

  useEffect(() => {
    if (currentCode) {
      reset({
        name: currentCode.name,
        code: currentCode.code,
      });
    }
  }, [currentCode, reset]);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  const onSubmit = async (data: { name: string; code: string }) => {
    try {
      setIsLoading(true);
      if (currentCode) {
        await workersCompCodesService.update(currentCode.id, data);
      } else {
        await workersCompCodesService.create(data);
      }

      queryClient.invalidateQueries({
        queryKey: ['workersCompCodes'],
      });

      toast({
        title: currentCode ? 'Edited Workers Comp Code' : 'Created Workers Comp Code',
        // TODO use formatDescription created by Subdho
        description: `Successfully ${
          currentCode ? 'edited' : 'created'
        } the workers comp code ${data.code} ・ ${data.name}`,
        toastVariant: 'success',
      });

      callback?.();
      setOpen(false);
    } catch (err) {
      logError(err);
      showErrorToast(err, 'Unable to save workers comp code');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModalV2
      icon={<MoneyDollarBoxLine />}
      open={open}
      setOpen={setOpen}
      title={currentCode ? 'Edit Workers Comp Code' : 'Create Workers Comp Code'}
    >
      <FormV2
        onCancel={() => setOpen(false)}
        onSubmit={handleSubmit(onSubmit)}
        submitText={currentCode ? 'Update' : 'Create'}
        isLoading={isLoading}
      >
        <div className="space-y-4">
          <TextField
            control={form.control}
            name="name"
            error={form.formState.errors.name?.message}
            label="Workers Comp Name"
            placeholder="Enter workers comp name"
            rules={{ required: 'Please enter a Workers Comp Name' }}
            required
          />
          <TextField
            control={form.control}
            name="code"
            error={form.formState.errors.code?.message}
            label="Workers Comp Code"
            placeholder="Enter workers comp code"
            rules={{ required: 'Please enter a Workers Comp Code' }}
            required
          />
        </div>
      </FormV2>
    </ModalV2>
  );
}
