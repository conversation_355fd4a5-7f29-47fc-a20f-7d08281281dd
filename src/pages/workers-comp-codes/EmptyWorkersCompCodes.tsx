import EmptyStateFinanceBanking from '@hammr-icons/EmptyStateFinanceBanking';
import Button from '@hammr-ui/components/button';
import AddLine from '@hammr-icons/AddLine';
import React from 'react';

export default function EmptyWorkersCompCodes({
  onButtonClick,
  activeTab,
}: {
  activeTab?: 'archived' | 'active';
  onButtonClick: () => void;
}) {
  return (
    <div className="mt-32 flex flex-1 flex-col items-center justify-center pb-8">
      <EmptyStateFinanceBanking />
      <div className="mt-5 text-center text-sm">
        <div className="text-soft-400">
          There is no <span className="font-semibold">{activeTab === 'archived' ? 'archived ' : ''}</span>
          workers comp code yet.
        </div>
        <div className="text-soft-400">Click the button below to add one.</div>
      </div>
      <Button beforeContent={<AddLine />} onClick={onButtonClick} className="mt-5">
        Create Workers Comp Code
      </Button>
    </div>
  );
}
