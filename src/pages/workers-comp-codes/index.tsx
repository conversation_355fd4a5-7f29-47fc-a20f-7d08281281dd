import React, { Suspense, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import MoneyDollarBoxLine from '@hammr-icons/MoneyDollarBoxLine';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import Button from '@/hammr-ui/components/button';
import AddLine from '@hammr-icons/AddLine';
import WorkersCompCodes from './WorkersCompCodes';
import WorkersCompCodeModal from '@/pages/workers-comp-codes/WorkersCompCodeModal';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';

const EmployeeAssignment = React.lazy(() => import('./EmployeeAssignment'));

const WorkersCompPage: React.FC = () => {
  const { user } = useAuth();
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Only allow company admins to access this page
  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout noPadding>
      <PageHeader
        title="Workers Comp"
        icon={<MoneyDollarBoxLine className="text-strong-950" />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateModal(true)}>
            Create Workers Comp Code
          </Button>
        }
      />
      <div className="relative flex flex-1 flex-col px-8">
        <Tabs defaultValue="codes" className="w-full">
          <TabsList>
            <TabsTrigger value="codes">Workers Comp Codes</TabsTrigger>
            <TabsTrigger value="assignment">Employee Assignment</TabsTrigger>
          </TabsList>

          <TabsContent value="codes">
            <WorkersCompCodes />
          </TabsContent>

          <TabsContent value="assignment">
            <Suspense
              fallback={
                <div className="mt-8 flex justify-center">
                  <LoadingIndicator />
                </div>
              }
            >
              <EmployeeAssignment />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
      <WorkersCompCodeModal open={showCreateModal} setOpen={setShowCreateModal} />
    </Layout>
  );
};

export default WorkersCompPage;
