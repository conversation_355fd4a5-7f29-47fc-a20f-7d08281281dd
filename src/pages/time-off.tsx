import TimeOffPolicies from '@/components/time-off-policies/time-off-policies-page';
import TimeOffRequests from '@/components/time-off-requests/time-off-requests-page';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export default function TimeOff() {
  const [timeOffTab, setTimeOffTab] = useState<'requests' | 'policies'>('requests');

  const router = useRouter();

  useEffect(() => {
    if (router.query.tab === 'policies') {
      setTimeOffTab('policies');
    } else {
      setTimeOffTab('requests');
    }
  }, [router.query]);

  function handleTabChange(tab: 'requests' | 'policies') {
    setTimeOffTab(tab);
    router.push(`/time-off?tab=${tab}`, undefined, { shallow: true });
  }

  if (timeOffTab === 'requests') {
    return <TimeOffRequests timeOffTab={timeOffTab} setTimeOffTab={handleTabChange} />;
  } else {
    return <TimeOffPolicies timeOffTab={timeOffTab} setTimeOffTab={handleTabChange} />;
  }
}
