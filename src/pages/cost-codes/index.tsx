import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import CreateCostCodeModal from 'components/cost-codes/CreateCostCodeModal';
import EditCostCodeModal from 'components/cost-codes/EditCostCodeModal';
import { CostCode } from 'interfaces/cost-code';
import { getCostCodes } from 'services/cost-codes';
import { sortListAlphabetically } from 'utils/collectionHelpers';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import AddLine from '@/hammr-icons/AddLine';
import Button from '@/hammr-ui/components/button';
import WalletLine from '@/hammr-icons/WalletLine';
import CostCodes from '@/components/cost-codes/CostCodes';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

export default function CostCodesPage() {
  const { user } = useAuth();
  const [showCreateCostCodeModal, setShowCreateCostCodeModal] = useState(false);
  const [showEditCostCodeModal, setShowEditCostCodeModal] = useState(false);
  const [selectedCostCode, setSelectedCostCode] = useState<CostCode | null>(null);

  const costCodes = useQuery<CostCode[]>({
    queryKey: ['costCodes', user?.companyId],
    queryFn: () => getCostCodes({ organizationId: user?.companyId }),
    select: (data) => sortListAlphabetically(data, 'name'),
    enabled: Boolean(user?.companyId),
  });

  const handleEditCostCodeClick = (id: number) => {
    const costCode = costCodes.data?.find((cc: CostCode) => cc.id === id);
    setSelectedCostCode(costCode || null);
    setShowEditCostCodeModal(true);
  };

  if (!user || !user?.isCompanyAdmin) return null;

  if (costCodes.isLoading) {
    return (
      <Layout>
        <div className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  return (
    <Layout noPadding>
      <PageHeader
        title="Cost Codes"
        icon={<WalletLine />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateCostCodeModal(true)}>
            Create Cost Code
          </Button>
        }
      />
      <div className="flex h-full flex-col pb-12 md:px-6">
        <div className="mt-6 flex-grow px-2">
          <CostCodes
            rowActionCallback={handleEditCostCodeClick}
            onUpdate={() => costCodes.refetch()}
            costCodes={costCodes.data || []}
            handleCreateClick={() => setShowCreateCostCodeModal(true)}
          />
        </div>
      </div>
      <CreateCostCodeModal
        open={showCreateCostCodeModal}
        setOpen={setShowCreateCostCodeModal}
        callback={() => costCodes.refetch()}
      />
      <EditCostCodeModal
        key={selectedCostCode?.name}
        open={showEditCostCodeModal}
        setOpen={setShowEditCostCodeModal}
        currentCostCode={selectedCostCode}
        callback={() => costCodes.refetch()}
      />
    </Layout>
  );
}
