import Layout from '@/components/dashboard/Layout';
import Payments from '@/components/people/contractor-view/Payments';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { useAuth } from '@/hooks/useAuth';
import { RiMoneyDollarBoxLine } from '@remixicon/react';

export default function PaymentsPage() {
  const { user } = useAuth();

  if (!user) return null;

  return (
    <Layout noPadding>
      <PageHeader title="Payments" icon={<RiMoneyDollarBoxLine />} />

      <section className="px-8 py-3">
        <Payments checkContractorId={user.checkContractorId} />
      </section>
    </Layout>
  );
}
