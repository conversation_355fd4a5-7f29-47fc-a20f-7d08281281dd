import React, { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';

import { useAuth } from 'hooks/useAuth';

import CreateWageTableModal from 'components/wage-tables/CreateWageTableModal';
import Layout from 'components/dashboard/Layout';
import Table2Line from '@/hammr-icons/Table2Line';
import AddLine from '@/hammr-icons/AddLine';
import Button from '@/hammr-ui/components/button';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { LinkButton } from '@/hammr-ui/components/LinkButton';
import EmptyStateStockMarketTracker from '@/hammr-icons/EmptyStateStockMarketTracker';
import { formatLocaleUsa } from '@/utils/dateHelper';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { Tab<PERSON>ontent, TabItem, TabList, Tabs } from '@/hammr-ui/components/tabs';
import { WageTable } from '@/interfaces/wage-table';
import { apiRequest } from '@/utils/requestHelpers';

const WageTables: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();

  const [showCreateWageTableModal, setShowCreateWageTableModal] = useState(false);

  useEffect(() => {
    if (router.query.create === 'true') {
      setShowCreateWageTableModal(true);
    }
  }, [router.query.create]);

  const wageTablesQuery = useQuery({
    queryKey: ['wage-tables', true],
    queryFn: () =>
      apiRequest<{
        wageTables: WageTable[];
      }>('wage-tables', { urlParams: { includeArchived: true } }).then((response) => response.wageTables),
    enabled: !!user?.companyId,
  });

  const { activeWageTables, archivedWageTables } = useMemo(
    () => ({
      activeWageTables: wageTablesQuery.data?.filter((table) => !table.isArchived) ?? [],
      archivedWageTables: wageTablesQuery.data?.filter((table) => table.isArchived) ?? [],
    }),
    [wageTablesQuery.data]
  );

  if (!user || !user?.isCompanyAdmin) return null;

  const renderWageTableCard = (wageTable: WageTable) => (
    <article
      key={wageTable.id}
      className="flex cursor-pointer flex-col items-start justify-between rounded-16 border border-soft-200 bg-white-0 p-5 shadow-xs hover:bg-weak-50"
      onClick={() => {
        router.push(`/wage-tables/${wageTable.id}`);
      }}
    >
      <hgroup className="flex w-full items-center justify-between">
        <h2 className="text-sm font-medium text-strong-950">{wageTable.name}</h2>
        <p className="h-fit text-xs text-sub-600">{formatLocaleUsa(wageTable.createdAt)}</p>
      </hgroup>
      <p className="mt-1.5 h-fit w-full text-sub-600">
        <span className="overflow-hidden text-ellipsis whitespace-nowrap">{wageTable.description}</span>
      </p>
      <LinkButton
        size="medium"
        style="primary"
        className="mt-5"
        onClick={(e) => {
          e.stopPropagation();
          if (wageTable.projects.length === 0) return;
          const projectIds = wageTable.projects.map((project) => project.id).join(',');
          router.push(`/projects?projects=${projectIds}`);
        }}
      >
        {wageTable.projects.length} Projects
      </LinkButton>
    </article>
  );

  return (
    <Layout noPadding>
      <PageHeader
        title="Wage Tables"
        icon={<Table2Line />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateWageTableModal(true)}>
            Create Wage Table
          </Button>
        }
      />

      {wageTablesQuery.isPending && (
        <section className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </section>
      )}

      {wageTablesQuery.isError && (
        <section className="flex size-full items-center justify-center">
          <p className="text-center text-sm text-soft-400">Error loading wage tables. Please try again.</p>
        </section>
      )}

      {wageTablesQuery.isSuccess && (
        <div className="mx-8">
          <Tabs
            defaultValue="active"
            value={(router.query.tab as string) ?? 'active'}
            onValueChange={(tab) =>
              router.push({ pathname: router.pathname, query: { tab } }, undefined, { shallow: true })
            }
          >
            <TabList>
              <TabItem value="active">Active</TabItem>
              <TabItem value="archived">Archived</TabItem>
            </TabList>

            <TabContent value="active">
              {activeWageTables.length === 0 ? (
                <section className="flex size-full flex-col items-center py-32">
                  <EmptyStateStockMarketTracker />
                  <p className="mt-5 text-center text-sm text-soft-400">
                    There is no active wage table yet. <br /> Click the button below to add one.
                  </p>
                  <Button
                    className="mt-5"
                    beforeContent={<AddLine />}
                    onClick={() => setShowCreateWageTableModal(true)}
                  >
                    Create Wage Table
                  </Button>
                </section>
              ) : (
                <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6 py-6">
                  {activeWageTables.map(renderWageTableCard)}
                </section>
              )}
            </TabContent>

            <TabContent value="archived">
              {archivedWageTables.length === 0 ? (
                <section className="flex size-full flex-col items-center py-32">
                  <EmptyStateStockMarketTracker />
                  <p className="mt-5 text-center text-sm text-soft-400">There is no archived wage table yet.</p>
                </section>
              ) : (
                <section className="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-6 py-6">
                  {archivedWageTables.map(renderWageTableCard)}
                </section>
              )}
            </TabContent>
          </Tabs>
        </div>
      )}

      <CreateWageTableModal
        open={showCreateWageTableModal}
        setOpen={setShowCreateWageTableModal}
        callback={() => wageTablesQuery.refetch()}
      />
    </Layout>
  );
};

export default WageTables;
