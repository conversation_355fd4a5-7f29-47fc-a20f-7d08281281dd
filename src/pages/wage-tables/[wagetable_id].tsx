import { useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import Layout from 'components/dashboard/Layout';
import WageTableDetails from 'components/wage-tables/WageTableDetails';
import { getWageTable } from 'services/wage-tables';
import { WageTable } from 'interfaces/wage-table';
import EditWageTableModal from 'components/wage-tables/EditWageTableModal';
import ArchiveWageTableModal from 'components/wage-tables/ArchiveWageTableModal';
import ClassificationsTable from 'components/classifications/ClassificationsTable';
import CreateClassificationModal from 'components/classifications/CreateClassificationModal';
import EmployeesTable from 'components/employees/EmployeesTable';
import AssignEmployeeModal from 'components/employees/AssignEmployeeModal';
import { getClassifications } from 'services/classifications';
import { EmployeeClassification, Classification } from 'interfaces/classifications';
import { getActiveClassification } from 'utils/temporalUtils';
import CreateFringeModal from 'components/fringes/CreateFringeModal';
import FringesTable from 'components/fringes/FringesTable';
import { getFringeClassifications } from 'services/fringes';
import { FringeBenefitClassificationObject } from 'interfaces/fringes';
import FringeBenefitsStatementTable from 'components/fringe-benefits/FringeBenefitsStatementTable';
import { AgGridReact } from '@ag-grid-community/react';
import FringeBenefitExportModal from 'components/fringes/FringeBenefitExportModal';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import Table2Line from '@/hammr-icons/Table2Line';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import Button from '@/hammr-ui/components/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import AddLine from '@/hammr-icons/AddLine';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { useCompany } from '@/hooks/useCompany';

const tabs = [
  {
    name: 'General',
    key: 'general',
  },
  {
    name: 'Classifications',
    key: 'classifications',
  },
  {
    name: 'Employees',
    key: 'employees',
  },
  {
    name: 'Project Fringes',
    key: 'project-fringes',
  },
  {
    name: 'Fringe Benefits Statement',
    key: 'fringe-benefits-statement',
  },
] as const;

type WageTabsKey = (typeof tabs)[number]['key'];

export default function WageTableComponent() {
  const router = useRouter();

  const [classifications, setClassifications] = useState<EmployeeClassification[]>([]);
  const [wageTableObj, setWageTableObj] = useState({} as WageTable);
  const [selectedTab, setSelectedTab] = useState<WageTabsKey>(tabs[0]?.key);
  const [isOpenWageTableEdit, setIsOpenWageTableEdit] = useState(false);
  const [isOpenWageTableArchive, setIsOpenWageTableArchive] = useState(false);
  const [isOpenClassificationCreateModal, setIsOpenClassificationCreateModal] = useState(false);
  const [isOpenAssignEmployeesModal, setIsOpenAssignEmployeesModal] = useState(false);
  const [isOpenCreateProjectFringeModal, setIsOpenCreateProjectFringeModal] = useState(false);
  const [isOpenFringeStatementExportModal, setIsOpenFringeStatementExportModal] = useState(false);
  const [fringes, setFringes] = useState<FringeBenefitClassificationObject[]>([]);
  const fringesTableRef = useRef<AgGridReact>();
  const employeesTableRef = useRef<AgGridReact>();
  const { company } = useCompany();

  const employeeClassificationsQuery = useQuery({
    queryKey: ['employee-classification', wageTableObj.id],
    queryFn() {
      return apiRequest<{ userClassifications: EmployeeClassification[] }>('user-classifications', {
        urlParams: { wageTableId: wageTableObj.id },
      });
    },
    enabled: !!wageTableObj.id,
  });

  useEffect(() => {
    if (router.query.tab) {
      const tabToSelect = tabs.find((tab) => router.query.tab === tab.key);
      if (tabToSelect) {
        setSelectedTab(tabToSelect.key);
      }
    }
  }, [router.query]);

  const fetchFringes = async () => {
    if (!wageTableObj.id) return;
    const data = await getFringeClassifications({
      wageTableId: wageTableObj.id,
    });
    setFringes(data.fringeBenefitClassifications);
  };

  const assignableClassifications = useMemo(() => {
    const uniqueClassificationNames = new Set<string>();

    // first, get all unique classification names
    classifications.forEach((classification) => {
      uniqueClassificationNames.add(classification.name);
    });

    // for each unique name, find the active classification
    return Array.from(uniqueClassificationNames)
      .map((classificationName) => {
        const classificationsWithSameName = classifications.filter(
          (classification) => classification.name === classificationName
        );

        // get the active classification for this name
        const activeClassification = getActiveClassification(classificationsWithSameName, company.timezone);

        return activeClassification;
      })
      .filter(Boolean); // remove null values
  }, [classifications, company?.timezone]);

  const fetchWageTable = async (wageTableId) => {
    const { wageTable } = await getWageTable(wageTableId);
    setWageTableObj(wageTable);
  };

  useEffect(() => {
    // this is to avoid fetching classifications on the server load where query params are not available
    if (!wageTableObj.id) return;

    fetchClassifications();
    fetchFringes();
  }, [wageTableObj.id]);

  const fetchClassifications = async () => {
    const classificationData = await getClassifications({
      wageTableId: wageTableObj.id,
    });

    setClassifications(classificationData.classifications);
  };

  useEffect(() => {
    if (!router.query.wagetable_id) return;
    // wage table
    fetchWageTable(router.query.wagetable_id);
  }, [router.query]);

  return (
    <Layout noPadding>
      <PageHeader
        title={wageTableObj?.name}
        icon={<Table2Line />}
        headerRight={
          <HeaderButton
            selectedTabKey={selectedTab}
            {...{
              setIsOpenAssignEmployeesModal,
              setIsOpenClassificationCreateModal,
              setIsOpenWageTableEdit,
              setIsOpenWageTableArchive,
              setIsOpenCreateProjectFringeModal,
              setIsOpenFringeStatementExportModal,
              employeesTableRef,
              wageTable: wageTableObj,
            }}
          />
        }
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Wage Tables" onClick={() => router.push('/wage-tables')} />
            <BreadcrumbItem text={wageTableObj?.name} active />
          </Breadcrumbs>
        }
      />

      <Tabs
        className="mx-8 flex grow flex-col"
        value={selectedTab}
        onValueChange={(value: WageTabsKey) => setSelectedTab(value)}
      >
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.key}
              value={tab.key}
              onClick={() => {
                const newQuery = { ...router.query, tab: tab.key };
                router.replace(
                  {
                    pathname: router.pathname,
                    query: newQuery as Record<any, any>,
                  },
                  '',
                  { shallow: false }
                );
              }}
            >
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="general">
          <WageTableDetails {...wageTableObj} />
        </TabsContent>
        <TabsContent value="classifications">
          <ClassificationsTable
            classifications={classifications}
            callback={fetchClassifications}
            wageTableId={wageTableObj.id}
          />
        </TabsContent>
        <TabsContent value="employees">
          <EmployeesTable
            classifications={classifications}
            wageTableId={wageTableObj.id}
            employeesTableRef={employeesTableRef}
            employeeClassifications={employeeClassificationsQuery.data?.userClassifications ?? []}
            callback={employeeClassificationsQuery.refetch}
          />
        </TabsContent>
        <TabsContent value="project-fringes">
          <FringesTable
            wageTableId={wageTableObj.id}
            classifications={classifications}
            fetchFringes={fetchFringes}
            fringes={fringes}
          />
        </TabsContent>
        <TabsContent value="fringe-benefits-statement" className="grow">
          <FringeBenefitsStatementTable tableRef={fringesTableRef} wageTableId={wageTableObj.id} />
        </TabsContent>
      </Tabs>

      <EditWageTableModal
        open={isOpenWageTableEdit}
        setOpen={setIsOpenWageTableEdit}
        wageTable={wageTableObj}
        callback={() => fetchWageTable(router.query.wagetable_id)}
      />
      <CreateClassificationModal
        isOpen={isOpenClassificationCreateModal}
        setIsOpen={setIsOpenClassificationCreateModal}
        callback={() => fetchClassifications()}
        wageTableId={wageTableObj.id}
      />
      <AssignEmployeeModal
        isOpen={isOpenAssignEmployeesModal}
        setIsOpen={setIsOpenAssignEmployeesModal}
        callback={employeeClassificationsQuery.refetch}
        classifications={assignableClassifications}
        employeeClassifications={employeeClassificationsQuery.data?.userClassifications ?? []}
      />
      <CreateFringeModal
        isOpen={isOpenCreateProjectFringeModal}
        setIsOpen={setIsOpenCreateProjectFringeModal}
        callback={fetchFringes}
        classifications={assignableClassifications}
        wageTableId={wageTableObj.id}
      />

      <FringeBenefitExportModal
        isOpen={isOpenFringeStatementExportModal}
        setIsOpen={setIsOpenFringeStatementExportModal}
        wageTableId={wageTableObj.id}
      />

      <ArchiveWageTableModal
        open={isOpenWageTableArchive}
        setOpen={setIsOpenWageTableArchive}
        wageTable={wageTableObj}
        callback={() => fetchWageTable(router.query.wagetable_id)}
      />
    </Layout>
  );
}

interface HeaderButtonProps {
  selectedTabKey: WageTabsKey;
  setIsOpenWageTableEdit: (value: boolean) => void;
  setIsOpenWageTableArchive: (value: boolean) => void;
  setIsOpenClassificationCreateModal: (value: boolean) => void;
  setIsOpenAssignEmployeesModal: (value: boolean) => void;
  setIsOpenCreateProjectFringeModal: (value: boolean) => void;
  setIsOpenFringeStatementExportModal: (value: boolean) => void;
  wageTable: WageTable;
}

function HeaderButton({
  selectedTabKey,
  setIsOpenWageTableEdit,
  setIsOpenWageTableArchive,
  setIsOpenClassificationCreateModal,
  setIsOpenAssignEmployeesModal,
  setIsOpenCreateProjectFringeModal,
  setIsOpenFringeStatementExportModal,
  wageTable,
}: HeaderButtonProps) {
  const isActive = !wageTable.isArchived;

  if (selectedTabKey === 'general') {
    return (
      <div className="flex gap-3">
        <Button
          variant="outline"
          color={isActive ? 'error' : 'primary'}
          onClick={() => setIsOpenWageTableArchive(true)}
          beforeContent={<ArchiveLine />}
        >
          {isActive ? 'Archive' : 'Unarchive'}
        </Button>
        <Button
          className="w-20"
          onClick={() => {
            setIsOpenWageTableEdit(true);
          }}
        >
          Edit
        </Button>
      </div>
    );
  } else if (selectedTabKey === 'classifications') {
    return (
      <Button
        className="whitespace-nowrap"
        onClick={() => setIsOpenClassificationCreateModal(true)}
        beforeContent={<AddLine />}
      >
        Create Classification
      </Button>
    );
  } else if (selectedTabKey === 'employees') {
    return <Button onClick={() => setIsOpenAssignEmployeesModal(true)}>Assign Employees</Button>;
  } else if (selectedTabKey === 'project-fringes') {
    return (
      <Button onClick={() => setIsOpenCreateProjectFringeModal(true)} beforeContent={<AddLine />}>
        Create Project Fringe
      </Button>
    );
  } else if (selectedTabKey === 'fringe-benefits-statement') {
    return <Button onClick={() => setIsOpenFringeStatementExportModal(true)}>Export Fringe Benefits Statement</Button>;
  }
}
