import Layout from 'components/dashboard/Layout';
import { ChatContainer } from 'components/chat';
import { useSendbirdSession } from 'components/chat/hooks/useSendbirdSession';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import React from 'react';

export default function ChatPage() {
  const { token, userId, loading } = useSendbirdSession();

  if (loading || !token || !userId) {
    return (
      <Layout noPadding>
        <div className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  return (
    <Layout noPadding>
      <ChatContainer userId={userId} accessToken={token} />
    </Layout>
  );
}
