import { AppProps } from 'next/app';

import { AuthProvider } from 'hooks/useAuth';
import { CompanyProvider } from 'hooks/useCompany';
import { AwsStsProvider } from 'hooks/useAwsSts';
import { AwsS3Provider } from 'hooks/useAwsS3';
import { DateSelectionProvider } from 'hooks/useDateSelection';
import 'css/tailwind.css';
import 'css/fonts.css';
import 'css/theme.css';
import 'css/fullcalendar.css';
import 'css/third-party.css';
import 'hammr-ui/components/calendar.css';
import 'hammr-ui/components/ag-grid.css';
import 'hammr-ui/components/sendbird.css';
import { MutationCache, QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query';

// TODO -> remove Toast provider from mui
import { Toaster } from '@/hammr-ui/components/toaster';
import { ConfirmDialogContainer } from '@/hammr-ui/components/ConfirmDialog/ConfirmDialogContainer';
import { logError, showErrorToast } from '@/utils/errorHandling';
import { INVALID_WORKER_CLASSIFICATION } from '@/utils/constants';
import { HandledError } from '@/utils/AppError';
import 'core-js/features/array/to-sorted';

const errorsToIgnore = [INVALID_WORKER_CLASSIFICATION];

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      if (errorsToIgnore.includes(error.message)) {
        return;
      }
      (error as HandledError).handled = false;
      logError(error, {
        extra: {
          network_status: navigator.onLine ? 'online' : 'offline',
          effective_connection: (navigator as any).connection?.effectiveType || 'unknown',
        },
      });
      showErrorToast(error);
    },
  }),
  mutationCache: new MutationCache({
    onError: (error) => {
      if (errorsToIgnore.includes(error.message)) {
        return;
      }
      (error as HandledError).handled = false;
      logError(error, {
        extra: {
          network_status: navigator.onLine ? 'online' : 'offline',
          effective_connection: (navigator as any).connection?.effectiveType || 'unknown',
        },
      });
      showErrorToast(error);
    },
  }),
  defaultOptions: {
    queries: {
      retry: 6,
    },
  },
});

export default function App({ Component, pageProps }: AppProps): JSX.Element {
  return (
    <>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <CompanyProvider>
            <AwsStsProvider>
              <AwsS3Provider>
                <DateSelectionProvider>
                  <ConfirmDialogContainer />
                  <Component {...pageProps} />
                </DateSelectionProvider>
              </AwsS3Provider>
            </AwsStsProvider>
          </CompanyProvider>
        </AuthProvider>
        <Toaster />
      </QueryClientProvider>
    </>
  );
}
