import Layout from 'components/dashboard/Layout';
import CertifiedPayrollReport from 'components/reports/CertifiedPayrollReport/CertifiedPayrollReport';
import FourZeroOneKReport from '@/components/reports/401KReport';
import PayrollJournal from 'components/reports/PayrollJournal';
import PayrollSummary from 'components/reports/PayrollSummary';
import { useAuth } from 'hooks/useAuth';
import WorkerCompReport from '@/components/reports/WorkersCompReport';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import LineChartLine from '@/hammr-icons/LineChartLine';
import TimeOffRequestsReport from '@/components/reports/TimeOffRequestsReport';
import TimeOffBalancesReport from '@/components/reports/TimeOffBalancesReport';
import WorkforceDemographicsReport from '@/components/reports/WorkforceDemographicsReport/WorkforceDemographicsReport';
import ProjectLaborReport from '@/components/reports/ProjectLaborReport';

const Reports: React.FC = () => {
  const { user } = useAuth();

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout noPadding>
      <PageHeader title="Reports" icon={<LineChartLine />} />

      <article className="px-8 py-6">
        <h2 className="font-medium text-strong-950">Payroll Reports</h2>
        <section className="mt-6 grid grid-cols-[repeat(auto-fill,352px)] gap-6">
          <PayrollSummary />
          <PayrollJournal />
        </section>
      </article>

      <article className="px-8 py-6">
        <h2 className="font-medium text-strong-950">Time-Off</h2>
        <section className="mt-6 grid grid-cols-[repeat(auto-fill,352px)] gap-6">
          <TimeOffRequestsReport />
          <TimeOffBalancesReport />
        </section>
      </article>

      <article className="px-8 py-6">
        <h2 className="font-medium text-strong-950">Labor Compliance</h2>
        <section className="mt-6 grid grid-cols-[repeat(auto-fill,352px)] gap-6">
          <CertifiedPayrollReport />
          <WorkforceDemographicsReport />
        </section>
      </article>

      <article className="px-8 py-6">
        <h2 className="font-medium text-strong-950">Other Reports</h2>
        <section className="mt-6 grid grid-cols-[repeat(auto-fill,352px)] gap-6">
          <ProjectLaborReport />
          <WorkerCompReport />
          <FourZeroOneKReport />
        </section>
      </article>
    </Layout>
  );
};

export default Reports;
