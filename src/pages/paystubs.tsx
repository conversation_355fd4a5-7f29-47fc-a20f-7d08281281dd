import Layout from '@/components/dashboard/Layout';
import Paystubs from '@/components/people/employee-view/Paystubs';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { useAuth } from '@/hooks/useAuth';
import { RiMoneyDollarBoxLine } from '@remixicon/react';

export default function PayStubsPage() {
  const { user } = useAuth();

  if (!user) return null;

  return (
    <Layout noPadding>
      <PageHeader title="Paystubs" icon={<RiMoneyDollarBoxLine />} />

      <section className="px-8 py-3">
        <Paystubs checkEmployeeId={user.checkEmployeeId} employeeFullName={user.name} />
      </section>
    </Layout>
  );
}
