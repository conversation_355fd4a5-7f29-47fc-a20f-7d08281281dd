import { useEffect, useState } from 'react';

import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import { useRouter } from 'next/router';
import ManualIntegrationDetail from 'components/settings/ManualIntegrationDetail';
import { IntegrationSupportedPlatform } from 'interfaces/integration-user-token';
import integrationUserTokenService from 'services/integration-user-token';

const ManualIntegrationDetailPage: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const { manualIntegration } = router.query;
  const [integrationInfo, setIntegrationInfo] = useState<IntegrationSupportedPlatform | null>(null);

  const [supportedPlatforms, setSupportedPlatforms] = useState<IntegrationSupportedPlatform[] | null>(null);

  const fetchSupportedPlatforms = async () => {
    const supportedPlatforms = await integrationUserTokenService.getSupportedPlatforms();
    setSupportedPlatforms(supportedPlatforms);
  };

  useEffect(() => {
    fetchSupportedPlatforms();
  }, []);

  useEffect(() => {
    if (!router.isReady || !supportedPlatforms) {
      return;
    }

    const foundIntegration = supportedPlatforms?.find((platform) => platform.urlId === manualIntegration);

    if (!foundIntegration) {
      router.push('/settings/integrations');
    } else {
      setIntegrationInfo(foundIntegration);
    }
  }, [router.isReady, supportedPlatforms]);

  if (!user || !user?.isCompanyAdmin || !integrationInfo) return null;

  return (
    <Layout noPadding>
      <ManualIntegrationDetail integration={integrationInfo} />
    </Layout>
  );
};

export default ManualIntegrationDetailPage;
