import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import { useRouter } from 'next/router';
import IntegrationDetail from 'components/settings/IntegrationDetail';
import { IntegrationSupportedPlatform } from 'interfaces/integration-user-token';
import integrationUserTokenService from 'services/integration-user-token';
import { useQuery } from '@tanstack/react-query';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';

const IntegrationDetailPage: React.FC = () => {
  const { user } = useAuth();
  const router = useRouter();
  const { integration } = router.query;

  const supportedPlatforms = useQuery({
    queryKey: ['supportedPlatforms'],
    queryFn: integrationUserTokenService.getSupportedPlatforms,
    enabled: router.isReady,
  });

  const integrationInfo =
    router.isReady && supportedPlatforms.data
      ? supportedPlatforms.data.find((platform: IntegrationSupportedPlatform) => platform.urlId === integration)
      : null;

  if (router.isReady && supportedPlatforms.data && !supportedPlatforms.isLoading && !integrationInfo) {
    router.push('/settings/integrations');
    return null;
  }

  if (supportedPlatforms.isLoading || !user || !user?.isCompanyAdmin || !integrationInfo) {
    return <Layout noPadding>{supportedPlatforms.isLoading ? <LoadingIndicator /> : null}</Layout>;
  }

  return (
    <Layout noPadding>
      <IntegrationDetail integration={integrationInfo} />
    </Layout>
  );
};

export default IntegrationDetailPage;
