import { useRouter } from 'next/router';
import { useQuery } from '@tanstack/react-query';
import Layout from '@/components/dashboard/Layout';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { equipmentService } from '@/services/equipment';
import { useState, useRef, useEffect } from 'react';
import { RiToolsFill } from '@remixicon/react';
import EquipmentDetails from '@/components/equipment/EquipmentDetails';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { Button } from '@/hammr-ui/components/button';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import ArrowGoBackLine from '@/hammr-icons/ArrowGoBackLine';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog/ConfirmDialog';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { Menu, MenuContent, MenuTrigger } from '@/hammr-ui/components/Menu';
import TimesheetExport from '@/components/timesheets/TimesheetExport';
import { AgGridReact } from '@ag-grid-community/react';
import { useDateSelection } from '@/hooks/useDateSelection';
import AddLine from '@/hammr-icons/AddLine';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { cn } from '@/hammr-ui/lib/cn';

export default function EquipmentDetailPage() {
  const router = useRouter();
  const { id, tab } = router.query;
  const equipmentId = typeof id === 'string' ? parseInt(id, 10) : undefined;
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [activeTab, setActiveTab] = useState<string>((tab as string) || 'general');

  // Timesheet functionality
  const [showTimesheetModal, setShowTimesheetModal] = useState(false);
  const gridRef = useRef<AgGridReact>(null);
  const isExportingRef = useRef<boolean>(false);
  const { currentStartDateSelected, currentEndDateSelected } = useDateSelection();

  const {
    data: equipment,
    isPending,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ['equipment', equipmentId],
    queryFn: async () => {
      if (!equipmentId) return Promise.resolve(null);
      // No need to modify the get method since we want to always fetch the equipment
      // regardless of its isArchived status
      return equipmentService.get(equipmentId);
    },
    enabled: !!equipmentId,
  });

  // Update the active tab when the URL query changes
  useEffect(() => {
    if (tab && typeof tab === 'string' && (tab === 'general' || tab === 'timesheets')) {
      setActiveTab(tab);
    }
  }, [tab]);

  // Refetch data when the timesheet modal closes
  useEffect(() => {
    if (!showTimesheetModal && equipmentId) {
      refetch();
    }
  }, [showTimesheetModal, equipmentId, refetch]);

  const handleArchiveRestore = async () => {
    if (!equipment || !equipmentId) return;

    try {
      if (!equipment.isArchived) {
        await equipmentService.archive(equipmentId);
      } else {
        await equipmentService.restore(equipmentId);
      }
      await refetch();
      setConfirmDialogOpen(false);
    } catch (error) {
      console.error('Error updating equipment status:', error);
    }
  };

  return (
    <Layout>
      {isPending ? (
        <div className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </div>
      ) : isError ? (
        <div className="flex size-full items-center justify-center">
          <p className="text-sm text-error-base">{error.message}</p>
        </div>
      ) : !equipment ? (
        <div className="flex size-full items-center justify-center">
          <p className="text-sm text-error-base">The requested equipment could not be found.</p>
        </div>
      ) : (
        <>
          <PageHeader
            noPadding
            title={equipment.name}
            icon={<RiToolsFill />}
            breadcrumb={
              <Breadcrumbs>
                <BreadcrumbItem text="Equipment" onClick={() => router.push('/equipment')} />
                <BreadcrumbItem text={equipment.name} active />
              </Breadcrumbs>
            }
            headerRight={
              <div className="flex gap-3">
                {activeTab === 'timesheets' ? (
                  <div className="flex gap-3">
                    <Menu>
                      <MenuTrigger asChild>
                        <Button
                          variant="outline"
                          color="neutral"
                          className="min-w-20"
                          afterContent={<ArrowDownSLine />}
                        >
                          Export
                        </Button>
                      </MenuTrigger>
                      <MenuContent className={cn('min-w-48')} align="end">
                        <TimesheetExport
                          gridRef={gridRef}
                          currentStartDateSelected={currentStartDateSelected}
                          currentEndDateSelected={currentEndDateSelected}
                          isExportingRef={isExportingRef}
                        />
                      </MenuContent>
                    </Menu>
                    <Button
                      variant="default"
                      color="primary"
                      className="min-w-20"
                      beforeContent={<AddLine />}
                      onClick={() => setShowTimesheetModal(true)}
                    >
                      Add Timesheet
                    </Button>
                  </div>
                ) : (
                  <>
                    <Button
                      variant={'outline'}
                      color={'neutral'}
                      className="min-w-20"
                      onClick={() => setConfirmDialogOpen(true)}
                      beforeContent={equipment.isArchived ? <ArrowGoBackLine /> : <ArchiveLine />}
                    >
                      {equipment.isArchived ? 'Restore' : 'Archive'}
                    </Button>
                    <Button
                      variant="default"
                      color="primary"
                      className="min-w-20"
                      onClick={() => setShowEditModal(true)}
                    >
                      Edit Equipment
                    </Button>
                  </>
                )}
              </div>
            }
          />

          <EquipmentDetails
            equipment={equipment}
            showEditModal={showEditModal}
            setShowEditModal={setShowEditModal}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            isExportingRef={isExportingRef}
            showTimesheetModal={showTimesheetModal}
            setShowTimesheetModal={setShowTimesheetModal}
            gridRef={gridRef}
          />

          <ConfirmDialog
            open={confirmDialogOpen}
            setOpen={setConfirmDialogOpen}
            onConfirm={handleArchiveRestore}
            title={`${equipment.isArchived ? 'Restore' : 'Archive'} Equipment`}
            subtitle={
              equipment
                ? `Are you sure you want to ${equipment.isArchived ? 'restore' : 'archive'} "${equipment.name}"?`
                : ''
            }
            confirmButtonText={equipment.isArchived ? 'Restore' : 'Archive'}
            confirmButton={{ color: equipment.isArchived ? 'primary' : 'error' }}
          />
        </>
      )}
    </Layout>
  );
}
