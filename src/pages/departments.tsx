import React, { Suspense, useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import Layout from 'components/dashboard/Layout';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import Button from '@/hammr-ui/components/button';
import AddLine from '@hammr-icons/AddLine';
import LoadingIndicator from '@hammr-ui/components/LoadingIndicator';
import Building4Line from '@/hammr-icons/Building4Line';
import { useQuery } from '@tanstack/react-query';
import { departmentsService } from '@/services/departments';
import { useCompany } from 'hooks/useCompany';
import DepartmentsView from '@/components/departments/DepartmentsView';
import { Department } from '@/interfaces/department';
import ConfirmDeleteDepartmentModal from '@/components/departments/ConfirmDeleteDepartmentModal';
import EmptyStateTrainingAnalyis from '@/hammr-icons/EmptyStateTrainingAnalyis';
import DepartmentMembersListModal from '@/components/departments/DepartmentMembersListModal';
import CreateDepartmentModal from '@/components/departments/CreateDepartmentModal';
import EditDepartmentModal from '@/components/departments/EditDepartmentModal';
const DepartmentEmployeeAssignment = React.lazy(() => import('@/components/departments/DepartmentEmployeeAssignment'));

const DepartmentsPage: React.FC = () => {
  const { user } = useAuth();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department>(null);

  const {
    data: departments,
    isLoading,
    refetch,
  } = useQuery<{ departments: Department[] }>({
    queryKey: ['departments'],
    queryFn: async () => {
      const result = await departmentsService.list();
      return {
        departments: result.departments.sort((a, b) => a.name.localeCompare(b.name)),
      };
    },
  });

  function handleCheckMembers(index: number) {
    setSelectedDepartment(departments?.departments[index]);
    setShowMembersModal(true);
  }

  const handleEditDepartmentClick = (index: number) => {
    setSelectedDepartment(departments?.departments[index]);
    setShowEditModal(true);
  };

  const handleDeleteDepartmentClick = (index: number) => {
    setSelectedDepartment(departments?.departments[index]);
    setShowDeleteModal(true);
  };

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout noPadding>
      <PageHeader
        title="Departments"
        icon={<Building4Line />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateModal(true)}>
            Create Department
          </Button>
        }
      />
      <div className="relative flex flex-1 flex-col px-8">
        <Tabs defaultValue="departments" className="w-full">
          <TabsList>
            <TabsTrigger value="departments">Departments</TabsTrigger>
            <TabsTrigger value="assignment">Employee Assignment</TabsTrigger>
          </TabsList>

          <TabsContent value="departments">
            {isLoading ? (
              <div className="mt-8 flex justify-center">
                <LoadingIndicator />
              </div>
            ) : departments?.departments && departments?.departments.length === 0 ? (
              <>
                <div className="flex flex-col items-center justify-center py-28">
                  <EmptyStateTrainingAnalyis />
                  <p className="mt-5 text-center text-sm text-soft-400">
                    There is no department yet.
                    <br />
                    Click the button below to add one.
                  </p>
                  <Button className="mt-5" beforeContent={<AddLine />} onClick={() => setShowCreateModal(true)}>
                    Create Department
                  </Button>
                </div>
              </>
            ) : (
              <DepartmentsView
                departments={departments?.departments}
                checkMembersCallback={handleCheckMembers}
                rowActionCallback={handleEditDepartmentClick}
                deleteActionCallback={handleDeleteDepartmentClick}
              />
            )}
          </TabsContent>

          <TabsContent value="assignment">
            <Suspense
              fallback={
                <div className="mt-8 flex justify-center">
                  <LoadingIndicator />
                </div>
              }
            >
              <DepartmentEmployeeAssignment setShowModal={setShowCreateModal} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
      <CreateDepartmentModal
        departments={departments?.departments}
        open={showCreateModal}
        setOpen={setShowCreateModal}
        callback={refetch}
      />
      {selectedDepartment && (
        <ConfirmDeleteDepartmentModal
          open={showDeleteModal}
          setOpen={setShowDeleteModal}
          currentDepartmentData={selectedDepartment}
          callback={refetch}
        />
      )}
      {selectedDepartment && (
        <DepartmentMembersListModal
          open={showMembersModal}
          setOpen={setShowMembersModal}
          currentDepartmentData={selectedDepartment}
        />
      )}
      {selectedDepartment && (
        <EditDepartmentModal
          open={showEditModal}
          setOpen={setShowEditModal}
          currentDepartmentData={selectedDepartment}
          departments={departments?.departments}
          callback={refetch}
        />
      )}
    </Layout>
  );
};

export default DepartmentsPage;
