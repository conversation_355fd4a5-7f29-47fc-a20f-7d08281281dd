import { useEffect } from 'react';
import { useRouter } from 'next/router';
import LoginForm from '../components/forms/LoginForm';
import Layout from 'components/home/<USER>';
import { useAuth } from 'hooks/useAuth';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      if (user.role === 'ADMIN') {
        router.push('/dashboard');
      } else if (user.role === 'FOREMAN') {
        router.push('/timesheets');
      } else if (user.role === 'WORKER') {
        if (user.workerClassification === 'EMPLOYEE') {
          router.push('/paystubs');
        } else {
          router.push('/payments');
        }
      }
    }
  }, [user, router]);

  if (user) {
    return <div className="flex h-screen items-center justify-center">Already logged in, redirecting...</div>;
  }

  return (
    <Layout>
      <div className="flex min-h-screen flex-col">
        <div className="mx-4 mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <div className="mt-6 text-center text-2xl text-strong-950">Log in</div>
          </div>
          <div className="mt-8 rounded-lg px-4 py-8 shadow-lg ring-1 ring-soft-200 sm:px-10">
            <LoginForm />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default LoginPage;
