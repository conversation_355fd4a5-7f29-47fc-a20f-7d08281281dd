import { useEffect, useRef, useState } from 'react';
import { InjuryReportsTable } from '@/components/injury-reports/InjuryReportsTable';
import { Ta<PERSON>, TabList, TabItem } from '@/hammr-ui/components/tabs';
import Layout from '@/components/dashboard/Layout';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { RiFirstAidKitFill } from '@remixicon/react';
import { apiRequest } from '@/utils/requestHelpers';
import { useQuery } from '@tanstack/react-query';
import { uniqBy } from 'lodash';
import { InjuryReport } from '@/interfaces/injury-report';
import { useMemo } from 'react';
import { MultiSelect } from '@/hammr-ui/components/multi-select';
import { AgGridReact } from '@ag-grid-community/react';

export default function InjuryReportsPage() {
  const [selectedStatus, setSelectedStatus] = useState<'OPEN' | 'RESOLVED'>('OPEN');
  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>([]);
  const [selectedEmployeeIds, setSelectedEmployeeIds] = useState<number[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const gridRef = useRef<AgGridReact>(null);

  const { data, isLoading } = useQuery({
    queryKey: ['injury-reports', selectedStatus, currentPage, pageSize],
    queryFn: async () => {
      const res = await apiRequest('/injury-reports/list', {
        urlParams: {
          isResolved: selectedStatus === 'RESOLVED',
          page: currentPage,
          limit: pageSize,
        },
        convertToJson: false,
      });
      const json = await res.json();

      return json as { data: InjuryReport[]; pagination: any };
    },
  });

  // Safely extract reports and pagination with default values
  const reports = data?.data || [];
  const pagination = data?.pagination || { total: 0, page: 1, limit: 10 };

  const employeeItems = useMemo(() => {
    return uniqBy(
      reports
        ?.filter((item) => !!item.user)
        ?.map((item) => ({
          label: item.user?.firstName + ' ' + item.user?.lastName,
          value: item.user.id,
          isSelected: selectedEmployeeIds.includes(item.user?.id),
        })) ?? [],
      (item) => item.value
    );
  }, [reports, selectedEmployeeIds]);

  const projectItems = useMemo(() => {
    return uniqBy(
      reports
        ?.filter((item) => !!item.timesheet?.project)
        ?.map((item) => ({
          label: item.timesheet?.project?.name,
          value: item.timesheet?.project?.id,
          isSelected: selectedProjectIds.includes(item.timesheet?.project?.id),
        })) ?? [],
      (item) => item.value
    );
  }, [reports, selectedProjectIds]);

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedStatus]);

  return (
    <Layout>
      <div className="flex flex-grow flex-col space-y-4">
        <PageHeader title="Injury Reports" icon={<RiFirstAidKitFill className="size-5" />} noPadding />

        <div className="flex items-center gap-3 pt-4">
          <Tabs value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as 'OPEN' | 'RESOLVED')}>
            <TabList>
              <TabItem value="OPEN">Open</TabItem>
              <TabItem value="RESOLVED">Resolved</TabItem>
            </TabList>
          </Tabs>

          <div className="flex items-center gap-4">
            <MultiSelect
              buttonProps={{ className: 'w-[150px]' }}
              popoverProps={{ align: 'start', className: 'w-[200px]' }}
              label="All Projects"
              items={projectItems}
              onChange={(newItems) => {
                setSelectedProjectIds(newItems.filter((item) => item.isSelected).map((item) => item.value as number));
                gridRef.current?.api?.onFilterChanged();
              }}
            />

            <MultiSelect
              buttonProps={{ className: 'w-[150px]' }}
              popoverProps={{ align: 'start', className: 'w-[200px]' }}
              label="All Employees"
              items={employeeItems}
              onChange={(newItems) => {
                setSelectedEmployeeIds(newItems.filter((item) => item.isSelected).map((item) => item.value as number));
                gridRef.current?.api?.onFilterChanged();
              }}
            />
          </div>
        </div>

        <InjuryReportsTable
          selectedStatus={selectedStatus}
          selectedProjectIds={selectedProjectIds}
          selectedEmployeeIds={selectedEmployeeIds}
          isLoading={isLoading}
          reports={reports}
          gridRef={gridRef}
          pagination={pagination}
          setCurrentPage={setCurrentPage}
          setPageSize={setPageSize}
          pageSize={pageSize}
        />
      </div>
    </Layout>
  );
}
