import Layout from 'components/dashboard/Layout';
import { useAuth } from 'hooks/useAuth';
import Details from 'components/company/CompanyDetails';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import Briefcase4Line from '@/hammr-icons/Briefcase4Line';

const CompanyDetails: React.FC = () => {
  const { user } = useAuth();

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout noPadding>
      <PageHeader icon={<Briefcase4Line />} title="Company Details" />
      <Details />
    </Layout>
  );
};

export default CompanyDetails;
