import { useState } from 'react';
import { useAuth } from 'hooks/useAuth';
import Layout from '@/components/dashboard/Layout';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import PieChartBoxLine from '@/hammr-icons/PieChartBoxLine';
import { Button } from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/utils/requestHelpers';
import { useAwsSts } from '@/hooks/useAwsSts';
import DailyReports from '@/components/daily-reports/DailyReports';

export default function DailyReportsPage() {
  const { user } = useAuth();
  const [showCreateReportModal, setShowCreateReportModal] = useState(false);
  const { stsCredentials, getStsCredentials } = useAwsSts();

  const reportsQuery = useQuery({
    queryKey: ['daily-reports', user?.companyId],
    queryFn: async () => {
      if (stsCredentials?.Expiration) {
        const expiration = new Date(stsCredentials.Expiration);
        if (new Date() >= expiration) {
          await getStsCredentials(user?.companyId);
        }
      }

      return apiRequest('daily-reports');
    },
    enabled: Boolean(user?.companyId),
  });

  if (reportsQuery.isLoading) {
    return (
      <Layout>
        <div className="flex size-full items-center justify-center">
          <LoadingIndicator />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageHeader
        noPadding
        title="Daily Reports"
        icon={<PieChartBoxLine />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={() => setShowCreateReportModal(true)}>
            Create Report
          </Button>
        }
      />

      <div className="mt-6 flex flex-grow flex-col">
        <DailyReports
          showCreateReportModal={showCreateReportModal}
          setShowCreateReportModal={setShowCreateReportModal}
        />
      </div>
    </Layout>
  );
}
