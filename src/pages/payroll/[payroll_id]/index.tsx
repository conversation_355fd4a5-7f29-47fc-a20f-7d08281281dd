import Layout from 'components/dashboard/Layout';
import PreparePayroll from 'components/payroll/PreparePayroll';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { getPayrollData } from 'services/payroll';
import { showErrorToast } from 'utils/errorHandling';

const PayrollPage = () => {
  const [selectedPreviewPayroll, setSelectedPreviewPayroll] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { payroll_id } = router.query;

  const refreshPayroll = async () => {
    if (!payroll_id) {
      return;
    }
    setIsLoading(true);

    let data;
    try {
      data = await getPayrollData(payroll_id as string);
      setSelectedPreviewPayroll(data);
    } catch (error) {
      showErrorToast(error);
      router.back();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshPayroll();
  }, [payroll_id]);

  return (
    <Layout noPadding>
      <PreparePayroll payrollData={selectedPreviewPayroll} refreshPayroll={refreshPayroll} isLoading={isLoading} />
    </Layout>
  );
};

export default PayrollPage;
