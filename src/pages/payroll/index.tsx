import Layout from 'components/dashboard/Layout';
import { useAuth } from 'hooks/useAuth';
import RunPayrollComponent from 'components/payroll/RunPayrollComponent';

const RunPayroll: React.FC = () => {
  const { user } = useAuth();

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout>
      <div className="flex h-full flex-col">
        <RunPayrollComponent />
      </div>
    </Layout>
  );
};

export default RunPayroll;
