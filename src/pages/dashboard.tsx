import { useAuth } from '@/hooks/useAuth';
import Layout from '@/components/dashboard/Layout';
import QuickActions from '@/components/dashboard/QuickActions';
import Notifications from '@/components/dashboard/Notifications';
import FieldActivity from '@/components/dashboard/FieldActivity';
import ProjectActivity from '@/components/dashboard/ProjectActivity';
import RealTimeMap from '@/components/dashboard/RealTimeMap';
import { Profile } from '@/components/people/sections/ProfileImage';
import { useQuery } from '@tanstack/react-query';
import { userService } from '@/services/user';
import { useState } from 'react';
import { cn } from '@hammr-ui/lib/utils';

const DashboardPage = () => {
  const { user: authUser } = useAuth();
  const [isRealTimeMapHidden, setIsRealTimeMapHidden] = useState(false);

  const user = useQuery({
    queryKey: ['me'],
    queryFn: () => userService.get(Number(authUser.uid)),
    enabled: !!authUser?.uid,
  });

  if (!authUser) return null;

  return (
    <Layout noPadding>
      <div className="max-w-screen-xl">
        <header className="flex items-center gap-3.5 px-8 py-5">
          <Profile user={user.data} className="h-12 w-12" />
          <div>
            <div className="text-2xl font-medium">
              {user.data ? (
                `${user.data.firstName} ${user.data.lastName}`
              ) : (
                <div className="h-8 w-40 animate-pulse rounded-md bg-weak-100"></div>
              )}
            </div>
            <div className="mt-1 text-sm text-sub-600">Welcome back to Hammr 🔨</div>
          </div>
        </header>

        <div className="flex flex-col gap-6 px-8 py-6 md:flex-col">
          <QuickActions className="flex-1" />

          <div className="grid grid-cols-2 gap-6 xl:grid-cols-[auto_min-content]">
            <RealTimeMap className="order-1 col-span-2 xl:col-span-1" onHide={() => setIsRealTimeMapHidden(true)} />
            <Notifications className="order-2 col-span-2 lg:col-span-1" />
            <FieldActivity
              className={cn(`order-4 col-span-2  xl:col-span-1`, isRealTimeMapHidden ? 'xl:order-1' : 'xl:order-3')}
            />
            <ProjectActivity className="order-3 col-span-2 lg:col-span-1" />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default DashboardPage;
