import Layout from '@/components/dashboard/Layout';
import PolicyForm from '@/components/time-off-policies/PolicyForm';
import ListSettingsLine from '@/hammr-icons/ListSettingsLine';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { useToast } from '@/hooks/useToast';
import { apiRequest } from '@/utils/requestHelpers';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { TimeOffPolicy } from '@/interfaces/timeoff';

export default function CreateTimeOffPolicy() {
  const router = useRouter();
  const { addToast } = useToast();

  const policyMutation = useMutation({
    async mutationFn(formData: Parameters<React.ComponentProps<typeof PolicyForm>['onSubmit']>[0]) {
      const payloadData = {
        ...(formData.type === 'UNPAID'
          ? {
              name: formData.name,
              type: formData.type,
              isLimited: false,
              addNewEmployeesAutomatically: formData.addNewEmployeesAutomatically,
            }
          : formData.isLimited === 'false'
            ? {
                name: formData.name,
                type: formData.type,
                isLimited: formData.isLimited,
                addNewEmployeesAutomatically: formData.addNewEmployeesAutomatically,
              }
            : formData),
      };
      return await apiRequest<TimeOffPolicy>('/time-off-policies', {
        method: 'POST',
        body: payloadData,
      });
    },
    onSuccess(createdPolicy) {
      router.push('/time-off?tab=policies');
      addToast({
        type: 'success',
        title: 'Created Time Off Policy',
        description: (
          <>
            Successfully created the Time Off policy <strong className="font-medium">{createdPolicy.name}</strong>.
            Next, please{' '}
            <a className="text-primary-base underline" href={`/time-off-policies/${createdPolicy.id}?tab=enrolled`}>
              enroll employees
            </a>
            .
          </>
        ),
      });
    },
  });

  return (
    <Layout noPadding>
      <PageHeader
        title="Create Time Off Policy"
        icon={<ListSettingsLine />}
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Time Off" onClick={() => router.push('/time-off?tab=policies')} />
            <BreadcrumbItem text="Create Time Off Policy" active />
          </Breadcrumbs>
        }
      />

      <section className="px-8 py-6">
        <PolicyForm
          onSubmit={policyMutation.mutate}
          onCancel={() => router.push('/time-off?tab=policies')}
          isSubmitting={policyMutation.isPending}
        />
      </section>
    </Layout>
  );
}
