import Layout from '@/components/dashboard/Layout';
import { UpdatedTable } from '@/components/shared/UpdatedTable';
import EnrollEmployeesModal from '@/components/time-off-policies/EnrollEmployeesModal';
import PolicyForm from '@/components/time-off-policies/PolicyForm';
import ArchiveLine from '@/hammr-icons/ArchiveLine';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';
import ListSettingsLine from '@/hammr-icons/ListSettingsLine';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import CompactButton from '@/hammr-ui/components/CompactButton';
import ConfirmDialog from '@/hammr-ui/components/ConfirmDialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import { KeyIcon } from '@/hammr-ui/components/KeyIcon';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import StatusBadge from '@/hammr-ui/components/StatusBadge';
import Button from '@/hammr-ui/components/button';
import { Tooltip } from '@/hammr-ui/components/tooltip';
import { useToast } from '@/hooks/useToast';
import { apiRequest } from '@/utils/requestHelpers';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { AccrualMethod, TimeOffPolicy } from '@/interfaces/timeoff';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { Badge } from '@/hammr-ui/components/badge';
import { policyTypeColors } from '@/components/time-off-requests/TimeOffCalendar';
import { formatHours } from '@/utils/format';

dayjs.extend(advancedFormat);

type TableItem = TimeOffPolicy['users'][number] & { actions: unknown };

export default function PolicyDetails() {
  const router = useRouter();
  const { addToast } = useToast();
  const policy_id = router.query.policy_id;

  const [selectedTab, setSelectedTab] = useState('general');
  useEffect(() => {
    const tab = router.query.tab;
    if (typeof tab === 'string') {
      setSelectedTab(tab);
    }
  }, [router.query.tab]);

  const [showArchivePolicyModal, setShowArchivePolicyModal] = useState(false);
  const [showEnrollEmployeesModal, setShowEnrollEmployeesModal] = useState(false);
  const [employeeAboutToUnenroll, setEmployeeAboutToUnenroll] = useState(null);
  const [showUnenrollEmployeeModal, setShowUnenrollEmployeeModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const {
    isPending,
    isError,
    data: policy,
    refetch,
  } = useQuery<TimeOffPolicy>({
    queryKey: ['time-off-policies', policy_id],
    queryFn: () =>
      apiRequest<{
        timeOffPolicy: TimeOffPolicy;
      }>(`time-off-policies/${policy_id}`).then((response) => response.timeOffPolicy),
    enabled: !!policy_id,
  });

  const policyMutation = useMutation({
    async mutationFn(formData: Parameters<React.ComponentProps<typeof PolicyForm>['onSubmit']>[0]) {
      const payloadData = {
        ...(formData.isLimited === 'false'
          ? {
              name: formData.name,
              type: formData.type,
              isLimited: formData.isLimited,
              addNewEmployeesAutomatically: formData.addNewEmployeesAutomatically,
            }
          : formData),
      };

      // isLimited is not allowed to change
      delete payloadData.isLimited;

      await apiRequest<TimeOffPolicy>(`/time-off-policies/${policy_id}`, {
        method: 'PATCH',
        body: payloadData,
      });
    },
    onSuccess(_, formData) {
      addToast({
        type: 'success',
        title: 'Edited Time Off Policy',
        description: (
          <>
            Successfully edited the Time Off policy <strong className="font-medium">{formData.name}</strong>.
          </>
        ),
      });
      setIsEditing(false);
      refetch();
    },
  });

  if (isPending) {
    return (
      <Layout>
        <div className="flex h-full items-center justify-center">
          <LoadingIndicator text="Loading Policy..." />
        </div>
      </Layout>
    );
  }

  if (isError) {
    return (
      <div className="flex h-full items-center justify-center text-sm text-error-base">
        An error occured when fetching the policy.
      </div>
    );
  }

  const isArchived = !!policy.endDate;

  const colDefs: ColDef<TableItem>[] = [
    {
      headerName: 'Employee',
      valueGetter: (params) => `${params.data.firstName} ${params.data.lastName}`,
    },
    {
      headerName: 'Enrolled Date',
      valueGetter: (params) => dayjs(params.data.timeOffPolicyEnrollment.startDate).format('MMM D, YYYY'),
    },
    {
      headerName: 'Accrued',
      valueGetter: (params) => formatHours(params.data.timeOffSummary.accruedBasedOnAccrualLimit),
    },
    {
      headerName: 'Carryover',
      valueGetter: (params) => formatHours(params.data.timeOffSummary.carryover),
    },
    {
      headerName: 'Used',
      valueGetter: (params) => formatHours(params.data.timeOffSummary.used),
    },
    {
      headerName: 'Available',
      valueGetter: (params) => formatHours(params.data.timeOffSummary.available),
    },
    {
      field: 'actions',
      headerName: '',
      maxWidth: 56,
      cellRenderer: (params: ValueFormatterParams) => {
        const employee = params.data;

        return (
          <div className="flex justify-center">
            <Tooltip content="Unenroll">
              <CompactButton
                size="large"
                variant="ghost"
                onClick={() => {
                  setEmployeeAboutToUnenroll(employee);
                  setShowUnenrollEmployeeModal(true);
                }}
                disabled={isArchived}
              >
                <CloseCircleLine />
              </CompactButton>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <Layout noPadding>
      <PageHeader
        title={policy.name}
        icon={<ListSettingsLine />}
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Time Off" onClick={() => router.push('/time-off?tab=policies')} />
            <BreadcrumbItem text={policy.name} active />
          </Breadcrumbs>
        }
        headerRight={
          selectedTab === 'general' ? (
            <div className="space-x-3">
              <Button
                variant="outline"
                color="neutral"
                onClick={() => setShowArchivePolicyModal(true)}
                disabled={isArchived}
              >
                Archive
              </Button>
              <Button onClick={() => setIsEditing(true)} disabled={isArchived}>
                Edit
              </Button>
            </div>
          ) : (
            <Button onClick={() => setShowEnrollEmployeesModal(true)} disabled={isArchived}>
              Enroll Employees
            </Button>
          )
        }
      />

      <Tabs className="mx-8" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="enrolled">Enrolled Employees</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          {isEditing ? (
            <PolicyForm
              onSubmit={policyMutation.mutate}
              onCancel={() => setIsEditing(false)}
              isSubmitting={policyMutation.isPending}
              policy={policy}
            />
          ) : (
            <PolicyInfo policy={policy} />
          )}
        </TabsContent>
        <TabsContent value="enrolled">
          <UpdatedTable<TableItem> colDefs={colDefs} rowData={policy.users as TableItem[]} />
        </TabsContent>
      </Tabs>

      <ConfirmDialog
        onConfirm={async () => {
          await apiRequest(`/time-off-policies/${policy_id}/archive`, {
            method: 'POST',
            body: { archived: true },
          });
          addToast({
            type: 'success',
            title: 'Archived Time Off Policy',
            description: (
              <>
                Successfully archived the Time Off policy <strong className="font-medium">{policy.name}</strong>.
              </>
            ),
          });
          refetch();
        }}
        open={showArchivePolicyModal}
        setOpen={setShowArchivePolicyModal}
        title="Archive Time Off Policy"
        subtitle={
          <>
            You’re about to archive the Time Off policy <strong className="font-medium">{policy.name}</strong>. Do you
            want to proceed?
          </>
        }
        confirmButtonText="Archive"
        icon={<KeyIcon icon={<ArchiveLine />} />}
      />

      <EnrollEmployeesModal
        isOpen={showEnrollEmployeesModal}
        setIsOpen={setShowEnrollEmployeesModal}
        alreadyEnrolledEmployeeIds={policy.users.map((user) => user.id)}
        policy={policy}
        onSuccess={refetch}
      />

      <ConfirmDialog
        onConfirm={async () => {
          await apiRequest(`/time-off-policies/${policy.id}/users/${employeeAboutToUnenroll.id}`, { method: 'DELETE' });
          addToast({
            type: 'success',
            title: 'Successfully Unenrolled Employee',
            description: (
              <>
                Successfully unenrolled{' '}
                <strong className="font-medium">
                  {employeeAboutToUnenroll.firstName} {employeeAboutToUnenroll.lastName}
                </strong>{' '}
                from the Time Off policy <strong className="font-medium">{policy.name}</strong>.
              </>
            ),
          });
          refetch();
        }}
        open={showUnenrollEmployeeModal}
        setOpen={setShowUnenrollEmployeeModal}
        title="Unenroll Employee"
        subtitle={
          <>
            You’re about to unenroll{' '}
            <strong className="font-medium">
              {employeeAboutToUnenroll?.firstName} {employeeAboutToUnenroll?.lastName}
            </strong>{' '}
            from the Time Off policy <strong className="font-medium">{policy.name}</strong>. Do you want to proceed?
          </>
        }
        confirmButtonText="Unenroll"
        icon={<KeyIcon icon={<ArchiveLine />} />}
      />
    </Layout>
  );
}

function PolicyInfo({ policy }: { policy: TimeOffPolicy }) {
  const accrualsDescription: Record<AccrualMethod, string> = {
    [AccrualMethod.ACCRUED]: 'Accrued over time',
    [AccrualMethod.FIXED]: 'Fixed amount accrued regardless of time',
    [AccrualMethod.HOURS_WORKED]: 'Accrued based on hours worked',
  };

  const backgroundColor = policyTypeColors[policy.type].bg;
  const color = policyTypeColors[policy.type].text;

  return (
    <section className="max-w-[446px] rounded-2xl border border-soft-200 p-5 shadow-xs">
      <h2 className="font-medium text-strong-950">General Information</h2>

      <h3 className="mt-5 text-xs text-sub-600">Policy Name</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">{policy.name}</p>

      <h3 className="mt-5 text-xs text-sub-600">Policy Type</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        <Badge style={{ backgroundColor, color }}>{policy.type}</Badge>
      </p>

      {policy.isLimited && (
        <>
          <hr className="mt-5 border-soft-200" />

          <h2 className="mt-5 font-medium text-strong-950">Accrual Details</h2>

          <h3 className="mt-5 flex items-center gap-px text-xs text-sub-600">Accrual Method</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">{accrualsDescription[policy.accrualMethod] || '-'}</p>

          <h3 className="mt-5 flex items-center gap-px text-xs text-sub-600">Accrual Rate</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {policy.accrualMethod === AccrualMethod.HOURS_WORKED
              ? `${policy.accrualHoursRate} hour${policy.accrualHoursRate > 1 ? 's' : ''} earned per ${policy.accrualHoursInterval} worked hours`
              : `${policy.accrualHoursRate} hours per year`}
          </p>

          <h3 className="mt-5 flex items-center gap-px text-xs text-sub-600">Accrual Period Reset</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {policy.accrualResetDate ? dayjs(policy.accrualResetDate).format('MMMM Do') : 'Employee Start Date'}
          </p>

          <h3 className="mt-5 flex items-center gap-px text-xs text-sub-600">Accrual limit</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {policy.accrualLimit ? `${+policy.accrualLimit} hours` : '-'}
          </p>

          <h3 className="mt-5 flex items-center gap-px text-xs text-sub-600">Carryover</h3>
          <p className="mt-1.5 h-fit text-sm text-strong-950">
            {policy.carryoverLimit ? `${+policy.carryoverLimit} hours` : '-'}
          </p>
        </>
      )}

      <hr className="mt-5 border-soft-200" />

      <h3 className="mt-5 text-xs text-sub-600">Add New Employees Automatically</h3>
      <p className="mt-1.5 h-fit text-sm text-strong-950">
        {policy.addNewEmployeesAutomatically ? (
          <StatusBadge>Enabled</StatusBadge>
        ) : (
          <StatusBadge status="disabled">Disabled</StatusBadge>
        )}
      </p>
    </section>
  );
}
