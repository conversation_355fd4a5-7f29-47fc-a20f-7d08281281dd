import Layout from 'components/dashboard/Layout';
import DocumentsView from 'components/documents/DocumentsView';
import EmployeeTaxDocuments from 'components/people/employee-view/EmployeeTaxDocuments';
import ContractorDocuments from 'components/people/contractor-view/ContractorDocuments';
import { useAuth } from 'hooks/useAuth';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import FileDownloadLine from '@/hammr-icons/FileDownloadLine';

const Documents: React.FC = () => {
  const { user } = useAuth();

  if (!user) return null;

  // TODO -> this will be handled in the future, right now this menu is not available to non-admins
  if (!user?.isCompanyAdmin) {
    return (
      <Layout>
        <PageHeader title="Documents" icon={<FileDownloadLine />} noPadding />
        <div>
          <div className="mx-auto pt-6 text-sm text-strong-950">
            View and download any of your previously signed documents.
          </div>
          <div className="lg:w-4/5">
            {user?.checkEmployeeId && <EmployeeTaxDocuments checkEmployeeId={user.checkEmployeeId} />}
            {user?.checkContractorId && <ContractorDocuments contractorId={user.checkContractorId} />}
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageHeader title="Documents" icon={<FileDownloadLine />} noPadding />
      <div className="flex h-full flex-col pb-6">
        <DocumentsView />
      </div>
    </Layout>
  );
};

export default Documents;
