/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef, useState } from 'react';
import { useDateSelection } from 'hooks/useDateSelection';
import Layout from 'components/dashboard/Layout';
import Timesheets from 'components/timesheets/Timesheets';
import { AgGridReact } from '@ag-grid-community/react';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import BillLine from '@/hammr-icons/BillLine';
import Button from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import ArrowDownSLine from '@/hammr-icons/ArrowDownSLine';
import { cn } from '@/hammr-ui/lib/utils';
import { Menu, MenuContent, MenuTrigger } from '@/hammr-ui/components/Menu';
import TimesheetExport from '@/components/timesheets/TimesheetExport';
import { useTimesheetListStore } from '@/components/timesheets/store';
import { useRouter } from 'next/router';
import CreateBulkTimesheetsModal from '@/components/timesheets/CreateBulkTimesheetsModal';
import { useAuth } from '@/hooks/useAuth';

const TimesheetsPage = () => {
  const { user } = useAuth();

  const isExportingRef = useRef(false);
  const [showTimesheetModal, setShowTimesheetModal] = useState(false);
  const [showBulkTimesheetModal, setShowBulkTimesheetModal] = useState(false);
  const gridRef = useRef<AgGridReact>(null);
  const router = useRouter();

  const { currentStartDateSelected, currentEndDateSelected, currentSelectedDateFilter, initialized } =
    useDateSelection();

  useEffect(() => {
    if (initialized && currentSelectedDateFilter !== 'CUSTOM') {
      router.replace('/timesheets', undefined, { shallow: true });
    }
  }, [currentSelectedDateFilter]);

  useEffect(() => {
    useTimesheetListStore.setState((state) => ({
      ...state,
      selectedProjectIds: [],
      selectedEmployeeIds: [],
    }));
  }, []);

  useEffect(() => {
    useTimesheetListStore.setState({
      selectedStatusKeys: router.query.status ? [(router.query.status as string).toUpperCase()] : [],
    });
  }, [router.query.status]);

  useEffect(() => {
    useTimesheetListStore.setState({
      selectedHasAlerts: router.query.isResolved === 'false',
      selectedProjectIds: Number(router.query.project) ? [Number(router.query.project)] : [],
    });
  }, [router.query.isResolved, router.query.project]);

  useEffect(() => {
    if (router.query.modal === 'add') {
      setShowTimesheetModal(true);
    }
  }, [router.query.modal]);

  const handleShowModal = (isOpen: boolean) => {
    setShowTimesheetModal(isOpen);

    // This is just for removing the query parameters like "modal=add",
    //    which makes the modal auto-show, e.g., from the /dashboard page
    // in the future, we need to specifically remove just the "modal" query param and not the other params too
    router.replace('/timesheets', undefined, { shallow: true });
  };

  return (
    <Layout noPadding>
      <PageHeader
        title="Timesheets"
        icon={<BillLine />}
        headerRight={
          <div className="flex items-center gap-3">
            {(user?.role === 'ADMIN' || user?.role === 'FOREMAN') && (
              <Menu>
                <MenuTrigger asChild>
                  <Button variant="outline" afterContent={<ArrowDownSLine />}>
                    Export
                  </Button>
                </MenuTrigger>
                <MenuContent className={cn('min-w-48')} align="end">
                  <TimesheetExport
                    gridRef={gridRef}
                    currentStartDateSelected={currentStartDateSelected}
                    currentEndDateSelected={currentEndDateSelected}
                    isExportingRef={isExportingRef}
                  />
                </MenuContent>
              </Menu>
            )}

            <Button beforeContent={<AddLine />} onClick={() => setShowTimesheetModal(true)}>
              Add Timesheet
            </Button>
          </div>
        }
      />
      <div className="flex flex-1 overflow-hidden px-8 py-6">
        <Timesheets
          isExportingRef={isExportingRef}
          showTimesheetModal={showTimesheetModal}
          setShowTimesheetModal={handleShowModal}
          gridRef={gridRef}
          from={parseInt(router.query.from as string, 10)}
          to={parseInt(router.query.to as string, 10)}
        />
      </div>

      <CreateBulkTimesheetsModal open={showBulkTimesheetModal} setOpen={setShowBulkTimesheetModal} />
    </Layout>
  );
};

export default TimesheetsPage;
