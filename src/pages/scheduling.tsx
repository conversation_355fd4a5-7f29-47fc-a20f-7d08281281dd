import { useState, useCallback, useMemo, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import dayjs from 'dayjs';

import Layout from 'components/dashboard/Layout';
import ViewScheduleEventSlideOver from 'components/scheduling/ViewScheduleEventSlideOver';
import ConfirmDeleteEventModal from 'components/scheduling/ConfirmDeleteEventModal';
import ScheduleCalendar from 'components/scheduling/ScheduleCalendar';

import { useAuth } from 'hooks/useAuth';
import { useCompany } from 'hooks/useCompany';

import {
  formatForCalendarResource,
  denormalizeAndFlattenResourceData,
  uniqueResources,
  formatForCalendarEvent,
  standardizeResourceData,
  formatProjectsForCalendarEvents,
} from 'utils/dataTransformers';
import { sortListAlphabetically, enhanceCrewsData } from 'utils/collectionHelpers';

import MultiScheduleDeleteModal from 'components/scheduling/MultiScheduleDeleteModal';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import CalendarLine from '@/hammr-icons/CalendarLine';
import AddLine from '@/hammr-icons/AddLine';
import Button from '@/hammr-ui/components/button';
import CreateProjectModal from 'components/projects/CreateProjectModal';
import { apiRequest } from '@/utils/requestHelpers';
import { PaginatedData } from '@/interfaces/pagination';
import { TimeOffRequest } from '@/interfaces/timeoff';
import { useRouter } from 'next/router';
import AddScheduleEventModal from '@/components/scheduling/AddScheduleEventModal';
import EditScheduleEventModal from '@/components/scheduling/EditScheduleEventModal';

export default function Scheduling() {
  const queryClient = useQueryClient();

  const { user } = useAuth();
  const { company } = useCompany();
  const router = useRouter();

  const [activeDates, setActiveDates] = useState<{ start: Date; end: Date }>({
    start: null,
    end: null,
  });

  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);
  const [isCreateSlideOverOpen, setIsCreateSlideOverOpen] = useState(false);
  useEffect(() => {
    if (router.query.modal === 'add') {
      setIsCreateSlideOverOpen(true);
    }
  }, [router.query.modal]);

  const [isViewSlideOverOpen, setIsViewSlideOverOpen] = useState(false);
  const [isEditSlideOverOpen, setIsEditSlideOverOpen] = useState(false);
  const [isMultiScheduleDeleteModalOpen, setIsMultiScheduleDeleteModalOpen] = useState(false);
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState(false);

  const [selectedResourceType, setSelectedResourceType] = useState<string>('projects');
  const [selectedResource, setSelectedResource] = useState(null);
  const [selectedEventData, setSelectedEventData] = useState(null);

  const urlParams = useMemo(() => {
    if (!activeDates.start || !activeDates.end) return null;

    return {
      from: dayjs(activeDates.start).valueOf(),
      to: dayjs(activeDates.end).valueOf(),
    };
  }, [activeDates]);

  const scheduleEventsQuery = useQuery({
    queryKey: ['scheduleEvents', urlParams],
    queryFn: () => apiRequest('/schedule-events', { urlParams }),
    enabled: !!urlParams,
  });

  const userSchedulesQuery = useQuery({
    queryKey: ['userSchedules', urlParams],
    queryFn: () => apiRequest('/users/schedule', { urlParams }),
    enabled: !!urlParams,
  });

  const crewsQuery = useQuery({
    queryKey: ['crews'],
    queryFn: () => apiRequest('/crews'),
  });

  const timeOffRequestsQuery = useQuery({
    queryKey: [
      'time-off-requests',
      dayjs(activeDates.start).startOf('day').valueOf(),
      dayjs(activeDates.end).endOf('day').valueOf(),
    ],
    queryFn() {
      return apiRequest<PaginatedData<TimeOffRequest, 'timeOffRequests'>>('/time-off-requests', {
        urlParams: {
          status: ['APPROVED', 'PAID'],
          startDate: dayjs(activeDates.start).startOf('day').valueOf(),
          endDate: dayjs(activeDates.end).endOf('day').valueOf(),
        },
      });
    },
    enabled: dayjs(activeDates.start).isValid(),
  });

  const availableUsers = sortListAlphabetically(userSchedulesQuery.data?.users ?? [], 'firstName');
  const availableCrews = enhanceCrewsData(crewsQuery.data?.crews ?? []);

  const timeOffEvents = useMemo(() => {
    if (!timeOffRequestsQuery.data?.timeOffRequests) return [];

    return timeOffRequestsQuery.data.timeOffRequests.flatMap((request) => {
      const startDate = dayjs(request.startDate);
      const endDate = dayjs(request.endDate);
      const daysDiff = endDate.diff(startDate, 'days');

      // If it's a single day request, return a single event
      if (daysDiff === 0) {
        return [
          {
            eventId: `timeoff-${request.id}`,
            projectName: request.timeOffPolicy.name, // This is only used for generating the title
            userId: request.userId, // This is only used for filtering unique resources
            startTime: dayjs(request.startDate).format('YYYY-MM-DD'),
            endTime: dayjs(request.endDate).add(1, 'day').format('YYYY-MM-DD'),
            resourceId: request.userId,
            isTimeOff: true,
            fullName: `${request.user.firstName} ${request.user.lastName}`,
            hours: request.totalHours,
          },
        ];
      }

      // For multi-day requests, create an array of daily events
      return Array.from({ length: daysDiff + 1 }, (_, index) => {
        const currentDate = startDate.add(index, 'days');
        return {
          eventId: `timeoff-${request.id}-${index}`,
          projectName: request.timeOffPolicy.name, // This is only used for generating the title
          userId: request.userId, // This is only used for filtering unique resources
          startTime: currentDate.format('YYYY-MM-DD'),
          endTime: currentDate.add(1, 'day').format('YYYY-MM-DD'),
          resourceId: request.userId,
          isTimeOff: true,
          fullName: `${request.user.firstName} ${request.user.lastName}`,
          hours: request.totalHours / (daysDiff + 1),
        };
      });
    });
  }, [timeOffRequestsQuery.data?.timeOffRequests]);

  // Transform data when queries complete
  const { resourceData, eventsData } = useMemo(() => {
    if (!scheduleEventsQuery.data) {
      return {
        resourceData: [],
        eventsData: [],
      };
    }

    const flattenedResources = denormalizeAndFlattenResourceData(scheduleEventsQuery.data.scheduleEvents);
    let filteredResources = [];
    let flattenedEvents = [];

    if (selectedResourceType === 'employees') {
      const allResources = [...flattenedResources, ...timeOffEvents];

      const result = uniqueResources(allResources, 'userId');
      filteredResources = formatForCalendarResource(result, 'users');
      flattenedEvents = formatForCalendarEvent(allResources, 'users');
    } else {
      const result = uniqueResources(flattenedResources, 'projectId');
      filteredResources = formatForCalendarResource(result, 'projects');
      const standardizedResources = standardizeResourceData(scheduleEventsQuery.data.scheduleEvents);
      flattenedEvents = formatProjectsForCalendarEvents(standardizedResources);
    }

    return {
      resourceData: filteredResources,
      eventsData: flattenedEvents,
    };
  }, [scheduleEventsQuery.data, selectedResourceType, timeOffEvents]);

  const refresh = useCallback(() => {
    setIsCreateSlideOverOpen(false);
    setIsViewSlideOverOpen(false);
    setIsEditSlideOverOpen(false);
    setIsConfirmDeleteModalOpen(false);

    // Invalidate relevant queries
    queryClient.invalidateQueries({ queryKey: ['scheduleEvents'] });
    queryClient.invalidateQueries({ queryKey: ['userSchedules'] });
  }, [queryClient]);

  const resourceSelectionHandler = (value) => {
    setSelectedResourceType(value);
  };

  const dateClickHandler = (data) => {
    // data should be resource
    setSelectedResource(data);
    setIsCreateSlideOverOpen(true);
  };

  const handleEventClick = (data) => {
    // open the slider and then update the data
    setIsViewSlideOverOpen(true);
    setSelectedEventData(data);
  };

  if (!user || !company?.isSchedulingEnabled) return null;

  return (
    <Layout noPadding>
      <div className="flex h-screen flex-col">
        <PageHeader
          title="Scheduling"
          icon={<CalendarLine />}
          headerRight={
            <div className="flex flex-row gap-4">
              <Button
                variant="outline"
                color="primary"
                beforeContent={<AddLine />}
                onClick={() => {
                  setShowCreateProjectModal(true);
                }}
              >
                Create Project
              </Button>
              <Button beforeContent={<AddLine />} onClick={() => setIsCreateSlideOverOpen(true)}>
                Add Event
              </Button>
            </div>
          }
        />
        <ScheduleCalendar
          dateSetHandler={setActiveDates}
          resourceSelectHandler={resourceSelectionHandler}
          resourceTypeSelected={selectedResourceType}
          weekStartDay={company?.overtimeSettings.weekStartDay}
          resourcesData={resourceData}
          availableUsers={availableUsers}
          availableCrews={availableCrews}
          eventsData={eventsData}
          isLoading={scheduleEventsQuery.isPending}
          dateClickHandler={(data) => dateClickHandler(data)}
          eventClickHandler={(data) => handleEventClick(data)}
          callback={refresh}
          timeOffEvents={timeOffEvents}
        />
      </div>
      <CreateProjectModal open={showCreateProjectModal} setOpen={setShowCreateProjectModal} />
      <AddScheduleEventModal
        open={isCreateSlideOverOpen}
        setOpen={(isOpen) => {
          setIsCreateSlideOverOpen(isOpen);
          if (isOpen === false) {
            router.replace('/scheduling', undefined, { shallow: true });
            setSelectedResource(null);
          }
        }}
        currentResourceData={selectedResource}
        equipmentId={Number(router.query.equipmentId)}
      />
      {selectedEventData && (
        <EditScheduleEventModal
          open={isEditSlideOverOpen}
          setOpen={setIsEditSlideOverOpen}
          eventData={selectedEventData}
        />
      )}
      <ViewScheduleEventSlideOver
        open={isViewSlideOverOpen}
        setOpen={setIsViewSlideOverOpen}
        currentEventData={selectedEventData}
        editCallback={setIsEditSlideOverOpen}
        deleteCallback={() => {
          if (selectedEventData?.extendedProps?.linkedEventId) {
            setIsMultiScheduleDeleteModalOpen(true);
          } else {
            setIsConfirmDeleteModalOpen(true);
          }
        }}
        persistDataCallback={(data) => setSelectedEventData(data)}
      />
      <MultiScheduleDeleteModal
        modalOpen={isMultiScheduleDeleteModalOpen}
        setModalOpen={setIsMultiScheduleDeleteModalOpen}
        projectName={selectedEventData?.extendedProps?.projectName}
        currentEventData={selectedEventData}
        callback={refresh}
      />
      <ConfirmDeleteEventModal
        open={isConfirmDeleteModalOpen}
        setOpen={setIsConfirmDeleteModalOpen}
        projectName={selectedEventData?.extendedProps?.projectName}
        currentEventData={selectedEventData}
        callback={refresh}
      />
    </Layout>
  );
}
