import { useEffect, useState } from 'react';
import Layout from '@/components/dashboard/Layout';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import { useCompany } from '@/hooks/useCompany';
import { useRouter } from 'next/router';
import { RiMap2Line, RiToolsFill } from '@remixicon/react';
import { Button } from '@/hammr-ui/components/button';
import AddLine from '@/hammr-icons/AddLine';
import { Tabs, TabList, TabItem, TabContent } from '@/hammr-ui/components/tabs';
import { ButtonGroup, ButtonGroupItem } from '@/hammr-ui/components/ButtonGroup';
import Table2Line from '@/hammr-icons/Table2Line';
import EquipmentContent from '@/components/equipment/EquipmentContent';
import { AddEquipmentModal } from '@/components/equipment/AddEquipmentModal';
import { equipmentService } from '@/services/equipment';
import { useQuery } from '@tanstack/react-query';
import { MultiSelect, Item } from '@/hammr-ui/components/multi-select';
import equipmentCategories from '@/services/equipment-categories';
import LoadingIndicator from '@/hammr-ui/components/LoadingIndicator';
import EquipmentEmptyState from '@/components/equipment/EquipmentEmptyState';

export default function EquipmentPage() {
  const { company } = useCompany();
  const router = useRouter();

  useEffect(() => {
    // Redirect if feature flag is not enabled
    if (company && !company.isEquipmentTrackingEnabled) {
      router.push('/dashboard');
    }
  }, [company, router]);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('in-use');
  const [viewMode, setViewMode] = useState<string>('table');
  const [selectedCategories, setSelectedCategories] = useState<Item[]>([]);

  const { data } = useQuery({
    queryKey: ['equipment', 'equipment-categories'],
    queryFn: () => equipmentCategories.getAll(),
  });

  // Create category items and preserve selection state from previous selections
  const categoryItems = data
    ? data.map((category) => {
        // Find if this category was previously selected
        const existingItem = selectedCategories.find((item) => item.value === category.id);
        return {
          label: category.name,
          value: category.id,
          isSelected: existingItem ? existingItem.isSelected : false,
        };
      })
    : [];

  // Handle category selection changes
  const handleCategoryChange = (items: Item[]) => {
    setSelectedCategories(items);
  };

  const selectedCategoryIds = selectedCategories.filter((item) => item.isSelected).map((item) => item.value as number);

  const equipmentQuery = useQuery({
    queryKey: ['equipment', activeTab, selectedCategoryIds],
    queryFn: () =>
      equipmentService.getAll({
        isArchived: activeTab === 'archived',
        categoryIds: selectedCategoryIds.length > 0 ? selectedCategoryIds : undefined,
      }),
  });

  const handleAddEquipment = () => setIsAddModalOpen(true);

  return (
    <Layout>
      <PageHeader
        noPadding
        title="Equipment"
        icon={<RiToolsFill />}
        headerRight={
          <Button beforeContent={<AddLine />} onClick={handleAddEquipment}>
            Add Equipment
          </Button>
        }
      />

      <div className="mt-6 h-full">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex h-full flex-col">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <TabList className="bg-weak-50">
                <TabItem value="in-use">In Use</TabItem>
                <TabItem value="archived">Archived</TabItem>
              </TabList>

              <MultiSelect
                label="All Categories"
                items={categoryItems}
                onChange={handleCategoryChange}
                searchable={true}
                buttonProps={{ className: 'min-w-[200px] capitalize' }}
                popoverProps={{ className: 'capitalize' }}
              />
            </div>

            <ButtonGroup>
              <ButtonGroupItem
                active={viewMode === 'table'}
                onClick={() => setViewMode('table')}
                className="w-[80px]"
                beforeContent={<Table2Line />}
              >
                Table
              </ButtonGroupItem>
              <ButtonGroupItem
                active={viewMode === 'map'}
                onClick={() => setViewMode('map')}
                className="w-[80px]"
                beforeContent={<RiMap2Line />}
              >
                Map
              </ButtonGroupItem>
            </ButtonGroup>
          </div>

          <TabContent value="in-use" className="mt-4 data-[state=active]:flex data-[state=active]:grow">
            {equipmentQuery.isPending ? (
              <div className="flex flex-grow items-center justify-center">
                <LoadingIndicator text="Fetching equipment..." />
              </div>
            ) : equipmentQuery.isError ? (
              <div className="flex flex-grow items-center justify-center">
                <p className="text-sm text-error-base">An error occured while fetching the equipment.</p>
              </div>
            ) : equipmentQuery.data.length === 0 ? (
              <div className="flex flex-grow items-center justify-center">
                <EquipmentEmptyState onAddEquipment={handleAddEquipment} isArchived={false} />
              </div>
            ) : (
              <EquipmentContent
                activeTab="in-use"
                viewMode={viewMode}
                equipment={equipmentQuery.data}
                refetchEquipment={equipmentQuery.refetch}
              />
            )}
          </TabContent>

          <TabContent value="archived" className="mt-4 data-[state=active]:flex data-[state=active]:grow">
            {equipmentQuery.isPending ? (
              <div className="flex flex-grow items-center justify-center">
                <LoadingIndicator text="Fetching equipment..." />
              </div>
            ) : equipmentQuery.isError ? (
              <div className="flex flex-grow items-center justify-center">
                <p className="text-sm text-error-base">An error occured while fetching the equipment.</p>
              </div>
            ) : equipmentQuery.data.length === 0 ? (
              <div className="flex flex-grow items-center justify-center">
                <EquipmentEmptyState onAddEquipment={handleAddEquipment} isArchived={true} />
              </div>
            ) : (
              <EquipmentContent
                activeTab="archived"
                viewMode={viewMode}
                equipment={equipmentQuery.data}
                refetchEquipment={equipmentQuery.refetch}
              />
            )}
          </TabContent>
        </Tabs>

        <AddEquipmentModal
          open={isAddModalOpen}
          setOpen={setIsAddModalOpen}
          categories={
            data
              ? data.map((category) => ({
                  label: category.name,
                  value: category.name,
                }))
              : []
          }
        />
      </div>
    </Layout>
  );
}
