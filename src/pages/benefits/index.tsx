import { useAuth } from 'hooks/useAuth';
import React, { useState } from 'react';
import Layout from 'components/dashboard/Layout';
import CreateCompanyBenefitModal from 'components/benefits/CreateCompanyBenefitModal';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import Button from '@/hammr-ui/components/button';
import Benefits from '@/components/benefits/Benefits';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import { useRouter } from 'next/router';
import GarnishmentsDeductions from '@/components/people/employee-view/GarnishmentsDeductions';
import { RiAddLine, RiHeart3Line } from '@remixicon/react';
import AddPostTaxDeductionModal from '@/components/post-tax-deductions/AddPostTaxDeductionModal';

export default function BenefitsPage() {
  const { user } = useAuth();

  const [showCreateCompanyBenefitModal, setShowCreateCompanyBenefitModal] = useState(false);
  const [showCreateDeductionModal, setShowCreateDeductionModal] = useState(false);

  const router = useRouter();

  if (!user || !user?.isCompanyAdmin) return null;

  return (
    <Layout noPadding>
      <PageHeader
        title="Benefits"
        icon={<RiHeart3Line />}
        headerRight={
          router.query.tab === 'post-tax-deductions' ? (
            <Button beforeContent={<RiAddLine />} onClick={() => setShowCreateDeductionModal(true)}>
              Add Post-Tax Deduction
            </Button>
          ) : (
            <Button beforeContent={<RiAddLine />} onClick={() => setShowCreateCompanyBenefitModal(true)}>
              Create Benefit
            </Button>
          )
        }
      />

      <Tabs
        className="mx-8"
        value={(router.query.tab as string) ?? 'benefits'}
        onValueChange={(tab) =>
          router.push({ pathname: router.pathname, query: { tab } }, undefined, { shallow: true })
        }
      >
        <TabsList>
          <TabsTrigger value="benefits">Benefits</TabsTrigger>
          <TabsTrigger value="post-tax-deductions">Garnishments & Deductions</TabsTrigger>
        </TabsList>
        <TabsContent value="benefits">
          <Benefits onCreateBenefitBtnClick={() => setShowCreateCompanyBenefitModal(true)} />
        </TabsContent>
        <TabsContent value="post-tax-deductions">
          <GarnishmentsDeductions />
        </TabsContent>
      </Tabs>

      <CreateCompanyBenefitModal open={showCreateCompanyBenefitModal} setOpen={setShowCreateCompanyBenefitModal} />
      <AddPostTaxDeductionModal open={showCreateDeductionModal} setOpen={setShowCreateDeductionModal} showEmployee />
    </Layout>
  );
}
