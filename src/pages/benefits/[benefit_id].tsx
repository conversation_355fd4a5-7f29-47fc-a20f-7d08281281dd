import BenefitsDetails from 'components/benefits/BenefitsDetails';
import CreateEmployeeBenefitModal from 'components/benefits/CreateEmployeeBenefitModal';
import DeleteCompanyBenefitModal from 'components/benefits/DeleteCompanyBenefitModal';
import EditCompanyBenefitModal from 'components/benefits/EditCompanyBenefitModal';
import EnrolledEmployees from 'components/benefits/EnrolledEmployees';
import Layout from 'components/dashboard/Layout';
import { CompanyBenefit, CompanyBenefitModified } from 'interfaces/benefit';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { getBenefit } from 'services/benefits';
import { getEmployeeBenefits } from 'services/employeeBenefits';
import { isItemActive } from 'utils/dateHelper';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween'; // ES 2015
import { useCompany } from 'hooks/useCompany';
import { PageHeader } from '@/hammr-ui/components/PageHeader';
import Heart3Line from '@/hammr-icons/Heart3Line';
import { BreadcrumbItem, Breadcrumbs } from '@/hammr-ui/components/Breadcrumbs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/hammr-ui/components/FlatTabs';
import Button from '@/hammr-ui/components/button';
import CloseCircleLine from '@/hammr-icons/CloseCircleLine';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

const tabs = [
  {
    name: 'General',
    key: 'general',
  },
  {
    name: 'Enrolled Employees',
    key: 'enrolled',
  },
] as const;

type BenefitTabsKey = (typeof tabs)[number]['key'];

const BenefitComponent = () => {
  const [benefitObject, setBenefitObject] = useState({} as CompanyBenefitModified);
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState<BenefitTabsKey>(tabs[0].key);
  const [isOpenEmployeeBenefit, setIsOpenEmployeeBenefit] = useState(false);
  const [employeeBenefits, setEmployeeBenefits] = useState([]);
  const [isOpenBenefitEdit, setIsOpenBenefitEdit] = useState(false);
  const [isOpenBenefitDelete, setIsOpenBenefitDelete] = useState(false);
  const { company } = useCompany();

  useEffect(() => {
    if (router.query.tab) {
      const tabToSelect = tabs.find((tab) => router.query.tab === tab.key);
      if (tabToSelect) {
        setSelectedTab(tabToSelect.key);
      }
    }
  }, [router.query]);

  const setEmployeeBenefitsRequest = async () => {
    // TODO -> fix this the correct way
    // this is a race condition when people go to ?tab=enrolled directly
    if (!benefitObject.id) {
      return;
    }

    const benefits = await getEmployeeBenefits({
      companyBenefitId: benefitObject.id.toString(),
    });

    setEmployeeBenefits(benefits.employeeBenefits);
  };

  const activeBenefits = employeeBenefits.filter(
    (eb) => !eb.benefitEndDate || dayjs.tz(eb.benefitEndDate, company.timezone) > dayjs.tz(new Date(), company.timezone)
  );

  const transformBenefit = (benefit: CompanyBenefit): CompanyBenefitModified => {
    const benefitStartDate = benefit.benefitStartDate ? benefit.benefitStartDate : null;
    const benefitEndDate = benefit.benefitEndDate ? benefit.benefitEndDate : null;
    return {
      ...benefit,
      benefitStartDate,
      benefitEndDate,
    };
  };

  useEffect(() => {
    setEmployeeBenefitsRequest();
  }, [benefitObject]);

  const fetchBenefit = async (benefitId) => {
    const { companyBenefit } = await getBenefit(benefitId);
    setBenefitObject(transformBenefit(companyBenefit));
  };

  useEffect(() => {
    if (!router.query.benefit_id) return;
    // company benefit
    fetchBenefit(router.query.benefit_id);
  }, [router.query]);

  const isActive = isItemActive(benefitObject.benefitStartDate, benefitObject.benefitEndDate);

  return (
    <Layout>
      <PageHeader
        noPadding
        title={benefitObject?.name}
        icon={<Heart3Line />}
        headerRight={
          selectedTab === 'general' ? (
            <div className="flex gap-3">
              <Button
                disabled={!isActive}
                variant="outline"
                color="error"
                onClick={() => setIsOpenBenefitDelete(true)}
                beforeContent={<CloseCircleLine />}
              >
                Deactivate
              </Button>
              <Button disabled={!isActive} className="w-20" onClick={() => setIsOpenBenefitEdit(true)}>
                Edit
              </Button>
            </div>
          ) : (
            <Button onClick={() => setIsOpenEmployeeBenefit(true)} disabled={!isActive}>
              Enroll Employee
            </Button>
          )
        }
        breadcrumb={
          <Breadcrumbs>
            <BreadcrumbItem text="Benefits" onClick={() => router.push('/benefits')} />
            <BreadcrumbItem text={benefitObject?.name} active />
          </Breadcrumbs>
        }
      />

      <Tabs
        className="mt-6 flex flex-grow flex-col"
        value={selectedTab}
        onValueChange={(value: BenefitTabsKey) => setSelectedTab(value)}
      >
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.key}
              value={tab.key}
              onClick={() => {
                const newQuery = { ...router.query, tab: tab.key };
                router.replace(
                  {
                    pathname: router.pathname,
                    query: newQuery as Record<any, any>,
                  },
                  '',
                  { shallow: false }
                );
              }}
            >
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="general">
          <BenefitsDetails {...benefitObject} />
        </TabsContent>
        <TabsContent value="enrolled" className="data-[state=active]:flex data-[state=active]:grow">
          <EnrolledEmployees
            employeeBenefits={employeeBenefits}
            setBenefitsRequest={setEmployeeBenefitsRequest}
            {...benefitObject}
          />
        </TabsContent>
      </Tabs>

      <EditCompanyBenefitModal
        open={isOpenBenefitEdit}
        setOpen={setIsOpenBenefitEdit}
        callback={() => fetchBenefit(benefitObject.id)}
        companyBenefit={benefitObject}
      />
      <DeleteCompanyBenefitModal
        open={isOpenBenefitDelete}
        setOpen={setIsOpenBenefitDelete}
        callback={() => fetchBenefit(benefitObject.id)}
        companyBenefit={benefitObject}
      />
      <CreateEmployeeBenefitModal
        open={isOpenEmployeeBenefit}
        setOpen={setIsOpenEmployeeBenefit}
        callback={setEmployeeBenefitsRequest}
        companyBenefit={benefitObject}
        enrolledEmployeesIds={activeBenefits.map((eb) => eb.userId)}
      />
    </Layout>
  );
};

export default BenefitComponent;
