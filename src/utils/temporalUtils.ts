import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { HammrEarningRate } from '@/interfaces/earning-rate';
import { EmployeeClassification } from '@/interfaces/classifications';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Validates and normalizes a date string to ensure it can be parsed by dayjs.
 * Handles common date formats including YYYY-MM-DD, ISO strings, and timestamps.
 * @param dateString - The date string to validate and normalize
 * @returns The normalized date string or null if invalid
 */
export const validateAndNormalizeDate = (dateString: string | null | undefined): string | null => {
  if (!dateString) return null;

  const parsed = dayjs(dateString);
  if (!parsed.isValid()) {
    console.warn(`Invalid date string provided: ${dateString}`);
    return null;
  }

  return parsed.toISOString();
};

/**
 * Safely parses a point in time, falling back to current time if invalid.
 * @param pointInTime - The point in time to parse
 * @param timezone - The timezone to use
 * @returns A dayjs object representing the parsed time
 */
const parsePointInTime = (
  pointInTime?: Date | string | number,
  timezone: string = 'America/Los_Angeles'
): dayjs.Dayjs => {
  if (!pointInTime) return dayjs().tz(timezone);

  const parsed = dayjs(pointInTime);
  if (!parsed.isValid()) {
    console.warn(`Invalid pointInTime provided: ${pointInTime}. Using current time instead.`);
    return dayjs().tz(timezone);
  }

  return parsed.tz(timezone);
};

/**
 * Generic function to find active entities based on temporal criteria
 * @param entities - Array of entities to check
 * @param timezone - Timezone to use for date calculations
 * @param dateAccessor - Function to extract date information from an entity
 * @param filterFn - Optional filter function to apply additional criteria
 * @param sortFn - Optional sort function to determine priority when multiple entities are active
 * @returns The active entity or null if none found
 */
export const getActiveEntity = <T>(
  entities: T[],
  timezone: string = 'America/Los_Angeles',
  dateAccessor: (entity: T) => { startDate?: number | string | Date; endDate?: number | string | Date | null },
  filterFn?: (entity: T) => boolean,
  sortFn?: (a: T, b: T) => number
): T | null => {
  if (!entities || entities.length === 0) {
    return null;
  }

  // Get current time in the specified timezone
  const now = dayjs().tz(timezone);

  // Find active entities based on current time and date ranges
  const activeEntities = entities
    .filter((entity) => {
      const dates = dateAccessor(entity);
      if (!dates?.startDate) return false;

      const startDate = dayjs(dates.startDate).tz(timezone);
      const endDate = dates.endDate ? dayjs(dates.endDate).tz(timezone) : null;

      // Check if current time falls within the entity's date range
      const isAfterStart = now.isAfter(startDate) || now.isSame(startDate);
      const isBeforeEnd = !endDate || now.isBefore(endDate) || now.isSame(endDate);

      const isInDateRange = isAfterStart && isBeforeEnd;

      // Apply additional filter if provided
      return isInDateRange && (!filterFn || filterFn(entity));
    })
    .sort(sortFn || (() => 0)); // Default to no sorting if no sort function provided

  // Return the first one if multiple are active
  return activeEntities[0] || null;
};

/**
 * Gets the active classification from a list of classifications based on a specific point in time.
 * @param classifications - Array of classifications to check
 * @param timezone - Timezone to use for date calculations (default: 'America/Los_Angeles')
 * @param pointInTime - Specific point in time to check (defaults to current time)
 * @returns The active classification or null if none found
 */
export const getActiveClassification = (
  classifications: Array<EmployeeClassification>,
  timezone: string = 'America/Los_Angeles',
  pointInTime?: Date | string | number
) => {
  if (!classifications || classifications.length === 0) {
    return null;
  }

  const now = parsePointInTime(pointInTime, timezone);

  // Find the active classification based on the specified point in time and date ranges
  const activeClassifications = classifications
    .filter((classification) => {
      // Check if classification exists
      if (!classification) {
        return false;
      }

      if (!classification?.startDate) {
        return false;
      }

      const startDate = dayjs(classification.startDate).tz(timezone);
      const endDate = classification.endDate ? dayjs(classification.endDate).tz(timezone) : null;

      // Check if the specified point in time falls within the classification's date range
      const isAfterStart = now.isAfter(startDate) || now.isSame(startDate);
      const isBeforeEnd = !endDate || now.isBefore(endDate) || now.isSame(endDate);

      return isAfterStart && isBeforeEnd;
    })
    .sort((a, b) => {
      // Sort by ID in descending order to get the highest ID first
      const idA = parseInt(a.id?.toString() || '0', 10);
      const idB = parseInt(b.id?.toString() || '0', 10);
      return idB - idA;
    });

  // Return the first one (highest ID) if multiple are active
  return activeClassifications[0] || null;
};

/**
 * Gets the active earning rate from a list of earning rates based on a specific point in time.
 * @param earningRates - Array of earning rates to check
 * @param timezone - Timezone to use for date calculations (default: 'America/Los_Angeles')
 * @param type - Type of earning rate to filter for ('REG' or 'OT')
 * @param pointInTime - Specific point in time to check (defaults to current time)
 * @returns The active earning rate or null if none found
 */
export const getActiveEarningRate = (
  earningRates: HammrEarningRate[],
  timezone: string = 'America/Los_Angeles',
  type: 'REG' | 'OT' = 'REG',
  pointInTime?: Date | string | number
): HammrEarningRate | null => {
  if (!earningRates || earningRates.length === 0) {
    return null;
  }

  const now = parsePointInTime(pointInTime, timezone);

  // Find the active earning rate based on the specified point in time and date ranges
  const activeEarningRates = earningRates
    .filter((earningRate) => {
      if (!earningRate?.startDate || earningRate.type !== type) return false;

      const startDate = dayjs(earningRate.startDate).tz(timezone);
      const endDate = earningRate.endDate ? dayjs(earningRate.endDate).tz(timezone) : null;

      // Check if the specified point in time falls within the earning rate's date range
      const isAfterStart = now.isAfter(startDate) || now.isSame(startDate);
      const isBeforeEnd = !endDate || now.isBefore(endDate) || now.isSame(endDate);

      return isAfterStart && isBeforeEnd;
    })
    .sort((a, b) => {
      // Sort by ID in descending order to get the highest ID first
      const idA = parseInt(a.id || '0', 10);
      const idB = parseInt(b.id || '0', 10);
      return idB - idA;
    });

  // Return the first one (highest ID) if multiple are active
  return activeEarningRates[0] || null;
};
