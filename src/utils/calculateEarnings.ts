import { HammrEarningRatePeriod } from '@/interfaces/earning-rate';
import { formatUSD } from '@/utils/format';

export function formatSalary(amount: number, rate: HammrEarningRatePeriod | Lowercase<HammrEarningRatePeriod>) {
  const rateMappings: Record<HammrEarningRatePeriod, string> = {
    HOURLY: 'hour',
    ANNUALLY: 'year',
    PIECE_RATE: '',
  };
  const transformedRate = rateMappings[rate.toUpperCase()] ? `/${rateMappings[rate.toUpperCase()]}` : '';
  return `${formatUSD.format(amount)}${transformedRate}`;
}
