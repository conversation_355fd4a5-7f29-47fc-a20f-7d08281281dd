export const WORKING_HOURS_IN_YEAR = 2080; // can be used as the divisor when converting annual salary to hourly rate - 2080 is approximate
export const DEFAULT_ERROR_MESSAGE = 'Something went wrong';
export const ONE_FOOT_TO_METERS = 0.3048;
export const AWS_CUSTOMER_BUCKET = process.env.NEXT_PUBLIC_CUSTOMER_BUCKET || 'hammr-customer-files-staging';

export const FOUNDATION_ACCOUNT_TYPES = ['assets', 'liabilities', "owner's equity", 'revenue', 'expenses'];

// employee - contractors
export const INVALID_WORKER_CLASSIFICATION = 'User work classification mismatch';
