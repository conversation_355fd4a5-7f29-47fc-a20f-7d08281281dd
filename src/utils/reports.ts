import dayjs from 'dayjs';
import { Project } from 'interfaces/project';
import _ from 'lodash';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
pdfMake.vfs = pdfFonts.pdfMake.vfs;
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween'; // ES 2015

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

export const generateFringeStatementPdf = ({
  fileName,
  project,
  fringeBenefitsResponse,
  weekEnding,
  signatoryName,
  signatoryTitle,
  orgTimezone,
}: {
  fileName: string;
  project: Project;
  fringeBenefitsResponse: Record<string, any>;
  weekEnding: number;
  signatoryName: string;
  signatoryTitle: string;
  orgTimezone: string;
}) => {
  const { companyName, companyAddress, wageTableData } = fringeBenefitsResponse;

  const uniqueBenefitProviders = {};
  const weekEndingDate = dayjs.tz(weekEnding, orgTimezone);
  const weekStartDate = weekEndingDate.subtract(6, 'days').startOf('day');

  const getBenefitSubtext = ({ startDate, endDate }) => {
    const startDateObject = dayjs.tz(startDate, orgTimezone).startOf('day');
    const endDateObject = dayjs.tz(endDate, orgTimezone).endOf('day');

    let appendix = '';
    if (endDate && endDateObject.isBefore(weekEndingDate.toDate())) {
      appendix = ` (end: ${endDateObject.format('MM/DD/YYYY')})`;
    } else if (startDate) {
      const showStart = startDateObject.isAfter(weekStartDate);
      appendix = showStart
        ? ` (start: ${startDateObject.format('MM/DD/YYYY')})`
        : '';
    }
    return appendix;
  };

  fringeBenefitsResponse.wageTableData.forEach((user) => {
    if (user.employeeBenefits) {
      user.employeeBenefits.forEach((eb) => {
        uniqueBenefitProviders[`${eb.benefitProviderName}-${eb.category}`] = {
          name: eb.benefitProviderName,
          address: eb.benefitProviderAddress,
          phone: eb.benefitProviderPhone,
          category: eb.category,
          benefitName: eb.benefitName,
        };
      });
    }
  });

  const headerSize = 13;

  const getFringeStatementHeaderTable = () => {
    const rows = [
      [
        {
          stack: [
            {
              text: 'Contractor/subcontractor name',
              fontSize: headerSize,
              bold: true,
              margin: [0, 0, 0, 12],
            },
            { text: companyName },
          ],
        },
        {
          stack: [
            {
              text: 'Business address',
              fontSize: headerSize,
              bold: true,
              margin: [0, 0, 0, 12],
            },
            { text: companyAddress },
          ],
        },
      ],
      [
        {
          stack: [
            {
              text: 'Project number/name',
              fontSize: headerSize,
              bold: true,
              margin: [0, 0, 0, 12],
            },
            { text: project.projectNumber },
          ],
        },
        {
          stack: [
            {
              text: 'Project address',
              fontSize: headerSize,
              bold: true,
              margin: [0, 0, 0, 12],
            },
            { text: project.address },
          ],
        },
      ],
      [
        {
          stack: [
            {
              text: `Today's date`,
              fontSize: headerSize,
              bold: true,
              margin: [0, 0, 0, 12],
            },
            { text: dayjs().format('MM/DD/YYYY') },
          ],
        },
        {
          stack: [
            {
              text: `Week ending`,
              fontSize: headerSize,
              bold: true,
              margin: [0, 0, 0, 12],
            },
            { text: weekEndingDate.format('MM/DD/YYYY') },
          ],
        },
      ],
    ];

    return {
      table: {
        headerRows: 2,
        margin: [12, 0],
        body: rows,
        widths: ['50%', '50%'],
        heights: 60,
      },
    };
  };

  const getBenefitProvidersTable = () => {
    const headerRowTable = [
      {
        text: 'Name',
        fontSize: headerSize,
        bold: true,
      },
      { text: 'Address', fontSize: headerSize, bold: true },
      { text: `Phone number`, fontSize: headerSize, bold: true },
    ];

    const rowsTable = Object.keys(uniqueBenefitProviders).map((key) => {
      return [
        { text: uniqueBenefitProviders[key].name },
        { text: uniqueBenefitProviders[key].address },
        { text: uniqueBenefitProviders[key].phone },
      ];
    });

    return {
      table: {
        headerRows: 1,

        body: [headerRowTable, ...rowsTable],
        widths: ['25%', '50%', '25%'],

        heights: (rowIndex) => (rowIndex === 0 ? 28 : 25),
      },
    };
  };

  const getEmployeeClassificationsTable = () => {
    const rows = _.flatten(
      wageTableData.map((wage) => {
        const startDateObject = dayjs.tz(wage.startDate, orgTimezone);
        const endDateObject = dayjs.tz(wage.endDate, orgTimezone);

        const startDateString = startDateObject.format('MM/DD/YYYY');
        const endDateString = wage.endDate
          ? endDateObject.format('MM/DD/YYYY')
          : 'present';

        const showClassificationsDates =
          startDateObject.isBetween(weekStartDate, weekEndingDate) ||
          endDateObject.isBetween(weekStartDate, weekEndingDate);

        const classificationDates = showClassificationsDates
          ? `(${startDateString} - ${endDateString})`
          : '';
        const classificationSubtext = `Fringe pay: $${wage.fringePay}/hour ${classificationDates}`;
        const mainHeader = [
          {
            fillColor: '#d9d9d9',
            colSpan: 4,
            stack: [
              {
                text:
                  wage.user.firstName +
                  ' ' +
                  wage.user.lastName +
                  ' (' +
                  wage.classification.name +
                  ')',
                bold: true,
                fontSize: headerSize,
                margin: [0, 2, 0, 2],
              },
              { text: classificationSubtext },
            ],
          },
          { text: '', width: 0 },
          {
            text: '',
          },
          { text: '', width: 0 },
        ];

        const headerRowTable = [
          {
            text: 'Benefit',
            fontSize: headerSize,
            bold: true,
          },
          { text: 'Category', fontSize: headerSize, bold: true },
          { text: `Provider name`, fontSize: headerSize, bold: true },
          { text: `Contribution`, fontSize: headerSize, bold: true },
        ];

        const rowsTable = [];

        wage.employeeBenefits.forEach((eb) => {
          const appendix = getBenefitSubtext({
            startDate: eb.benefitStartDate,
            endDate: eb.benefitEndDate,
          });

          rowsTable.push([
            { text: eb.benefitName },
            { text: eb.category.split('_').join(' ') },
            { text: eb.benefitProviderName },
            {
              stack: [
                { text: `$${eb.fringeHourlyContribution}/hour` },
                { text: appendix },
              ],
            },
          ]);
        });

        wage.fringeBenefits.forEach((fb) => {
          const appendix = getBenefitSubtext({
            startDate: fb.startDate,
            endDate: fb.endDate,
          });
          rowsTable.push([
            { text: fb.name },
            { text: '' },
            { text: '' },
            { stack: [{ text: `$${fb.amount}/hour` }, { text: appendix }] },
          ]);
        });

        rowsTable.push([
          { text: 'Cash fringe' },
          { text: '' },
          { text: '' },
          { text: `$${wage.cashFringe}/hour` },
        ]);

        const benefitTotal = wage.employeeBenefits.reduce((acc, eb) => {
          if (eb.endDate && eb.endDate < new Date()) {
            return acc;
          }
          return acc + parseFloat(eb.fringeHourlyContribution);
        }, 0);

        const fringeTotal = wage.fringeBenefits.reduce((acc, fb) => {
          if (fb.endDate && fb.endDate < new Date()) {
            return acc;
          }
          return acc + parseFloat(fb.amount);
        }, 0);

        const total = benefitTotal + fringeTotal + parseFloat(wage.cashFringe);

        rowsTable.push([
          { text: 'Total', bold: true },
          { text: '' },
          { text: '' },
          { text: `$${total.toFixed(2)}/hour`, bold: true },
        ]);

        return [mainHeader, headerRowTable, ...rowsTable];
      })
    );

    return {
      table: {
        headerRows: 0,
        body: rows,
        widths: ['25%', '20%', '25%', '30%'],
        heights: 25,
      },
    };
  };

  const getFringeStatementFooterTable = () => {
    return {
      margin: [0, 24],
      table: {
        headerRows: 0,
        body: [
          [
            {
              stack: [
                { text: 'Name and title', bold: true },
                { text: signatoryName },
                { text: signatoryTitle },
              ],
            },
            {
              stack: [
                { text: 'Signature', bold: true },
                {
                  text: signatoryName,
                  italics: true,
                  bold: true,
                  fontSize: 18,
                },
              ],
            },
          ],
        ],
        widths: ['50%', '50%'],
        heights: 50,
      },
    };
  };

  const providerDetailsArray =
    Object.keys(uniqueBenefitProviders)?.length > 0
      ? [
          { text: 'Provider details', margin: [0, 24, 0, 12], fontSize: 16 },
          getBenefitProvidersTable(),
        ]
      : [];

  const doc = {
    pageOrientation: 'portrait',
    pageMargins: [12, 40, 12, 40],
    header: function (currentPage, pageCount, pageSize) {
      // pages index start at 1
      if (currentPage === 1) {
        return {
          text: 'Fringe Benefit Statement',
          alignment: 'left',
          margin: [12, 12],
          fontSize: 16,
          bold: true,
        };
      }
      // you can apply any logic and return any valid pdfmake element
    },
    content: [
      getFringeStatementHeaderTable(),
      ...providerDetailsArray,
      {
        text: 'Employee classifications',
        margin: [0, 24, 0, 12],
        fontSize: 16,
      },
      getEmployeeClassificationsTable(),
      getFringeStatementFooterTable(),
    ],
  };

  pdfMake.createPdf(doc).download(fileName);
};
