import { sortBy } from 'lodash';
import { Crew } from 'interfaces/crew';

export const sortListAlphabetically = <T = any>(collection: T[], propertyName: string) => {
  if (collection && collection.length) {
    return collection.sort((a, b) => {
      return a[propertyName].localeCompare(b[propertyName]);
    });
  }
  return [];
};

export const sortListByAscInts = (collection: any[], propertyName: string) => {
  if (collection && collection.length) {
    return collection.sort((a, b) => {
      return a[propertyName] - b[propertyName];
    });
  }

  return [];
};

export const range = (start, stop, step) =>
  Array.from({ length: (stop - start) / step + 1 }, (_, i) => start + i * step);

export const enhanceCrewsData = (crewsData: Crew[], hidden?: number[]) => {
  // essentially if crewLeadUser exists and they are not part of crewMembers
  // prepend that object to the crewMembers array
  const enhancedCrewsDataWithLead = crewsData
    .map((crew) => {
      const crewMembers = [...crew.crewMembers];
      if (crew.crewLeadUser) {
        const crewLeadUser = { ...crew.crewLeadUser };
        const memberIds = crewMembers.map((member) => member.crewMemberUser.id);
        if (!memberIds.includes(crewLeadUser.id)) {
          crewMembers.unshift({
            id: 0,
            crewMemberUser: crewLeadUser,
          });
        }
      }

      const sortedCrewMembers = sortBy(crewMembers, [(member) => member.crewMemberUser.firstName]);

      const filteredCrewMembers = hidden?.length
        ? sortedCrewMembers.filter((member) => !hidden.includes(member.crewMemberUser.id))
        : sortedCrewMembers;

      return {
        ...crew,
        crewMembers: filteredCrewMembers,
      };
    })
    .filter((crew) => crew.crewMembers.length);

  return enhancedCrewsDataWithLead;
};

type SortOrder = 'asc' | 'desc';
type PropertyPath = string[];

export interface SortCriteria {
  path: PropertyPath;
  order: SortOrder;
}

export const dynamicSort = <T>(items: T[], sortCriteria: SortCriteria[]): T[] => {
  return items.sort((a, b) => {
    for (const criterion of sortCriteria) {
      const aValue = getNestedValue(a, criterion.path);
      const bValue = getNestedValue(b, criterion.path);

      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      if (aValue > bValue) comparison = 1;

      if (comparison !== 0) {
        return criterion.order === 'asc' ? comparison : -comparison;
      }
    }

    // if criteria matched equally
    return 0;
  });
};

export const getNestedValue = (obj: any, path: PropertyPath) => {
  return path.reduce((acc: any, part: string) => acc && acc[part], obj);
};
