import { Company } from '@/interfaces/company';
import { FilterModel, GridApi, IRowNode, ValueFormatterParams } from '@ag-grid-community/core';
import { getColumnsToExportIds } from 'components/timesheets/utils';
import { flatten } from 'lodash';
import moment from 'moment';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import { formatterTwoDecimal, minutesToFormattedDecimals, minutesToFormattedTime } from 'utils/format';

pdfMake.vfs = pdfFonts.pdfMake.vfs;

const FONT_SIZE = 8.6;

export const getSetColumnFilterComparator = (valueA, valueB) => {
  if (valueA === null || valueA === undefined) {
    return -1;
  }

  if (valueB === null || valueB === undefined) {
    return 1;
  }

  return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
};

const cellValueAggregator = (params: ValueFormatterParams) => {
  const index = params.column.getColId();
  const children = getAllVisibleChildren(params.node);
  if (params.node.allChildrenCount > 0) {
    const total = children
      .map((child) => {
        return child.data[index];
      })
      .reduce((acc, curr) => acc + curr, 0);

    return total;
  }
  return params.value;
};

// used for columns that can't be grouped
export const customCellValueFormatter = ({
  params,
  type,
  useDecimalHours,
  isExporting,
  skipFormatting,
}: {
  params: ValueFormatterParams;
  type?: 'billableTime' | 'currency' | 'time';
  useDecimalHours?: boolean;
  isExporting?: boolean;
  skipFormatting?: boolean;
}) => {
  let formatter: (...args: any) => any = (value) => value;

  if (type === 'billableTime') {
    if (useDecimalHours && isExporting) {
      formatter = minutesToFormattedDecimals;
    } else {
      formatter = minutesToFormattedTime;
    }
  } else if (type === 'currency') {
    formatter = formatterTwoDecimal;
  } else if (type === 'time') {
    // time like 4:42 PM can't be grouped
    if (params.node.group) return;
    formatter = (value) => moment(value).format('h:mm a');
  }

  const aggregatedValues = cellValueAggregator(params);

  const formattedValue = skipFormatting ? aggregatedValues : formatter(aggregatedValues);

  if (formattedValue === 'Invalid date') {
    return '-';
  }

  return formattedValue;
};

export const getAllVisibleChildren = (node: IRowNode) => {
  if (!node.childrenAfterFilter) {
    return node;
  }

  const allChildren = node.childrenAfterFilter.map((child: IRowNode) => {
    return getAllVisibleChildren(child);
  });

  const allChildrenFlat = flatten(allChildren);
  return allChildrenFlat;
};

export const isFirstRowItem = (node: IRowNode) => {
  const firstRowItem = node.rowIndex === 0;
  const firstChildItem = node.parent && node.childIndex === 0;
  const firstItem = firstRowItem || firstChildItem;
  return firstItem;
};

/**
 * This function iterates over all of the columns to create a row of header cells
 * Only used for PDF export
 */

const getHeaderToExport = (gridApi, company, userRole = 'ADMIN') => {
  const columns = getColumnsToExportObjects(gridApi, company, userRole);

  return columns.map((column) => {
    const { field } = column.getColDef();
    const sort = column.getSort();

    // in the future with more PDF exports we can have a function that generates headerName
    let headerName = column.getColDef().headerName ?? field;

    if (field === 'employeeId') {
      headerName = 'Emp ID';
    } else if (field === 'breakMinutes') {
      headerName = 'Break';
    } else if (field === 'description') {
      headerName = 'Notes';
    } else if (field === 'position') {
      headerName = 'Pos';
    } else if (field === 'regularMinutes') {
      headerName = 'Reg';
    } else if (field === 'overtimeMinutes') {
      headerName = 'OT';
    } else if (field === 'totalWages') {
      headerName = 'Earnings';
    } else if (field === 'hourlyWage') {
      headerName = 'Wage';
    }

    const headerNameUppercase = headerName[0].toUpperCase() + headerName.slice(1);
    const headerCell = {
      text: headerNameUppercase + (sort ? ` (${sort})` : ''),
      bold: true,
      fontSize: FONT_SIZE,
    };
    return headerCell;
  });
};

const getCellToExport = (
  gridApi,
  column,
  node,
  {
    onlyAggregatedData = false,
  }: {
    onlyAggregatedData?: boolean;
  } = {}
) => {
  const columnObject = gridApi.getColumn(column);
  const colDef = columnObject.getColDef();
  const { valueFormatter, cellRenderer } = colDef;

  // For group rows
  if (node.group) {
    // For the grouping column, use the displayed value
    if (columnObject.getColId() === node.rowGroupColumn?.getColId()) {
      // Get the displayed value from the UI (the grouping cell already shows formatted value)
      return {
        text:
          valueFormatter?.({
            value: node.key,
            node,
            column: columnObject,
            api: gridApi,
          }) ??
          node.key ??
          '',
        fontSize: FONT_SIZE,
      };
    }

    // For other columns in group rows, use aggFunc if available
    try {
      const value = node.aggData?.[columnObject.getColId()];
      if (value !== undefined) {
        // If there's a value formatter for this column, use it
        if (valueFormatter) {
          const formattedValue = valueFormatter({
            value,
            node,
            column: columnObject,
            api: gridApi,
          });
          return { text: cellRenderer ? cellRenderer(formattedValue) : formattedValue || '', fontSize: FONT_SIZE };
        }
        return { text: cellRenderer ? cellRenderer(value) : value, fontSize: FONT_SIZE };
      }
    } catch (e) {
      // If no aggData or error, continue to regular cell handling
    }
  } else {
    if (onlyAggregatedData) {
      return;
    }
  }

  // Regular cell handling
  const value = gridApi.getValue(column, node);
  let formattedValue = value;

  if (valueFormatter) {
    formattedValue =
      valueFormatter({
        node,
        column: columnObject,
        value: value,
      }) ?? '';
  }
  return { text: formattedValue, fontSize: FONT_SIZE };
};

/**
 * This function iterates over all of the rows and columns to create
 * a matrix of cells
 */
const getRowsToExport = (gridApi, company, userRole = 'ADMIN') => {
  const columns = getColumnsToExportObjects(gridApi, company, userRole);

  const rowsToExport = [];
  gridApi.forEachNodeAfterFilterAndSort((node) => {
    const rowToExport = columns.map((column) => getCellToExport(gridApi, column, node)).filter(Boolean);
    if (rowToExport.length) {
      rowsToExport.push(rowToExport);
    }
  });

  return rowsToExport;
};

/**
 * This function returns a PDF document definition object - the input for pdfMake.
 */
const getDocument = (
  gridApi,
  company,
  header,
  columnNames?: string[],
  columnWidths: string[] | undefined = undefined,
  userRole = 'ADMIN'
) => {
  // used for default in case columnWidths is not provided
  const columns = gridApi.getAllDisplayedColumns();

  const headerRow = getHeaderToExport(gridApi, company, userRole);
  const rows = getRowsToExport(gridApi, company, userRole);

  // in case we ever do PDF export for a different table than timesheets
  const widths = columnWidths ? columnWidths : `${100 / columns.length}%`;

  return {
    pageOrientation: 'landscape', // can also be 'portrait'
    // top and bottom margins are necessary to make room for the header and footer
    pageMargins: [12, 40, 12, 60],
    footer: function (currentPage, pageCount) {
      if (currentPage === pageCount) {
        return {
          margin: [0, 8, 0, 0],
          stack: [
            {
              image:
                'data:image/png;base64,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',
              alignment: 'left',
              height: 26,
              width: 105,
              margin: [12, -15],
              color: 'black',
            },
            {
              text: 'Construction Payroll, HR, Operations \nCustom report generated in 1-click | Hammr.com',
              alignment: 'left',
              margin: [12, 16],
              color: 'black',
              fontSize: 10,
              bold: true,
              lineHeight: 1.1,
            },
          ],
        };
      }
    },
    header: function (currentPage, pageCount, pageSize) {
      // pages index start at 1
      if (currentPage === 1) {
        return {
          text: header,
          alignment: 'center',
          margin: [0, 12],
          fontSize: 14,
          bold: true,
        };
      }
      // you can apply any logic and return any valid pdfmake element
    },

    content: [
      {
        layout: 'lightHorizontalLines',
        table: {
          // the number of header rows
          headerRows: 1,

          // the width of each column, can be an array of widths
          widths,

          // all the rows to display, including the header rows
          body: [headerRow, ...rows],

          heights: (rowIndex) => (rowIndex === 0 ? 28 : 25),
        },
      },
    ],
  };
};

const getColumnsToExportObjects = (gridApi, company: Company | undefined, userRole = 'ADMIN') => {
  const columnsToExport = getColumnsToExportIds(gridApi, company?.timeTrackingSettings.isDriveTimeEnabled, userRole);

  const columns = gridApi.getColumns();

  const filteredCols = columns
    .filter((column) => {
      return columnsToExport.findIndex((_col) => _col === column.getColId()) > -1;
    })
    .sort((a, b) => columnsToExport.indexOf(a.getColId()) - columnsToExport.indexOf(b.getColId()));

  return filteredCols;
};

/**
 * @deprecated this function is hardcoded to export only timesheet related data. please use exportToPDF to export any type of data
 */
export const exportToPDF = (
  gridApi,
  fileName: string,
  header: string,
  columnNames?: string[],
  columnWidths?: string[],
  company?: Company,
  userRole = 'ADMIN'
) => {
  const doc = getDocument(gridApi, company, header, columnNames, columnWidths, userRole);
  pdfMake.createPdf(doc).download(fileName);
};

export const exportTableToPDF = (
  gridApi,
  fileName: string,
  header: string | ((currentPage, pageCount, pageSize) => any),
  {
    columnNames,
    columnWidths,
    onlyAggregatedData = false,
    footerRenderer,
  }: {
    columnNames?: string[];
    columnWidths?: string[];
    onlyAggregatedData?: boolean;
    footerRenderer?: (gridAPI: GridApi<unknown>) => { text: string; fontSize: number }[];
  } = {}
) => {
  const columns = gridApi.getAllDisplayedColumns();

  const columnsToExport = columns.filter((column) =>
    columnNames?.length ? columnNames.findIndex((_col) => _col === column.getColId()) > -1 : true
  );

  const headerRow = columnsToExport.map((column) => {
    const { field } = column.getColDef();
    const sort = column.getSort();
    const headerName = column.getColDef().headerName ?? field;

    const headerNameUppercase = headerName[0].toUpperCase() + headerName.slice(1);
    return {
      text: headerNameUppercase + (sort ? ` (${sort})` : ''),
      bold: true,
      fontSize: 9,
    };
  });

  const rows = [];
  gridApi.forEachNodeAfterFilterAndSort((node) => {
    const row = columns
      .map((column) =>
        getCellToExport(gridApi, column, node, {
          onlyAggregatedData,
        })
      )
      .filter(Boolean);
    if (row.length) {
      rows.push(row);
    }
  });

  if (footerRenderer) {
    rows.push(footerRenderer(gridApi));
  }

  // in case we ever do PDF export for a different table than timesheets
  const widths = columnWidths ? columnWidths : `${100 / columns.length}%`;

  const doc = {
    pageOrientation: 'landscape', // can also be 'portrait'
    // top and bottom margins are necessary to make room for the header and footer
    pageMargins: [12, 40, 12, 40],
    footer: function (currentPage, pageCount) {
      if (currentPage === pageCount) {
        return {
          margin: [0, 8, 0, 0],
          stack: [
            {
              image:
                'data:image/png;base64,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',
              alignment: 'left',
              height: 26,
              width: 105,
              margin: [12, -30],
              color: 'black',
            },
            {
              text: 'Construction Payroll, HR, Operations \nCustom report generated in 1-click | Hammr.com',
              alignment: 'left',
              margin: [12, 36],
              color: 'black',
              fontSize: 10,
              bold: true,
              lineHeight: 1.1,
            },
          ],
        };
      }
    },
    header:
      typeof header === 'function'
        ? header
        : function (currentPage, pageCount, pageSize) {
            // pages index start at 1
            if (currentPage === 1) {
              return {
                text: header,
                alignment: 'center',
                margin: [0, 12],
                fontSize: 14,
                bold: true,
              };
            }
            // you can apply any logic and return any valid pdfmake element
          },

    content: [
      {
        layout: 'lightHorizontalLines',
        table: {
          // the number of header rows
          headerRows: 1,

          // the width of each column, can be an array of widths
          widths,

          // all the rows to display, including the header rows
          body: [headerRow, ...rows],

          heights: (rowIndex) => (rowIndex === 0 ? 28 : 25),
        },
      },
    ],
  };

  pdfMake.createPdf(doc).download(fileName);
};

export const formatAgGridFilters = (filters: FilterModel, filtersDataMap: Record<string, any>) => {
  const transformedMap = {};

  const keyMap = {
    costCodeName: 'costCodes',
    employeeName: 'employees',
    projectName: 'projects',
  };

  // we need the values to be string delimited ids
  for (const key in filters) {
    if (keyMap[key]) {
      transformedMap[keyMap[key]] = filters[key].values.map((entity) => {
        if (key === 'employeeName') {
          return filtersDataMap[keyMap[key]].find((item) => `${item.firstName} ${item.lastName}` === entity).id;
        } else {
          const filterEntry = filtersDataMap[keyMap[key]].find((item) => item?.name === entity);
          if (filterEntry && filterEntry.id) {
            return filterEntry.id;
          } else {
            return -1;
          }
        }
      });
    }
  }

  return transformedMap;
};

export const filterComparator = (valueA, valueB, n1, n2, isDescending) => {
  // if the values are the same we return 0
  if (valueA === valueB) {
    return 0;
  }

  // the following two blocks are there to send undefined and null values to the bottom of the stack
  if (valueA === null || valueA === undefined) {
    return isDescending ? -1 : 1;
  }

  if (valueB === null || valueB === undefined) {
    return isDescending ? 1 : -1;
  }

  // some fields are defined as strings but in practice are numbers so we need to compare them as numbers if we can
  const parsedNumberA = parseFloat(valueA);
  const parsedNumberB = parseFloat(valueB);

  if (!isNaN(parsedNumberA) && !isNaN(parsedNumberB)) {
    return parsedNumberA - parsedNumberB;
  }

  // ag-grid sorts strings in a case sensitive way so this block ensures we are case insensitive and we send null and undefined values to the bottom of the stack
  if (typeof valueA === 'string' && typeof valueB === 'string') {
    // the following 3 blocks are there to send null and undefined values to the bottom of the stack
    if (valueB === '' && valueA !== '') {
      return isDescending ? 1 : -1;
    }

    if (valueA === '' && valueB !== '') {
      return isDescending ? -1 : 1;
    }

    // compares strings in a case insensitive way
    return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
  }

  // this is the default in case there are objects that have to be sorted so that the algorithm can still handle those
  return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
};
