import { pad<PERSON>ero } from 'utils/utils';
import moment from 'moment';

export const formatterTwoDecimal = (number: number): string => {
  return formatUSD.format(number);
};

export const formatterTwoDecimalString = (number: string): string => {
  return `$ ${parseFloat(number).toFixed(2).toLocaleString()}`;
};

export const formatUSD = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
});

// New formatUSD without decimals
export const formatUSDWithoutDecimals = new Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

export const formatNumberWithoutDecimals = new Intl.NumberFormat('en-US', {
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

export const formatPhoneNumber = (phoneNumberString) => {
  const cleaned = ('' + phoneNumberString).replace(/\D/g, '');
  const match = cleaned.match(/^(1|)?(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    const intlCode = match[1] ? '+1 ' : '';
    return [intlCode, '(', match[2], ') ', match[3], '-', match[4]].join('');
  }
  return phoneNumberString;
};

export const formatPhoneInputToDigits = (phoneString) => {
  return phoneString.replace(/(\D+)/g, '');
};

export const formatTotalHours = (minutes) => {
  return `${Math.floor(minutes / 60)}h ${Math.round(minutes % 60)}m`;
};

// there is an edge case with phone numbers where country code is not +1
// in those cases, we will return the last 10 digits of the phone number
export const formatPhoneFromApi = (phoneString: string) => {
  return phoneString?.startsWith('+1') ? phoneString?.substring(2) : phoneString?.substring(phoneString.length - 10);
};

export const formatTotalWages = (wages) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0,
    minimumFractionDigits: 0,
  }).format(wages);
};

export const formatHoursWorked = (startTimestamp: number, endTimestamp: number, breakDuration = 0, breaks = []) => {
  if (!endTimestamp) {
    endTimestamp = parseInt(moment().format('x'), 10);
  }

  const startTime = moment(startTimestamp);
  const endTime = moment(endTimestamp);

  const minutesBeforeBreakDeduction = endTime.diff(startTime, 'minutes');
  let minutes = minutesBeforeBreakDeduction;

  if (breaks.length) {
    breaks.forEach((b) => {
      if (b?.start && b?.end) {
        const start = moment(b.start);
        const end = moment(b.end);
        minutes -= end.diff(start, 'minutes');
      }
    });
  } else if (breakDuration) {
    minutes = minutesBeforeBreakDeduction - breakDuration / 60;
  }

  return `${Math.floor(minutes / 60)}h ${Math.round(minutes % 60)}m`;
};

/**
 * Helper function to get the compensation type from an earning rate period
 */
export const getCompensationTypeFromPeriod = (period?: string): string | undefined => {
  if (!period) return undefined;

  switch (period) {
    case 'ANNUALLY':
      return 'Salaried';
    case 'HOURLY':
      return 'Hourly';
    default:
      return undefined;
  }
};

export const calculateMinutesWorked = (
  startTimestamp: string,
  endTimestamp: string,
  breakDuration = 0,
  breaks = []
) => {
  if (!endTimestamp) {
    endTimestamp = moment().toISOString();
  }

  const startTime = moment(startTimestamp);
  const endTime = moment(endTimestamp);

  const minutesBeforeBreakDeduction = endTime.diff(startTime, 'minutes');
  let minutes = minutesBeforeBreakDeduction;

  if (breaks.length) {
    breaks.forEach((b) => {
      if (b?.start && b?.end) {
        const start = moment(b.start);
        const end = moment(b.end);
        minutes -= end.diff(start, 'minutes');
      }
    });
  } else if (breakDuration) {
    minutes = minutesBeforeBreakDeduction - breakDuration / 60;
  }

  return minutes;
};

export const formatMinutesToHoursWorked = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0 && remainingMinutes === 0) {
    return '-';
  }

  if (hours === 0) {
    return `${remainingMinutes.toFixed(0)}m`;
  }

  return `${hours}h ${remainingMinutes.toFixed(0)}m`;
};

export const minutesToFormattedTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (hours === 0 && remainingMinutes === 0) return '';
  return `${padZero(hours)}:${padZero(remainingMinutes)}`;
};

const format = (value: any) => (value < 10 ? `0${value}` : value.toString());

export const minutesToFormattedDecimals = (value: number) =>
  `${format(Math.floor(value / 60))}.${format(Math.round((value % 60) / 0.6))}`;

export function formatFileSize(sizeInBytes: number): string {
  if (sizeInBytes < 100 * 1024) {
    return `${(sizeInBytes / 1024).toFixed(1)} KB`;
  }
  return `${(sizeInBytes / 1024 / 1024).toFixed(1)} MB`;
}

/**
 * Formats hours for display with 'h' suffix
 * Shows decimals only when non-zero
 * @param hours - The hours value to format
 * @returns Formatted string with 'h' suffix
 */
export function formatHours(hours: number): string {
  const rounded = Math.floor(hours * 100) / 100;

  if (rounded === 0) {
    return '0h';
  }

  if (Number.isInteger(rounded)) {
    return `${rounded.toFixed(0)}h`;
  }

  return `${rounded.toFixed(2)}h`;
}
