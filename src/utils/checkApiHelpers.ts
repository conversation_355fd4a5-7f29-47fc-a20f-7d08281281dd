import { apiRequestCheck, downloadFileFromResponse } from '@/utils/requestHelpers';

// First call to get the polling URL
export const getPaperChecksUrl = async (payrollId: string): Promise<string> => {
  const response = await apiRequestCheck(`payrolls/${payrollId}/paper_checks`, {
    method: 'GET',
    convertToJson: true,
  });
  return response.url;
};

// Download PDF through our proxy
export const downloadPaperChecksPdf = async (url: string) => {
  const MAX_RETRIES = 30;
  const POLLING_INTERVAL = 1000; // 1 second

  const poll = async (attempts = 0): Promise<void> => {
    if (attempts >= MAX_RETRIES) {
      throw new Error('The document request timed out. Please try again.');
    }

    try {
      const response = await apiRequestCheck(removeCheckApiPrefix(url), {
        method: 'GET',
        convertToJson: false,
        validateResponse: false,
      });

      if (response.ok) {
        const filename = `paper_checks-${new Date().toISOString()}.pdf`;
        await downloadFileFromResponse(response, filename);
        return;
      }

      if (response.status === 403) {
        throw new Error('Failed to download the PDF. Please try again.');
      }

      if (response.status === 404 || response.status === 500) {
        // Keep polling - PDF not ready yet
        return new Promise((resolve) => {
          setTimeout(() => resolve(poll(attempts + 1)), POLLING_INTERVAL);
        });
      }

      throw new Error(`Failed to download the paper checks with status code: ${response.status}`);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      throw error;
    }
  };

  return poll();
};

export const removeCheckApiPrefix = (url: string) => {
  return url
    .replace('https://sandbox.checkhq.com/', '')
    .replace('https://api.checkhq.com/', '')
    .replace('https://render-sandbox.checkhq.com/', '')
    .replace('https://render.checkhq.com/', '');
};
