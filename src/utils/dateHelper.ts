import moment from 'moment';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(timezone);
dayjs.extend(utc);

export const isNowBetweenDates = (date1: Date, date2: Date): boolean => {
  const now = new Date();
  return date1 <= now && now <= date2;
};

export const isItemActive = (startDateStr: string, endDateStr: string) => {
  if (startDateStr && endDateStr) {
    const momentStartDate = moment(startDateStr);
    const momentEndDate = moment(endDateStr);
    return isNowBetweenDates(momentStartDate.toDate(), momentEndDate.toDate());
  } else if (startDateStr) {
    const momentStartDate = moment(startDateStr);
    return momentStartDate.toDate() <= new Date();
  } else {
    return false;
  }
};

export const formatLocaleUsa = (date: Date | string | number) => {
  if (!date) return;
  const momentDate = moment(date).format('M/DD/YYYY');

  return momentDate;
};

/**
 * @param inputDate - The date to format. This should be a Unix timestamp (milliseconds since epoch).
 * @returns A formatted string representing the date.
 */
export const labelDate = (inputDate: number) => {
  const inputDateDayjs = dayjs(inputDate);
  const today = dayjs().startOf('day');
  const tomorrow = dayjs().add(1, 'days').startOf('day');
  const dateToCheck = inputDateDayjs.startOf('day');

  if (dateToCheck.isSame(today)) {
    return inputDateDayjs.format('[at] h:mm a [today]');
  } else if (dateToCheck.isSame(tomorrow)) {
    return inputDateDayjs.format('[at] h:mm a [tomorrow]');
  } else {
    return inputDateDayjs.format('[on] MMM D, YYYY, [at] h:mm a');
  }
};

export const minutesAfterMidnight = (time: moment.Moment | dayjs.Dayjs) => {
  return time.minute() + time.hour() * 60;
};

export function formatToYYYYMMDD(date: Date) {
  if (date) {
    const yyyy = date.getFullYear();
    const mm = date.getMonth() + 1;
    const dd = date.getDate();
    return `${yyyy}-${mm < 10 ? '0' + mm : mm}-${dd < 10 ? '0' + dd : dd}`;
  }
}

export function getNumberOfDaysInMonth(month: number, year?: number) {
  const currentYear = year || new Date().getFullYear();
  return new Date(currentYear, month, 0).getDate();
}

type PayPeriod = {
  start: string;
  end: string;
};

/**
 * Calculates pay periods between fromDate and toDate.
 * Each period spans 7 days, except possibly the last one which may be shorter.
 * If the date range is less than 7 days, returns a single period spanning the exact range.
 *
 * @param fromDate - Start date in YYYY-MM-DD format
 * @param toDate - End date in YYYY-MM-DD format
 * @returns Array of pay periods with start and end dates
 */
export function calculatePayPeriods({ fromDate, toDate }: { fromDate: string; toDate: string }): PayPeriod[] {
  const periods: PayPeriod[] = [];

  const rangeStart = dayjs(fromDate).startOf('day');
  const rangeEnd = dayjs(toDate).endOf('day');

  // If the range is invalid, return empty array
  if (rangeStart.isAfter(rangeEnd)) {
    return periods;
  }

  // If the range is 7 days or less, return a single period
  if (rangeEnd.diff(rangeStart, 'day') <= 6) {
    return [
      {
        start: rangeStart.format('YYYY-MM-DD'),
        end: rangeEnd.format('YYYY-MM-DD'),
      },
    ];
  }

  // Set up the first period
  let periodStart = rangeStart;
  let periodEnd = rangeStart.add(6, 'day');

  // Generate periods within the range
  while (!periodStart.isAfter(rangeEnd)) {
    // If this period would extend beyond the range end, adjust it
    if (periodEnd.isAfter(rangeEnd)) {
      periodEnd = rangeEnd;
    }

    periods.push({
      start: periodStart.format('YYYY-MM-DD'),
      end: periodEnd.format('YYYY-MM-DD'),
    });

    // Move to next period
    periodStart = periodEnd.add(1, 'day');
    periodEnd = periodStart.add(6, 'day');

    // If we've reached or passed the end, stop
    if (periodStart.isAfter(rangeEnd)) {
      break;
    }
  }

  return periods;
}
