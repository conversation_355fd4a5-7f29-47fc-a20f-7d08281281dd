/* eslint-disable @typescript-eslint/no-var-requires */
// This file sets a custom webpack configuration to use your Next.js app
// with Sentry.
// https://nextjs.org/docs/api-reference/next.config.js/introduction
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
const { withSentryConfig } = require('@sentry/nextjs');

module.exports = {
  webpack: (cfg) => {
    cfg.module.rules.push({
      test: /\.md$/,
      loader: 'frontmatter-markdown-loader',
    });
    return cfg;
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_CHECK_API_URL}/:path*`,
      },
    ];
  },
  images: {
    domains: [
      'hammr-customer-files-staging.s3.us-west-1.amazonaws.com',
      'hammr-customer-files-production.s3.us-west-1.amazonaws.com',
    ],
  },
};

module.exports = withSentryConfig(module.exports, {
  silent: true,
  ignore: [
    '.next/static/chunks/framework-*',
    '.next/static/chunks/framework.*',
    '.next/static/chunks/polyfills-*',
    '.next/static/chunks/webpack-*',
  ],
});
