{
  "compilerOptions": {
    "baseUrl": "./src",
    "target": "es2015",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": false,
    //    "noImplicitAny": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "paths": {
      "@/*": ["*"],
      "@/utils/*": ["utils/*", "hammr-ui/lib/*"],
      "@/components/ui/*": ["hammr-ui/components/*"],
      "@hammr-ui": ["hammr-ui"],
      "@hammr-ui/*": ["hammr-ui/*"],
      "@hammr-icons/*": ["hammr-icons/*"]
    }
  },
  "exclude": ["node_modules"],
  "include": ["next-env.d.ts", "**/*.d.ts", "**/*.ts", "**/*.tsx"]
}
