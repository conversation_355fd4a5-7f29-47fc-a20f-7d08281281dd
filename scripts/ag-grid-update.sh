#!/bin/bash

# requires jq -> brew install jq

# Fetch available versions of @ag-grid-community/core
echo "Fetching available versions of @ag-grid-community/core..."
versions=$(npm show @ag-grid-community/core versions --json | jq -r '.[]')

# Display versions in a numbered list
echo "Select a version to install:"
select version in $versions; do
  if [[ -n $version ]]; then
    echo "You have selected version: $version"
    break
  else
    echo "Invalid selection. Please try again."
  fi
done

# List of ag-grid dependencies
dependencies=(
  "@ag-grid-community/client-side-row-model"
  "@ag-grid-community/core"
  "@ag-grid-community/csv-export"
  "@ag-grid-community/react"
  "@ag-grid-community/styles"
  "@ag-grid-enterprise/column-tool-panel"
  "@ag-grid-enterprise/filter-tool-panel"
  "@ag-grid-enterprise/row-grouping"
  "@ag-grid-enterprise/set-filter"
  "@ag-grid-enterprise/side-bar"
  "@ag-grid-enterprise/menu"
  "@ag-grid-enterprise/server-side-row-model"
)

# Install each dependency using yarn with the selected version
for dependency in "${dependencies[@]}"; do
  yarn add "${dependency}@${version}"
done
