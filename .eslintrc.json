{
  "parser": "@typescript-eslint/parser",
  "extends": [
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:prettier/recommended"
    // "plugin:@next/next/recommended"
  ],
  "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier"],
  "rules": {
    "react/react-in-jsx-scope": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "react/prop-types": "off",
    "react-hooks/rules-of-hooks": "warn",
    "react-hooks/exhaustive-deps": "warn",
    "prettier/prettier": "warn",
    "@typescript-eslint/no-unused-vars": "warn",
    "react/display-name": "off"
  },
  "globals": {
    "React": "writable",
    "cy": true,
    "Cypress": true
  }
}
