# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# build
/build
/lib/

# misc
.DS_Store
.vscode
.lazy.lua

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
firestore-debug.log
ui-debug.log
firebase-debug.log

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# emulator files
.runtimeconfig.json
firestore_mock_data/

# cypress
screenshots/
# Sentry
.sentryclirc

# Sentry
next.config.original.js
.idea

# AI
.cursorrules
CONVERSION_SUMMARY.md
tsconfig.tsbuildinfo
