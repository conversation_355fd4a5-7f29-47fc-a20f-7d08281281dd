import type { Config } from 'tailwindcss';
import defaultTheme from 'tailwindcss/defaultTheme';
import plugin from 'tailwindcss/plugin';

const config: Config = {
  important: false,
  content: ['./src/**/*.{js,tsx}'],
  theme: {
    extend: {
      fontSize: {
        '2xs': [
          '11px',
          {
            lineHeight: '12px',
          },
        ],
      },
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        'steel-blue': {
          100: '#dae6f0',
          200: '#b5cde1',
          300: '#90b4d2',
          400: '#6b9bc3',
          500: '#4682B4',
          600: '#386890',
          700: '#2a4e6c',
          800: '#1c3448',
          900: '#0e1a24',
        },
        'royal-blue': {
          100: '#F0F1FC',
          200: '#F9FAFD',
          300: '#E7EEFA',
          400: '#959CED',
          500: '#1E5AD2',
          600: '#006CFA',
          700: '#3E4489',
          800: '#0144CC',
          900: '#1F2245',
        },
        'royal-gray': {
          50: '#FBFCFE',
          100: '#F1F5F9',
          150: '#E5E7EB',
          200: '#E2E8F0',
          300: '#CBD5E1',
          400: '#94A3B8',
          500: '#64748B',
          600: '#475569',
          700: '#334155',
          800: '#1E293B',
          900: '#0F172A',
        },
        'hammr-orange': {
          100: '#FFF0ED',
          200: '#FFD1C8',
          300: '#FFB2A4',
          400: '#FF937F',
          500: '#FF836D',
          600: '#FF6448',
          700: '#CC503A',
          800: '#993C2B',
          900: '#66281D',
        },
        'hammr-green': '#4BAB5B',
        'hammr-gray': '#8D8D8D',
        'hammr-black': '#111827',
        'hammr-blue': '#2196F3',
        'slate-gray': '#708090',
        'olive-drab': '#6B8E23',
        'hammr-gray-light': '#F8F8F8',
        'hammr-gray-dark': '#BABFC7',
        'hammr-gray-darker': '#727272',
        'hammr-gray-darkest': '#3D4043',
        teal: '#008080',
        'rosy-brown': '#BC8F8F',
        'hammr-yellow': '#F2AE03',
        'brick-red': '#CB4154',

        // hammr-ui - start
        strong: {
          950: 'rgb(var(--raw-strong-950))',
          400: 'rgb(var(--raw-strong-400))',
        },
        soft: {
          200: 'rgb(var(--raw-soft-200))',
          400: 'rgb(var(--raw-soft-400))',
        },
        sub: {
          300: 'rgb(var(--raw-sub-300))',
          600: 'rgb(var(--raw-sub-600))',
        },
        primary: {
          base: 'rgb(var(--raw-primary-base))',
          dark: 'rgb(var(--raw-primary-dark))',
          darker: 'rgb(var(--raw-primary-darker))',
        },
        error: {
          base: 'rgb(var(--raw-error-base))',
          darker: 'rgb(var(--raw-error-darker))',
          light: 'rgb(var(--raw-error-light))',
          lighter: 'rgb(var(--raw-error-lighter))',
        },
        success: {
          base: 'rgb(var(--raw-success-base))',
          dark: 'rgb(var(--raw-success-dark))',
          light: 'rgb(var(--raw-success-light))',
          lighter: 'rgb(var(--raw-success-lighter))',
        },
        faded: {
          base: 'rgb(var(--raw-faded-base))',
          dark: 'rgb(var(--raw-faded-dark))',
          light: 'rgb(var(--raw-faded-light))',
          lighter: 'rgb(var(--raw-faded-lighter))',
        },
        information: {
          base: 'rgb(var(--raw-information-base))',
          darker: 'rgb(var(--raw-information-darker))',
          light: 'rgb(var(--raw-information-light))',
          lighter: 'rgb(var(--raw-information-lighter))',
        },
        warning: {
          base: 'rgb(var(--raw-warning-base))',
          darker: 'rgb(var(--raw-warning-darker))',
          light: 'rgb(var(--raw-warning-light))',
          lighter: 'rgb(var(--raw-warning-lighter))',
        },
        feature: {
          base: 'rgb(var(--raw-feature-base))',
          darker: 'rgb(var(--raw-feature-darker))',
          light: 'rgb(var(--raw-feature-light))',
          lighter: 'rgb(var(--raw-feature-lighter))',
        },
        away: {
          base: 'rgb(var(--raw-away-base))',
          dark: 'rgb(var(--raw-away-dark))',
          light: 'rgb(var(--raw-away-light))',
          lighter: 'rgb(var(--raw-away-lighter))',
        },
        verified: {
          base: 'rgb(var(--raw-verified-base))',
          dark: 'rgb(var(--raw-verified-dark))',
          light: 'rgb(var(--raw-verified-light))',
          lighter: 'rgb(var(--raw-verified-lighter))',
        },
        static: {
          white: 'rgb(var(--raw-static-white))',
          black: 'rgb(var(--raw-static-black))',
        },
        disabled: {
          300: 'rgb(var(--raw-disabled-300))',
        },
        weak: {
          50: 'rgb(var(--raw-weak-50))',
          100: 'rgb(var(--raw-weak-100))',
        },
        surface: {
          800: 'rgb(var(--raw-surface-800))',
        },
        background: 'rgb(var(--raw-background))',
        foreground: 'rgb(var(--raw-foreground))',
        white: {
          DEFAULT: '#ffffff',
          0: 'rgb(var(--raw-white-0))',
        },
        neutral: {
          alpha: {
            24: 'rgb(var(--raw-color-neutral-alpha-24))',
          },
        },
        // hammr-ui - end
      },
      fontFamily: {
        sans: ['Inter', ...defaultTheme.fontFamily.sans],
      },
      maxWidth: {
        '8xl': '90rem',
      },
      borderRadius: {
        20: '20px',
        16: '16px',
        12: '12px',
        10: '10px',
        8: '8px',
        6: '6px',
        4: '4px',
      },
      boxShadow: {
        xs: '0px 1px 2px 0px rgba(10, 13, 20, 0.03)',
        md: '0px 16px 32px -12px rgba(14, 18, 27, 0.10)',
      },
      animation: {
        fadein: 'fadeIn 0.3s ease-in-out',
        fadeout: 'fadeOut 0.3s ease-in-out',
        zoomin: 'zoomIn 0.3s ease-in-out',
        zoomout: 'zoomOut 0.3s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          from: { opacity: '0' },
          to: { opacity: '1' },
        },
        fadeOut: {
          from: { opacity: '1' },
          to: { opacity: '0' },
        },
        zoomIn: {
          from: { scale: '0' },
          to: { scale: '1' },
        },
        zoomOut: {
          from: { scale: '1' },
          to: { scale: '0' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    plugin(function ({ addVariant }) {
      addVariant('disabled-within', `&:has(input:is(:disabled),button:is(:disabled))`);
    }),
  ],
};

export default config;
