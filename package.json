{"name": "Express", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "lint": "eslint --ext .ts,.tsx", "lint:fix": "eslint --ext .ts,.tsx --fix", "start": "next start", "postinstall": "patch-package", "install-ag-grid": "sh scripts/ag-grid-update.sh"}, "browserslist": ["> 1%"], "husky": {"hooks": {"pre-commit": "pretty-quick --staged && npm run lint"}}, "resolutions": {"strip-ansi": "6.0.1"}, "dependencies": {"@ag-grid-community/client-side-row-model": "32.3.6", "@ag-grid-community/core": "32.3.6", "@ag-grid-community/csv-export": "32.3.6", "@ag-grid-community/react": "32.3.6", "@ag-grid-community/styles": "32.3.6", "@ag-grid-enterprise/column-tool-panel": "32.3.6", "@ag-grid-enterprise/filter-tool-panel": "32.3.6", "@ag-grid-enterprise/menu": "32.3.6", "@ag-grid-enterprise/row-grouping": "32.3.6", "@ag-grid-enterprise/server-side-row-model": "32.3.6", "@ag-grid-enterprise/set-filter": "32.3.6", "@ag-grid-enterprise/side-bar": "32.3.6", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/react": "^6.1.8", "@fullcalendar/resource": "^6.1.8", "@fullcalendar/resource-daygrid": "^6.1.8", "@fullcalendar/resource-timeline": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@fullcalendar/timeline": "^6.1.8", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@react-google-maps/api": "^2.20.6", "@remixicon/react": "^4.6.0", "@sendbird/uikit-react": "^3.15.9", "@sentry/nextjs": "^7.39.0", "@tailwindcss/typography": "0.5.8", "@tanstack/react-query": "5", "autoprefixer": "10.0.2", "aws-sdk": "^2.1464.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "core-js": "^3.44.0", "dayjs": "^1.11.11", "eventemitter3": "^5.0.1", "fast-deep-equal": "^3.1.3", "file-saver": "^2.0.5", "immutability-helper": "^3.1.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.29.1", "netlify-cms-app": "2.15.72", "next": "^12.2", "patch-package": "^8.0.0", "pdfmake": "^0.2.12", "postcss": "8.4.14", "postcss-nesting": "^13.0.1", "react": "18.2", "react-day-picker": "^9.1.3", "react-dnd": "^16.0.1", "react-dom": "18.2", "react-hook-form": "^7.53.1", "react-phone-number-input": "^3.3.6", "react-rutter-link": "^1.2.0", "recharts": "^2.13.0", "tailwind-merge": "^2.5.3", "tailwindcss": "^3.4.4", "tailwindcss-animate": "^1.0.7", "tsify": "^5.0.4", "uuid": "^8.3.2", "yup": "^1.6.1", "zustand": "^4.4.4"}, "devDependencies": {"@next/eslint-plugin-next": "^13.1.2", "@types/google.maps": "^3.54.3", "@types/lodash": "^4.14.197", "@types/node": "14.11.5", "@types/react": "18.0.26", "@types/uuid": "^9.0.6", "@types/webpack-env": "1.15.3", "@typescript-eslint/eslint-plugin": "^7.5.0", "@typescript-eslint/parser": "^7.5.0", "eslint": "8.57.1", "eslint-config-prettier": "6.12.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.21.3", "eslint-plugin-react-hooks": "4.6.0", "express": "^4.17.1", "frontmatter-markdown-loader": "3.7.0", "http-proxy-middleware": "^0.19.1", "husky": "4.3.0", "lint-staged": "10.4.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "pretty-quick": "^4.0.0", "typescript": "^5.4.4", "webpack-dev-server": "^4.9.3"}}