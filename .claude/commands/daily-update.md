Get all the commits made **today** to this repository. Feel free to use `git` or `gh` CLI tools.

For each branch, list the commits created today in the format:

```
Today's date:
- Task 1 that I accomplished today
- Task 2 that I accomplished today
- Task 2 that I accomplished today: link to a github PR with corresponding this this branch
```

Here's a concrete example:

```
August 4:
- Refactored the authentication module to improve security
- Fixed a bug in the user profile page: https://github.com/Hammr-Inc/web/pull/1039
- Updated the README with new setup instructions
```

Note:

- For each branch, only add one entry.
- If there are no commits today, do not add an entry for that branch.
- If I have multiple commits, summarize them into a single entry.
